*
* Visual Studio Code Server
*
* By using the software, you agree to
* the Visual Studio Code Server License Terms (https://aka.ms/vscode-server-license) and
* the Microsoft Privacy Statement (https://privacy.microsoft.com/en-US/privacystatement).
*
Server bound to /tmp/code-************************************
Extension host agent listening on /tmp/code-************************************

[17:54:37] 




[17:54:37] Extension host agent started.
[node.js fs] readdir with filetypes failed with error:  Error: ENOENT: no such file or directory, scandir '/home/<USER>/.vscode-server/extensions/github.copilot-1.325.0'
    at async Object.readdir (node:internal/fs/promises:952:18)
    at async SC (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/out/server-main.js:43:4567)
    at async Object.Ah (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/out/server-main.js:43:4496)
    at async Ou.readdir (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/out/server-main.js:67:16002)
    at async Sl.t (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/out/server-main.js:64:51815)
    at async Sl.resolve (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/out/server-main.js:64:50931)
    at async yf.H (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/out/server-main.js:55:25227)
    at async Promise.all (index 1)
    at async yf.scanAllUserExtensions (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/out/server-main.js:55:20374)
    at async R8.H (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/out/server-main.js:55:9952)
    at async R8.s (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/out/server-main.js:55:7847) {
  errno: -2,
  code: 'ENOENT',
  syscall: 'scandir',
  path: '/home/<USER>/.vscode-server/extensions/github.copilot-1.325.0'
}
[17:54:38] Deleted marked for removal extension from disk github.copilot /home/<USER>/.vscode-server/extensions/github.copilot-1.325.0
[17:54:38] [<unknown>][ea4a47a8][ManagementConnection] New connection established.
[17:54:38] [<unknown>][095b529f][ExtensionHostConnection] New connection established.
[17:54:38] [<unknown>][095b529f][ExtensionHostConnection] <1861> Launched Extension Host Process.
[17:54:38] ComputeTargetPlatform: linux-x64
[17:54:40] ComputeTargetPlatform: linux-x64
New EH opened, aborting shutdown
[17:59:37] New EH opened, aborting shutdown
[18:08:19] [<unknown>][ea4a47a8][ManagementConnection] The client has disconnected gracefully, so the connection will be disposed.
[18:08:20] [<unknown>][095b529f][ExtensionHostConnection] <1861> Extension Host Process exited with code: 0, signal: null.
Last EH closed, waiting before shutting down
[18:08:20] Last EH closed, waiting before shutting down
[18:08:21] [<unknown>][28209d63][ExtensionHostConnection] New connection established.
[18:08:21] [<unknown>][313d4175][ManagementConnection] New connection established.
[18:08:21] [<unknown>][28209d63][ExtensionHostConnection] <4613> Launched Extension Host Process.
New EH opened, aborting shutdown
[18:13:20] New EH opened, aborting shutdown
rejected promise not handled within 1 second: Error: connect ECONNREFUSED 127.0.0.1:5000
stack trace: Error: connect ECONNREFUSED 127.0.0.1:5000
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1611:16)
[18:27:54] Error: connect ECONNREFUSED 127.0.0.1:5000
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1611:16) {
  errno: -111,
  code: 'ECONNREFUSED',
  syscall: 'connect',
  address: '127.0.0.1',
  port: 5000
}
rejected promise not handled within 1 second: Error: connect ECONNREFUSED 127.0.0.1:5000
stack trace: Error: connect ECONNREFUSED 127.0.0.1:5000
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1611:16)
[18:27:54] Error: connect ECONNREFUSED 127.0.0.1:5000
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1611:16) {
  errno: -111,
  code: 'ECONNREFUSED',
  syscall: 'connect',
  address: '127.0.0.1',
  port: 5000
}
