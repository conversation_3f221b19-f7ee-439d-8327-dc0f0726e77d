import { defineStore } from 'pinia'
import { ref, computed } from 'vue'

export const usePersonnelStore = defineStore('personnel', () => {
  // State
  const users = ref([])
  const departments = ref([])
  const skills = ref([])
  const currentUser = ref(null)
  const loading = ref(false)
  const error = ref(null)

  // Filters state
  const filters = ref({
    search: '',
    department: null,
    skill: null,
    role: null,
    location: null,
    sort: 'name'
  })

  // Pagination state
  const pagination = ref({
    page: 1,
    per_page: 20,
    total: 0,
    pages: 0
  })

  // Getters
  const filteredUsers = computed(() => {
    let filtered = users.value

    if (filters.value.search) {
      const search = filters.value.search.toLowerCase()
      filtered = filtered.filter(user =>
        user.full_name?.toLowerCase().includes(search) ||
        user.email?.toLowerCase().includes(search) ||
        user.position?.toLowerCase().includes(search)
      )
    }

    if (filters.value.department) {
      filtered = filtered.filter(user =>
        user.department_id === filters.value.department
      )
    }

    if (filters.value.skill) {
      filtered = filtered.filter(user =>
        user.skills?.some(skill => skill.id === filters.value.skill)
      )
    }

    if (filters.value.role) {
      filtered = filtered.filter(user => user.role === filters.value.role)
    }

    return filtered
  })

  const departmentTree = computed(() => {
    const buildTree = (parentId = null) => {
      return departments.value
        .filter(dept => dept.parent_id === parentId)
        .map(dept => ({
          ...dept,
          children: buildTree(dept.id)
        }))
    }
    return buildTree()
  })

  // Actions
  const fetchUsers = async (params = {}) => {
    loading.value = true
    error.value = null

    try {
      const queryParams = new URLSearchParams({
        page: params.page || pagination.value.page,
        per_page: params.per_page || pagination.value.per_page,
        ...params
      })

      const response = await fetch(`/api/personnel/users?${queryParams}`, {
        credentials: 'include'
      })

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }

      const data = await response.json()

      if (data.success) {
        users.value = data.data.users || []

        // L'API restituisce pagination come oggetto separato
        const paginationData = data.data.pagination || {}
        pagination.value = {
          page: paginationData.page || 1,
          per_page: paginationData.per_page || 20,
          total: paginationData.total || 0,
          pages: paginationData.pages || 0
        }
      } else {
        throw new Error(data.message || 'Errore nel caricamento utenti')
      }
    } catch (err) {
      error.value = err.message
      console.error('Errore fetchUsers:', err)
    } finally {
      loading.value = false
    }
  }

  const fetchUser = async (id) => {
    loading.value = true
    error.value = null

    try {
      const response = await fetch(`/api/personnel/users/${id}`, {
        credentials: 'include'
      })

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }

      const data = await response.json()

      if (data.success) {
        currentUser.value = data.data.user
        return data.data.user
      } else {
        throw new Error(data.message || 'Errore nel caricamento utente')
      }
    } catch (err) {
      error.value = err.message
      console.error('Errore fetchUser:', err)
      throw err
    } finally {
      loading.value = false
    }
  }

  const updateUser = async (id, userData) => {
    loading.value = true
    error.value = null

    try {
      const response = await fetch(`/api/personnel/users/${id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        credentials: 'include',
        body: JSON.stringify(userData)
      })

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }

      const data = await response.json()

      if (data.success) {
        // Update user in the list
        const index = users.value.findIndex(u => u.id === id)
        if (index !== -1) {
          users.value[index] = { ...users.value[index], ...data.data.user }
        }

        // Update current user if it's the same
        if (currentUser.value?.id === id) {
          currentUser.value = { ...currentUser.value, ...data.data.user }
        }

        return data.data.user
      } else {
        throw new Error(data.message || 'Errore nell\'aggiornamento utente')
      }
    } catch (err) {
      error.value = err.message
      console.error('Errore updateUser:', err)
      throw err
    } finally {
      loading.value = false
    }
  }

  const fetchDepartments = async () => {
    loading.value = true
    error.value = null

    try {
      const response = await fetch('/api/personnel/departments', {
        credentials: 'include'
      })

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }

      const data = await response.json()

      if (data.success) {
        departments.value = data.data.departments || []
      } else {
        throw new Error(data.message || 'Errore nel caricamento dipartimenti')
      }
    } catch (err) {
      error.value = err.message
      console.error('Errore fetchDepartments:', err)
    } finally {
      loading.value = false
    }
  }

  const fetchSkills = async () => {
    loading.value = true
    error.value = null

    try {
      const response = await fetch('/api/personnel/skills', {
        credentials: 'include'
      })

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }

      const data = await response.json()

      if (data.success) {
        skills.value = data.data.skills || []
      } else {
        throw new Error(data.message || 'Errore nel caricamento competenze')
      }
    } catch (err) {
      error.value = err.message
      console.error('Errore fetchSkills:', err)
    } finally {
      loading.value = false
    }
  }

  // Filter actions
  const setFilter = (key, value) => {
    filters.value[key] = value
  }

  const clearFilters = () => {
    filters.value = {
      search: '',
      department: null,
      skill: null,
      role: null,
      location: null,
      sort: 'name'
    }
  }

  const setPagination = (page, per_page = null) => {
    pagination.value.page = page
    if (per_page) {
      pagination.value.per_page = per_page
    }
  }

  // Reset state
  const $reset = () => {
    users.value = []
    departments.value = []
    skills.value = []
    currentUser.value = null
    loading.value = false
    error.value = null
    clearFilters()
    pagination.value = {
      page: 1,
      per_page: 20,
      total: 0,
      pages: 0
    }
  }

  return {
    // State
    users,
    departments,
    skills,
    currentUser,
    loading,
    error,
    filters,
    pagination,

    // Getters
    filteredUsers,
    departmentTree,

    // Actions
    fetchUsers,
    fetchUser,
    updateUser,
    fetchDepartments,
    fetchSkills,
    setFilter,
    clearFilters,
    setPagination,
    $reset
  }
})
