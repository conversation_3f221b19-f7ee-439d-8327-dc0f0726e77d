<template>
  <div class="h-screen flex bg-gray-50 dark:bg-gray-900">
    <!-- Mobile sidebar backdrop -->
    <div
      v-if="isMobileSidebarOpen"
      @click="closeMobileSidebar"
      class="fixed inset-0 z-20 bg-black bg-opacity-50 lg:hidden"
    ></div>

    <!-- Sidebar -->
    <AppSidebar
      :is-mobile-open="isMobileSidebarOpen"
      @close="closeMobileSidebar"
      @toggle-collapsed="handleSidebarCollapse"
    />

    <!-- Main Content -->
    <div
      class="flex flex-col flex-1 overflow-hidden transition-all duration-300"
      :class="[
        isSidebarCollapsed ? 'lg:ml-20' : 'lg:ml-64'
      ]"
    >
      <!-- Top Header -->
      <AppHeader
        :page-title="pageTitle"
        :breadcrumbs="breadcrumbs"
        @toggle-mobile-sidebar="toggleMobileSidebar"
        @quick-create-project="handleCreateProject"
        @quick-add-task="handleCreateTask"
      />

      <!-- Page Content -->
      <main class="flex-1 overflow-y-auto bg-gray-50 dark:bg-gray-900">
        <div class="py-6">
          <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <!-- Optional: Page actions bar -->
            <div v-if="hasPageActions" class="mb-6">
              <slot name="page-actions" />
            </div>

            <!-- Main content with loading state -->
            <div v-if="isLoading" class="flex items-center justify-center h-64">
              <LoadingSpinner />
            </div>
            <router-view v-else />
          </div>
        </div>
      </main>
    </div>

    <!-- Global notifications -->
    <NotificationManager />
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import { useTenantStore } from '@/stores/tenant'
import AppSidebar from './AppSidebar.vue'
import AppHeader from './AppHeader.vue'
import LoadingSpinner from '@/components/ui/LoadingSpinner.vue'
import NotificationManager from '@/components/ui/NotificationManager.vue'

const route = useRoute()
const tenantStore = useTenantStore()

// Mobile sidebar state
const isMobileSidebarOpen = ref(false)

// Desktop sidebar state
const isSidebarCollapsed = ref(false)

// Loading state for page transitions
const isLoading = ref(false)

// Tenant configuration
const tenantConfig = computed(() => tenantStore.config || {})
const isLoaded = computed(() => tenantStore.config !== null)

// Computed properties
const pageTitle = computed(() => {
  // Extract title from route meta or generate from route name
  if (route.meta?.title) return route.meta.title

  const routeNames = {
    dashboard: 'Dashboard',
    projects: 'Progetti',
    'projects-list': 'Elenco Progetti',
    'projects-view': 'Dettaglio Progetto',
    'projects-create': 'Nuovo Progetto',
    personnel: 'Personale',
    'personnel-directory': 'Rubrica Aziendale',
    'personnel-orgchart': 'Organigramma',
    'personnel-skills': 'Competenze'
  }
  return routeNames[route.name] || 'DatPortal'
})

const breadcrumbs = computed(() => {
  // Generate breadcrumbs from route hierarchy
  if (!route.meta?.breadcrumbs) return []

  return route.meta.breadcrumbs.map(crumb => ({
    label: crumb.label,
    to: crumb.to,
    icon: crumb.icon
  }))
})

const hasPageActions = computed(() => {
  // Check if current page has action buttons
  return route.meta?.hasActions || false
})

// Methods
function toggleMobileSidebar() {
  isMobileSidebarOpen.value = !isMobileSidebarOpen.value
}

function closeMobileSidebar() {
  isMobileSidebarOpen.value = false
}

function handleSidebarCollapse(isCollapsed) {
  isSidebarCollapsed.value = isCollapsed
}

// Watch route changes for loading states
watch(route, () => {
  isLoading.value = true
  // Simulate loading for smooth transitions
  setTimeout(() => {
    isLoading.value = false
  }, 300)
})

// Close mobile sidebar on route change
watch(route, () => {
  closeMobileSidebar()
})

onMounted(() => {
  // Carica la configurazione tenant se non è già stata caricata
  if (!isLoaded.value) {
    tenantStore.loadConfig()
  }
})
</script>