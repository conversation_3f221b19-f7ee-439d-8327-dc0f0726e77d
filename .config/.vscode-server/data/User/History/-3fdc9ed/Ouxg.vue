<template>
  <div class="project-kpi">
    <div v-if="loading" class="animate-pulse space-y-4">
      <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div v-for="i in 4" :key="i" class="bg-gray-200 rounded-lg h-24"></div>
      </div>
      <div class="bg-gray-200 rounded-lg h-64"></div>
    </div>

    <div v-else-if="project" class="space-y-6">
      <!-- K<PERSON> Header -->
      <div class="bg-white shadow rounded-lg p-6">
        <div class="flex items-center justify-between">
          <div>
            <h3 class="text-lg font-medium text-gray-900">KPI Progetto</h3>
            <p class="text-sm text-gray-600">Dashboard metriche e performance del progetto</p>
          </div>
          <button
            @click="refreshKPIs"
            :disabled="refreshing"
            class="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50"
          >
            <svg class="w-4 h-4 mr-2" :class="{ 'animate-spin': refreshing }" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
            </svg>
            Aggiorna
          </button>
        </div>
      </div>

      <!-- KPI Overview Cards -->
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <!-- Ore Totali -->
        <div class="bg-white shadow rounded-lg p-6">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <svg class="h-8 w-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <div class="ml-5 w-0 flex-1">
              <dl>
                <dt class="text-sm font-medium text-gray-500 truncate">Ore Totali</dt>
                <dd class="text-lg font-medium text-gray-900">{{ formatHours(kpiData.totalHours) }}</dd>
                <dd class="text-xs text-gray-500">{{ kpiData.workDays }} giorni lavorati</dd>
              </dl>
            </div>
          </div>
        </div>

        <!-- Costi Totali -->
        <div class="bg-white shadow rounded-lg p-6">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <svg class="h-8 w-8 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
              </svg>
            </div>
            <div class="ml-5 w-0 flex-1">
              <dl>
                <dt class="text-sm font-medium text-gray-500 truncate">Costi Totali</dt>
                <dd class="text-lg font-medium text-gray-900">{{ formatCurrency(kpiData.totalCosts) }}</dd>
                <dd class="text-xs" :class="costVarianceClass">{{ formatCurrency(kpiData.costVariance) }} vs budget</dd>
              </dl>
            </div>
          </div>
        </div>

        <!-- Ricavi Potenziali -->
        <div class="bg-white shadow rounded-lg p-6">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <svg class="h-8 w-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
              </svg>
            </div>
            <div class="ml-5 w-0 flex-1">
              <dl>
                <dt class="text-sm font-medium text-gray-500 truncate">Ricavi Potenziali</dt>
                <dd class="text-lg font-medium text-gray-900">{{ formatCurrency(kpiData.potentialRevenue) }}</dd>
                <dd class="text-xs text-gray-500">{{ formatCurrency(kpiData.actualRevenue) }} fatturati</dd>
              </dl>
            </div>
          </div>
        </div>

        <!-- Margine Progetto -->
        <div class="bg-white shadow rounded-lg p-6">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <svg class="h-8 w-8 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
              </svg>
            </div>
            <div class="ml-5 w-0 flex-1">
              <dl>
                <dt class="text-sm font-medium text-gray-500 truncate">Margine</dt>
                <dd class="text-lg font-medium text-gray-900">{{ formatPercentage(kpiData.marginPercentage) }}</dd>
                <dd class="text-xs" :class="marginClass">{{ marginStatus }}</dd>
              </dl>
            </div>
          </div>
        </div>
      </div>

      <!-- Performance Charts -->
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- Budget Progress Chart -->
        <div class="bg-white shadow rounded-lg p-6">
          <h4 class="text-lg font-medium text-gray-900 mb-4">Andamento Budget</h4>
          <div class="space-y-4">
            <div class="flex justify-between text-sm">
              <span class="text-gray-600">Budget Totale</span>
              <span class="font-medium">{{ formatCurrency(project.budget || 0) }}</span>
            </div>
            <div class="w-full bg-gray-200 rounded-full h-3">
              <div
                class="bg-blue-600 h-3 rounded-full transition-all duration-300"
                :style="{ width: budgetUsagePercentage + '%' }"
              ></div>
            </div>
            <div class="flex justify-between text-sm">
              <span class="text-gray-600">Utilizzato: {{ formatCurrency(kpiData.totalCosts) }}</span>
              <span class="font-medium">{{ budgetUsagePercentage }}%</span>
            </div>
          </div>
        </div>

        <!-- Time Progress Chart -->
        <div class="bg-white shadow rounded-lg p-6">
          <h4 class="text-lg font-medium text-gray-900 mb-4">Andamento Tempo</h4>
          <div class="space-y-4">
            <div class="flex justify-between text-sm">
              <span class="text-gray-600">Ore Stimate</span>
              <span class="font-medium">{{ formatHours(project.estimated_hours || 0) }}</span>
            </div>
            <div class="w-full bg-gray-200 rounded-full h-3">
              <div
                class="bg-green-600 h-3 rounded-full transition-all duration-300"
                :style="{ width: timeUsagePercentage + '%' }"
              ></div>
            </div>
            <div class="flex justify-between text-sm">
              <span class="text-gray-600">Lavorate: {{ formatHours(kpiData.totalHours) }}</span>
              <span class="font-medium">{{ timeUsagePercentage }}%</span>
            </div>
          </div>
        </div>
      </div>

      <!-- KPI Thresholds Configuration -->
      <div class="bg-white shadow rounded-lg p-6">
        <div class="flex items-center justify-between mb-4">
          <h4 class="text-lg font-medium text-gray-900">Soglie KPI</h4>
          <button
            @click="openKPIConfigModal"
            class="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
          >
            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
            </svg>
            Configura KPI
          </button>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div class="text-center p-4 border rounded-lg">
            <div class="text-2xl font-bold" :class="budgetThresholdClass">
              {{ budgetUsagePercentage }}%
            </div>
            <div class="text-sm text-gray-600">Budget Usage</div>
            <div class="text-xs text-gray-500">Soglia: {{ kpiThresholds.budget }}%</div>
          </div>

          <div class="text-center p-4 border rounded-lg">
            <div class="text-2xl font-bold" :class="timeThresholdClass">
              {{ timeUsagePercentage }}%
            </div>
            <div class="text-sm text-gray-600">Time Usage</div>
            <div class="text-xs text-gray-500">Soglia: {{ kpiThresholds.time }}%</div>
          </div>

          <div class="text-center p-4 border rounded-lg">
            <div class="text-2xl font-bold" :class="marginThresholdClass">
              {{ formatPercentage(kpiData.marginPercentage) }}
            </div>
            <div class="text-sm text-gray-600">Margine</div>
            <div class="text-xs text-gray-500">Soglia: {{ kpiThresholds.margin }}%</div>
          </div>
        </div>
      </div>
    </div>

    <div v-else class="text-center py-8">
      <p class="text-gray-500">Progetto non trovato</p>
    </div>

    <!-- KPI Configuration Modal -->
    <div v-if="showKPIConfigModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
      <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white">
        <div class="mt-3">
          <!-- Modal Header -->
          <div class="flex items-center justify-between pb-4 border-b">
            <h3 class="text-lg font-medium text-gray-900">Configurazione KPI Progetto</h3>
            <button @click="closeKPIConfigModal" class="text-gray-400 hover:text-gray-600">
              <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>

          <!-- Modal Content -->
          <div class="mt-6 space-y-6">
            <!-- Project Info -->
            <div class="bg-gray-50 p-4 rounded-lg">
              <h4 class="font-medium text-gray-900">{{ project?.name }}</h4>
              <p class="text-sm text-gray-600">Tipo: {{ getProjectTypeLabel(project?.project_type) }}</p>
            </div>

            <!-- KPI Configuration Forms -->
            <div class="space-y-6">
              <div v-for="kpi in availableKPIs" :key="kpi.name" class="border border-gray-200 rounded-lg p-4">
                <div class="flex items-center justify-between mb-4">
                  <div>
                    <h5 class="font-medium text-gray-900">{{ kpi.display_name }}</h5>
                    <p class="text-sm text-gray-600">{{ kpi.description }}</p>
                  </div>
                  <div class="flex items-center space-x-2">
                    <span class="text-xs text-gray-500">{{ kpi.unit }}</span>
                    <button
                      @click="resetKPIToDefault(kpi.name)"
                      class="text-xs text-blue-600 hover:text-blue-800"
                      title="Reset ai valori di default"
                    >
                      Reset
                    </button>
                  </div>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Target Minimo</label>
                    <input
                      type="number"
                      step="0.1"
                      v-model="kpiConfigs[kpi.name].target_min"
                      @input="markKPIAsDirty(kpi.name)"
                      class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm"
                    >
                  </div>
                  <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Target Massimo</label>
                    <input
                      type="number"
                      step="0.1"
                      v-model="kpiConfigs[kpi.name].target_max"
                      @input="markKPIAsDirty(kpi.name)"
                      class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm"
                    >
                  </div>
                  <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Soglia Warning</label>
                    <input
                      type="number"
                      step="0.1"
                      v-model="kpiConfigs[kpi.name].warning_threshold"
                      @input="markKPIAsDirty(kpi.name)"
                      class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm"
                    >
                  </div>
                </div>

                <div class="mt-4">
                  <label class="block text-sm font-medium text-gray-700 mb-1">Descrizione Personalizzata</label>
                  <textarea
                    v-model="kpiConfigs[kpi.name].custom_description"
                    @input="markKPIAsDirty(kpi.name)"
                    rows="2"
                    class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm"
                    placeholder="Descrizione specifica per questo progetto..."
                  ></textarea>
                </div>

                <!-- Save Button per singolo KPI -->
                <div class="mt-4 flex justify-end">
                  <button
                    v-if="kpiConfigs[kpi.name]?.isDirty"
                    @click="saveKPIConfig(kpi.name)"
                    :disabled="savingKPI === kpi.name"
                    class="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 disabled:opacity-50"
                  >
                    <svg v-if="savingKPI === kpi.name" class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" fill="none" viewBox="0 0 24 24">
                      <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                      <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    {{ savingKPI === kpi.name ? 'Salvataggio...' : 'Salva KPI' }}
                  </button>
                  <span v-else-if="kpiConfigs[kpi.name]?.isSaved" class="text-sm text-green-600">✓ Salvato</span>
                </div>
              </div>
            </div>
          </div>

          <!-- Modal Footer -->
          <div class="mt-6 pt-4 border-t flex justify-between">
            <button
              @click="resetAllKPIsToDefault"
              class="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
            >
              Reset Tutti
            </button>
            <div class="flex space-x-3">
              <button
                @click="closeKPIConfigModal"
                class="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
              >
                Chiudi
              </button>
              <button
                @click="saveAllKPIConfigs"
                :disabled="!hasUnsavedChanges"
                class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 disabled:opacity-50"
              >
                Salva Tutto
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue'

// Props
const props = defineProps({
  project: { type: Object, default: null },
  loading: { type: Boolean, default: false }
})

// Emits
const emit = defineEmits(['refresh'])

// Reactive data
const refreshing = ref(false)
const showKPIConfigModal = ref(false)
const savingKPI = ref(null)
const kpiConfigs = ref({})
const kpiData = ref({
  totalHours: 0,
  workDays: 0,
  totalCosts: 0,
  costVariance: 0,
  potentialRevenue: 0,
  actualRevenue: 0,
  marginPercentage: 0
})

const kpiThresholds = ref({
  budget: 80,
  time: 85,
  margin: 15
})

// Computed properties
const budgetUsagePercentage = computed(() => {
  if (!props.project?.budget || kpiData.value.totalCosts === 0) return 0
  return Math.round((kpiData.value.totalCosts / props.project.budget) * 100)
})

const timeUsagePercentage = computed(() => {
  if (!props.project?.estimated_hours || kpiData.value.totalHours === 0) return 0
  return Math.round((kpiData.value.totalHours / props.project.estimated_hours) * 100)
})

const costVarianceClass = computed(() => {
  const variance = kpiData.value.costVariance
  if (variance > 0) return 'text-red-600'
  if (variance < 0) return 'text-green-600'
  return 'text-gray-600'
})

const marginClass = computed(() => {
  const margin = kpiData.value.marginPercentage
  if (margin >= kpiThresholds.value.margin) return 'text-green-600'
  if (margin >= kpiThresholds.value.margin * 0.7) return 'text-yellow-600'
  return 'text-red-600'
})

const marginStatus = computed(() => {
  const margin = kpiData.value.marginPercentage
  if (margin >= kpiThresholds.value.margin) return 'Ottimo'
  if (margin >= kpiThresholds.value.margin * 0.7) return 'Accettabile'
  return 'Critico'
})

const budgetThresholdClass = computed(() => {
  const usage = budgetUsagePercentage.value
  if (usage >= kpiThresholds.value.budget) return 'text-red-600'
  if (usage >= kpiThresholds.value.budget * 0.8) return 'text-yellow-600'
  return 'text-green-600'
})

const timeThresholdClass = computed(() => {
  const usage = timeUsagePercentage.value
  if (usage >= kpiThresholds.value.time) return 'text-red-600'
  if (usage >= kpiThresholds.value.time * 0.8) return 'text-yellow-600'
  return 'text-green-600'
})

const marginThresholdClass = computed(() => {
  const margin = kpiData.value.marginPercentage
  if (margin >= kpiThresholds.value.margin) return 'text-green-600'
  if (margin >= kpiThresholds.value.margin * 0.7) return 'text-yellow-600'
  return 'text-red-600'
})

// Methods
const formatCurrency = (amount) => {
  return new Intl.NumberFormat('it-IT', {
    style: 'currency',
    currency: 'EUR'
  }).format(amount || 0)
}

const formatHours = (hours) => {
  if (!hours || hours === 0) return '0h'
  return `${parseFloat(hours).toFixed(2)}h`
}

const formatPercentage = (percentage) => {
  return `${(percentage || 0).toFixed(1)}%`
}

const loadKPIData = async () => {
  if (!props.project?.id) return

  // API endpoint non esiste ancora, usa sempre calcoli fallback
  calculateFallbackKPIs()
}

const calculateFallbackKPIs = () => {
  // Calculate basic KPIs from project data
  const project = props.project
  if (!project) return

  // Basic calculations (these would normally come from API)
  kpiData.value = {
    totalHours: project.total_hours || 0,
    workDays: Math.ceil((project.total_hours || 0) / 8),
    totalCosts: (project.total_hours || 0) * 50, // Assuming 50€/hour average
    costVariance: ((project.total_hours || 0) * 50) - (project.budget || 0),
    potentialRevenue: project.budget || 0,
    actualRevenue: project.invoiced_amount || 0,
    marginPercentage: project.budget ?
      (((project.budget - ((project.total_hours || 0) * 50)) / project.budget) * 100) : 0
  }
}

const refreshKPIs = async () => {
  refreshing.value = true
  try {
    await loadKPIData()
    emit('refresh')
  } catch (error) {
    console.error('Error refreshing KPIs:', error)
  } finally {
    refreshing.value = false
  }
}

// KPI Configuration Modal Functions
const availableKPIs = computed(() => {
  const projectType = props.project?.project_type || 'service'
  return getKPITemplatesForProjectType(projectType)
})

const getKPITemplatesForProjectType = (projectType) => {
  const templates = {
    service: [
      {
        name: 'margin_percentage',
        display_name: 'Margine Netto %',
        description: 'Percentuale di margine netto sul fatturato',
        unit: '%',
        target_min: 25,
        target_max: 40,
        warning_threshold: 15
      },
      {
        name: 'utilization_rate',
        display_name: 'Tasso di Utilizzo %',
        description: 'Percentuale di utilizzo del team rispetto alla capacità teorica',
        unit: '%',
        target_min: 75,
        target_max: 85,
        warning_threshold: 60
      },
      {
        name: 'cost_per_hour',
        display_name: 'Costo per Ora',
        description: 'Costo medio per ora di lavoro, inclusi tutti i costi',
        unit: '€',
        target_min: 30,
        target_max: 50,
        warning_threshold: 60
      },
      {
        name: 'cost_revenue_ratio',
        display_name: 'Rapporto C/R',
        description: 'Rapporto tra costi sostenuti e ricavi generati',
        unit: 'ratio',
        target_min: 0.6,
        target_max: 0.75,
        warning_threshold: 0.85
      }
    ]
  }

  return templates[projectType] || templates.service
}

const getProjectTypeLabel = (projectType) => {
  const labels = {
    service: '🔧 Servizio',
    license: '📄 Licenza',
    consulting: '💼 Consulenza',
    product: '📦 Prodotto',
    rd: '🔬 R&D',
    internal: '🏢 Interno'
  }
  return labels[projectType] || 'Sconosciuto'
}

const openKPIConfigModal = () => {
  // Inizializza configurazioni KPI con valori di default
  const templates = availableKPIs.value
  templates.forEach(kpi => {
    if (!kpiConfigs.value[kpi.name]) {
      kpiConfigs.value[kpi.name] = {
        target_min: kpi.target_min,
        target_max: kpi.target_max,
        warning_threshold: kpi.warning_threshold,
        custom_description: '',
        isDirty: false,
        isSaved: false
      }
    }
  })

  showKPIConfigModal.value = true
}

const closeKPIConfigModal = () => {
  showKPIConfigModal.value = false
}

const markKPIAsDirty = (kpiName) => {
  if (kpiConfigs.value[kpiName]) {
    kpiConfigs.value[kpiName].isDirty = true
    kpiConfigs.value[kpiName].isSaved = false
  }
}

const resetKPIToDefault = (kpiName) => {
  const template = availableKPIs.value.find(kpi => kpi.name === kpiName)
  if (template && kpiConfigs.value[kpiName]) {
    kpiConfigs.value[kpiName].target_min = template.target_min
    kpiConfigs.value[kpiName].target_max = template.target_max
    kpiConfigs.value[kpiName].warning_threshold = template.warning_threshold
    kpiConfigs.value[kpiName].custom_description = ''
    kpiConfigs.value[kpiName].isDirty = true
    kpiConfigs.value[kpiName].isSaved = false
  }
}

const resetAllKPIsToDefault = () => {
  if (confirm('Sei sicuro di voler ripristinare tutti i KPI ai valori di default?')) {
    availableKPIs.value.forEach(kpi => {
      resetKPIToDefault(kpi.name)
    })
  }
}

const saveKPIConfig = async (kpiName) => {
  if (!kpiConfigs.value[kpiName]) return

  savingKPI.value = kpiName

  try {
    // TODO: Implementare chiamata API per salvare configurazione KPI
    const config = kpiConfigs.value[kpiName]

    // Simula chiamata API
    await new Promise(resolve => setTimeout(resolve, 1000))

    console.log('Saving KPI config:', {
      project_id: props.project?.id,
      kpi_name: kpiName,
      target_min: config.target_min,
      target_max: config.target_max,
      warning_threshold: config.warning_threshold,
      custom_description: config.custom_description
    })

    kpiConfigs.value[kpiName].isDirty = false
    kpiConfigs.value[kpiName].isSaved = true

    // Auto-hide saved status after 3 seconds
    setTimeout(() => {
      if (kpiConfigs.value[kpiName]) {
        kpiConfigs.value[kpiName].isSaved = false
      }
    }, 3000)

  } catch (error) {
    console.error('Error saving KPI config:', error)
    alert('Errore nel salvataggio della configurazione KPI')
  } finally {
    savingKPI.value = null
  }
}

const saveAllKPIConfigs = async () => {
  const dirtyKPIs = availableKPIs.value.filter(kpi => kpiConfigs.value[kpi.name]?.isDirty)

  for (const kpi of dirtyKPIs) {
    await saveKPIConfig(kpi.name)
  }
}

const hasUnsavedChanges = computed(() => {
  return availableKPIs.value.some(kpi => kpiConfigs.value[kpi.name]?.isDirty)
})

// Watchers
watch(() => props.project, (newProject) => {
  if (newProject) {
    loadKPIData()
  }
}, { immediate: true })

// Lifecycle
onMounted(() => {
  if (props.project) {
    loadKPIData()
  }
})
</script>