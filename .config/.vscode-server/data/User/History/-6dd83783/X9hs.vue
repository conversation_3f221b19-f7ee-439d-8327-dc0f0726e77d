<template>
  <div class="fixed inset-0 z-50 overflow-y-auto">
    <div class="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
      <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" @click="$emit('close')"></div>

      <div class="inline-block align-bottom bg-white dark:bg-gray-800 rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
        <form @submit.prevent="saveExpense">
          <div class="bg-white dark:bg-gray-800 px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
            <div class="mb-4">
              <h3 class="text-lg font-medium text-gray-900 dark:text-white">
                {{ expense ? 'Modifica Spesa' : 'Aggiungi Spesa' }}
              </h3>
            </div>

            <div class="space-y-4">
              <!-- Description -->
              <div>
                <label for="description" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Descrizione
                </label>
                <input
                  v-model="form.description"
                  type="text"
                  id="description"
                  required
                  class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white"
                  placeholder="Descrizione della spesa"
                />
              </div>

              <!-- Amount -->
              <div>
                <label for="amount" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Importo (€)
                </label>
                <input
                  v-model.number="form.amount"
                  type="number"
                  step="0.01"
                  id="amount"
                  required
                  class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white"
                  placeholder="0.00"
                />
              </div>

              <!-- Category -->
              <div>
                <label for="category" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Categoria
                </label>
                <select
                  v-model="form.category"
                  id="category"
                  required
                  class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white"
                >
                  <option value="">Seleziona categoria</option>
                  <option value="licenses">📄 Licenze</option>
                  <option value="travel">✈️ Viaggi</option>
                  <option value="meals">🍽️ Pasti</option>
                  <option value="equipment">🖥️ Attrezzature</option>
                  <option value="external">🏢 Servizi Esterni</option>
                  <option value="other">📦 Altro</option>
                </select>
              </div>

              <!-- Billing Type -->
              <div>
                <label for="billing_type" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Tipo Fatturazione
                </label>
                <select
                  v-model="form.billing_type"
                  id="billing_type"
                  required
                  class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white"
                >
                  <option value="billable">💰 Fatturabile al Cliente</option>
                  <option value="non-billable">🏢 Assorbimento Interno</option>
                  <option value="reimbursable">💳 Rimborsabile</option>
                </select>
              </div>

              <!-- Status -->
              <div>
                <label for="status" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Stato
                </label>
                <select
                  v-model="form.status"
                  id="status"
                  required
                  class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white"
                >
                  <option value="pending">⏳ In Attesa di Approvazione</option>
                  <option value="approved">✅ Approvata</option>
                  <option value="rejected">❌ Rifiutata</option>
                </select>
              </div>

              <!-- Date -->
              <div>
                <label for="date" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Data
                </label>
                <input
                  v-model="form.date"
                  type="date"
                  id="date"
                  required
                  class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white"
                />
              </div>

              <!-- Notes -->
              <div>
                <label for="notes" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Note (opzionale)
                </label>
                <textarea
                  v-model="form.notes"
                  id="notes"
                  rows="3"
                  class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white"
                  placeholder="Note aggiuntive..."
                ></textarea>
              </div>

              <!-- Receipt Upload -->
              <div>
                <label for="receipt" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Ricevuta/Scontrino
                </label>
                <input
                  type="file"
                  id="receipt"
                  accept="image/*,.pdf"
                  @change="handleFileUpload"
                  class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-primary-50 file:text-primary-700 hover:file:bg-primary-100"
                />
                <p class="mt-1 text-xs text-gray-500 dark:text-gray-400">
                  Carica immagine o PDF della ricevuta (max 5MB)
                </p>
              </div>
            </div>
          </div>

          <div class="bg-gray-50 dark:bg-gray-700 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
            <button
              type="submit"
              :disabled="saving"
              class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-primary-600 text-base font-medium text-white hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 sm:ml-3 sm:w-auto sm:text-sm disabled:opacity-50"
            >
              <span v-if="saving">Salvando...</span>
              <span v-else>{{ expense ? 'Aggiorna' : 'Salva' }}</span>
            </button>
            <button
              type="button"
              @click="$emit('close')"
              class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 dark:border-gray-600 shadow-sm px-4 py-2 bg-white dark:bg-gray-800 text-base font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm"
            >
              Annulla
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'

const props = defineProps({
  projectId: {
    type: [String, Number],
    required: true
  },
  expense: {
    type: Object,
    default: null
  }
})

const emit = defineEmits(['close', 'saved'])

// State
const saving = ref(false)
const form = reactive({
  description: '',
  amount: 0,
  category: '',
  date: new Date().toISOString().split('T')[0],
  notes: ''
})

// Methods
const saveExpense = async () => {
  saving.value = true
  try {
    const url = props.expense
      ? `/api/expenses/${props.expense.id}`
      : `/api/projects/${props.projectId}/expenses`

    const method = props.expense ? 'PUT' : 'POST'

    const response = await fetch(url, {
      method,
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(form)
    })

    if (response.ok) {
      emit('saved')
    } else {
      console.error('Error saving expense')
    }
  } catch (error) {
    console.error('Error saving expense:', error)
  } finally {
    saving.value = false
  }
}

// Lifecycle
onMounted(() => {
  if (props.expense) {
    Object.assign(form, {
      description: props.expense.description,
      amount: props.expense.amount,
      category: props.expense.category,
      date: props.expense.date.split('T')[0],
      notes: props.expense.notes || ''
    })
  }
})
</script>