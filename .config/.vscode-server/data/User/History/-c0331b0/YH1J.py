"""
API Blueprint per la gestione delle spese
"""
from flask import Blueprint, request, jsonify, current_app
from flask_login import login_required, current_user
from extensions import db
from models import Project, ProjectExpense, User
from sqlalchemy import and_
from datetime import datetime
import logging

logger = logging.getLogger(__name__)

api_expenses = Blueprint('api_expenses', __name__)

@api_expenses.route('/api/projects/<int:project_id>/expenses', methods=['GET'])
@login_required
def get_project_expenses(project_id):
    """Ottieni tutte le spese di un progetto"""
    try:
        project = Project.query.get_or_404(project_id)

        # Verifica permessi
        if not current_user.can_view_project(project):
            return jsonify({'error': 'Accesso negato'}), 403

        expenses = ProjectExpense.query.filter_by(project_id=project_id).order_by(ProjectExpense.date.desc()).all()

        return jsonify([{
            'id': expense.id,
            'description': expense.description,
            'amount': float(expense.amount),
            'category': expense.category,
            'date': expense.date.isoformat(),
            'notes': expense.receipt_path or '',
            'status': expense.status,
            'billing_type': expense.billing_type,
            'user': {
                'id': expense.user.id,
                'name': f"{expense.user.first_name} {expense.user.last_name}".strip() or expense.user.username
            } if expense.user else None,
            'created_at': expense.created_at.isoformat() if expense.created_at else None
        } for expense in expenses])

    except Exception as e:
        logger.error(f"Errore nel recupero spese progetto {project_id}: {str(e)}")
        return jsonify({'error': 'Errore interno del server'}), 500

@api_expenses.route('/api/projects/<int:project_id>/expenses', methods=['POST'])
@login_required
def create_project_expense(project_id):
    """Crea una nuova spesa per un progetto"""
    try:
        project = Project.query.get_or_404(project_id)

        # Verifica permessi
        if not current_user.has_permission('manage_expenses'):
            return jsonify({'error': 'Permessi insufficienti'}), 403

        data = request.get_json()

        # Validazione dati
        required_fields = ['description', 'amount', 'category', 'date']
        for field in required_fields:
            if field not in data:
                return jsonify({'error': f'Campo {field} richiesto'}), 400

        # Conversione data
        try:
            expense_date = datetime.strptime(data['date'], '%Y-%m-%d').date()
        except ValueError:
            return jsonify({'error': 'Formato data non valido (YYYY-MM-DD)'}), 400

        # Validazione importo
        try:
            amount = float(data['amount'])
            if amount <= 0:
                return jsonify({'error': 'L\'importo deve essere maggiore di zero'}), 400
        except (ValueError, TypeError):
            return jsonify({'error': 'Importo non valido'}), 400

        # Creazione spesa
        expense = ProjectExpense(
            project_id=project_id,
            user_id=current_user.id,
            description=data['description'],
            amount=amount,
            category=data['category'],
            date=expense_date,
            billing_type=data.get('billing_type', 'billable'),
            status='pending',
            created_at=datetime.utcnow()
        )

        db.session.add(expense)
        db.session.commit()

        return jsonify({
            'id': expense.id,
            'description': expense.description,
            'amount': float(expense.amount),
            'category': expense.category,
            'date': expense.date.isoformat(),
            'notes': expense.notes,
            'user': {
                'id': expense.user.id,
                'name': expense.user.name
            },
            'created_at': expense.created_at.isoformat()
        }), 201

    except Exception as e:
        db.session.rollback()
        logger.error(f"Errore nella creazione spesa per progetto {project_id}: {str(e)}")
        return jsonify({'error': 'Errore interno del server'}), 500

@api_expenses.route('/api/expenses/<int:expense_id>', methods=['PUT'])
@login_required
def update_expense(expense_id):
    """Aggiorna una spesa esistente"""
    try:
        expense = ProjectExpense.query.get_or_404(expense_id)

        # Verifica permessi
        if not current_user.has_permission('manage_expenses'):
            return jsonify({'error': 'Permessi insufficienti'}), 403

        data = request.get_json()

        # Aggiornamento campi
        if 'description' in data:
            expense.description = data['description']
        if 'amount' in data:
            try:
                amount = float(data['amount'])
                if amount <= 0:
                    return jsonify({'error': 'L\'importo deve essere maggiore di zero'}), 400
                expense.amount = amount
            except (ValueError, TypeError):
                return jsonify({'error': 'Importo non valido'}), 400
        if 'category' in data:
            expense.category = data['category']
        if 'date' in data:
            try:
                expense.date = datetime.strptime(data['date'], '%Y-%m-%d').date()
            except ValueError:
                return jsonify({'error': 'Formato data non valido (YYYY-MM-DD)'}), 400
        if 'notes' in data:
            expense.notes = data['notes']

        expense.updated_at = datetime.utcnow()
        db.session.commit()

        return jsonify({
            'id': expense.id,
            'description': expense.description,
            'amount': float(expense.amount),
            'category': expense.category,
            'date': expense.date.isoformat(),
            'notes': expense.notes,
            'user': {
                'id': expense.user.id,
                'name': expense.user.name
            } if expense.user else None,
            'updated_at': expense.updated_at.isoformat() if expense.updated_at else None
        })

    except Exception as e:
        db.session.rollback()
        logger.error(f"Errore nell'aggiornamento spesa {expense_id}: {str(e)}")
        return jsonify({'error': 'Errore interno del server'}), 500

@api_expenses.route('/api/expenses/<int:expense_id>', methods=['DELETE'])
@login_required
def delete_expense(expense_id):
    """Elimina una spesa"""
    try:
        expense = ProjectExpense.query.get_or_404(expense_id)

        # Verifica permessi
        if not current_user.has_permission('manage_expenses'):
            return jsonify({'error': 'Permessi insufficienti'}), 403

        db.session.delete(expense)
        db.session.commit()

        return jsonify({'message': 'Spesa eliminata con successo'}), 200

    except Exception as e:
        db.session.rollback()
        logger.error(f"Errore nell'eliminazione spesa {expense_id}: {str(e)}")
        return jsonify({'error': 'Errore interno del server'}), 500

@api_expenses.route('/api/projects/<int:project_id>/expenses/summary', methods=['GET'])
@login_required
def get_project_expenses_summary(project_id):
    """Ottieni un riepilogo delle spese del progetto"""
    try:
        project = Project.query.get_or_404(project_id)

        # Verifica permessi
        if not current_user.can_view_project(project):
            return jsonify({'error': 'Accesso negato'}), 403

        expenses = ProjectExpense.query.filter_by(project_id=project_id).all()

        # Calcolo statistiche
        total_amount = sum(expense.amount for expense in expenses)
        expenses_by_category = {}

        for expense in expenses:
            category = expense.category
            if category not in expenses_by_category:
                expenses_by_category[category] = {
                    'count': 0,
                    'total': 0
                }
            expenses_by_category[category]['count'] += 1
            expenses_by_category[category]['total'] += float(expense.amount)

        return jsonify({
            'total_amount': float(total_amount),
            'total_expenses': len(expenses),
            'by_category': expenses_by_category,
            'average_expense': float(total_amount / len(expenses)) if expenses else 0
        })

    except Exception as e:
        logger.error(f"Errore nel riepilogo spese progetto {project_id}: {str(e)}")
        return jsonify({'error': 'Errore interno del server'}), 500