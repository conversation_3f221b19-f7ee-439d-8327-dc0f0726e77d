<template>
  <div class="flex-shrink-0 border-t border-gray-200 p-4">
    <!-- User Profile Section -->
    <div class="flex items-center" :class="{ 'justify-center': isCollapsed }">
      <div class="flex-shrink-0">
        <div class="h-8 w-8 rounded-full bg-primary-100 flex items-center justify-center">
          <span class="text-sm font-medium text-primary-700">
            {{ userInitials }}
          </span>
        </div>
      </div>

      <div v-if="!isCollapsed" class="ml-3 flex-1 min-w-0">
        <p class="text-sm font-medium text-gray-900 truncate">
          {{ userName }}
        </p>
        <p class="text-xs text-gray-500 truncate">
          {{ userRole }}
        </p>
      </div>

      <!-- User Menu Dropdown -->
      <div class="relative" :class="{ 'ml-3': !isCollapsed }">
        <button
          @click="showUserMenu = !showUserMenu"
          class="p-1 rounded-full text-gray-400 hover:text-gray-600 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-primary-500"
        >
          <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 12h.01M12 12h.01M19 12h.01M6 12a1 1 0 11-2 0 1 1 0 012 0zM13 12a1 1 0 11-2 0 1 1 0 012 0zM20 12a1 1 0 11-2 0 1 1 0 012 0z" />
          </svg>
        </button>

        <!-- Dropdown Menu -->
        <div
          v-if="showUserMenu"
          @click.away="showUserMenu = false"
          class="origin-bottom-left absolute bottom-full left-0 mb-2 w-48 rounded-md shadow-lg bg-white dark:bg-gray-800 ring-1 ring-black ring-opacity-5 z-[9999]"
        >
          <div class="py-1">
            <router-link
              to="/app/profile"
              class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
              @click="showUserMenu = false"
            >
              Il tuo profilo
            </router-link>
            <router-link
              to="/app/settings"
              class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
              @click="showUserMenu = false"
            >
              Impostazioni
            </router-link>
            <hr class="my-1">
            <button
              @click="logout"
              class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
            >
              Esci
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- App Version Info -->
    <div v-if="appVersion && !isCollapsed" class="mt-3 text-xs text-gray-400 text-center">
      v{{ appVersion }}
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'

const props = defineProps({
  isCollapsed: {
    type: Boolean,
    default: false
  }
})

const router = useRouter()
const authStore = useAuthStore()
const showUserMenu = ref(false)

const userName = computed(() => {
  if (!authStore.user) return 'Utente'
  return authStore.user.name || authStore.user.username || 'Utente'
})

const userInitials = computed(() => {
  if (!authStore.user) return 'U'
  const name = userName.value
  return name.charAt(0).toUpperCase()
})

const userRole = computed(() => {
  if (!authStore.user) return ''
  const roles = {
    admin: 'Amministratore',
    manager: 'Manager',
    employee: 'Dipendente',
    client: 'Cliente'
  }
  return roles[authStore.user.role] || authStore.user.role
})

const appVersion = computed(() => {
  // This could come from environment variables or package.json
  return import.meta.env.VITE_APP_VERSION || '1.0.0'
})

async function logout() {
  showUserMenu.value = false
  await authStore.logout()
  router.push('/auth/login')
}
</script>