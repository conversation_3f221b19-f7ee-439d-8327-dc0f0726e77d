<template>
  <div class="project-expenses">
    <div class="space-y-6">
      <!-- Header with Add Button -->
      <div class="flex justify-between items-center">
        <h3 class="text-lg font-medium text-gray-900 dark:text-white">Spese Progetto</h3>
        <button
          v-if="canManageExpenses"
          @click="showAddExpenseModal = true"
          class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
        >
          <PlusIcon class="w-4 h-4 mr-2" />
          Aggiungi Spesa
        </button>
      </div>

      <!-- Expenses List -->
      <div v-if="loading" class="text-center py-8">
        <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
        <p class="mt-2 text-sm text-gray-500">Caricamento spese...</p>
      </div>

      <div v-else-if="expenses.length === 0" class="text-center py-12">
        <CreditCardIcon class="mx-auto h-12 w-12 text-gray-400" />
        <h3 class="mt-2 text-sm font-medium text-gray-900 dark:text-white">Nessuna spesa</h3>
        <p class="mt-1 text-sm text-gray-500">Non ci sono ancora spese registrate per questo progetto.</p>
      </div>

      <div v-else class="bg-white dark:bg-gray-800 shadow overflow-hidden sm:rounded-md">
        <ul class="divide-y divide-gray-200 dark:divide-gray-700">
          <li v-for="expense in expenses" :key="expense.id" class="px-6 py-4">
            <div class="flex items-center justify-between">
              <div class="flex-1">
                <div class="flex items-center">
                  <div class="flex-shrink-0">
                    <div class="h-10 w-10 rounded-full bg-gray-300 dark:bg-gray-600 flex items-center justify-center">
                      <CreditCardIcon class="h-5 w-5 text-gray-600 dark:text-gray-300" />
                    </div>
                  </div>
                  <div class="ml-4 flex-1">
                    <div class="flex items-center justify-between">
                      <p class="text-sm font-medium text-gray-900 dark:text-white">
                        {{ expense.description }}
                      </p>
                      <div class="ml-2 flex-shrink-0">
                        <p class="text-sm font-medium text-gray-900 dark:text-white">
                          €{{ formatCurrency(expense.amount) }}
                        </p>
                      </div>
                    </div>
                    <div class="mt-1 flex items-center text-sm text-gray-500 dark:text-gray-400">
                      <CalendarIcon class="flex-shrink-0 mr-1.5 h-4 w-4" />
                      {{ formatDate(expense.date) }}
                      <span class="mx-2">•</span>
                      <span class="capitalize">{{ expense.category }}</span>
                      <span v-if="expense.user" class="mx-2">•</span>
                      <span v-if="expense.user">{{ expense.user.name }}</span>
                    </div>
                  </div>
                </div>
              </div>
              <div v-if="canManageExpenses" class="flex items-center space-x-2">
                <button
                  @click="editExpense(expense)"
                  class="text-primary-600 hover:text-primary-900 text-sm font-medium"
                >
                  Modifica
                </button>
                <button
                  @click="deleteExpense(expense.id)"
                  class="text-red-600 hover:text-red-900 text-sm font-medium"
                >
                  Elimina
                </button>
              </div>
            </div>
          </li>
        </ul>
      </div>

      <!-- Summary Card -->
      <div v-if="expenses.length > 0" class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
        <div class="flex justify-between items-center">
          <span class="text-sm font-medium text-gray-900 dark:text-white">Totale Spese:</span>
          <span class="text-lg font-bold text-gray-900 dark:text-white">€{{ formatCurrency(totalExpenses) }}</span>
        </div>
      </div>
    </div>

    <!-- Add/Edit Expense Modal -->
    <ExpenseModal
      v-if="showAddExpenseModal"
      :project-id="projectId"
      :expense="editingExpense"
      @close="closeExpenseModal"
      @saved="onExpenseSaved"
    />
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useProjectsStore } from '../../../stores/projects'
import { useAuthStore } from '../../../stores/auth'
import { PlusIcon, CreditCardIcon, CalendarIcon } from '@heroicons/vue/24/outline'
import ExpenseModal from './ExpenseModal.vue'

const props = defineProps({
  project: {
    type: Object,
    required: true
  },
  loading: {
    type: Boolean,
    default: false
  }
})

const projectsStore = useProjectsStore()
const authStore = useAuthStore()

// State
const loading = ref(false)
const expenses = ref([])
const showAddExpenseModal = ref(false)
const editingExpense = ref(null)

// Computed
const canManageExpenses = computed(() => {
  return authStore.hasPermission('manage_expenses')
})

const totalExpenses = computed(() => {
  return expenses.value.reduce((total, expense) => total + expense.amount, 0)
})

// Methods
const loadExpenses = async () => {
  loading.value = true
  try {
    const response = await fetch(`/api/projects/${props.projectId}/expenses`)
    if (response.ok) {
      expenses.value = await response.json()
    }
  } catch (error) {
    console.error('Error loading expenses:', error)
  } finally {
    loading.value = false
  }
}

const editExpense = (expense) => {
  editingExpense.value = expense
  showAddExpenseModal.value = true
}

const deleteExpense = async (expenseId) => {
  if (!confirm('Sei sicuro di voler eliminare questa spesa?')) {
    return
  }

  try {
    const response = await fetch(`/api/expenses/${expenseId}`, {
      method: 'DELETE'
    })
    if (response.ok) {
      expenses.value = expenses.value.filter(e => e.id !== expenseId)
    }
  } catch (error) {
    console.error('Error deleting expense:', error)
  }
}

const closeExpenseModal = () => {
  showAddExpenseModal.value = false
  editingExpense.value = null
}

const onExpenseSaved = () => {
  closeExpenseModal()
  loadExpenses()
}

const formatCurrency = (amount) => {
  return new Intl.NumberFormat('it-IT', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  }).format(amount)
}

const formatDate = (dateString) => {
  return new Date(dateString).toLocaleDateString('it-IT')
}

// Lifecycle
onMounted(() => {
  loadExpenses()
})
</script>