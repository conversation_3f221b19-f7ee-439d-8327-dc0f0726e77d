{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/blueprints/api/clients.py"}, "modifiedCode": "\"\"\"\nAPI RESTful per la gestione dei clienti.\n\"\"\"\nfrom flask import Blueprint, request, jsonify, current_app\nfrom flask_login import login_required, current_user\nfrom sqlalchemy import desc\nfrom models import Client, Contact\nfrom utils.api_utils import (\n    api_response, handle_api_error, get_pagination_params,\n    format_pagination, api_permission_required\n)\nfrom utils.permissions import (\n    PERMISSION_VIEW_ALL_PROJECTS, PERMISSION_EDIT_PROJECT\n)\nfrom utils.db import db\n\n# Crea il blueprint per le API dei clienti\napi_clients = Blueprint('api_clients', __name__, url_prefix='/clients')\n\n@api_clients.route('/', methods=['GET'])\n@login_required\ndef get_clients():\n    \"\"\"\n    Ottiene la lista dei clienti con supporto per filtri e paginazione.\n    ---\n    tags:\n      - clients\n    parameters:\n      - $ref: '#/components/parameters/pageParam'\n      - $ref: '#/components/parameters/perPageParam'\n      - name: search\n        in: query\n        description: Cerca nei nomi dei clienti\n        schema:\n          type: string\n    responses:\n      200:\n        description: Lista di clienti\n        content:\n          application/json:\n            schema:\n              type: object\n              properties:\n                success:\n                  type: boolean\n                  example: true\n                data:\n                  type: object\n                  properties:\n                    clients:\n                      type: array\n                      items:\n                        type: object\n                        properties:\n                          id:\n                            type: integer\n                          name:\n                            type: string\n                          industry:\n                            type: string\n                          description:\n                            type: string\n                          website:\n                            type: string\n                          address:\n                            type: string\n                          created_at:\n                            type: string\n                            format: date-time\n                    pagination:\n                      $ref: '#/components/schemas/Pagination'\n      500:\n        description: Errore interno del server\n    \"\"\"\n    try:\n        # Parametri di paginazione\n        page, per_page = get_pagination_params()\n        \n        # Parametri di ricerca\n        search = request.args.get('search', '').strip()\n        \n        # Query base\n        query = Client.query\n        \n        # Applica filtri di ricerca\n        if search:\n            query = query.filter(\n                Client.name.ilike(f'%{search}%')\n            )\n        \n        # Ordina per nome\n        query = query.order_by(Client.name)\n        \n        # Paginazione\n        clients_paginated = query.paginate(\n            page=page, \n            per_page=per_page, \n            error_out=False\n        )\n        \n        # Prepara i dati dei clienti\n        clients_data = []\n        for client in clients_paginated.items:\n            client_data = {\n                'id': client.id,\n                'name': client.name,\n                'industry': client.industry,\n                'description': client.description,\n                'website': client.website,\n                'address': client.address,\n                'created_at': client.created_at.isoformat() if client.created_at else None\n            }\n            clients_data.append(client_data)\n        \n        return api_response(\n            data={\n                'clients': clients_data,\n                'pagination': format_pagination(clients_paginated)\n            }\n        )\n        \n    except Exception as e:\n        current_app.logger.error(f\"Error in get_clients: {str(e)}\")\n        return handle_api_error(e)\n\n\n@api_clients.route('/', methods=['POST'])\n@login_required\n@api_permission_required(PERMISSION_EDIT_PROJECT)\ndef create_client():\n    \"\"\"\n    Crea un nuovo cliente.\n    ---\n    tags:\n      - clients\n    requestBody:\n      required: true\n      content:\n        application/json:\n          schema:\n            type: object\n            required:\n              - name\n            properties:\n              name:\n                type: string\n                description: Nome del cliente\n              industry:\n                type: string\n                description: Settore di attività\n              description:\n                type: string\n                description: Descrizione del cliente\n              website:\n                type: string\n                description: Sito web\n              address:\n                type: string\n                description: Indirizzo\n    responses:\n      201:\n        description: Cliente creato con successo\n      400:\n        description: Dati non validi\n      500:\n        description: Errore interno del server\n    \"\"\"\n    try:\n        data = request.get_json()\n        \n        # Validazione dati richiesti\n        if not data.get('name'):\n            return api_response(\n                success=False,\n                message=\"Il nome del cliente è obbligatorio\",\n                status_code=400\n            )\n        \n        # Crea il nuovo cliente\n        new_client = Client(\n            name=data['name'],\n            industry=data.get('industry'),\n            description=data.get('description'),\n            website=data.get('website'),\n            address=data.get('address')\n        )\n        \n        # Aggiungi il cliente al database\n        db.session.add(new_client)\n        db.session.commit()\n        \n        # Prepara i dati del cliente per la risposta\n        client_data = {\n            'id': new_client.id,\n            'name': new_client.name,\n            'industry': new_client.industry,\n            'description': new_client.description,\n            'website': new_client.website,\n            'address': new_client.address,\n            'created_at': new_client.created_at.isoformat()\n        }\n        \n        return api_response(\n            data={'client': client_data},\n            message=\"Cliente creato con successo\",\n            status_code=201\n        )\n        \n    except Exception as e:\n        db.session.rollback()\n        current_app.logger.error(f\"Error in create_client: {str(e)}\")\n        return handle_api_error(e)\n\n\n@api_clients.route('/<int:client_id>', methods=['GET'])\n@login_required\ndef get_client(client_id):\n    \"\"\"\n    Ottiene i dettagli di un cliente specifico.\n    ---\n    tags:\n      - clients\n    parameters:\n      - name: client_id\n        in: path\n        required: true\n        description: ID del cliente\n        schema:\n          type: integer\n    responses:\n      200:\n        description: Dettagli del cliente\n      404:\n        description: Cliente non trovato\n      500:\n        description: Errore interno del server\n    \"\"\"\n    try:\n        client = Client.query.get_or_404(client_id)\n        \n        # Prepara i dati del cliente\n        client_data = {\n            'id': client.id,\n            'name': client.name,\n            'industry': client.industry,\n            'description': client.description,\n            'website': client.website,\n            'address': client.address,\n            'created_at': client.created_at.isoformat() if client.created_at else None,\n            'updated_at': client.updated_at.isoformat() if client.updated_at else None\n        }\n        \n        return api_response(\n            data={'client': client_data}\n        )\n        \n    except Exception as e:\n        current_app.logger.error(f\"Error in get_client: {str(e)}\")\n        return handle_api_error(e)\n\n\n@api_clients.route('/<int:client_id>', methods=['PUT'])\n@login_required\n@api_permission_required(PERMISSION_EDIT_PROJECT)\ndef update_client(client_id):\n    \"\"\"\n    Aggiorna un cliente esistente.\n    ---\n    tags:\n      - clients\n    parameters:\n      - name: client_id\n        in: path\n        required: true\n        description: ID del cliente da aggiornare\n        schema:\n          type: integer\n    requestBody:\n      required: true\n      content:\n        application/json:\n          schema:\n            type: object\n            properties:\n              name:\n                type: string\n                description: Nome del cliente\n              industry:\n                type: string\n                description: Settore di attività\n              description:\n                type: string\n                description: Descrizione del cliente\n              website:\n                type: string\n                description: Sito web\n              address:\n                type: string\n                description: Indirizzo\n    responses:\n      200:\n        description: Cliente aggiornato con successo\n      404:\n        description: Cliente non trovato\n      500:\n        description: Errore interno del server\n    \"\"\"\n    try:\n        client = Client.query.get_or_404(client_id)\n        data = request.get_json()\n        \n        # Aggiorna i campi del cliente\n        if 'name' in data:\n            client.name = data['name']\n        if 'industry' in data:\n            client.industry = data['industry']\n        if 'description' in data:\n            client.description = data['description']\n        if 'website' in data:\n            client.website = data['website']\n        if 'address' in data:\n            client.address = data['address']\n        \n        # Salva le modifiche\n        db.session.commit()\n        \n        # Prepara i dati del cliente aggiornato\n        client_data = {\n            'id': client.id,\n            'name': client.name,\n            'industry': client.industry,\n            'description': client.description,\n            'website': client.website,\n            'address': client.address,\n            'created_at': client.created_at.isoformat() if client.created_at else None,\n            'updated_at': client.updated_at.isoformat() if client.updated_at else None\n        }\n        \n        return api_response(\n            data={'client': client_data},\n            message=\"Cliente aggiornato con successo\"\n        )\n        \n    except Exception as e:\n        db.session.rollback()\n        current_app.logger.error(f\"Error in update_client: {str(e)}\")\n        return handle_api_error(e)\n\n\n@api_clients.route('/<int:client_id>', methods=['DELETE'])\n@login_required\n@api_permission_required(PERMISSION_EDIT_PROJECT)\ndef delete_client(client_id):\n    \"\"\"\n    Elimina un cliente esistente.\n    ---\n    tags:\n      - clients\n    parameters:\n      - name: client_id\n        in: path\n        required: true\n        description: ID del cliente da eliminare\n        schema:\n          type: integer\n    responses:\n      200:\n        description: Cliente eliminato con successo\n      404:\n        description: Cliente non trovato\n      500:\n        description: Errore interno del server\n    \"\"\"\n    try:\n        client = Client.query.get_or_404(client_id)\n        \n        # Elimina il cliente\n        db.session.delete(client)\n        db.session.commit()\n        \n        return api_response(\n            message=f\"Cliente '{client.name}' eliminato con successo\"\n        )\n        \n    except Exception as e:\n        db.session.rollback()\n        current_app.logger.error(f\"Error in delete_client: {str(e)}\")\n        return handle_api_error(e)\n"}