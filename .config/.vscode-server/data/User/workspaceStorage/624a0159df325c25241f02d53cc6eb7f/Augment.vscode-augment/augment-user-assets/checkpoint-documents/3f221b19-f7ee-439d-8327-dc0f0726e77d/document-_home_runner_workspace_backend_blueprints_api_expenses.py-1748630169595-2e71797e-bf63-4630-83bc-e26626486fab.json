{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/blueprints/api/expenses.py"}, "originalCode": "\"\"\"\nAPI Blueprint per la gestione delle spese\n\"\"\"\nfrom flask import Blueprint, request, jsonify, current_app\nfrom flask_login import login_required, current_user\nfrom backend.extensions import db\nfrom backend.models import Project, ProjectExpense, User\nfrom sqlalchemy import and_\nfrom datetime import datetime\nimport logging\n\nlogger = logging.getLogger(__name__)\n\napi_expenses = Blueprint('api_expenses', __name__)\n\n@api_expenses.route('/api/projects/<int:project_id>/expenses', methods=['GET'])\n@login_required\ndef get_project_expenses(project_id):\n    \"\"\"Ottieni tutte le spese di un progetto\"\"\"\n    try:\n        project = Project.query.get_or_404(project_id)\n\n        # Verifica permessi\n        if not current_user.can_view_project(project):\n            return jsonify({'error': 'Accesso negato'}), 403\n\n        expenses = ProjectExpense.query.filter_by(project_id=project_id).order_by(ProjectExpense.date.desc()).all()\n\n        return jsonify([{\n            'id': expense.id,\n            'description': expense.description,\n            'amount': float(expense.amount),\n            'category': expense.category,\n            'date': expense.date.isoformat(),\n            'notes': expense.receipt_path or '',\n            'status': expense.status,\n            'billing_type': expense.billing_type,\n            'user': {\n                'id': expense.user.id,\n                'name': f\"{expense.user.first_name} {expense.user.last_name}\".strip() or expense.user.username\n            } if expense.user else None,\n            'created_at': expense.created_at.isoformat() if expense.created_at else None\n        } for expense in expenses])\n\n    except Exception as e:\n        logger.error(f\"Errore nel recupero spese progetto {project_id}: {str(e)}\")\n        return jsonify({'error': 'Errore interno del server'}), 500\n\n@api_expenses.route('/api/projects/<int:project_id>/expenses', methods=['POST'])\n@login_required\ndef create_project_expense(project_id):\n    \"\"\"Crea una nuova spesa per un progetto\"\"\"\n    try:\n        project = Project.query.get_or_404(project_id)\n\n        # Verifica permessi\n        if not current_user.has_permission('manage_expenses'):\n            return jsonify({'error': 'Permessi insufficienti'}), 403\n\n        data = request.get_json()\n\n        # Validazione dati\n        required_fields = ['description', 'amount', 'category', 'date']\n        for field in required_fields:\n            if field not in data:\n                return jsonify({'error': f'Campo {field} richiesto'}), 400\n\n        # Conversione data\n        try:\n            expense_date = datetime.strptime(data['date'], '%Y-%m-%d').date()\n        except ValueError:\n            return jsonify({'error': 'Formato data non valido (YYYY-MM-DD)'}), 400\n\n        # Validazione importo\n        try:\n            amount = float(data['amount'])\n            if amount <= 0:\n                return jsonify({'error': 'L\\'importo deve essere maggiore di zero'}), 400\n        except (ValueError, TypeError):\n            return jsonify({'error': 'Importo non valido'}), 400\n\n        # Creazione spesa\n        expense = ProjectExpense(\n            project_id=project_id,\n            user_id=current_user.id,\n            description=data['description'],\n            amount=amount,\n            category=data['category'],\n            date=expense_date,\n            billing_type=data.get('billing_type', 'billable'),\n            status='pending',\n            created_at=datetime.utcnow()\n        )\n\n        db.session.add(expense)\n        db.session.commit()\n\n        return jsonify({\n            'id': expense.id,\n            'description': expense.description,\n            'amount': float(expense.amount),\n            'category': expense.category,\n            'date': expense.date.isoformat(),\n            'notes': expense.notes,\n            'user': {\n                'id': expense.user.id,\n                'name': expense.user.name\n            },\n            'created_at': expense.created_at.isoformat()\n        }), 201\n\n    except Exception as e:\n        db.session.rollback()\n        logger.error(f\"Errore nella creazione spesa per progetto {project_id}: {str(e)}\")\n        return jsonify({'error': 'Errore interno del server'}), 500\n\n@api_expenses.route('/api/expenses/<int:expense_id>', methods=['PUT'])\n@login_required\ndef update_expense(expense_id):\n    \"\"\"Aggiorna una spesa esistente\"\"\"\n    try:\n        expense = ProjectExpense.query.get_or_404(expense_id)\n\n        # Verifica permessi\n        if not current_user.has_permission('manage_expenses'):\n            return jsonify({'error': 'Permessi insufficienti'}), 403\n\n        data = request.get_json()\n\n        # Aggiornamento campi\n        if 'description' in data:\n            expense.description = data['description']\n        if 'amount' in data:\n            try:\n                amount = float(data['amount'])\n                if amount <= 0:\n                    return jsonify({'error': 'L\\'importo deve essere maggiore di zero'}), 400\n                expense.amount = amount\n            except (ValueError, TypeError):\n                return jsonify({'error': 'Importo non valido'}), 400\n        if 'category' in data:\n            expense.category = data['category']\n        if 'date' in data:\n            try:\n                expense.date = datetime.strptime(data['date'], '%Y-%m-%d').date()\n            except ValueError:\n                return jsonify({'error': 'Formato data non valido (YYYY-MM-DD)'}), 400\n        if 'notes' in data:\n            expense.notes = data['notes']\n\n        expense.updated_at = datetime.utcnow()\n        db.session.commit()\n\n        return jsonify({\n            'id': expense.id,\n            'description': expense.description,\n            'amount': float(expense.amount),\n            'category': expense.category,\n            'date': expense.date.isoformat(),\n            'notes': expense.notes,\n            'user': {\n                'id': expense.user.id,\n                'name': expense.user.name\n            } if expense.user else None,\n            'updated_at': expense.updated_at.isoformat() if expense.updated_at else None\n        })\n\n    except Exception as e:\n        db.session.rollback()\n        logger.error(f\"Errore nell'aggiornamento spesa {expense_id}: {str(e)}\")\n        return jsonify({'error': 'Errore interno del server'}), 500\n\n@api_expenses.route('/api/expenses/<int:expense_id>', methods=['DELETE'])\n@login_required\ndef delete_expense(expense_id):\n    \"\"\"Elimina una spesa\"\"\"\n    try:\n        expense = ProjectExpense.query.get_or_404(expense_id)\n\n        # Verifica permessi\n        if not current_user.has_permission('manage_expenses'):\n            return jsonify({'error': 'Permessi insufficienti'}), 403\n\n        db.session.delete(expense)\n        db.session.commit()\n\n        return jsonify({'message': 'Spesa eliminata con successo'}), 200\n\n    except Exception as e:\n        db.session.rollback()\n        logger.error(f\"Errore nell'eliminazione spesa {expense_id}: {str(e)}\")\n        return jsonify({'error': 'Errore interno del server'}), 500\n\n@api_expenses.route('/api/projects/<int:project_id>/expenses/summary', methods=['GET'])\n@login_required\ndef get_project_expenses_summary(project_id):\n    \"\"\"Ottieni un riepilogo delle spese del progetto\"\"\"\n    try:\n        project = Project.query.get_or_404(project_id)\n\n        # Verifica permessi\n        if not current_user.can_view_project(project):\n            return jsonify({'error': 'Accesso negato'}), 403\n\n        expenses = ProjectExpense.query.filter_by(project_id=project_id).all()\n\n        # Calcolo statistiche\n        total_amount = sum(expense.amount for expense in expenses)\n        expenses_by_category = {}\n\n        for expense in expenses:\n            category = expense.category\n            if category not in expenses_by_category:\n                expenses_by_category[category] = {\n                    'count': 0,\n                    'total': 0\n                }\n            expenses_by_category[category]['count'] += 1\n            expenses_by_category[category]['total'] += float(expense.amount)\n\n        return jsonify({\n            'total_amount': float(total_amount),\n            'total_expenses': len(expenses),\n            'by_category': expenses_by_category,\n            'average_expense': float(total_amount / len(expenses)) if expenses else 0\n        })\n\n    except Exception as e:\n        logger.error(f\"Errore nel riepilogo spese progetto {project_id}: {str(e)}\")\n        return jsonify({'error': 'Errore interno del server'}), 500", "modifiedCode": "\"\"\"\nAPI Blueprint per la gestione delle spese\n\"\"\"\nfrom flask import Blueprint, request, jsonify, current_app\nfrom flask_login import login_required, current_user\nfrom extensions import db\nfrom models import Project, ProjectExpense, User\nfrom sqlalchemy import and_\nfrom datetime import datetime\nimport logging\n\nlogger = logging.getLogger(__name__)\n\napi_expenses = Blueprint('api_expenses', __name__)\n\n@api_expenses.route('/api/projects/<int:project_id>/expenses', methods=['GET'])\n@login_required\ndef get_project_expenses(project_id):\n    \"\"\"Ottieni tutte le spese di un progetto\"\"\"\n    try:\n        project = Project.query.get_or_404(project_id)\n\n        # Verifica permessi\n        if not current_user.can_view_project(project):\n            return jsonify({'error': 'Accesso negato'}), 403\n\n        expenses = ProjectExpense.query.filter_by(project_id=project_id).order_by(ProjectExpense.date.desc()).all()\n\n        return jsonify([{\n            'id': expense.id,\n            'description': expense.description,\n            'amount': float(expense.amount),\n            'category': expense.category,\n            'date': expense.date.isoformat(),\n            'notes': expense.receipt_path or '',\n            'status': expense.status,\n            'billing_type': expense.billing_type,\n            'user': {\n                'id': expense.user.id,\n                'name': f\"{expense.user.first_name} {expense.user.last_name}\".strip() or expense.user.username\n            } if expense.user else None,\n            'created_at': expense.created_at.isoformat() if expense.created_at else None\n        } for expense in expenses])\n\n    except Exception as e:\n        logger.error(f\"Errore nel recupero spese progetto {project_id}: {str(e)}\")\n        return jsonify({'error': 'Errore interno del server'}), 500\n\n@api_expenses.route('/api/projects/<int:project_id>/expenses', methods=['POST'])\n@login_required\ndef create_project_expense(project_id):\n    \"\"\"Crea una nuova spesa per un progetto\"\"\"\n    try:\n        project = Project.query.get_or_404(project_id)\n\n        # Verifica permessi\n        if not current_user.has_permission('manage_expenses'):\n            return jsonify({'error': 'Permessi insufficienti'}), 403\n\n        data = request.get_json()\n\n        # Validazione dati\n        required_fields = ['description', 'amount', 'category', 'date']\n        for field in required_fields:\n            if field not in data:\n                return jsonify({'error': f'Campo {field} richiesto'}), 400\n\n        # Conversione data\n        try:\n            expense_date = datetime.strptime(data['date'], '%Y-%m-%d').date()\n        except ValueError:\n            return jsonify({'error': 'Formato data non valido (YYYY-MM-DD)'}), 400\n\n        # Validazione importo\n        try:\n            amount = float(data['amount'])\n            if amount <= 0:\n                return jsonify({'error': 'L\\'importo deve essere maggiore di zero'}), 400\n        except (ValueError, TypeError):\n            return jsonify({'error': 'Importo non valido'}), 400\n\n        # Creazione spesa\n        expense = ProjectExpense(\n            project_id=project_id,\n            user_id=current_user.id,\n            description=data['description'],\n            amount=amount,\n            category=data['category'],\n            date=expense_date,\n            billing_type=data.get('billing_type', 'billable'),\n            status='pending',\n            created_at=datetime.utcnow()\n        )\n\n        db.session.add(expense)\n        db.session.commit()\n\n        return jsonify({\n            'id': expense.id,\n            'description': expense.description,\n            'amount': float(expense.amount),\n            'category': expense.category,\n            'date': expense.date.isoformat(),\n            'notes': expense.notes,\n            'user': {\n                'id': expense.user.id,\n                'name': expense.user.name\n            },\n            'created_at': expense.created_at.isoformat()\n        }), 201\n\n    except Exception as e:\n        db.session.rollback()\n        logger.error(f\"Errore nella creazione spesa per progetto {project_id}: {str(e)}\")\n        return jsonify({'error': 'Errore interno del server'}), 500\n\n@api_expenses.route('/api/expenses/<int:expense_id>', methods=['PUT'])\n@login_required\ndef update_expense(expense_id):\n    \"\"\"Aggiorna una spesa esistente\"\"\"\n    try:\n        expense = ProjectExpense.query.get_or_404(expense_id)\n\n        # Verifica permessi\n        if not current_user.has_permission('manage_expenses'):\n            return jsonify({'error': 'Permessi insufficienti'}), 403\n\n        data = request.get_json()\n\n        # Aggiornamento campi\n        if 'description' in data:\n            expense.description = data['description']\n        if 'amount' in data:\n            try:\n                amount = float(data['amount'])\n                if amount <= 0:\n                    return jsonify({'error': 'L\\'importo deve essere maggiore di zero'}), 400\n                expense.amount = amount\n            except (ValueError, TypeError):\n                return jsonify({'error': 'Importo non valido'}), 400\n        if 'category' in data:\n            expense.category = data['category']\n        if 'date' in data:\n            try:\n                expense.date = datetime.strptime(data['date'], '%Y-%m-%d').date()\n            except ValueError:\n                return jsonify({'error': 'Formato data non valido (YYYY-MM-DD)'}), 400\n        if 'notes' in data:\n            expense.notes = data['notes']\n\n        expense.updated_at = datetime.utcnow()\n        db.session.commit()\n\n        return jsonify({\n            'id': expense.id,\n            'description': expense.description,\n            'amount': float(expense.amount),\n            'category': expense.category,\n            'date': expense.date.isoformat(),\n            'notes': expense.notes,\n            'user': {\n                'id': expense.user.id,\n                'name': expense.user.name\n            } if expense.user else None,\n            'updated_at': expense.updated_at.isoformat() if expense.updated_at else None\n        })\n\n    except Exception as e:\n        db.session.rollback()\n        logger.error(f\"Errore nell'aggiornamento spesa {expense_id}: {str(e)}\")\n        return jsonify({'error': 'Errore interno del server'}), 500\n\n@api_expenses.route('/api/expenses/<int:expense_id>', methods=['DELETE'])\n@login_required\ndef delete_expense(expense_id):\n    \"\"\"Elimina una spesa\"\"\"\n    try:\n        expense = ProjectExpense.query.get_or_404(expense_id)\n\n        # Verifica permessi\n        if not current_user.has_permission('manage_expenses'):\n            return jsonify({'error': 'Permessi insufficienti'}), 403\n\n        db.session.delete(expense)\n        db.session.commit()\n\n        return jsonify({'message': 'Spesa eliminata con successo'}), 200\n\n    except Exception as e:\n        db.session.rollback()\n        logger.error(f\"Errore nell'eliminazione spesa {expense_id}: {str(e)}\")\n        return jsonify({'error': 'Errore interno del server'}), 500\n\n@api_expenses.route('/api/projects/<int:project_id>/expenses/summary', methods=['GET'])\n@login_required\ndef get_project_expenses_summary(project_id):\n    \"\"\"Ottieni un riepilogo delle spese del progetto\"\"\"\n    try:\n        project = Project.query.get_or_404(project_id)\n\n        # Verifica permessi\n        if not current_user.can_view_project(project):\n            return jsonify({'error': 'Accesso negato'}), 403\n\n        expenses = ProjectExpense.query.filter_by(project_id=project_id).all()\n\n        # Calcolo statistiche\n        total_amount = sum(expense.amount for expense in expenses)\n        expenses_by_category = {}\n\n        for expense in expenses:\n            category = expense.category\n            if category not in expenses_by_category:\n                expenses_by_category[category] = {\n                    'count': 0,\n                    'total': 0\n                }\n            expenses_by_category[category]['count'] += 1\n            expenses_by_category[category]['total'] += float(expense.amount)\n\n        return jsonify({\n            'total_amount': float(total_amount),\n            'total_expenses': len(expenses),\n            'by_category': expenses_by_category,\n            'average_expense': float(total_amount / len(expenses)) if expenses else 0\n        })\n\n    except Exception as e:\n        logger.error(f\"Errore nel riepilogo spese progetto {project_id}: {str(e)}\")\n        return jsonify({'error': 'Errore interno del server'}), 500"}