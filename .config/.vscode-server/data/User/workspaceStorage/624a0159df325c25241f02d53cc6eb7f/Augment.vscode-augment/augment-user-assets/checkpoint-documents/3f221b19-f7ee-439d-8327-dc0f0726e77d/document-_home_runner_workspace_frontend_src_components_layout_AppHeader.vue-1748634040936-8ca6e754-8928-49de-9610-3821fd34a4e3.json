{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/components/layout/AppHeader.vue"}, "originalCode": "<template>\n  <header class=\"bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700\">\n    <div class=\"flex justify-between items-center px-4 py-4 sm:px-6 lg:px-8\">\n      <div class=\"flex items-center space-x-4\">\n        <!-- Mobile Menu Button -->\n        <button\n          @click=\"$emit('toggle-mobile-sidebar')\"\n          class=\"lg:hidden p-2 rounded-md text-gray-400 hover:text-gray-600 hover:bg-gray-100 dark:text-gray-500 dark:hover:text-gray-300 dark:hover:bg-gray-700\"\n        >\n          <svg class=\"h-6 w-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M4 6h16M4 12h16M4 18h16\" />\n          </svg>\n        </button>\n\n        <!-- Page Title and Breadcrumbs -->\n        <div class=\"flex flex-col\">\n          <h2 class=\"text-lg font-semibold text-gray-900 dark:text-white\">\n            {{ pageTitle }}\n          </h2>\n          <HeaderBreadcrumbs v-if=\"breadcrumbs.length > 0\" :breadcrumbs=\"breadcrumbs\" />\n        </div>\n      </div>\n\n      <div class=\"flex items-center space-x-4\">\n        <!-- Quick Actions -->\n        <div class=\"hidden md:flex items-center space-x-2\">\n          <HeaderQuickActions\n            @quick-create-project=\"$emit('quick-create-project')\"\n          />\n        </div>\n\n        <!-- Notifications -->\n        <HeaderNotifications />\n\n        <!-- Search -->\n        <HeaderSearch />\n\n        <!-- User Menu -->\n        <HeaderUserMenu />\n      </div>\n    </div>\n  </header>\n</template>\n\n<script setup>\nimport HeaderBreadcrumbs from './HeaderBreadcrumbs.vue'\nimport HeaderQuickActions from './HeaderQuickActions.vue'\nimport HeaderNotifications from './HeaderNotifications.vue'\nimport HeaderSearch from './HeaderSearch.vue'\nimport HeaderUserMenu from './HeaderUserMenu.vue'\n\ndefineProps({\n  pageTitle: {\n    type: String,\n    required: true\n  },\n  breadcrumbs: {\n    type: Array,\n    default: () => []\n  }\n})\n\ndefineEmits(['toggle-mobile-sidebar', 'quick-create-project', 'quick-add-task'])\n</script>", "modifiedCode": "<template>\n  <header class=\"bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700\">\n    <div class=\"flex justify-between items-center px-4 py-4 sm:px-6 lg:px-8\">\n      <div class=\"flex items-center space-x-4\">\n        <!-- Mobile Menu Button -->\n        <button\n          @click=\"$emit('toggle-mobile-sidebar')\"\n          class=\"lg:hidden p-2 rounded-md text-gray-400 hover:text-gray-600 hover:bg-gray-100 dark:text-gray-500 dark:hover:text-gray-300 dark:hover:bg-gray-700\"\n        >\n          <svg class=\"h-6 w-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M4 6h16M4 12h16M4 18h16\" />\n          </svg>\n        </button>\n\n        <!-- Page Title and Breadcrumbs -->\n        <div class=\"flex flex-col\">\n          <h2 class=\"text-lg font-semibold text-gray-900 dark:text-white\">\n            {{ pageTitle }}\n          </h2>\n          <HeaderBreadcrumbs v-if=\"breadcrumbs.length > 0\" :breadcrumbs=\"breadcrumbs\" />\n        </div>\n      </div>\n\n      <div class=\"flex items-center space-x-4\">\n        <!-- Quick Actions -->\n        <div class=\"hidden md:flex items-center space-x-2\">\n          <HeaderQuickActions\n            @quick-create-project=\"$emit('quick-create-project')\"\n          />\n        </div>\n\n        <!-- Notifications -->\n        <HeaderNotifications />\n\n        <!-- Search -->\n        <HeaderSearch />\n\n        <!-- User Menu -->\n        <HeaderUserMenu />\n      </div>\n    </div>\n  </header>\n</template>\n\n<script setup>\nimport HeaderBreadcrumbs from './HeaderBreadcrumbs.vue'\nimport HeaderQuickActions from './HeaderQuickActions.vue'\nimport HeaderNotifications from './HeaderNotifications.vue'\nimport HeaderSearch from './HeaderSearch.vue'\nimport HeaderUserMenu from './HeaderUserMenu.vue'\n\ndefineProps({\n  pageTitle: {\n    type: String,\n    required: true\n  },\n  breadcrumbs: {\n    type: Array,\n    default: () => []\n  }\n})\n\ndefineEmits(['toggle-mobile-sidebar', 'quick-create-project'])\n</script>"}