{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/components/layout/AppLayout.vue"}, "originalCode": "<template>\n  <div class=\"h-screen flex bg-gray-50 dark:bg-gray-900\">\n    <!-- Mobile sidebar backdrop -->\n    <div\n      v-if=\"isMobileSidebarOpen\"\n      @click=\"closeMobileSidebar\"\n      class=\"fixed inset-0 z-20 bg-black bg-opacity-50 lg:hidden\"\n    ></div>\n\n    <!-- Sidebar -->\n    <AppSidebar\n      :is-mobile-open=\"isMobileSidebarOpen\"\n      @close=\"closeMobileSidebar\"\n      @toggle-collapsed=\"handleSidebarCollapse\"\n    />\n\n    <!-- Main Content -->\n    <div\n      class=\"flex flex-col flex-1 overflow-hidden transition-all duration-300\"\n      :class=\"[\n        isSidebarCollapsed ? 'lg:ml-20' : 'lg:ml-64'\n      ]\"\n    >\n      <!-- Top Header -->\n      <AppHeader\n        :page-title=\"pageTitle\"\n        :breadcrumbs=\"breadcrumbs\"\n        @toggle-mobile-sidebar=\"toggleMobileSidebar\"\n        @quick-create-project=\"handleCreateProject\"\n        @quick-add-task=\"handleCreateTask\"\n      />\n\n      <!-- Page Content -->\n      <main class=\"flex-1 overflow-y-auto bg-gray-50 dark:bg-gray-900\">\n        <div class=\"py-6\">\n          <div class=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n            <!-- Optional: Page actions bar -->\n            <div v-if=\"hasPageActions\" class=\"mb-6\">\n              <slot name=\"page-actions\" />\n            </div>\n\n            <!-- Main content with loading state -->\n            <div v-if=\"isLoading\" class=\"flex items-center justify-center h-64\">\n              <LoadingSpinner />\n            </div>\n            <router-view v-else />\n          </div>\n        </div>\n      </main>\n    </div>\n\n    <!-- Global notifications -->\n    <NotificationManager />\n  </div>\n</template>\n\n<script setup>\nimport { ref, computed, watch, onMounted } from 'vue'\nimport { useRoute, useRouter } from 'vue-router'\nimport { useTenantStore } from '@/stores/tenant'\nimport AppSidebar from './AppSidebar.vue'\nimport AppHeader from './AppHeader.vue'\nimport LoadingSpinner from '@/components/ui/LoadingSpinner.vue'\nimport NotificationManager from '@/components/ui/NotificationManager.vue'\n\nconst route = useRoute()\nconst tenantStore = useTenantStore()\n\n// Mobile sidebar state\nconst isMobileSidebarOpen = ref(false)\n\n// Desktop sidebar state\nconst isSidebarCollapsed = ref(false)\n\n// Loading state for page transitions\nconst isLoading = ref(false)\n\n// Tenant configuration\nconst tenantConfig = computed(() => tenantStore.config || {})\nconst isLoaded = computed(() => tenantStore.config !== null)\n\n// Computed properties\nconst pageTitle = computed(() => {\n  // Extract title from route meta or generate from route name\n  if (route.meta?.title) return route.meta.title\n\n  const routeNames = {\n    dashboard: 'Dashboard',\n    projects: 'Progetti',\n    'projects-list': 'Elenco Progetti',\n    'projects-view': 'Dettaglio Progetto',\n    'projects-create': 'Nuovo Progetto',\n    personnel: 'Personale',\n    'personnel-directory': 'Rubrica Aziendale',\n    'personnel-orgchart': 'Organigramma',\n    'personnel-skills': 'Competenze'\n  }\n  return routeNames[route.name] || 'DatPortal'\n})\n\nconst breadcrumbs = computed(() => {\n  // Generate breadcrumbs from route hierarchy\n  if (!route.meta?.breadcrumbs) return []\n\n  return route.meta.breadcrumbs.map(crumb => ({\n    label: crumb.label,\n    to: crumb.to,\n    icon: crumb.icon\n  }))\n})\n\nconst hasPageActions = computed(() => {\n  // Check if current page has action buttons\n  return route.meta?.hasActions || false\n})\n\n// Methods\nfunction toggleMobileSidebar() {\n  isMobileSidebarOpen.value = !isMobileSidebarOpen.value\n}\n\nfunction closeMobileSidebar() {\n  isMobileSidebarOpen.value = false\n}\n\nfunction handleSidebarCollapse(isCollapsed) {\n  isSidebarCollapsed.value = isCollapsed\n}\n\n// Watch route changes for loading states\nwatch(route, () => {\n  isLoading.value = true\n  // Simulate loading for smooth transitions\n  setTimeout(() => {\n    isLoading.value = false\n  }, 300)\n})\n\n// Close mobile sidebar on route change\nwatch(route, () => {\n  closeMobileSidebar()\n})\n\nonMounted(() => {\n  // Carica la configurazione tenant se non è già stata caricata\n  if (!isLoaded.value) {\n    tenantStore.loadConfig()\n  }\n})\n</script>", "modifiedCode": "<template>\n  <div class=\"h-screen flex bg-gray-50 dark:bg-gray-900\">\n    <!-- Mobile sidebar backdrop -->\n    <div\n      v-if=\"isMobileSidebarOpen\"\n      @click=\"closeMobileSidebar\"\n      class=\"fixed inset-0 z-20 bg-black bg-opacity-50 lg:hidden\"\n    ></div>\n\n    <!-- Sidebar -->\n    <AppSidebar\n      :is-mobile-open=\"isMobileSidebarOpen\"\n      @close=\"closeMobileSidebar\"\n      @toggle-collapsed=\"handleSidebarCollapse\"\n    />\n\n    <!-- Main Content -->\n    <div\n      class=\"flex flex-col flex-1 overflow-hidden transition-all duration-300\"\n      :class=\"[\n        isSidebarCollapsed ? 'lg:ml-20' : 'lg:ml-64'\n      ]\"\n    >\n      <!-- Top Header -->\n      <AppHeader\n        :page-title=\"pageTitle\"\n        :breadcrumbs=\"breadcrumbs\"\n        @toggle-mobile-sidebar=\"toggleMobileSidebar\"\n        @quick-create-project=\"handleCreateProject\"\n        @quick-add-task=\"handleCreateTask\"\n      />\n\n      <!-- Page Content -->\n      <main class=\"flex-1 overflow-y-auto bg-gray-50 dark:bg-gray-900\">\n        <div class=\"py-6\">\n          <div class=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n            <!-- Optional: Page actions bar -->\n            <div v-if=\"hasPageActions\" class=\"mb-6\">\n              <slot name=\"page-actions\" />\n            </div>\n\n            <!-- Main content with loading state -->\n            <div v-if=\"isLoading\" class=\"flex items-center justify-center h-64\">\n              <LoadingSpinner />\n            </div>\n            <router-view v-else />\n          </div>\n        </div>\n      </main>\n    </div>\n\n    <!-- Global notifications -->\n    <NotificationManager />\n  </div>\n</template>\n\n<script setup>\nimport { ref, computed, watch, onMounted } from 'vue'\nimport { useRoute, useRouter } from 'vue-router'\nimport { useTenantStore } from '@/stores/tenant'\nimport AppSidebar from './AppSidebar.vue'\nimport AppHeader from './AppHeader.vue'\nimport LoadingSpinner from '@/components/ui/LoadingSpinner.vue'\nimport NotificationManager from '@/components/ui/NotificationManager.vue'\n\nconst route = useRoute()\nconst router = useRouter()\nconst tenantStore = useTenantStore()\n\n// Mobile sidebar state\nconst isMobileSidebarOpen = ref(false)\n\n// Desktop sidebar state\nconst isSidebarCollapsed = ref(false)\n\n// Loading state for page transitions\nconst isLoading = ref(false)\n\n// Tenant configuration\nconst tenantConfig = computed(() => tenantStore.config || {})\nconst isLoaded = computed(() => tenantStore.config !== null)\n\n// Computed properties\nconst pageTitle = computed(() => {\n  // Extract title from route meta or generate from route name\n  if (route.meta?.title) return route.meta.title\n\n  const routeNames = {\n    dashboard: 'Dashboard',\n    projects: 'Progetti',\n    'projects-list': 'Elenco Progetti',\n    'projects-view': 'Dettaglio Progetto',\n    'projects-create': 'Nuovo Progetto',\n    personnel: 'Personale',\n    'personnel-directory': 'Rubrica Aziendale',\n    'personnel-orgchart': 'Organigramma',\n    'personnel-skills': 'Competenze'\n  }\n  return routeNames[route.name] || 'DatPortal'\n})\n\nconst breadcrumbs = computed(() => {\n  // Generate breadcrumbs from route hierarchy\n  if (!route.meta?.breadcrumbs) return []\n\n  return route.meta.breadcrumbs.map(crumb => ({\n    label: crumb.label,\n    to: crumb.to,\n    icon: crumb.icon\n  }))\n})\n\nconst hasPageActions = computed(() => {\n  // Check if current page has action buttons\n  return route.meta?.hasActions || false\n})\n\n// Methods\nfunction toggleMobileSidebar() {\n  isMobileSidebarOpen.value = !isMobileSidebarOpen.value\n}\n\nfunction closeMobileSidebar() {\n  isMobileSidebarOpen.value = false\n}\n\nfunction handleSidebarCollapse(isCollapsed) {\n  isSidebarCollapsed.value = isCollapsed\n}\n\n// Watch route changes for loading states\nwatch(route, () => {\n  isLoading.value = true\n  // Simulate loading for smooth transitions\n  setTimeout(() => {\n    isLoading.value = false\n  }, 300)\n})\n\n// Close mobile sidebar on route change\nwatch(route, () => {\n  closeMobileSidebar()\n})\n\nonMounted(() => {\n  // Carica la configurazione tenant se non è già stata caricata\n  if (!isLoaded.value) {\n    tenantStore.loadConfig()\n  }\n})\n</script>"}