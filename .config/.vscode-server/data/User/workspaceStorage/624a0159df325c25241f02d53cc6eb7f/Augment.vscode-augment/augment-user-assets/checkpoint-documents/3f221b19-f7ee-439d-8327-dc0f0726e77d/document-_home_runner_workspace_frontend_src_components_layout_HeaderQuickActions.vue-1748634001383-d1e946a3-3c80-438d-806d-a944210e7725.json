{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/components/layout/HeaderQuickActions.vue"}, "originalCode": "<template>\n  <div class=\"flex items-center space-x-2\">\n    <!-- Quick actions basate sulla pagina corrente -->\n    <template v-if=\"showProjectActions\">\n      <button\n        @click=\"$emit('quick-create-project')\"\n        class=\"inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded-md text-primary-700 bg-primary-100 hover:bg-primary-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500\"\n      >\n        <svg class=\"h-3 w-3 mr-1\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 6v6m0 0v6m0-6h6m-6 0H6\" />\n        </svg>\n        Nuovo Progetto\n      </button>\n    </template>\n\n\n\n    <!-- Dark Mode Toggle -->\n    <button\n      @click=\"toggleDarkMode\"\n      class=\"inline-flex items-center px-3 py-1.5 border border-gray-300 text-xs font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 dark:bg-gray-800 dark:text-gray-300 dark:border-gray-600 dark:hover:bg-gray-700\"\n      title=\"Cambia tema\"\n    >\n      <svg v-if=\"!isDarkMode\" class=\"h-3 w-3\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n        <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z\" />\n      </svg>\n      <svg v-else class=\"h-3 w-3\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n        <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z\" />\n      </svg>\n    </button>\n  </div>\n</template>\n\n<script setup>\nimport { computed } from 'vue'\nimport { useRoute } from 'vue-router'\nimport { useDarkMode } from '@/composables/useDarkMode'\n\nconst route = useRoute()\n\ndefineEmits(['quick-create-project', 'quick-add-task'])\n\n// Dark mode composable\nconst { isDarkMode, toggleDarkMode } = useDarkMode()\n\n// Mostra azioni progetto solo nelle pagine progetti\nconst showProjectActions = computed(() => {\n  return route.name?.includes('projects') || route.path.includes('/projects')\n})\n\n// Mostra azioni task solo nelle pagine task o progetti\nconst showTaskActions = computed(() => {\n  return route.name?.includes('tasks') ||\n         route.name?.includes('projects') ||\n         route.path.includes('/tasks') ||\n         route.path.includes('/projects')\n})\n</script>", "modifiedCode": "<template>\n  <div class=\"flex items-center space-x-2\">\n    <!-- Quick actions basate sulla pagina corrente -->\n    <template v-if=\"showProjectActions\">\n      <button\n        @click=\"$emit('quick-create-project')\"\n        class=\"inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded-md text-primary-700 bg-primary-100 hover:bg-primary-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500\"\n      >\n        <svg class=\"h-3 w-3 mr-1\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 6v6m0 0v6m0-6h6m-6 0H6\" />\n        </svg>\n        Nuovo Progetto\n      </button>\n    </template>\n\n\n\n    <!-- Dark Mode Toggle -->\n    <button\n      @click=\"toggleDarkMode\"\n      class=\"inline-flex items-center px-3 py-1.5 border border-gray-300 text-xs font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 dark:bg-gray-800 dark:text-gray-300 dark:border-gray-600 dark:hover:bg-gray-700\"\n      title=\"Cambia tema\"\n    >\n      <svg v-if=\"!isDarkMode\" class=\"h-3 w-3\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n        <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z\" />\n      </svg>\n      <svg v-else class=\"h-3 w-3\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n        <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z\" />\n      </svg>\n    </button>\n  </div>\n</template>\n\n<script setup>\nimport { computed } from 'vue'\nimport { useRoute } from 'vue-router'\nimport { useDarkMode } from '@/composables/useDarkMode'\n\nconst route = useRoute()\n\ndefineEmits(['quick-create-project'])\n\n// Dark mode composable\nconst { isDarkMode, toggleDarkMode } = useDarkMode()\n\n// Mostra azioni progetto solo nelle pagine progetti\nconst showProjectActions = computed(() => {\n  return route.name?.includes('projects') || route.path.includes('/projects')\n})\n\n// Mostra azioni task solo nelle pagine task o progetti\nconst showTaskActions = computed(() => {\n  return route.name?.includes('tasks') ||\n         route.name?.includes('projects') ||\n         route.path.includes('/tasks') ||\n         route.path.includes('/projects')\n})\n</script>"}