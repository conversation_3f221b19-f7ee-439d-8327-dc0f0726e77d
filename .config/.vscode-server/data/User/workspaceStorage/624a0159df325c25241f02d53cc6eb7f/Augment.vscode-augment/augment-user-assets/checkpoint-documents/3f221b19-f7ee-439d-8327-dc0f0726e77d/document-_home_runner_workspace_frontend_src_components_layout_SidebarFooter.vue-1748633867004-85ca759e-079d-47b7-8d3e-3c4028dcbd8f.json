{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/components/layout/SidebarFooter.vue"}, "originalCode": "<template>\n  <div class=\"flex-shrink-0 border-t border-gray-200 p-4\">\n    <!-- User Profile Section -->\n    <div class=\"flex items-center\" :class=\"{ 'justify-center': isCollapsed }\">\n      <div class=\"flex-shrink-0\">\n        <div class=\"h-8 w-8 rounded-full bg-primary-100 flex items-center justify-center\">\n          <span class=\"text-sm font-medium text-primary-700\">\n            {{ userInitials }}\n          </span>\n        </div>\n      </div>\n\n      <div v-if=\"!isCollapsed\" class=\"ml-3 flex-1 min-w-0\">\n        <p class=\"text-sm font-medium text-gray-900 truncate\">\n          {{ userName }}\n        </p>\n        <p class=\"text-xs text-gray-500 truncate\">\n          {{ userRole }}\n        </p>\n      </div>\n\n      <!-- User Menu Dropdown -->\n      <div class=\"relative\" :class=\"{ 'ml-3': !isCollapsed }\">\n        <button\n          @click=\"showUserMenu = !showUserMenu\"\n          class=\"p-1 rounded-full text-gray-400 hover:text-gray-600 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-primary-500\"\n        >\n          <svg class=\"h-4 w-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M5 12h.01M12 12h.01M19 12h.01M6 12a1 1 0 11-2 0 1 1 0 012 0zM13 12a1 1 0 11-2 0 1 1 0 012 0zM20 12a1 1 0 11-2 0 1 1 0 012 0z\" />\n          </svg>\n        </button>\n\n        <!-- Dropdown Menu -->\n        <div\n          v-if=\"showUserMenu\"\n          @click.away=\"showUserMenu = false\"\n          class=\"origin-bottom-left absolute bottom-full left-0 mb-2 w-48 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5 z-50\"\n        >\n          <div class=\"py-1\">\n            <router-link\n              to=\"/app/profile\"\n              class=\"block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\"\n              @click=\"showUserMenu = false\"\n            >\n              Il tuo profilo\n            </router-link>\n            <router-link\n              to=\"/app/settings\"\n              class=\"block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\"\n              @click=\"showUserMenu = false\"\n            >\n              Impostazioni\n            </router-link>\n            <hr class=\"my-1\">\n            <button\n              @click=\"logout\"\n              class=\"block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\"\n            >\n              Esci\n            </button>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- App Version Info -->\n    <div v-if=\"appVersion && !isCollapsed\" class=\"mt-3 text-xs text-gray-400 text-center\">\n      v{{ appVersion }}\n    </div>\n  </div>\n</template>\n\n<script setup>\nimport { ref, computed } from 'vue'\nimport { useRouter } from 'vue-router'\nimport { useAuthStore } from '@/stores/auth'\n\nconst props = defineProps({\n  isCollapsed: {\n    type: Boolean,\n    default: false\n  }\n})\n\nconst router = useRouter()\nconst authStore = useAuthStore()\nconst showUserMenu = ref(false)\n\nconst userName = computed(() => {\n  if (!authStore.user) return 'Utente'\n  return authStore.user.name || authStore.user.username || 'Utente'\n})\n\nconst userInitials = computed(() => {\n  if (!authStore.user) return 'U'\n  const name = userName.value\n  return name.charAt(0).toUpperCase()\n})\n\nconst userRole = computed(() => {\n  if (!authStore.user) return ''\n  const roles = {\n    admin: 'Amministratore',\n    manager: 'Manager',\n    employee: 'Dipendente',\n    client: 'Cliente'\n  }\n  return roles[authStore.user.role] || authStore.user.role\n})\n\nconst appVersion = computed(() => {\n  // This could come from environment variables or package.json\n  return import.meta.env.VITE_APP_VERSION || '1.0.0'\n})\n\nasync function logout() {\n  showUserMenu.value = false\n  await authStore.logout()\n  router.push('/auth/login')\n}\n</script>", "modifiedCode": "<template>\n  <div class=\"flex-shrink-0 border-t border-gray-200 p-4\">\n    <!-- User Profile Section -->\n    <div class=\"flex items-center\" :class=\"{ 'justify-center': isCollapsed }\">\n      <div class=\"flex-shrink-0\">\n        <div class=\"h-8 w-8 rounded-full bg-primary-100 flex items-center justify-center\">\n          <span class=\"text-sm font-medium text-primary-700\">\n            {{ userInitials }}\n          </span>\n        </div>\n      </div>\n\n      <div v-if=\"!isCollapsed\" class=\"ml-3 flex-1 min-w-0\">\n        <p class=\"text-sm font-medium text-gray-900 truncate\">\n          {{ userName }}\n        </p>\n        <p class=\"text-xs text-gray-500 truncate\">\n          {{ userRole }}\n        </p>\n      </div>\n\n      <!-- User Menu Dropdown -->\n      <div class=\"relative\" :class=\"{ 'ml-3': !isCollapsed }\">\n        <button\n          @click=\"showUserMenu = !showUserMenu\"\n          class=\"p-1 rounded-full text-gray-400 hover:text-gray-600 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-primary-500\"\n        >\n          <svg class=\"h-4 w-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M5 12h.01M12 12h.01M19 12h.01M6 12a1 1 0 11-2 0 1 1 0 012 0zM13 12a1 1 0 11-2 0 1 1 0 012 0zM20 12a1 1 0 11-2 0 1 1 0 012 0z\" />\n          </svg>\n        </button>\n\n        <!-- Dropdown Menu -->\n        <div\n          v-if=\"showUserMenu\"\n          @click.away=\"showUserMenu = false\"\n          class=\"origin-bottom-left absolute bottom-full left-0 mb-2 w-48 rounded-md shadow-lg bg-white dark:bg-gray-800 ring-1 ring-black ring-opacity-5 z-[9999]\"\n        >\n          <div class=\"py-1\">\n            <router-link\n              to=\"/app/profile\"\n              class=\"block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\"\n              @click=\"showUserMenu = false\"\n            >\n              Il tuo profilo\n            </router-link>\n            <router-link\n              to=\"/app/settings\"\n              class=\"block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\"\n              @click=\"showUserMenu = false\"\n            >\n              Impostazioni\n            </router-link>\n            <hr class=\"my-1\">\n            <button\n              @click=\"logout\"\n              class=\"block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\"\n            >\n              Esci\n            </button>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- App Version Info -->\n    <div v-if=\"appVersion && !isCollapsed\" class=\"mt-3 text-xs text-gray-400 text-center\">\n      v{{ appVersion }}\n    </div>\n  </div>\n</template>\n\n<script setup>\nimport { ref, computed } from 'vue'\nimport { useRouter } from 'vue-router'\nimport { useAuthStore } from '@/stores/auth'\n\nconst props = defineProps({\n  isCollapsed: {\n    type: Boolean,\n    default: false\n  }\n})\n\nconst router = useRouter()\nconst authStore = useAuthStore()\nconst showUserMenu = ref(false)\n\nconst userName = computed(() => {\n  if (!authStore.user) return 'Utente'\n  return authStore.user.name || authStore.user.username || 'Utente'\n})\n\nconst userInitials = computed(() => {\n  if (!authStore.user) return 'U'\n  const name = userName.value\n  return name.charAt(0).toUpperCase()\n})\n\nconst userRole = computed(() => {\n  if (!authStore.user) return ''\n  const roles = {\n    admin: 'Amministratore',\n    manager: 'Manager',\n    employee: 'Dipendente',\n    client: 'Cliente'\n  }\n  return roles[authStore.user.role] || authStore.user.role\n})\n\nconst appVersion = computed(() => {\n  // This could come from environment variables or package.json\n  return import.meta.env.VITE_APP_VERSION || '1.0.0'\n})\n\nasync function logout() {\n  showUserMenu.value = false\n  await authStore.logout()\n  router.push('/auth/login')\n}\n</script>"}