{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/components/layout/SidebarNavItemCollapsible.vue"}, "originalCode": "<template>\n  <div>\n    <!-- Main Collapsible Button -->\n    <button\n      @click=\"toggleExpanded\"\n      class=\"group w-full flex items-center px-2 py-2 text-sm font-medium rounded-md transition-colors duration-150\"\n      :class=\"[\n        navItemClasses,\n        { 'justify-center': isCollapsed }\n      ]\"\n    >\n      <SidebarIcon\n        :icon=\"item.icon\"\n        :class=\"[\n          'flex-shrink-0 h-6 w-6',\n          { 'mr-0': isCollapsed, 'mr-3': !isCollapsed }\n        ]\"\n      />\n      <span\n        v-if=\"!isCollapsed\"\n        class=\"flex-1 text-left truncate\"\n      >\n        {{ item.name }}\n      </span>\n      <svg\n        v-if=\"!isCollapsed\"\n        :class=\"{ 'rotate-90': isExpanded }\"\n        class=\"ml-2 h-4 w-4 transition-transform duration-150\"\n        fill=\"none\"\n        viewBox=\"0 0 24 24\"\n        stroke=\"currentColor\"\n      >\n        <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9 5l7 7-7 7\" />\n      </svg>\n    </button>\n\n    <!-- Submenu Items -->\n    <div\n      v-if=\"isExpanded && !isCollapsed\"\n      class=\"ml-6 space-y-1 mt-1\"\n    >\n      <router-link\n        v-for=\"child in visibleChildren\"\n        :key=\"child.name\"\n        :to=\"child.path\"\n        :class=\"[\n          'group flex items-center px-2 py-2 text-sm font-medium rounded-md transition-colors duration-150',\n          child.path === '#'\n            ? 'text-gray-400 dark:text-gray-500 hover:text-gray-500 dark:hover:text-gray-400 cursor-not-allowed opacity-75'\n            : 'text-gray-600 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 hover:text-primary-600 dark:hover:text-primary-400'\n        ]\"\n        active-class=\"text-primary-600 dark:text-primary-400 bg-primary-50 dark:bg-primary-900\"\n        @click=\"handleChildClick(child)\"\n      >\n        <SidebarIcon\n          v-if=\"child.icon\"\n          :icon=\"child.icon\"\n          class=\"flex-shrink-0 h-4 w-4 mr-2\"\n        />\n        <span class=\"truncate\">{{ child.name }}</span>\n      </router-link>\n    </div>\n  </div>\n</template>\n\n<script setup>\nimport { ref, computed } from 'vue'\nimport { useRoute } from 'vue-router'\nimport { useAuthStore } from '@/stores/auth'\nimport SidebarIcon from './SidebarIcon.vue'\n\nconst props = defineProps({\n  item: {\n    type: Object,\n    required: true\n  },\n  isCollapsed: {\n    type: Boolean,\n    default: false\n  }\n})\n\ndefineEmits(['click'])\n\nconst route = useRoute()\nconst authStore = useAuthStore()\nconst isExpanded = ref(false)\n\nconst navItemClasses = computed(() => [\n  'text-gray-700 hover:bg-gray-50 hover:text-primary-600',\n  {\n    'text-primary-600 bg-primary-50': isActiveParent.value\n  }\n])\n\nconst isActiveParent = computed(() => {\n  if (!props.item.children) return false\n  return props.item.children.some(child =>\n    child.path !== '#' && route.path.startsWith(child.path)\n  )\n})\n\nconst visibleChildren = computed(() => {\n  if (!props.item.children) return []\n\n  return props.item.children.filter(child => {\n    // Se l'elemento richiede permessi admin, verifica il ruolo utente\n    if (child.admin) {\n      return authStore.user?.role === 'admin'\n    }\n    return true\n  })\n})\n\n// Auto-expand if any child is active\nif (isActiveParent.value) {\n  isExpanded.value = true\n}\n\nfunction toggleExpanded() {\n  if (props.isCollapsed) {\n    // Se la sidebar è collassata, espandi la sidebar prima\n    return\n  }\n  isExpanded.value = !isExpanded.value\n}\n\nfunction handleChildClick(child) {\n  if (child.path === '#') {\n    // Prevent navigation for placeholder links\n    return false\n  }\n}\n</script>", "modifiedCode": "<template>\n  <div>\n    <!-- Main Collapsible Button -->\n    <button\n      @click=\"toggleExpanded\"\n      class=\"group w-full flex items-center px-2 py-2 text-sm font-medium rounded-md transition-colors duration-150\"\n      :class=\"[\n        navItemClasses,\n        { 'justify-center': isCollapsed }\n      ]\"\n    >\n      <SidebarIcon\n        :icon=\"item.icon\"\n        :class=\"[\n          'flex-shrink-0 h-6 w-6',\n          { 'mr-0': isCollapsed, 'mr-3': !isCollapsed }\n        ]\"\n      />\n      <span\n        v-if=\"!isCollapsed\"\n        class=\"flex-1 text-left truncate\"\n      >\n        {{ item.name }}\n      </span>\n      <svg\n        v-if=\"!isCollapsed\"\n        :class=\"{ 'rotate-90': isExpanded }\"\n        class=\"ml-2 h-4 w-4 transition-transform duration-150\"\n        fill=\"none\"\n        viewBox=\"0 0 24 24\"\n        stroke=\"currentColor\"\n      >\n        <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9 5l7 7-7 7\" />\n      </svg>\n    </button>\n\n    <!-- Submenu Items -->\n    <div\n      v-if=\"isExpanded && !isCollapsed\"\n      class=\"ml-6 space-y-1 mt-1\"\n    >\n      <router-link\n        v-for=\"child in visibleChildren\"\n        :key=\"child.name\"\n        :to=\"child.path\"\n        :class=\"[\n          'group flex items-center px-2 py-2 text-sm font-medium rounded-md transition-colors duration-150',\n          child.path === '#'\n            ? 'text-gray-400 dark:text-gray-500 hover:text-gray-500 dark:hover:text-gray-400 cursor-not-allowed opacity-75'\n            : 'text-gray-600 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 hover:text-primary-600 dark:hover:text-primary-400'\n        ]\"\n        active-class=\"text-primary-600 dark:text-primary-400 bg-primary-50 dark:bg-primary-900\"\n        @click=\"handleChildClick(child)\"\n      >\n        <SidebarIcon\n          v-if=\"child.icon\"\n          :icon=\"child.icon\"\n          class=\"flex-shrink-0 h-4 w-4 mr-2\"\n        />\n        <span class=\"truncate\">{{ child.name }}</span>\n      </router-link>\n    </div>\n  </div>\n</template>\n\n<script setup>\nimport { ref, computed } from 'vue'\nimport { useRoute } from 'vue-router'\nimport { useAuthStore } from '@/stores/auth'\nimport SidebarIcon from './SidebarIcon.vue'\n\nconst props = defineProps({\n  item: {\n    type: Object,\n    required: true\n  },\n  isCollapsed: {\n    type: Boolean,\n    default: false\n  }\n})\n\ndefineEmits(['click'])\n\nconst route = useRoute()\nconst authStore = useAuthStore()\nconst isExpanded = ref(false)\n\nconst navItemClasses = computed(() => [\n  'text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 hover:text-primary-600 dark:hover:text-primary-400',\n  {\n    'text-primary-600 dark:text-primary-400 bg-primary-50 dark:bg-primary-900': isActiveParent.value\n  }\n])\n\nconst isActiveParent = computed(() => {\n  if (!props.item.children) return false\n  return props.item.children.some(child =>\n    child.path !== '#' && route.path.startsWith(child.path)\n  )\n})\n\nconst visibleChildren = computed(() => {\n  if (!props.item.children) return []\n\n  return props.item.children.filter(child => {\n    // Se l'elemento richiede permessi admin, verifica il ruolo utente\n    if (child.admin) {\n      return authStore.user?.role === 'admin'\n    }\n    return true\n  })\n})\n\n// Auto-expand if any child is active\nif (isActiveParent.value) {\n  isExpanded.value = true\n}\n\nfunction toggleExpanded() {\n  if (props.isCollapsed) {\n    // Se la sidebar è collassata, espandi la sidebar prima\n    return\n  }\n  isExpanded.value = !isExpanded.value\n}\n\nfunction handleChildClick(child) {\n  if (child.path === '#') {\n    // Prevent navigation for placeholder links\n    return false\n  }\n}\n</script>"}