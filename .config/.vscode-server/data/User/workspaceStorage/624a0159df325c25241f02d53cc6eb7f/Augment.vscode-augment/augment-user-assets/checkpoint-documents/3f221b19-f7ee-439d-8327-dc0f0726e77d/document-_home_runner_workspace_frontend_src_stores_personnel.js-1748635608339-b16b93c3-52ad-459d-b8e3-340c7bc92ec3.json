{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/stores/personnel.js"}, "modifiedCode": "import { defineStore } from 'pinia'\nimport { ref, computed } from 'vue'\n\nexport const usePersonnelStore = defineStore('personnel', () => {\n  // State\n  const users = ref([])\n  const departments = ref([])\n  const skills = ref([])\n  const currentUser = ref(null)\n  const loading = ref(false)\n  const error = ref(null)\n  \n  // Filters state\n  const filters = ref({\n    search: '',\n    department: null,\n    skill: null,\n    role: null,\n    location: null,\n    sort: 'name'\n  })\n  \n  // Pagination state\n  const pagination = ref({\n    page: 1,\n    per_page: 20,\n    total: 0,\n    pages: 0\n  })\n\n  // Getters\n  const filteredUsers = computed(() => {\n    let filtered = users.value\n    \n    if (filters.value.search) {\n      const search = filters.value.search.toLowerCase()\n      filtered = filtered.filter(user => \n        user.full_name?.toLowerCase().includes(search) ||\n        user.email?.toLowerCase().includes(search) ||\n        user.position?.toLowerCase().includes(search)\n      )\n    }\n    \n    if (filters.value.department) {\n      filtered = filtered.filter(user => \n        user.department_id === filters.value.department\n      )\n    }\n    \n    if (filters.value.skill) {\n      filtered = filtered.filter(user => \n        user.skills?.some(skill => skill.id === filters.value.skill)\n      )\n    }\n    \n    if (filters.value.role) {\n      filtered = filtered.filter(user => user.role === filters.value.role)\n    }\n    \n    return filtered\n  })\n\n  const departmentTree = computed(() => {\n    const buildTree = (parentId = null) => {\n      return departments.value\n        .filter(dept => dept.parent_id === parentId)\n        .map(dept => ({\n          ...dept,\n          children: buildTree(dept.id)\n        }))\n    }\n    return buildTree()\n  })\n\n  // Actions\n  const fetchUsers = async (params = {}) => {\n    loading.value = true\n    error.value = null\n    \n    try {\n      const queryParams = new URLSearchParams({\n        page: params.page || pagination.value.page,\n        per_page: params.per_page || pagination.value.per_page,\n        ...params\n      })\n      \n      const response = await fetch(`/api/personnel/users?${queryParams}`, {\n        credentials: 'include'\n      })\n      \n      if (!response.ok) {\n        throw new Error(`HTTP ${response.status}: ${response.statusText}`)\n      }\n      \n      const data = await response.json()\n      \n      if (data.success) {\n        users.value = data.data.users || []\n        pagination.value = {\n          page: data.data.page || 1,\n          per_page: data.data.per_page || 20,\n          total: data.data.total || 0,\n          pages: data.data.pages || 0\n        }\n      } else {\n        throw new Error(data.message || 'Errore nel caricamento utenti')\n      }\n    } catch (err) {\n      error.value = err.message\n      console.error('Errore fetchUsers:', err)\n    } finally {\n      loading.value = false\n    }\n  }\n\n  const fetchUser = async (id) => {\n    loading.value = true\n    error.value = null\n    \n    try {\n      const response = await fetch(`/api/personnel/users/${id}`, {\n        credentials: 'include'\n      })\n      \n      if (!response.ok) {\n        throw new Error(`HTTP ${response.status}: ${response.statusText}`)\n      }\n      \n      const data = await response.json()\n      \n      if (data.success) {\n        currentUser.value = data.data.user\n        return data.data.user\n      } else {\n        throw new Error(data.message || 'Errore nel caricamento utente')\n      }\n    } catch (err) {\n      error.value = err.message\n      console.error('Errore fetchUser:', err)\n      throw err\n    } finally {\n      loading.value = false\n    }\n  }\n\n  const updateUser = async (id, userData) => {\n    loading.value = true\n    error.value = null\n    \n    try {\n      const response = await fetch(`/api/personnel/users/${id}`, {\n        method: 'PUT',\n        headers: {\n          'Content-Type': 'application/json'\n        },\n        credentials: 'include',\n        body: JSON.stringify(userData)\n      })\n      \n      if (!response.ok) {\n        throw new Error(`HTTP ${response.status}: ${response.statusText}`)\n      }\n      \n      const data = await response.json()\n      \n      if (data.success) {\n        // Update user in the list\n        const index = users.value.findIndex(u => u.id === id)\n        if (index !== -1) {\n          users.value[index] = { ...users.value[index], ...data.data.user }\n        }\n        \n        // Update current user if it's the same\n        if (currentUser.value?.id === id) {\n          currentUser.value = { ...currentUser.value, ...data.data.user }\n        }\n        \n        return data.data.user\n      } else {\n        throw new Error(data.message || 'Errore nell\\'aggiornamento utente')\n      }\n    } catch (err) {\n      error.value = err.message\n      console.error('Errore updateUser:', err)\n      throw err\n    } finally {\n      loading.value = false\n    }\n  }\n\n  const fetchDepartments = async () => {\n    loading.value = true\n    error.value = null\n    \n    try {\n      const response = await fetch('/api/personnel/departments', {\n        credentials: 'include'\n      })\n      \n      if (!response.ok) {\n        throw new Error(`HTTP ${response.status}: ${response.statusText}`)\n      }\n      \n      const data = await response.json()\n      \n      if (data.success) {\n        departments.value = data.data.departments || []\n      } else {\n        throw new Error(data.message || 'Errore nel caricamento dipartimenti')\n      }\n    } catch (err) {\n      error.value = err.message\n      console.error('Errore fetchDepartments:', err)\n    } finally {\n      loading.value = false\n    }\n  }\n\n  const fetchSkills = async () => {\n    loading.value = true\n    error.value = null\n    \n    try {\n      const response = await fetch('/api/personnel/skills', {\n        credentials: 'include'\n      })\n      \n      if (!response.ok) {\n        throw new Error(`HTTP ${response.status}: ${response.statusText}`)\n      }\n      \n      const data = await response.json()\n      \n      if (data.success) {\n        skills.value = data.data.skills || []\n      } else {\n        throw new Error(data.message || 'Errore nel caricamento competenze')\n      }\n    } catch (err) {\n      error.value = err.message\n      console.error('Errore fetchSkills:', err)\n    } finally {\n      loading.value = false\n    }\n  }\n\n  // Filter actions\n  const setFilter = (key, value) => {\n    filters.value[key] = value\n  }\n\n  const clearFilters = () => {\n    filters.value = {\n      search: '',\n      department: null,\n      skill: null,\n      role: null,\n      location: null,\n      sort: 'name'\n    }\n  }\n\n  const setPagination = (page, per_page = null) => {\n    pagination.value.page = page\n    if (per_page) {\n      pagination.value.per_page = per_page\n    }\n  }\n\n  // Reset state\n  const $reset = () => {\n    users.value = []\n    departments.value = []\n    skills.value = []\n    currentUser.value = null\n    loading.value = false\n    error.value = null\n    clearFilters()\n    pagination.value = {\n      page: 1,\n      per_page: 20,\n      total: 0,\n      pages: 0\n    }\n  }\n\n  return {\n    // State\n    users,\n    departments,\n    skills,\n    currentUser,\n    loading,\n    error,\n    filters,\n    pagination,\n    \n    // Getters\n    filteredUsers,\n    departmentTree,\n    \n    // Actions\n    fetchUsers,\n    fetchUser,\n    updateUser,\n    fetchDepartments,\n    fetchSkills,\n    setFilter,\n    clearFilters,\n    setPagination,\n    $reset\n  }\n})\n"}