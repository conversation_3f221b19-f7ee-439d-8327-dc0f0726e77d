{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/views/personnel/Personnel.vue"}, "originalCode": "<template>\n  <div>\n    <h1 class=\"text-2xl font-bold text-gray-900 mb-6\">Personale</h1>\n    <div class=\"card\">\n      <p class=\"text-gray-600\">Gestione personale in fase di migrazione...</p>\n    </div>\n  </div>\n</template>", "modifiedCode": "<template>\n  <div>\n    <!-- Header -->\n    <div class=\"mb-6\">\n      <div class=\"flex flex-col sm:flex-row sm:items-center sm:justify-between\">\n        <div>\n          <h1 class=\"text-2xl font-bold text-gray-900 dark:text-white\">👥 Personale</h1>\n          <p class=\"mt-1 text-sm text-gray-500 dark:text-gray-400\">\n            Gestisci il team e le competenze aziendali\n          </p>\n        </div>\n        <div class=\"mt-4 sm:mt-0 flex flex-wrap gap-3\">\n          <router-link\n            to=\"/app/personnel/directory\"\n            class=\"inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700\"\n          >\n            📖 Directory\n          </router-link>\n          <router-link\n            to=\"/app/personnel/orgchart\"\n            class=\"inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700\"\n          >\n            🏢 Organigramma\n          </router-link>\n          <router-link\n            to=\"/app/personnel/skills\"\n            class=\"inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700\"\n          >\n            🎯 Competenze\n          </router-link>\n          <router-link\n            v-if=\"hasAdminAccess\"\n            to=\"/app/personnel/admin\"\n            class=\"inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700\"\n          >\n            ⚙️ Amministrazione\n          </router-link>\n        </div>\n      </div>\n    </div>\n\n    <!-- Filtri -->\n    <div class=\"bg-white dark:bg-gray-800 shadow rounded-lg mb-6\">\n      <div class=\"px-6 py-4 border-b border-gray-200 dark:border-gray-700\">\n        <h3 class=\"text-lg font-medium text-gray-900 dark:text-white\">Filtri</h3>\n      </div>\n      <div class=\"p-6\">\n        <div class=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n          <!-- Filtro Dipartimento -->\n          <div>\n            <label class=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n              Dipartimento\n            </label>\n            <select\n              v-model=\"filters.department\"\n              @change=\"applyFilters\"\n              class=\"w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white\"\n            >\n              <option value=\"\">Tutti i dipartimenti</option>\n              <option\n                v-for=\"dept in departments\"\n                :key=\"dept.id\"\n                :value=\"dept.id\"\n              >\n                {{ dept.name }}\n              </option>\n            </select>\n          </div>\n\n          <!-- Filtro Competenza -->\n          <div>\n            <label class=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n              Competenza\n            </label>\n            <select\n              v-model=\"filters.skill\"\n              @change=\"applyFilters\"\n              class=\"w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white\"\n            >\n              <option value=\"\">Tutte le competenze</option>\n              <option\n                v-for=\"skill in skills\"\n                :key=\"skill.id\"\n                :value=\"skill.id\"\n              >\n                {{ skill.name }} ({{ skill.category }})\n              </option>\n            </select>\n          </div>\n\n          <!-- Ricerca -->\n          <div>\n            <label class=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n              Ricerca\n            </label>\n            <input\n              v-model=\"searchQuery\"\n              @input=\"debounceSearch\"\n              type=\"text\"\n              placeholder=\"Nome, email, posizione...\"\n              class=\"w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white\"\n            >\n          </div>\n        </div>\n\n        <!-- Pulsante reset filtri -->\n        <div v-if=\"hasActiveFilters\" class=\"mt-4 flex justify-end\">\n          <button\n            @click=\"clearFilters\"\n            class=\"inline-flex items-center px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700\"\n          >\n            🗑️ Pulisci filtri\n          </button>\n        </div>\n      </div>\n    </div>\n\n    <!-- Lista Personale -->\n    <div class=\"bg-white dark:bg-gray-800 shadow rounded-lg\">\n      <div class=\"px-6 py-4 border-b border-gray-200 dark:border-gray-700\">\n        <div class=\"flex items-center justify-between\">\n          <h3 class=\"text-lg font-medium text-gray-900 dark:text-white\">\n            Team ({{ pagination.total }} dipendenti{{ hasActiveFilters ? ' - filtrati' : '' }})\n          </h3>\n\n          <div v-if=\"hasActiveFilters\" class=\"text-sm text-gray-500 dark:text-gray-400\">\n            <span\n              v-if=\"searchQuery\"\n              class=\"inline-flex items-center px-2 py-1 rounded-full text-xs bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200 mr-2\"\n            >\n              🔍 \"{{ searchQuery }}\"\n            </span>\n            <span\n              v-if=\"selectedDepartment\"\n              class=\"inline-flex items-center px-2 py-1 rounded-full text-xs bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200 mr-2\"\n            >\n              🏢 {{ selectedDepartment.name }}\n            </span>\n            <span\n              v-if=\"selectedSkill\"\n              class=\"inline-flex items-center px-2 py-1 rounded-full text-xs bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200\"\n            >\n              🎯 {{ selectedSkill.name }}\n            </span>\n          </div>\n        </div>\n      </div>\n\n      <!-- Loading State -->\n      <div v-if=\"loading\" class=\"p-6\">\n        <div class=\"flex items-center justify-center\">\n          <div class=\"animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600\"></div>\n          <span class=\"ml-2 text-gray-600 dark:text-gray-400\">Caricamento...</span>\n        </div>\n      </div>\n\n      <!-- Error State -->\n      <div v-else-if=\"error\" class=\"p-6\">\n        <div class=\"bg-red-50 dark:bg-red-900 border border-red-200 dark:border-red-700 rounded-md p-4\">\n          <div class=\"flex\">\n            <div class=\"flex-shrink-0\">\n              <svg class=\"h-5 w-5 text-red-400\" viewBox=\"0 0 20 20\" fill=\"currentColor\">\n                <path fill-rule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z\" clip-rule=\"evenodd\" />\n              </svg>\n            </div>\n            <div class=\"ml-3\">\n              <h3 class=\"text-sm font-medium text-red-800 dark:text-red-200\">\n                Errore nel caricamento\n              </h3>\n              <div class=\"mt-2 text-sm text-red-700 dark:text-red-300\">\n                {{ error }}\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <!-- Users Grid -->\n      <div v-else-if=\"users.length > 0\" class=\"overflow-hidden\">\n        <div class=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 p-6\">\n          <div\n            v-for=\"user in users\"\n            :key=\"user.id\"\n            class=\"bg-gray-50 dark:bg-gray-700 rounded-lg p-6 hover:shadow-md transition-shadow duration-200\"\n          >\n            <!-- Avatar e Info Base -->\n            <div class=\"flex items-center mb-4\">\n              <div class=\"flex-shrink-0\">\n                <img\n                  v-if=\"user.profile_image\"\n                  :src=\"user.profile_image\"\n                  :alt=\"user.full_name\"\n                  class=\"h-12 w-12 rounded-full\"\n                >\n                <div\n                  v-else\n                  class=\"h-12 w-12 rounded-full bg-primary-500 dark:bg-primary-600 flex items-center justify-center text-white text-lg font-medium\"\n                >\n                  {{ getUserInitials(user) }}\n                </div>\n              </div>\n              <div class=\"ml-4 flex-1\">\n                <h4 class=\"text-lg font-medium text-gray-900 dark:text-white\">\n                  {{ user.full_name }}\n                </h4>\n                <p class=\"text-sm text-gray-500 dark:text-gray-400\">\n                  {{ user.position || 'Posizione non specificata' }}\n                </p>\n              </div>\n            </div>\n\n            <!-- Dettagli -->\n            <div class=\"space-y-2 mb-4\">\n              <div class=\"flex items-center text-sm\">\n                <span class=\"text-gray-500 dark:text-gray-400 w-20\">Email:</span>\n                <span class=\"text-gray-900 dark:text-white\">{{ user.email }}</span>\n              </div>\n              <div class=\"flex items-center text-sm\">\n                <span class=\"text-gray-500 dark:text-gray-400 w-20\">Ruolo:</span>\n                <span :class=\"getRoleClass(user.role)\">\n                  {{ getRoleLabel(user.role) }}\n                </span>\n              </div>\n              <div v-if=\"user.department\" class=\"flex items-center text-sm\">\n                <span class=\"text-gray-500 dark:text-gray-400 w-20\">Dipart.:</span>\n                <span class=\"text-gray-900 dark:text-white\">{{ user.department.name }}</span>\n              </div>\n            </div>\n\n            <!-- Competenze -->\n            <div v-if=\"user.skills && user.skills.length > 0\" class=\"mb-4\">\n              <p class=\"text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">Competenze:</p>\n              <div class=\"flex flex-wrap gap-1\">\n                <span\n                  v-for=\"skill in user.skills.slice(0, 3)\"\n                  :key=\"skill.id\"\n                  class=\"inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-primary-100 text-primary-800 dark:bg-primary-900 dark:text-primary-200\"\n                >\n                  {{ skill.name }}\n                </span>\n                <span\n                  v-if=\"user.skills.length > 3\"\n                  class=\"inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-gray-100 text-gray-600 dark:bg-gray-600 dark:text-gray-300\"\n                >\n                  +{{ user.skills.length - 3 }}\n                </span>\n              </div>\n            </div>\n\n            <!-- Azioni -->\n            <div class=\"flex space-x-2\">\n              <router-link\n                :to=\"`/app/personnel/${user.id}`\"\n                class=\"flex-1 text-center px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700\"\n              >\n                👤 Profilo\n              </router-link>\n              <a\n                v-if=\"user.phone\"\n                :href=\"`tel:${user.phone}`\"\n                class=\"px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700\"\n              >\n                📞\n              </a>\n              <a\n                :href=\"`mailto:${user.email}`\"\n                class=\"px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700\"\n              >\n                ✉️\n              </a>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <!-- Empty State -->\n      <div v-else class=\"text-center py-12\">\n        <div class=\"text-gray-400 dark:text-gray-500 text-6xl mb-4\">👥</div>\n        <h3 class=\"text-lg font-medium text-gray-900 dark:text-white mb-2\">Nessun dipendente trovato</h3>\n        <p class=\"text-gray-500 dark:text-gray-400\">\n          {{ hasActiveFilters ? 'Prova a modificare i filtri di ricerca' : 'Non ci sono dipendenti da visualizzare' }}\n        </p>\n      </div>\n\n      <!-- Paginazione -->\n      <div v-if=\"pagination.pages > 1\" class=\"px-6 py-4 border-t border-gray-200 dark:border-gray-700\">\n        <div class=\"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4\">\n          <!-- Info risultati -->\n          <div class=\"text-sm text-gray-700 dark:text-gray-300\">\n            Mostrando <span class=\"font-medium\">{{ (pagination.page - 1) * pagination.per_page + 1 }}</span> -\n            <span class=\"font-medium\">{{ Math.min(pagination.page * pagination.per_page, pagination.total) }}</span>\n            di <span class=\"font-medium\">{{ pagination.total }}</span> dipendenti\n          </div>\n\n          <!-- Controlli paginazione -->\n          <nav class=\"flex items-center space-x-1\">\n            <!-- Precedente -->\n            <button\n              v-if=\"pagination.page > 1\"\n              @click=\"changePage(pagination.page - 1)\"\n              class=\"px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors\"\n            >\n              ← Precedente\n            </button>\n            <span\n              v-else\n              class=\"px-3 py-2 border border-gray-200 dark:border-gray-700 rounded-md text-sm font-medium text-gray-400 dark:text-gray-600 bg-gray-50 dark:bg-gray-900 cursor-not-allowed\"\n            >\n              ← Precedente\n            </span>\n\n            <!-- Numeri pagina -->\n            <template v-for=\"page in getPageNumbers()\" :key=\"page\">\n              <button\n                v-if=\"page !== '...'\"\n                @click=\"changePage(page)\"\n                :class=\"[\n                  'px-3 py-2 border rounded-md text-sm font-medium transition-colors',\n                  page === pagination.page\n                    ? 'border-primary-500 dark:border-primary-400 text-white bg-primary-600 dark:bg-primary-500'\n                    : 'border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700'\n                ]\"\n              >\n                {{ page }}\n              </button>\n              <span v-else class=\"px-2 py-2 text-sm text-gray-500 dark:text-gray-400\">…</span>\n            </template>\n\n            <!-- Successiva -->\n            <button\n              v-if=\"pagination.page < pagination.pages\"\n              @click=\"changePage(pagination.page + 1)\"\n              class=\"px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors\"\n            >\n              Successiva →\n            </button>\n            <span\n              v-else\n              class=\"px-3 py-2 border border-gray-200 dark:border-gray-700 rounded-md text-sm font-medium text-gray-400 dark:text-gray-600 bg-gray-50 dark:bg-gray-900 cursor-not-allowed\"\n            >\n              Successiva →\n            </span>\n          </nav>\n        </div>\n      </div>\n    </div>\n  </div>\n</template>"}