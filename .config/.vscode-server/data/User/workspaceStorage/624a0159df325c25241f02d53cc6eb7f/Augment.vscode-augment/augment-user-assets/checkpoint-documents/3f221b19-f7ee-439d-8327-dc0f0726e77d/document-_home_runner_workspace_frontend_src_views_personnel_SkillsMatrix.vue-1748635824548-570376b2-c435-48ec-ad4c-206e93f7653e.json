{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/views/personnel/SkillsMatrix.vue"}, "modifiedCode": "<template>\n  <div>\n    <h1 class=\"text-2xl font-bold text-gray-900 dark:text-white mb-6\">🎯 <PERSON>rice Competenze</h1>\n    <div class=\"bg-white dark:bg-gray-800 shadow rounded-lg p-6\">\n      <p class=\"text-gray-600 dark:text-gray-400\">Matrice competenze in fase di migrazione...</p>\n      <div class=\"mt-4\">\n        <router-link \n          to=\"/app/personnel\" \n          class=\"text-primary-600 dark:text-primary-400 hover:underline\"\n        >\n          ← Torna al Team\n        </router-link>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script setup>\n// Placeholder component - will be implemented in Sprint 3\n</script>\n"}