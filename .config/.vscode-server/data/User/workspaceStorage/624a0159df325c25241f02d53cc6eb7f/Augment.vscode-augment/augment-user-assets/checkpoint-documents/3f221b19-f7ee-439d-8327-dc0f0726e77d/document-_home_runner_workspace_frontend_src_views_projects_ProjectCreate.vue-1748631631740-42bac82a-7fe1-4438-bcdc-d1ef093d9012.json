{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/views/projects/ProjectCreate.vue"}, "modifiedCode": "<template>\n  <div class=\"max-w-4xl mx-auto\">\n    <div class=\"bg-white shadow rounded-lg\">\n      <div class=\"px-6 py-4 border-b border-gray-200\">\n        <h1 class=\"text-xl font-semibold text-gray-900\">Nuovo Progetto</h1>\n        <p class=\"mt-1 text-sm text-gray-600\">Crea un nuovo progetto per la tua organizzazione</p>\n      </div>\n\n      <form @submit.prevent=\"saveProject\" class=\"p-6 space-y-6\">\n        <!-- Nome Progetto -->\n        <div>\n          <label for=\"name\" class=\"block text-sm font-medium text-gray-700\">Nome Progetto *</label>\n          <input\n            id=\"name\"\n            v-model=\"form.name\"\n            type=\"text\"\n            required\n            class=\"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500\"\n            placeholder=\"Inserisci il nome del progetto\"\n          />\n        </div>\n\n        <!-- Descrizione -->\n        <div>\n          <label for=\"description\" class=\"block text-sm font-medium text-gray-700\">Descrizione</label>\n          <textarea\n            id=\"description\"\n            v-model=\"form.description\"\n            rows=\"3\"\n            class=\"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500\"\n            placeholder=\"Descrivi il progetto...\"\n          ></textarea>\n        </div>\n\n        <!-- Cliente -->\n        <div>\n          <label for=\"client_id\" class=\"block text-sm font-medium text-gray-700\">Cliente</label>\n          <select\n            id=\"client_id\"\n            v-model=\"form.client_id\"\n            class=\"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500\"\n          >\n            <option value=\"\">Seleziona cliente</option>\n            <option v-for=\"client in clients\" :key=\"client.id\" :value=\"client.id\">\n              {{ client.name }}\n            </option>\n          </select>\n        </div>\n\n        <!-- Date -->\n        <div class=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n          <div>\n            <label for=\"start_date\" class=\"block text-sm font-medium text-gray-700\">Data Inizio</label>\n            <input\n              id=\"start_date\"\n              v-model=\"form.start_date\"\n              type=\"date\"\n              class=\"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500\"\n            />\n          </div>\n          <div>\n            <label for=\"end_date\" class=\"block text-sm font-medium text-gray-700\">Data Fine</label>\n            <input\n              id=\"end_date\"\n              v-model=\"form.end_date\"\n              type=\"date\"\n              class=\"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500\"\n            />\n          </div>\n        </div>\n\n        <!-- Tipo Progetto e Budget -->\n        <div class=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n          <div>\n            <label for=\"project_type\" class=\"block text-sm font-medium text-gray-700\">Tipo Progetto</label>\n            <select\n              id=\"project_type\"\n              v-model=\"form.project_type\"\n              class=\"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500\"\n            >\n              <option value=\"service\">🔧 Servizio</option>\n              <option value=\"license\">📄 Licenza</option>\n              <option value=\"consulting\">💼 Consulenza</option>\n              <option value=\"product\">📦 Prodotto</option>\n              <option value=\"rd\">🔬 R&D</option>\n              <option value=\"internal\">🏢 Interno</option>\n            </select>\n          </div>\n          <div>\n            <label for=\"budget\" class=\"block text-sm font-medium text-gray-700\">Budget (€)</label>\n            <input\n              id=\"budget\"\n              v-model=\"form.budget\"\n              type=\"number\"\n              step=\"0.01\"\n              min=\"0\"\n              class=\"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500\"\n              placeholder=\"0.00\"\n            />\n          </div>\n        </div>\n\n        <!-- Status e Fatturabile -->\n        <div class=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n          <div>\n            <label for=\"status\" class=\"block text-sm font-medium text-gray-700\">Stato</label>\n            <select\n              id=\"status\"\n              v-model=\"form.status\"\n              class=\"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500\"\n            >\n              <option value=\"planning\">📋 Pianificazione</option>\n              <option value=\"active\">🚀 Attivo</option>\n              <option value=\"on-hold\">⏸️ In Pausa</option>\n              <option value=\"completed\">✅ Completato</option>\n            </select>\n          </div>\n          <div class=\"flex items-center\">\n            <input\n              id=\"is_billable\"\n              v-model=\"form.is_billable\"\n              type=\"checkbox\"\n              class=\"h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded\"\n            />\n            <label for=\"is_billable\" class=\"ml-2 block text-sm text-gray-900\">\n              Progetto fatturabile\n            </label>\n          </div>\n        </div>\n\n        <!-- Azioni -->\n        <div class=\"flex justify-end space-x-3 pt-6 border-t border-gray-200\">\n          <button\n            type=\"button\"\n            @click=\"$router.go(-1)\"\n            class=\"px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500\"\n          >\n            Annulla\n          </button>\n          <button\n            type=\"submit\"\n            :disabled=\"saving\"\n            class=\"px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50\"\n          >\n            {{ saving ? 'Salvataggio...' : 'Crea Progetto' }}\n          </button>\n        </div>\n      </form>\n    </div>\n  </div>\n</template>\n\n<script setup>\nimport { ref, onMounted } from 'vue'\nimport { useRouter } from 'vue-router'\nimport { useAuthStore } from '@/stores/auth'\n\nconst router = useRouter()\nconst authStore = useAuthStore()\n\n// State\nconst saving = ref(false)\nconst clients = ref([])\nconst form = ref({\n  name: '',\n  description: '',\n  client_id: '',\n  start_date: '',\n  end_date: '',\n  project_type: 'service',\n  budget: '',\n  status: 'planning',\n  is_billable: true\n})\n\n// Methods\nconst loadClients = async () => {\n  try {\n    const response = await fetch('/api/clients', {\n      headers: {\n        'Content-Type': 'application/json',\n        'X-CSRFToken': authStore.csrfToken\n      }\n    })\n    \n    if (response.ok) {\n      const result = await response.json()\n      clients.value = result.data?.clients || []\n    }\n  } catch (error) {\n    console.error('Error loading clients:', error)\n  }\n}\n\nconst saveProject = async () => {\n  saving.value = true\n  \n  try {\n    const response = await fetch('/api/projects', {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json',\n        'X-CSRFToken': authStore.csrfToken\n      },\n      body: JSON.stringify(form.value)\n    })\n\n    if (!response.ok) {\n      throw new Error('Errore nella creazione del progetto')\n    }\n\n    const result = await response.json()\n    \n    // Redirect al progetto creato\n    router.push(`/app/projects/${result.data.project.id}`)\n  } catch (error) {\n    console.error('Error creating project:', error)\n    alert('Errore nella creazione del progetto')\n  } finally {\n    saving.value = false\n  }\n}\n\n// Lifecycle\nonMounted(() => {\n  loadClients()\n})\n</script>\n"}