{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/views/projects/ProjectEdit.vue"}, "modifiedCode": "<template>\n  <div class=\"max-w-4xl mx-auto\">\n    <div v-if=\"loading\" class=\"flex items-center justify-center h-64\">\n      <div class=\"animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600\"></div>\n    </div>\n\n    <div v-else-if=\"project\" class=\"bg-white shadow rounded-lg\">\n      <div class=\"px-6 py-4 border-b border-gray-200\">\n        <h1 class=\"text-xl font-semibold text-gray-900\">Modifica Progetto</h1>\n        <p class=\"mt-1 text-sm text-gray-600\">{{ project.name }}</p>\n      </div>\n\n      <form @submit.prevent=\"saveProject\" class=\"p-6 space-y-6\">\n        <!-- Nome Progetto -->\n        <div>\n          <label for=\"name\" class=\"block text-sm font-medium text-gray-700\">Nome Progetto *</label>\n          <input\n            id=\"name\"\n            v-model=\"form.name\"\n            type=\"text\"\n            required\n            class=\"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500\"\n          />\n        </div>\n\n        <!-- Descrizione -->\n        <div>\n          <label for=\"description\" class=\"block text-sm font-medium text-gray-700\">Descrizione</label>\n          <textarea\n            id=\"description\"\n            v-model=\"form.description\"\n            rows=\"3\"\n            class=\"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500\"\n          ></textarea>\n        </div>\n\n        <!-- Cliente -->\n        <div>\n          <label for=\"client_id\" class=\"block text-sm font-medium text-gray-700\">Cliente</label>\n          <select\n            id=\"client_id\"\n            v-model=\"form.client_id\"\n            class=\"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500\"\n          >\n            <option value=\"\">Seleziona cliente</option>\n            <option v-for=\"client in clients\" :key=\"client.id\" :value=\"client.id\">\n              {{ client.name }}\n            </option>\n          </select>\n        </div>\n\n        <!-- Date -->\n        <div class=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n          <div>\n            <label for=\"start_date\" class=\"block text-sm font-medium text-gray-700\">Data Inizio</label>\n            <input\n              id=\"start_date\"\n              v-model=\"form.start_date\"\n              type=\"date\"\n              class=\"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500\"\n            />\n          </div>\n          <div>\n            <label for=\"end_date\" class=\"block text-sm font-medium text-gray-700\">Data Fine</label>\n            <input\n              id=\"end_date\"\n              v-model=\"form.end_date\"\n              type=\"date\"\n              class=\"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500\"\n            />\n          </div>\n        </div>\n\n        <!-- Tipo Progetto e Budget -->\n        <div class=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n          <div>\n            <label for=\"project_type\" class=\"block text-sm font-medium text-gray-700\">Tipo Progetto</label>\n            <select\n              id=\"project_type\"\n              v-model=\"form.project_type\"\n              class=\"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500\"\n            >\n              <option value=\"service\">🔧 Servizio</option>\n              <option value=\"license\">📄 Licenza</option>\n              <option value=\"consulting\">💼 Consulenza</option>\n              <option value=\"product\">📦 Prodotto</option>\n              <option value=\"rd\">🔬 R&D</option>\n              <option value=\"internal\">🏢 Interno</option>\n            </select>\n          </div>\n          <div>\n            <label for=\"budget\" class=\"block text-sm font-medium text-gray-700\">Budget (€)</label>\n            <input\n              id=\"budget\"\n              v-model=\"form.budget\"\n              type=\"number\"\n              step=\"0.01\"\n              min=\"0\"\n              class=\"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500\"\n            />\n          </div>\n        </div>\n\n        <!-- Status e Fatturabile -->\n        <div class=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n          <div>\n            <label for=\"status\" class=\"block text-sm font-medium text-gray-700\">Stato</label>\n            <select\n              id=\"status\"\n              v-model=\"form.status\"\n              class=\"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500\"\n            >\n              <option value=\"planning\">📋 Pianificazione</option>\n              <option value=\"active\">🚀 Attivo</option>\n              <option value=\"on-hold\">⏸️ In Pausa</option>\n              <option value=\"completed\">✅ Completato</option>\n            </select>\n          </div>\n          <div class=\"flex items-center\">\n            <input\n              id=\"is_billable\"\n              v-model=\"form.is_billable\"\n              type=\"checkbox\"\n              class=\"h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded\"\n            />\n            <label for=\"is_billable\" class=\"ml-2 block text-sm text-gray-900\">\n              Progetto fatturabile\n            </label>\n          </div>\n        </div>\n\n        <!-- Azioni -->\n        <div class=\"flex justify-between pt-6 border-t border-gray-200\">\n          <button\n            type=\"button\"\n            @click=\"deleteProject\"\n            class=\"px-4 py-2 border border-red-300 rounded-md shadow-sm text-sm font-medium text-red-700 bg-white hover:bg-red-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500\"\n          >\n            Elimina Progetto\n          </button>\n          \n          <div class=\"flex space-x-3\">\n            <button\n              type=\"button\"\n              @click=\"$router.go(-1)\"\n              class=\"px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500\"\n            >\n              Annulla\n            </button>\n            <button\n              type=\"submit\"\n              :disabled=\"saving\"\n              class=\"px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50\"\n            >\n              {{ saving ? 'Salvataggio...' : 'Salva Modifiche' }}\n            </button>\n          </div>\n        </div>\n      </form>\n    </div>\n  </div>\n</template>\n\n<script setup>\nimport { ref, onMounted } from 'vue'\nimport { useRoute, useRouter } from 'vue-router'\nimport { useAuthStore } from '@/stores/auth'\n\nconst route = useRoute()\nconst router = useRouter()\nconst authStore = useAuthStore()\n\n// State\nconst loading = ref(true)\nconst saving = ref(false)\nconst project = ref(null)\nconst clients = ref([])\nconst form = ref({\n  name: '',\n  description: '',\n  client_id: '',\n  start_date: '',\n  end_date: '',\n  project_type: 'service',\n  budget: '',\n  status: 'planning',\n  is_billable: true\n})\n\n// Methods\nconst loadProject = async () => {\n  try {\n    const response = await fetch(`/api/projects/${route.params.id}`, {\n      headers: {\n        'Content-Type': 'application/json',\n        'X-CSRFToken': authStore.csrfToken\n      }\n    })\n    \n    if (!response.ok) {\n      throw new Error('Progetto non trovato')\n    }\n    \n    const result = await response.json()\n    project.value = result.data.project\n    \n    // Popola il form\n    form.value = {\n      name: project.value.name || '',\n      description: project.value.description || '',\n      client_id: project.value.client_id || '',\n      start_date: project.value.start_date || '',\n      end_date: project.value.end_date || '',\n      project_type: project.value.project_type || 'service',\n      budget: project.value.budget || '',\n      status: project.value.status || 'planning',\n      is_billable: project.value.is_billable !== false\n    }\n  } catch (error) {\n    console.error('Error loading project:', error)\n    router.push('/app/projects')\n  } finally {\n    loading.value = false\n  }\n}\n\nconst loadClients = async () => {\n  try {\n    const response = await fetch('/api/clients', {\n      headers: {\n        'Content-Type': 'application/json',\n        'X-CSRFToken': authStore.csrfToken\n      }\n    })\n    \n    if (response.ok) {\n      const result = await response.json()\n      clients.value = result.data?.clients || []\n    }\n  } catch (error) {\n    console.error('Error loading clients:', error)\n  }\n}\n\nconst saveProject = async () => {\n  saving.value = true\n  \n  try {\n    const response = await fetch(`/api/projects/${route.params.id}`, {\n      method: 'PUT',\n      headers: {\n        'Content-Type': 'application/json',\n        'X-CSRFToken': authStore.csrfToken\n      },\n      body: JSON.stringify(form.value)\n    })\n\n    if (!response.ok) {\n      throw new Error('Errore nel salvataggio del progetto')\n    }\n\n    // Redirect al progetto\n    router.push(`/app/projects/${route.params.id}`)\n  } catch (error) {\n    console.error('Error saving project:', error)\n    alert('Errore nel salvataggio del progetto')\n  } finally {\n    saving.value = false\n  }\n}\n\nconst deleteProject = async () => {\n  if (!confirm('Sei sicuro di voler eliminare questo progetto? Questa azione non può essere annullata.')) {\n    return\n  }\n  \n  try {\n    const response = await fetch(`/api/projects/${route.params.id}`, {\n      method: 'DELETE',\n      headers: {\n        'Content-Type': 'application/json',\n        'X-CSRFToken': authStore.csrfToken\n      }\n    })\n\n    if (!response.ok) {\n      throw new Error('Errore nell\\'eliminazione del progetto')\n    }\n\n    router.push('/app/projects')\n  } catch (error) {\n    console.error('Error deleting project:', error)\n    alert('Errore nell\\'eliminazione del progetto')\n  }\n}\n\n// Lifecycle\nonMounted(() => {\n  loadProject()\n  loadClients()\n})\n</script>\n"}