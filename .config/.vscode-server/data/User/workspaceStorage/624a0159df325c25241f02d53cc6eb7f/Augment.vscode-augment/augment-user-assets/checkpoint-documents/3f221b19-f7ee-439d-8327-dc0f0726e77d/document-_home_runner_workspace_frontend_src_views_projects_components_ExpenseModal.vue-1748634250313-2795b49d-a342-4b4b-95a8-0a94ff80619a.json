{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/views/projects/components/ExpenseModal.vue"}, "originalCode": "<template>\n  <div class=\"fixed inset-0 z-50 overflow-y-auto\">\n    <div class=\"flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0\">\n      <div class=\"fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity\" @click=\"$emit('close')\"></div>\n\n      <div class=\"inline-block align-bottom bg-white dark:bg-gray-800 rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full\">\n        <form @submit.prevent=\"saveExpense\">\n          <div class=\"bg-white dark:bg-gray-800 px-4 pt-5 pb-4 sm:p-6 sm:pb-4\">\n            <div class=\"mb-4\">\n              <h3 class=\"text-lg font-medium text-gray-900 dark:text-white\">\n                {{ expense ? 'Modifica Spesa' : 'Aggiungi Spesa' }}\n              </h3>\n            </div>\n\n            <div class=\"space-y-4\">\n              <!-- Description -->\n              <div>\n                <label for=\"description\" class=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                  Descrizione\n                </label>\n                <input\n                  v-model=\"form.description\"\n                  type=\"text\"\n                  id=\"description\"\n                  required\n                  class=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white\"\n                  placeholder=\"Descrizione della spesa\"\n                />\n              </div>\n\n              <!-- Amount -->\n              <div>\n                <label for=\"amount\" class=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                  Importo (€)\n                </label>\n                <input\n                  v-model.number=\"form.amount\"\n                  type=\"number\"\n                  step=\"0.01\"\n                  id=\"amount\"\n                  required\n                  class=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white\"\n                  placeholder=\"0.00\"\n                />\n              </div>\n\n              <!-- Category -->\n              <div>\n                <label for=\"category\" class=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                  Categoria\n                </label>\n                <select\n                  v-model=\"form.category\"\n                  id=\"category\"\n                  required\n                  class=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white\"\n                >\n                  <option value=\"\">Seleziona categoria</option>\n                  <option value=\"licenses\">📄 Licenze</option>\n                  <option value=\"travel\">✈️ Viaggi</option>\n                  <option value=\"meals\">🍽️ Pasti</option>\n                  <option value=\"equipment\">🖥️ Attrezzature</option>\n                  <option value=\"external\">🏢 Servizi Esterni</option>\n                  <option value=\"other\">📦 Altro</option>\n                </select>\n              </div>\n\n              <!-- Billing Type -->\n              <div>\n                <label for=\"billing_type\" class=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                  Tipo Fatturazione\n                </label>\n                <select\n                  v-model=\"form.billing_type\"\n                  id=\"billing_type\"\n                  required\n                  class=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white\"\n                >\n                  <option value=\"billable\">💰 Fatturabile al Cliente</option>\n                  <option value=\"non-billable\">🏢 Assorbimento Interno</option>\n                  <option value=\"reimbursable\">💳 Rimborsabile</option>\n                </select>\n              </div>\n\n              <!-- Status -->\n              <div>\n                <label for=\"status\" class=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                  Stato\n                </label>\n                <select\n                  v-model=\"form.status\"\n                  id=\"status\"\n                  required\n                  class=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white\"\n                >\n                  <option value=\"pending\">⏳ In Attesa di Approvazione</option>\n                  <option value=\"approved\">✅ Approvata</option>\n                  <option value=\"rejected\">❌ Rifiutata</option>\n                </select>\n              </div>\n\n              <!-- Date -->\n              <div>\n                <label for=\"date\" class=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                  Data\n                </label>\n                <input\n                  v-model=\"form.date\"\n                  type=\"date\"\n                  id=\"date\"\n                  required\n                  class=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white\"\n                />\n              </div>\n\n              <!-- Notes -->\n              <div>\n                <label for=\"notes\" class=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                  Note (opzionale)\n                </label>\n                <textarea\n                  v-model=\"form.notes\"\n                  id=\"notes\"\n                  rows=\"3\"\n                  class=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white\"\n                  placeholder=\"Note aggiuntive...\"\n                ></textarea>\n              </div>\n            </div>\n          </div>\n\n          <div class=\"bg-gray-50 dark:bg-gray-700 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse\">\n            <button\n              type=\"submit\"\n              :disabled=\"saving\"\n              class=\"w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-primary-600 text-base font-medium text-white hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 sm:ml-3 sm:w-auto sm:text-sm disabled:opacity-50\"\n            >\n              <span v-if=\"saving\">Salvando...</span>\n              <span v-else>{{ expense ? 'Aggiorna' : 'Salva' }}</span>\n            </button>\n            <button\n              type=\"button\"\n              @click=\"$emit('close')\"\n              class=\"mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 dark:border-gray-600 shadow-sm px-4 py-2 bg-white dark:bg-gray-800 text-base font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm\"\n            >\n              Annulla\n            </button>\n          </div>\n        </form>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script setup>\nimport { ref, reactive, onMounted } from 'vue'\n\nconst props = defineProps({\n  projectId: {\n    type: [String, Number],\n    required: true\n  },\n  expense: {\n    type: Object,\n    default: null\n  }\n})\n\nconst emit = defineEmits(['close', 'saved'])\n\n// State\nconst saving = ref(false)\nconst form = reactive({\n  description: '',\n  amount: 0,\n  category: '',\n  date: new Date().toISOString().split('T')[0],\n  notes: ''\n})\n\n// Methods\nconst saveExpense = async () => {\n  saving.value = true\n  try {\n    const url = props.expense\n      ? `/api/expenses/${props.expense.id}`\n      : `/api/projects/${props.projectId}/expenses`\n\n    const method = props.expense ? 'PUT' : 'POST'\n\n    const response = await fetch(url, {\n      method,\n      headers: {\n        'Content-Type': 'application/json'\n      },\n      body: JSON.stringify(form)\n    })\n\n    if (response.ok) {\n      emit('saved')\n    } else {\n      console.error('Error saving expense')\n    }\n  } catch (error) {\n    console.error('Error saving expense:', error)\n  } finally {\n    saving.value = false\n  }\n}\n\n// Lifecycle\nonMounted(() => {\n  if (props.expense) {\n    Object.assign(form, {\n      description: props.expense.description,\n      amount: props.expense.amount,\n      category: props.expense.category,\n      date: props.expense.date.split('T')[0],\n      notes: props.expense.notes || ''\n    })\n  }\n})\n</script>", "modifiedCode": "<template>\n  <div class=\"fixed inset-0 z-50 overflow-y-auto\">\n    <div class=\"flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0\">\n      <div class=\"fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity\" @click=\"$emit('close')\"></div>\n\n      <div class=\"inline-block align-bottom bg-white dark:bg-gray-800 rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full\">\n        <form @submit.prevent=\"saveExpense\">\n          <div class=\"bg-white dark:bg-gray-800 px-4 pt-5 pb-4 sm:p-6 sm:pb-4\">\n            <div class=\"mb-4\">\n              <h3 class=\"text-lg font-medium text-gray-900 dark:text-white\">\n                {{ expense ? 'Modifica Spesa' : 'Aggiungi Spesa' }}\n              </h3>\n            </div>\n\n            <div class=\"space-y-4\">\n              <!-- Description -->\n              <div>\n                <label for=\"description\" class=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                  Descrizione\n                </label>\n                <input\n                  v-model=\"form.description\"\n                  type=\"text\"\n                  id=\"description\"\n                  required\n                  class=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white\"\n                  placeholder=\"Descrizione della spesa\"\n                />\n              </div>\n\n              <!-- Amount -->\n              <div>\n                <label for=\"amount\" class=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                  Importo (€)\n                </label>\n                <input\n                  v-model.number=\"form.amount\"\n                  type=\"number\"\n                  step=\"0.01\"\n                  id=\"amount\"\n                  required\n                  class=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white\"\n                  placeholder=\"0.00\"\n                />\n              </div>\n\n              <!-- Category -->\n              <div>\n                <label for=\"category\" class=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                  Categoria\n                </label>\n                <select\n                  v-model=\"form.category\"\n                  id=\"category\"\n                  required\n                  class=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white\"\n                >\n                  <option value=\"\">Seleziona categoria</option>\n                  <option value=\"licenses\">📄 Licenze</option>\n                  <option value=\"travel\">✈️ Viaggi</option>\n                  <option value=\"meals\">🍽️ Pasti</option>\n                  <option value=\"equipment\">🖥️ Attrezzature</option>\n                  <option value=\"external\">🏢 Servizi Esterni</option>\n                  <option value=\"other\">📦 Altro</option>\n                </select>\n              </div>\n\n              <!-- Billing Type -->\n              <div>\n                <label for=\"billing_type\" class=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                  Tipo Fatturazione\n                </label>\n                <select\n                  v-model=\"form.billing_type\"\n                  id=\"billing_type\"\n                  required\n                  class=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white\"\n                >\n                  <option value=\"billable\">💰 Fatturabile al Cliente</option>\n                  <option value=\"non-billable\">🏢 Assorbimento Interno</option>\n                  <option value=\"reimbursable\">💳 Rimborsabile</option>\n                </select>\n              </div>\n\n              <!-- Status -->\n              <div>\n                <label for=\"status\" class=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                  Stato\n                </label>\n                <select\n                  v-model=\"form.status\"\n                  id=\"status\"\n                  required\n                  class=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white\"\n                >\n                  <option value=\"pending\">⏳ In Attesa di Approvazione</option>\n                  <option value=\"approved\">✅ Approvata</option>\n                  <option value=\"rejected\">❌ Rifiutata</option>\n                </select>\n              </div>\n\n              <!-- Date -->\n              <div>\n                <label for=\"date\" class=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                  Data\n                </label>\n                <input\n                  v-model=\"form.date\"\n                  type=\"date\"\n                  id=\"date\"\n                  required\n                  class=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white\"\n                />\n              </div>\n\n              <!-- Notes -->\n              <div>\n                <label for=\"notes\" class=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                  Note (opzionale)\n                </label>\n                <textarea\n                  v-model=\"form.notes\"\n                  id=\"notes\"\n                  rows=\"3\"\n                  class=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white\"\n                  placeholder=\"Note aggiuntive...\"\n                ></textarea>\n              </div>\n\n              <!-- Receipt Upload -->\n              <div>\n                <label for=\"receipt\" class=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                  Ricevuta/Scontrino\n                </label>\n                <input\n                  type=\"file\"\n                  id=\"receipt\"\n                  accept=\"image/*,.pdf\"\n                  @change=\"handleFileUpload\"\n                  class=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-primary-50 file:text-primary-700 hover:file:bg-primary-100\"\n                />\n                <p class=\"mt-1 text-xs text-gray-500 dark:text-gray-400\">\n                  Carica immagine o PDF della ricevuta (max 5MB)\n                </p>\n              </div>\n            </div>\n          </div>\n\n          <div class=\"bg-gray-50 dark:bg-gray-700 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse\">\n            <button\n              type=\"submit\"\n              :disabled=\"saving\"\n              class=\"w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-primary-600 text-base font-medium text-white hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 sm:ml-3 sm:w-auto sm:text-sm disabled:opacity-50\"\n            >\n              <span v-if=\"saving\">Salvando...</span>\n              <span v-else>{{ expense ? 'Aggiorna' : 'Salva' }}</span>\n            </button>\n            <button\n              type=\"button\"\n              @click=\"$emit('close')\"\n              class=\"mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 dark:border-gray-600 shadow-sm px-4 py-2 bg-white dark:bg-gray-800 text-base font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm\"\n            >\n              Annulla\n            </button>\n          </div>\n        </form>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script setup>\nimport { ref, reactive, onMounted } from 'vue'\n\nconst props = defineProps({\n  projectId: {\n    type: [String, Number],\n    required: true\n  },\n  expense: {\n    type: Object,\n    default: null\n  }\n})\n\nconst emit = defineEmits(['close', 'saved'])\n\n// State\nconst saving = ref(false)\nconst form = reactive({\n  description: '',\n  amount: 0,\n  category: '',\n  date: new Date().toISOString().split('T')[0],\n  notes: ''\n})\n\n// Methods\nconst saveExpense = async () => {\n  saving.value = true\n  try {\n    const url = props.expense\n      ? `/api/expenses/${props.expense.id}`\n      : `/api/projects/${props.projectId}/expenses`\n\n    const method = props.expense ? 'PUT' : 'POST'\n\n    const response = await fetch(url, {\n      method,\n      headers: {\n        'Content-Type': 'application/json'\n      },\n      body: JSON.stringify(form)\n    })\n\n    if (response.ok) {\n      emit('saved')\n    } else {\n      console.error('Error saving expense')\n    }\n  } catch (error) {\n    console.error('Error saving expense:', error)\n  } finally {\n    saving.value = false\n  }\n}\n\n// Lifecycle\nonMounted(() => {\n  if (props.expense) {\n    Object.assign(form, {\n      description: props.expense.description,\n      amount: props.expense.amount,\n      category: props.expense.category,\n      date: props.expense.date.split('T')[0],\n      notes: props.expense.notes || ''\n    })\n  }\n})\n</script>"}