{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/views/projects/components/ProjectExpenses.vue"}, "originalCode": "<template>\n  <div class=\"project-expenses\">\n    <div class=\"space-y-6\">\n      <!-- Header with Add Button -->\n      <div class=\"flex justify-between items-center\">\n        <h3 class=\"text-lg font-medium text-gray-900 dark:text-white\">Spese Progetto</h3>\n        <button\n          v-if=\"canManageExpenses\"\n          @click=\"showAddExpenseModal = true\"\n          class=\"inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500\"\n        >\n          <PlusIcon class=\"w-4 h-4 mr-2\" />\n          Aggiungi Spesa\n        </button>\n      </div>\n\n      <!-- Expenses List -->\n      <div v-if=\"loading\" class=\"text-center py-8\">\n        <div class=\"inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600\"></div>\n        <p class=\"mt-2 text-sm text-gray-500\">Caricamento spese...</p>\n      </div>\n\n      <div v-else-if=\"expenses.length === 0\" class=\"text-center py-12\">\n        <CreditCardIcon class=\"mx-auto h-12 w-12 text-gray-400\" />\n        <h3 class=\"mt-2 text-sm font-medium text-gray-900 dark:text-white\">Nessuna spesa</h3>\n        <p class=\"mt-1 text-sm text-gray-500\">Non ci sono ancora spese registrate per questo progetto.</p>\n      </div>\n\n      <div v-else class=\"bg-white dark:bg-gray-800 shadow overflow-hidden sm:rounded-md\">\n        <ul class=\"divide-y divide-gray-200 dark:divide-gray-700\">\n          <li v-for=\"expense in expenses\" :key=\"expense.id\" class=\"px-6 py-4\">\n            <div class=\"flex items-center justify-between\">\n              <div class=\"flex-1\">\n                <div class=\"flex items-center\">\n                  <div class=\"flex-shrink-0\">\n                    <div class=\"h-10 w-10 rounded-full bg-gray-300 dark:bg-gray-600 flex items-center justify-center\">\n                      <CreditCardIcon class=\"h-5 w-5 text-gray-600 dark:text-gray-300\" />\n                    </div>\n                  </div>\n                  <div class=\"ml-4 flex-1\">\n                    <div class=\"flex items-center justify-between\">\n                      <p class=\"text-sm font-medium text-gray-900 dark:text-white\">\n                        {{ expense.description }}\n                      </p>\n                      <div class=\"ml-2 flex-shrink-0\">\n                        <p class=\"text-sm font-medium text-gray-900 dark:text-white\">\n                          €{{ formatCurrency(expense.amount) }}\n                        </p>\n                      </div>\n                    </div>\n                    <div class=\"mt-1 flex items-center text-sm text-gray-500 dark:text-gray-400\">\n                      <CalendarIcon class=\"flex-shrink-0 mr-1.5 h-4 w-4\" />\n                      {{ formatDate(expense.date) }}\n                      <span class=\"mx-2\">•</span>\n                      <span class=\"capitalize\">{{ expense.category }}</span>\n                      <span v-if=\"expense.user\" class=\"mx-2\">•</span>\n                      <span v-if=\"expense.user\">{{ expense.user.name }}</span>\n                    </div>\n                  </div>\n                </div>\n              </div>\n              <div v-if=\"canManageExpenses\" class=\"flex items-center space-x-2\">\n                <button\n                  @click=\"editExpense(expense)\"\n                  class=\"text-primary-600 hover:text-primary-900 text-sm font-medium\"\n                >\n                  Modifica\n                </button>\n                <button\n                  @click=\"deleteExpense(expense.id)\"\n                  class=\"text-red-600 hover:text-red-900 text-sm font-medium\"\n                >\n                  Elimina\n                </button>\n              </div>\n            </div>\n          </li>\n        </ul>\n      </div>\n\n      <!-- Summary Card -->\n      <div v-if=\"expenses.length > 0\" class=\"bg-gray-50 dark:bg-gray-700 rounded-lg p-4\">\n        <div class=\"flex justify-between items-center\">\n          <span class=\"text-sm font-medium text-gray-900 dark:text-white\">Totale Spese:</span>\n          <span class=\"text-lg font-bold text-gray-900 dark:text-white\">€{{ formatCurrency(totalExpenses) }}</span>\n        </div>\n      </div>\n    </div>\n\n    <!-- Add/Edit Expense Modal -->\n    <ExpenseModal\n      v-if=\"showAddExpenseModal\"\n      :project-id=\"project?.id\"\n      :expense=\"editingExpense\"\n      @close=\"closeExpenseModal\"\n      @saved=\"onExpenseSaved\"\n    />\n  </div>\n</template>\n\n<script setup>\nimport { ref, computed, onMounted, watch } from 'vue'\nimport { useProjectsStore } from '../../../stores/projects'\nimport { useAuthStore } from '../../../stores/auth'\nimport { PlusIcon, CreditCardIcon, CalendarIcon } from '@heroicons/vue/24/outline'\nimport ExpenseModal from './ExpenseModal.vue'\n\nconst props = defineProps({\n  project: {\n    type: Object,\n    required: true\n  },\n  loading: {\n    type: Boolean,\n    default: false\n  }\n})\n\nconst projectsStore = useProjectsStore()\nconst authStore = useAuthStore()\n\n// State\nconst loading = ref(false)\nconst expenses = ref([])\nconst showAddExpenseModal = ref(false)\nconst editingExpense = ref(null)\n\n// Computed\nconst canManageExpenses = computed(() => {\n  return authStore.hasPermission('manage_expenses')\n})\n\nconst totalExpenses = computed(() => {\n  return expenses.value.reduce((total, expense) => total + expense.amount, 0)\n})\n\n// Methods\nconst loadExpenses = async () => {\n  if (!props.project?.id) return\n\n  loading.value = true\n  try {\n    const response = await fetch(`/api/projects/${props.project.id}/expenses`)\n    if (response.ok) {\n      expenses.value = await response.json()\n    }\n  } catch (error) {\n    console.error('Error loading expenses:', error)\n  } finally {\n    loading.value = false\n  }\n}\n\nconst editExpense = (expense) => {\n  editingExpense.value = expense\n  showAddExpenseModal.value = true\n}\n\nconst deleteExpense = async (expenseId) => {\n  if (!confirm('Sei sicuro di voler eliminare questa spesa?')) {\n    return\n  }\n\n  try {\n    const response = await fetch(`/api/expenses/${expenseId}`, {\n      method: 'DELETE'\n    })\n    if (response.ok) {\n      expenses.value = expenses.value.filter(e => e.id !== expenseId)\n    }\n  } catch (error) {\n    console.error('Error deleting expense:', error)\n  }\n}\n\nconst closeExpenseModal = () => {\n  showAddExpenseModal.value = false\n  editingExpense.value = null\n}\n\nconst onExpenseSaved = () => {\n  closeExpenseModal()\n  loadExpenses()\n}\n\nconst formatCurrency = (amount) => {\n  return new Intl.NumberFormat('it-IT', {\n    minimumFractionDigits: 2,\n    maximumFractionDigits: 2\n  }).format(amount)\n}\n\nconst formatDate = (dateString) => {\n  return new Date(dateString).toLocaleDateString('it-IT')\n}\n\n// Watchers\nwatch(() => props.project?.id, (newId, oldId) => {\n  if (newId && newId !== oldId) {\n    loadExpenses()\n  }\n}, { immediate: true })\n\n// Lifecycle\nonMounted(() => {\n  loadExpenses()\n})\n</script>", "modifiedCode": "<template>\n  <div class=\"project-expenses\">\n    <div class=\"space-y-6\">\n      <!-- Header with Add Button -->\n      <div class=\"flex justify-between items-center\">\n        <h3 class=\"text-lg font-medium text-gray-900 dark:text-white\">Spese Progetto</h3>\n        <button\n          v-if=\"canManageExpenses\"\n          @click=\"showAddExpenseModal = true\"\n          class=\"inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500\"\n        >\n          <PlusIcon class=\"w-4 h-4 mr-2\" />\n          Aggiungi Spesa\n        </button>\n      </div>\n\n      <!-- Expenses List -->\n      <div v-if=\"loading\" class=\"text-center py-8\">\n        <div class=\"inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600\"></div>\n        <p class=\"mt-2 text-sm text-gray-500\">Caricamento spese...</p>\n      </div>\n\n      <div v-else-if=\"expenses.length === 0\" class=\"text-center py-12\">\n        <CreditCardIcon class=\"mx-auto h-12 w-12 text-gray-400\" />\n        <h3 class=\"mt-2 text-sm font-medium text-gray-900 dark:text-white\">Nessuna spesa</h3>\n        <p class=\"mt-1 text-sm text-gray-500\">Non ci sono ancora spese registrate per questo progetto.</p>\n      </div>\n\n      <div v-else class=\"bg-white dark:bg-gray-800 shadow overflow-hidden sm:rounded-md\">\n        <ul class=\"divide-y divide-gray-200 dark:divide-gray-700\">\n          <li v-for=\"expense in expenses\" :key=\"expense.id\" class=\"px-6 py-4\">\n            <div class=\"flex items-center justify-between\">\n              <div class=\"flex-1\">\n                <div class=\"flex items-center\">\n                  <div class=\"flex-shrink-0\">\n                    <div class=\"h-10 w-10 rounded-full bg-gray-300 dark:bg-gray-600 flex items-center justify-center\">\n                      <CreditCardIcon class=\"h-5 w-5 text-gray-600 dark:text-gray-300\" />\n                    </div>\n                  </div>\n                  <div class=\"ml-4 flex-1\">\n                    <div class=\"flex items-center justify-between\">\n                      <p class=\"text-sm font-medium text-gray-900 dark:text-white\">\n                        {{ expense.description }}\n                      </p>\n                      <div class=\"ml-2 flex-shrink-0\">\n                        <p class=\"text-sm font-medium text-gray-900 dark:text-white\">\n                          €{{ formatCurrency(expense.amount) }}\n                        </p>\n                      </div>\n                    </div>\n                    <div class=\"mt-1 flex items-center text-sm text-gray-500 dark:text-gray-400\">\n                      <CalendarIcon class=\"flex-shrink-0 mr-1.5 h-4 w-4\" />\n                      {{ formatDate(expense.date) }}\n                      <span class=\"mx-2\">•</span>\n                      <span class=\"capitalize\">{{ getCategoryLabel(expense.category) }}</span>\n                      <span v-if=\"expense.user\" class=\"mx-2\">•</span>\n                      <span v-if=\"expense.user\">{{ expense.user.name }}</span>\n                    </div>\n                    <div class=\"mt-2 flex items-center space-x-4 text-xs\">\n                      <!-- Billing Type -->\n                      <span :class=\"getBillingTypeClass(expense.billing_type)\" class=\"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium\">\n                        {{ getBillingTypeLabel(expense.billing_type) }}\n                      </span>\n                      <!-- Status -->\n                      <span :class=\"getStatusClass(expense.status)\" class=\"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium\">\n                        {{ getStatusLabel(expense.status) }}\n                      </span>\n                      <!-- Receipt -->\n                      <span v-if=\"expense.receipt_path\" class=\"inline-flex items-center text-green-600 dark:text-green-400\">\n                        <svg class=\"w-3 h-3 mr-1\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                          <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\" />\n                        </svg>\n                        Ricevuta\n                      </span>\n                    </div>\n                  </div>\n                </div>\n              </div>\n              <div v-if=\"canManageExpenses\" class=\"flex items-center space-x-2\">\n                <button\n                  @click=\"editExpense(expense)\"\n                  class=\"text-primary-600 hover:text-primary-900 text-sm font-medium\"\n                >\n                  Modifica\n                </button>\n                <button\n                  @click=\"deleteExpense(expense.id)\"\n                  class=\"text-red-600 hover:text-red-900 text-sm font-medium\"\n                >\n                  Elimina\n                </button>\n              </div>\n            </div>\n          </li>\n        </ul>\n      </div>\n\n      <!-- Summary Card -->\n      <div v-if=\"expenses.length > 0\" class=\"bg-gray-50 dark:bg-gray-700 rounded-lg p-4\">\n        <div class=\"flex justify-between items-center\">\n          <span class=\"text-sm font-medium text-gray-900 dark:text-white\">Totale Spese:</span>\n          <span class=\"text-lg font-bold text-gray-900 dark:text-white\">€{{ formatCurrency(totalExpenses) }}</span>\n        </div>\n      </div>\n    </div>\n\n    <!-- Add/Edit Expense Modal -->\n    <ExpenseModal\n      v-if=\"showAddExpenseModal\"\n      :project-id=\"project?.id\"\n      :expense=\"editingExpense\"\n      @close=\"closeExpenseModal\"\n      @saved=\"onExpenseSaved\"\n    />\n  </div>\n</template>\n\n<script setup>\nimport { ref, computed, onMounted, watch } from 'vue'\nimport { useProjectsStore } from '../../../stores/projects'\nimport { useAuthStore } from '../../../stores/auth'\nimport { PlusIcon, CreditCardIcon, CalendarIcon } from '@heroicons/vue/24/outline'\nimport ExpenseModal from './ExpenseModal.vue'\n\nconst props = defineProps({\n  project: {\n    type: Object,\n    required: true\n  },\n  loading: {\n    type: Boolean,\n    default: false\n  }\n})\n\nconst projectsStore = useProjectsStore()\nconst authStore = useAuthStore()\n\n// State\nconst loading = ref(false)\nconst expenses = ref([])\nconst showAddExpenseModal = ref(false)\nconst editingExpense = ref(null)\n\n// Computed\nconst canManageExpenses = computed(() => {\n  return authStore.hasPermission('manage_expenses')\n})\n\nconst totalExpenses = computed(() => {\n  return expenses.value.reduce((total, expense) => total + expense.amount, 0)\n})\n\n// Methods\nconst loadExpenses = async () => {\n  if (!props.project?.id) return\n\n  loading.value = true\n  try {\n    const response = await fetch(`/api/projects/${props.project.id}/expenses`)\n    if (response.ok) {\n      expenses.value = await response.json()\n    }\n  } catch (error) {\n    console.error('Error loading expenses:', error)\n  } finally {\n    loading.value = false\n  }\n}\n\nconst editExpense = (expense) => {\n  editingExpense.value = expense\n  showAddExpenseModal.value = true\n}\n\nconst deleteExpense = async (expenseId) => {\n  if (!confirm('Sei sicuro di voler eliminare questa spesa?')) {\n    return\n  }\n\n  try {\n    const response = await fetch(`/api/expenses/${expenseId}`, {\n      method: 'DELETE'\n    })\n    if (response.ok) {\n      expenses.value = expenses.value.filter(e => e.id !== expenseId)\n    }\n  } catch (error) {\n    console.error('Error deleting expense:', error)\n  }\n}\n\nconst closeExpenseModal = () => {\n  showAddExpenseModal.value = false\n  editingExpense.value = null\n}\n\nconst onExpenseSaved = () => {\n  closeExpenseModal()\n  loadExpenses()\n}\n\nconst formatCurrency = (amount) => {\n  return new Intl.NumberFormat('it-IT', {\n    minimumFractionDigits: 2,\n    maximumFractionDigits: 2\n  }).format(amount)\n}\n\nconst formatDate = (dateString) => {\n  return new Date(dateString).toLocaleDateString('it-IT')\n}\n\n// Watchers\nwatch(() => props.project?.id, (newId, oldId) => {\n  if (newId && newId !== oldId) {\n    loadExpenses()\n  }\n}, { immediate: true })\n\n// Lifecycle\nonMounted(() => {\n  loadExpenses()\n})\n</script>"}