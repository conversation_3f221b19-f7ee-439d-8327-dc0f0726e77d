{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/views/projects/components/ProjectGantt.vue"}, "originalCode": "<template>\n  <div class=\"space-y-6\">\n    <div class=\"bg-white dark:bg-gray-800 shadow rounded-lg\">\n      <div class=\"px-6 py-4 border-b border-gray-200 dark:border-gray-700\">\n        <div class=\"flex items-center justify-between\">\n          <h3 class=\"text-lg font-medium text-gray-900 dark:text-white\">\n            Diagramma di Gantt\n          </h3>\n          <div class=\"flex items-center space-x-4\">\n            <div class=\"flex items-center space-x-2\">\n              <span class=\"text-sm text-gray-500 dark:text-gray-400\">Vista:</span>\n              <select\n                v-model=\"timeScale\"\n                @change=\"calculateTimeline\"\n                class=\"text-sm border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md shadow-sm focus:border-primary-500 focus:ring-primary-500\"\n              >\n                <option value=\"weeks\">Settimane</option>\n                <option value=\"months\">Mesi</option>\n              </select>\n            </div>\n            <button\n              @click=\"resetToToday\"\n              class=\"px-3 py-1 text-sm bg-primary-100 text-primary-700 rounded-md hover:bg-primary-200\"\n            >\n              Oggi\n            </button>\n          </div>\n        </div>\n      </div>\n\n      <!-- Gantt Chart -->\n      <div v-if=\"!loading && tasksWithDates.length > 0\" class=\"p-6\">\n        <div class=\"overflow-x-auto\">\n          <div class=\"min-w-[1000px]\">\n            <!-- Header with dates -->\n            <div class=\"flex mb-4\">\n              <div class=\"w-80 flex-shrink-0 px-4 py-2 text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider\">\n                Task\n              </div>\n              <div class=\"flex-1 flex\">\n                <div\n                  v-for=\"(period, index) in timelinePeriods\"\n                  :key=\"index\"\n                  class=\"flex-1 text-xs text-center text-gray-500 dark:text-gray-400 py-2 border-l border-gray-200 dark:border-gray-600\"\n                  :class=\"{ 'bg-blue-50 dark:bg-blue-900': isCurrentPeriod(period) }\"\n                >\n                  {{ formatPeriodLabel(period) }}\n                </div>\n              </div>\n            </div>\n\n            <!-- Task rows -->\n            <div class=\"space-y-1\">\n              <div\n                v-for=\"task in tasksWithDates\"\n                :key=\"task.id\"\n                class=\"flex items-center hover:bg-gray-50 dark:hover:bg-gray-700 rounded\"\n              >\n                <!-- Task info -->\n                <div class=\"w-80 flex-shrink-0 px-4 py-3\">\n                  <div class=\"flex items-center space-x-2\">\n                    <div class=\"w-3 h-3 rounded-full\" :class=\"getStatusColor(task.status)\"></div>\n                    <div class=\"flex-1 min-w-0\">\n                      <p class=\"text-sm font-medium text-gray-900 dark:text-white truncate\">{{ task.name }}</p>\n                      <div class=\"flex items-center space-x-2 text-xs text-gray-500 dark:text-gray-400\">\n                        <span v-if=\"task.assignee\">{{ task.assignee.full_name }}</span>\n                        <span class=\"inline-flex items-center px-1.5 py-0.5 rounded-full text-xs font-medium\" :class=\"getPriorityClass(task.priority)\">\n                          {{ getPriorityLabel(task.priority) }}\n                        </span>\n                      </div>\n                      <div class=\"text-xs text-gray-500 dark:text-gray-400 mt-1\">\n                        {{ formatDate(task.start_date) }} - {{ formatDate(task.due_date) }}\n                      </div>\n                    </div>\n                  </div>\n                </div>\n\n                <!-- Gantt timeline -->\n                <div class=\"flex-1 relative h-12 flex items-center\">\n                  <div\n                    v-if=\"task.timeline\"\n                    class=\"absolute h-6 rounded-md flex items-center justify-between px-2 text-xs text-white font-medium shadow-sm cursor-pointer\"\n                    :class=\"getTaskBarColor(task.status)\"\n                    :style=\"{\n                      left: task.timeline.leftPercent + '%',\n                      width: task.timeline.widthPercent + '%',\n                      minWidth: '60px'\n                    }\"\n                    :title=\"`${task.name} - ${getTaskProgress(task)}% completato`\"\n                  >\n                    <span class=\"truncate\">{{ task.name.length > 15 ? task.name.substring(0, 15) + '...' : task.name }}</span>\n                    <span class=\"ml-2\">{{ getTaskProgress(task) }}%</span>\n                  </div>\n\n                  <!-- Progress overlay -->\n                  <div\n                    v-if=\"task.timeline && getTaskProgress(task) > 0 && getTaskProgress(task) < 100\"\n                    class=\"absolute h-6 rounded-md bg-green-600 opacity-80\"\n                    :style=\"{\n                      left: task.timeline.leftPercent + '%',\n                      width: (task.timeline.widthPercent * getTaskProgress(task) / 100) + '%',\n                      minWidth: '2px'\n                    }\"\n                  ></div>\n\n                  <!-- Period dividers -->\n                  <div\n                    v-for=\"(period, index) in timelinePeriods\"\n                    :key=\"index\"\n                    class=\"absolute top-0 bottom-0 border-l border-gray-200 dark:border-gray-600\"\n                    :style=\"{ left: (index / timelinePeriods.length * 100) + '%' }\"\n                  ></div>\n\n                  <!-- Today indicator -->\n                  <div\n                    v-if=\"todayPosition >= 0 && todayPosition <= 100\"\n                    class=\"absolute top-0 bottom-0 w-0.5 bg-red-500 z-10\"\n                    :style=\"{ left: todayPosition + '%' }\"\n                  ></div>\n                </div>\n              </div>\n            </div>\n\n            <!-- Today line with label -->\n            <div\n              v-if=\"todayPosition >= 0 && todayPosition <= 100\"\n              class=\"absolute top-16 bottom-4 w-0.5 bg-red-500 z-20 pointer-events-none\"\n              :style=\"{ left: `calc(320px + ${todayPosition}% * (100% - 320px) / 100)` }\"\n            >\n              <div class=\"absolute -top-6 -left-8 bg-red-500 text-white text-xs px-2 py-1 rounded\">\n                Oggi\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <!-- Legend -->\n        <div class=\"mt-6 flex items-center space-x-6 text-xs\">\n          <div class=\"flex items-center space-x-2\">\n            <div class=\"w-3 h-3 bg-gray-400 rounded\"></div>\n            <span class=\"text-gray-600 dark:text-gray-400\">Da fare</span>\n          </div>\n          <div class=\"flex items-center space-x-2\">\n            <div class=\"w-3 h-3 bg-blue-500 rounded\"></div>\n            <span class=\"text-gray-600 dark:text-gray-400\">In corso</span>\n          </div>\n          <div class=\"flex items-center space-x-2\">\n            <div class=\"w-3 h-3 bg-yellow-500 rounded\"></div>\n            <span class=\"text-gray-600 dark:text-gray-400\">In revisione</span>\n          </div>\n          <div class=\"flex items-center space-x-2\">\n            <div class=\"w-3 h-3 bg-green-500 rounded\"></div>\n            <span class=\"text-gray-600 dark:text-gray-400\">Completato</span>\n          </div>\n          <div class=\"flex items-center space-x-2\">\n            <div class=\"w-0.5 h-4 bg-red-500\"></div>\n            <span class=\"text-gray-600 dark:text-gray-400\">Oggi</span>\n          </div>\n        </div>\n      </div>\n\n      <!-- Empty state -->\n      <div v-else-if=\"!loading\" class=\"text-center py-12\">\n        <svg class=\"mx-auto h-12 w-12 text-gray-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z\"></path>\n        </svg>\n        <h3 class=\"mt-2 text-sm font-medium text-gray-900 dark:text-white\">Nessun task pianificato</h3>\n        <p class=\"mt-1 text-sm text-gray-500 dark:text-gray-400\">I task con date di inizio e fine appariranno nel diagramma di Gantt.</p>\n      </div>\n\n      <!-- Loading -->\n      <div v-if=\"loading\" class=\"flex justify-center py-12\">\n        <div class=\"animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600\"></div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script setup>\nimport { ref, computed, onMounted, watch } from 'vue'\n\nconst props = defineProps({\n  project: {\n    type: Object,\n    default: null\n  },\n  loading: {\n    type: Boolean,\n    default: false\n  }\n})\n\n// State\nconst timeScale = ref('weeks')\nconst viewStartDate = ref(new Date())\nconst timelinePeriods = ref([])\nconst todayPosition = ref(0)\n\n// Computed\nconst tasks = computed(() => {\n  return props.project?.tasks || []\n})\n\nconst tasksWithDates = computed(() => {\n  return tasks.value.filter(task => task.start_date && task.due_date).map(task => {\n    const timeline = calculateTaskTimeline(task)\n    return { ...task, timeline }\n  })\n})\n\n// Methods\nconst calculateTimeline = () => {\n  const now = new Date()\n  const startDate = new Date(viewStartDate.value)\n\n  // Calculate periods based on time scale\n  const periods = []\n  const periodCount = 12\n\n  for (let i = 0; i < periodCount; i++) {\n    const periodStart = new Date(startDate)\n\n    if (timeScale.value === 'weeks') {\n      periodStart.setDate(startDate.getDate() + (i * 7))\n    } else if (timeScale.value === 'months') {\n      periodStart.setMonth(startDate.getMonth() + i)\n    }\n\n    periods.push(periodStart)\n  }\n\n  timelinePeriods.value = periods\n\n  // Calculate today position\n  const totalDuration = periods[periods.length - 1] - periods[0]\n  const todayOffset = now - periods[0]\n  todayPosition.value = Math.max(0, Math.min(100, (todayOffset / totalDuration) * 100))\n}\n\nconst calculateTaskTimeline = (task) => {\n  if (!timelinePeriods.value.length) return null\n\n  const taskStart = new Date(task.start_date)\n  const taskEnd = new Date(task.due_date)\n  const timelineStart = timelinePeriods.value[0]\n  const timelineEnd = timelinePeriods.value[timelinePeriods.value.length - 1]\n\n  const totalDuration = timelineEnd - timelineStart\n  const taskStartOffset = taskStart - timelineStart\n  const taskDuration = taskEnd - taskStart\n\n  const leftPercent = Math.max(0, (taskStartOffset / totalDuration) * 100)\n  const widthPercent = Math.min(100 - leftPercent, (taskDuration / totalDuration) * 100)\n\n  return {\n    leftPercent: leftPercent,\n    widthPercent: Math.max(5, widthPercent) // Minimum width for visibility\n  }\n}\n\nconst formatPeriodLabel = (period) => {\n  if (timeScale.value === 'weeks') {\n    return `${period.getDate()}/${period.getMonth() + 1}`\n  } else if (timeScale.value === 'months') {\n    return period.toLocaleDateString('it-IT', { month: 'short', year: '2-digit' })\n  }\n  return ''\n}\n\nconst isCurrentPeriod = (period) => {\n  const now = new Date()\n  const periodDate = new Date(period)\n\n  if (timeScale.value === 'weeks') {\n    const weekStart = new Date(periodDate)\n    const weekEnd = new Date(periodDate)\n    weekEnd.setDate(weekEnd.getDate() + 6)\n    return now >= weekStart && now <= weekEnd\n  } else if (timeScale.value === 'months') {\n    return periodDate.getMonth() === now.getMonth() && periodDate.getFullYear() === now.getFullYear()\n  }\n  return false\n}\n\nconst resetToToday = () => {\n  const now = new Date()\n  // Start from beginning of current month/week\n  if (timeScale.value === 'weeks') {\n    const startOfWeek = new Date(now)\n    startOfWeek.setDate(now.getDate() - now.getDay())\n    viewStartDate.value = startOfWeek\n  } else {\n    const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1)\n    viewStartDate.value = startOfMonth\n  }\n  calculateTimeline()\n}\n\nconst getStatusColor = (status) => {\n  const colors = {\n    'todo': 'bg-gray-400',\n    'in-progress': 'bg-blue-500',\n    'review': 'bg-yellow-500',\n    'done': 'bg-green-500'\n  }\n  return colors[status] || 'bg-gray-400'\n}\n\nconst getTaskBarColor = (status) => {\n  const colors = {\n    'todo': 'bg-gray-500',\n    'in-progress': 'bg-blue-600',\n    'review': 'bg-yellow-600',\n    'done': 'bg-green-600'\n  }\n  return colors[status] || 'bg-gray-500'\n}\n\nconst getPriorityClass = (priority) => {\n  const classes = {\n    'low': 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200',\n    'medium': 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200',\n    'high': 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200',\n    'urgent': 'bg-red-200 text-red-900 dark:bg-red-800 dark:text-red-100'\n  }\n  return classes[priority] || 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200'\n}\n\nconst getPriorityLabel = (priority) => {\n  const labels = {\n    'low': 'Bassa',\n    'medium': 'Media',\n    'high': 'Alta',\n    'urgent': 'Urgente'\n  }\n  return labels[priority] || 'Non specificata'\n}\n\nconst getTaskProgress = (task) => {\n  const statusProgress = {\n    'todo': 0,\n    'in-progress': 50,\n    'review': 75,\n    'done': 100\n  }\n  return statusProgress[task.status] || 0\n}\n\nconst formatDate = (dateString) => {\n  if (!dateString) return ''\n  return new Date(dateString).toLocaleDateString('it-IT', {\n    day: '2-digit',\n    month: '2-digit'\n  })\n}\n\n// Watchers\nwatch(() => props.project, () => {\n  calculateTimeline()\n}, { immediate: true })\n\n// Lifecycle\nonMounted(() => {\n  resetToToday()\n})\n\n// Expose methods\ndefineExpose({\n  refresh: calculateTimeline\n})\n</script>", "modifiedCode": "<template>\n  <div class=\"space-y-6\">\n    <div class=\"bg-white dark:bg-gray-800 shadow rounded-lg\">\n      <div class=\"px-6 py-4 border-b border-gray-200 dark:border-gray-700\">\n        <div class=\"flex items-center justify-between\">\n          <h3 class=\"text-lg font-medium text-gray-900 dark:text-white\">\n            Diagramma di Gantt\n          </h3>\n          <div class=\"flex items-center space-x-4\">\n            <div class=\"flex items-center space-x-2\">\n              <span class=\"text-sm text-gray-500 dark:text-gray-400\">Vista:</span>\n              <select\n                v-model=\"timeScale\"\n                @change=\"calculateTimeline\"\n                class=\"text-sm border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md shadow-sm focus:border-primary-500 focus:ring-primary-500\"\n              >\n                <option value=\"weeks\">Settimane</option>\n                <option value=\"months\">Mesi</option>\n              </select>\n            </div>\n            <button\n              @click=\"resetToToday\"\n              class=\"px-3 py-1 text-sm bg-primary-100 text-primary-700 rounded-md hover:bg-primary-200\"\n            >\n              Oggi\n            </button>\n          </div>\n        </div>\n      </div>\n\n      <!-- Gantt Chart -->\n      <div v-if=\"!loading && tasksWithDates.length > 0\" class=\"p-6\">\n        <div class=\"overflow-x-auto\">\n          <div class=\"min-w-[1000px]\">\n            <!-- Header with dates -->\n            <div class=\"flex mb-4\">\n              <div class=\"w-80 flex-shrink-0 px-4 py-2 text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider\">\n                Task\n              </div>\n              <div class=\"flex-1 flex\">\n                <div\n                  v-for=\"(period, index) in timelinePeriods\"\n                  :key=\"index\"\n                  class=\"flex-1 text-xs text-center text-gray-500 dark:text-gray-400 py-2 border-l border-gray-200 dark:border-gray-600\"\n                  :class=\"{ 'bg-blue-50 dark:bg-blue-900': isCurrentPeriod(period) }\"\n                >\n                  {{ formatPeriodLabel(period) }}\n                </div>\n              </div>\n            </div>\n\n            <!-- Task rows -->\n            <div class=\"space-y-1\">\n              <div\n                v-for=\"task in tasksWithDates\"\n                :key=\"task.id\"\n                class=\"flex items-center hover:bg-gray-50 dark:hover:bg-gray-700 rounded\"\n              >\n                <!-- Task info -->\n                <div class=\"w-80 flex-shrink-0 px-4 py-3\">\n                  <div class=\"flex items-center space-x-2\">\n                    <div class=\"w-3 h-3 rounded-full\" :class=\"getStatusColor(task.status)\"></div>\n                    <div class=\"flex-1 min-w-0\">\n                      <p class=\"text-sm font-medium text-gray-900 dark:text-white truncate\">{{ task.name }}</p>\n                      <div class=\"flex items-center space-x-2 text-xs text-gray-500 dark:text-gray-400\">\n                        <span v-if=\"task.assignee\">{{ task.assignee.full_name }}</span>\n                        <span class=\"inline-flex items-center px-1.5 py-0.5 rounded-full text-xs font-medium\" :class=\"getPriorityClass(task.priority)\">\n                          {{ getPriorityLabel(task.priority) }}\n                        </span>\n                      </div>\n                      <div class=\"text-xs text-gray-500 dark:text-gray-400 mt-1\">\n                        {{ formatDate(task.start_date) }} - {{ formatDate(task.due_date) }}\n                      </div>\n                    </div>\n                  </div>\n                </div>\n\n                <!-- Gantt timeline -->\n                <div class=\"flex-1 relative h-12 flex items-center\">\n                  <div\n                    v-if=\"task.timeline\"\n                    class=\"absolute h-6 rounded-md flex items-center justify-between px-2 text-xs text-white font-medium shadow-sm cursor-pointer\"\n                    :class=\"getTaskBarColor(task.status)\"\n                    :style=\"{\n                      left: task.timeline.leftPercent + '%',\n                      width: task.timeline.widthPercent + '%',\n                      minWidth: '60px'\n                    }\"\n                    :title=\"`${task.name} - ${getTaskProgress(task)}% completato`\"\n                  >\n                    <span class=\"truncate\">{{ task.name.length > 15 ? task.name.substring(0, 15) + '...' : task.name }}</span>\n                    <span class=\"ml-2\">{{ getTaskProgress(task) }}%</span>\n                  </div>\n\n                  <!-- Progress overlay -->\n                  <div\n                    v-if=\"task.timeline && getTaskProgress(task) > 0 && getTaskProgress(task) < 100\"\n                    class=\"absolute h-6 rounded-md bg-green-600 opacity-80\"\n                    :style=\"{\n                      left: task.timeline.leftPercent + '%',\n                      width: (task.timeline.widthPercent * getTaskProgress(task) / 100) + '%',\n                      minWidth: '2px'\n                    }\"\n                  ></div>\n\n                  <!-- Period dividers -->\n                  <div\n                    v-for=\"(period, index) in timelinePeriods\"\n                    :key=\"index\"\n                    class=\"absolute top-0 bottom-0 border-l border-gray-200 dark:border-gray-600\"\n                    :style=\"{ left: (index / timelinePeriods.length * 100) + '%' }\"\n                  ></div>\n\n                  <!-- Today indicator -->\n                  <div\n                    v-if=\"todayPosition >= 0 && todayPosition <= 100\"\n                    class=\"absolute top-0 bottom-0 w-0.5 bg-red-500 z-10\"\n                    :style=\"{ left: todayPosition + '%' }\"\n                  ></div>\n                </div>\n              </div>\n            </div>\n\n            <!-- Today line with label -->\n            <div\n              v-if=\"todayPosition >= 0 && todayPosition <= 100\"\n              class=\"absolute top-16 bottom-4 w-0.5 bg-red-500 z-20 pointer-events-none\"\n              :style=\"{ left: `calc(320px + ${todayPosition}% * (100vw - 320px - 48px) / 100)` }\"\n            >\n              <div class=\"absolute -top-6 -left-8 bg-red-500 text-white text-xs px-2 py-1 rounded shadow-lg\">\n                Oggi\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <!-- Legend -->\n        <div class=\"mt-6 flex items-center space-x-6 text-xs\">\n          <div class=\"flex items-center space-x-2\">\n            <div class=\"w-3 h-3 bg-gray-400 rounded\"></div>\n            <span class=\"text-gray-600 dark:text-gray-400\">Da fare</span>\n          </div>\n          <div class=\"flex items-center space-x-2\">\n            <div class=\"w-3 h-3 bg-blue-500 rounded\"></div>\n            <span class=\"text-gray-600 dark:text-gray-400\">In corso</span>\n          </div>\n          <div class=\"flex items-center space-x-2\">\n            <div class=\"w-3 h-3 bg-yellow-500 rounded\"></div>\n            <span class=\"text-gray-600 dark:text-gray-400\">In revisione</span>\n          </div>\n          <div class=\"flex items-center space-x-2\">\n            <div class=\"w-3 h-3 bg-green-500 rounded\"></div>\n            <span class=\"text-gray-600 dark:text-gray-400\">Completato</span>\n          </div>\n          <div class=\"flex items-center space-x-2\">\n            <div class=\"w-0.5 h-4 bg-red-500\"></div>\n            <span class=\"text-gray-600 dark:text-gray-400\">Oggi</span>\n          </div>\n        </div>\n      </div>\n\n      <!-- Empty state -->\n      <div v-else-if=\"!loading\" class=\"text-center py-12\">\n        <svg class=\"mx-auto h-12 w-12 text-gray-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z\"></path>\n        </svg>\n        <h3 class=\"mt-2 text-sm font-medium text-gray-900 dark:text-white\">Nessun task pianificato</h3>\n        <p class=\"mt-1 text-sm text-gray-500 dark:text-gray-400\">I task con date di inizio e fine appariranno nel diagramma di Gantt.</p>\n      </div>\n\n      <!-- Loading -->\n      <div v-if=\"loading\" class=\"flex justify-center py-12\">\n        <div class=\"animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600\"></div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script setup>\nimport { ref, computed, onMounted, watch } from 'vue'\n\nconst props = defineProps({\n  project: {\n    type: Object,\n    default: null\n  },\n  loading: {\n    type: Boolean,\n    default: false\n  }\n})\n\n// State\nconst timeScale = ref('weeks')\nconst viewStartDate = ref(new Date())\nconst timelinePeriods = ref([])\nconst todayPosition = ref(0)\n\n// Computed\nconst tasks = computed(() => {\n  return props.project?.tasks || []\n})\n\nconst tasksWithDates = computed(() => {\n  return tasks.value.filter(task => task.start_date && task.due_date).map(task => {\n    const timeline = calculateTaskTimeline(task)\n    return { ...task, timeline }\n  })\n})\n\n// Methods\nconst calculateTimeline = () => {\n  const now = new Date()\n  const startDate = new Date(viewStartDate.value)\n\n  // Calculate periods based on time scale\n  const periods = []\n  const periodCount = 12\n\n  for (let i = 0; i < periodCount; i++) {\n    const periodStart = new Date(startDate)\n\n    if (timeScale.value === 'weeks') {\n      periodStart.setDate(startDate.getDate() + (i * 7))\n    } else if (timeScale.value === 'months') {\n      periodStart.setMonth(startDate.getMonth() + i)\n    }\n\n    periods.push(periodStart)\n  }\n\n  timelinePeriods.value = periods\n\n  // Calculate today position\n  const totalDuration = periods[periods.length - 1] - periods[0]\n  const todayOffset = now - periods[0]\n  todayPosition.value = Math.max(0, Math.min(100, (todayOffset / totalDuration) * 100))\n}\n\nconst calculateTaskTimeline = (task) => {\n  if (!timelinePeriods.value.length) return null\n\n  const taskStart = new Date(task.start_date)\n  const taskEnd = new Date(task.due_date)\n  const timelineStart = timelinePeriods.value[0]\n  const timelineEnd = timelinePeriods.value[timelinePeriods.value.length - 1]\n\n  const totalDuration = timelineEnd - timelineStart\n  const taskStartOffset = taskStart - timelineStart\n  const taskDuration = taskEnd - taskStart\n\n  const leftPercent = Math.max(0, (taskStartOffset / totalDuration) * 100)\n  const widthPercent = Math.min(100 - leftPercent, (taskDuration / totalDuration) * 100)\n\n  return {\n    leftPercent: leftPercent,\n    widthPercent: Math.max(5, widthPercent) // Minimum width for visibility\n  }\n}\n\nconst formatPeriodLabel = (period) => {\n  if (timeScale.value === 'weeks') {\n    return `${period.getDate()}/${period.getMonth() + 1}`\n  } else if (timeScale.value === 'months') {\n    return period.toLocaleDateString('it-IT', { month: 'short', year: '2-digit' })\n  }\n  return ''\n}\n\nconst isCurrentPeriod = (period) => {\n  const now = new Date()\n  const periodDate = new Date(period)\n\n  if (timeScale.value === 'weeks') {\n    const weekStart = new Date(periodDate)\n    const weekEnd = new Date(periodDate)\n    weekEnd.setDate(weekEnd.getDate() + 6)\n    return now >= weekStart && now <= weekEnd\n  } else if (timeScale.value === 'months') {\n    return periodDate.getMonth() === now.getMonth() && periodDate.getFullYear() === now.getFullYear()\n  }\n  return false\n}\n\nconst resetToToday = () => {\n  const now = new Date()\n  // Start from beginning of current month/week\n  if (timeScale.value === 'weeks') {\n    const startOfWeek = new Date(now)\n    startOfWeek.setDate(now.getDate() - now.getDay())\n    viewStartDate.value = startOfWeek\n  } else {\n    const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1)\n    viewStartDate.value = startOfMonth\n  }\n  calculateTimeline()\n}\n\nconst getStatusColor = (status) => {\n  const colors = {\n    'todo': 'bg-gray-400',\n    'in-progress': 'bg-blue-500',\n    'review': 'bg-yellow-500',\n    'done': 'bg-green-500'\n  }\n  return colors[status] || 'bg-gray-400'\n}\n\nconst getTaskBarColor = (status) => {\n  const colors = {\n    'todo': 'bg-gray-500',\n    'in-progress': 'bg-blue-600',\n    'review': 'bg-yellow-600',\n    'done': 'bg-green-600'\n  }\n  return colors[status] || 'bg-gray-500'\n}\n\nconst getPriorityClass = (priority) => {\n  const classes = {\n    'low': 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200',\n    'medium': 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200',\n    'high': 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200',\n    'urgent': 'bg-red-200 text-red-900 dark:bg-red-800 dark:text-red-100'\n  }\n  return classes[priority] || 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200'\n}\n\nconst getPriorityLabel = (priority) => {\n  const labels = {\n    'low': 'Bassa',\n    'medium': 'Media',\n    'high': 'Alta',\n    'urgent': 'Urgente'\n  }\n  return labels[priority] || 'Non specificata'\n}\n\nconst getTaskProgress = (task) => {\n  const statusProgress = {\n    'todo': 0,\n    'in-progress': 50,\n    'review': 75,\n    'done': 100\n  }\n  return statusProgress[task.status] || 0\n}\n\nconst formatDate = (dateString) => {\n  if (!dateString) return ''\n  return new Date(dateString).toLocaleDateString('it-IT', {\n    day: '2-digit',\n    month: '2-digit'\n  })\n}\n\n// Watchers\nwatch(() => props.project, () => {\n  calculateTimeline()\n}, { immediate: true })\n\n// Lifecycle\nonMounted(() => {\n  resetToToday()\n})\n\n// Expose methods\ndefineExpose({\n  refresh: calculateTimeline\n})\n</script>"}