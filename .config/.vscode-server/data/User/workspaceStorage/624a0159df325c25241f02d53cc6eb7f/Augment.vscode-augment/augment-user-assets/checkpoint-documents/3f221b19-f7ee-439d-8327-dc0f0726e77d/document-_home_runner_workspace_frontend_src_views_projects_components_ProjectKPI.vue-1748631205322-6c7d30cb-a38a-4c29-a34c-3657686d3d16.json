{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/views/projects/components/ProjectKPI.vue"}, "originalCode": "<template>\n  <div class=\"project-kpi\">\n    <div v-if=\"loading\" class=\"animate-pulse space-y-4\">\n      <div class=\"grid grid-cols-1 md:grid-cols-4 gap-4\">\n        <div v-for=\"i in 4\" :key=\"i\" class=\"bg-gray-200 rounded-lg h-24\"></div>\n      </div>\n      <div class=\"bg-gray-200 rounded-lg h-64\"></div>\n    </div>\n\n    <div v-else-if=\"project\" class=\"space-y-6\">\n      <!-- K<PERSON> Header -->\n      <div class=\"bg-white shadow rounded-lg p-6\">\n        <div class=\"flex items-center justify-between\">\n          <div>\n            <h3 class=\"text-lg font-medium text-gray-900\">KPI Progetto</h3>\n            <p class=\"text-sm text-gray-600\">Dashboard metriche e performance del progetto</p>\n          </div>\n          <button\n            @click=\"refreshKPIs\"\n            :disabled=\"refreshing\"\n            class=\"inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50\"\n          >\n            <svg class=\"w-4 h-4 mr-2\" :class=\"{ 'animate-spin': refreshing }\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15\" />\n            </svg>\n            Aggiorna\n          </button>\n        </div>\n      </div>\n\n      <!-- KPI Overview Cards -->\n      <div class=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\">\n        <!-- Ore Totali -->\n        <div class=\"bg-white shadow rounded-lg p-6\">\n          <div class=\"flex items-center\">\n            <div class=\"flex-shrink-0\">\n              <svg class=\"h-8 w-8 text-blue-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\" />\n              </svg>\n            </div>\n            <div class=\"ml-5 w-0 flex-1\">\n              <dl>\n                <dt class=\"text-sm font-medium text-gray-500 truncate\">Ore Totali</dt>\n                <dd class=\"text-lg font-medium text-gray-900\">{{ formatHours(kpiData.totalHours) }}</dd>\n                <dd class=\"text-xs text-gray-500\">{{ kpiData.workDays }} giorni lavorati</dd>\n              </dl>\n            </div>\n          </div>\n        </div>\n\n        <!-- Costi Totali -->\n        <div class=\"bg-white shadow rounded-lg p-6\">\n          <div class=\"flex items-center\">\n            <div class=\"flex-shrink-0\">\n              <svg class=\"h-8 w-8 text-red-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1\" />\n              </svg>\n            </div>\n            <div class=\"ml-5 w-0 flex-1\">\n              <dl>\n                <dt class=\"text-sm font-medium text-gray-500 truncate\">Costi Totali</dt>\n                <dd class=\"text-lg font-medium text-gray-900\">{{ formatCurrency(kpiData.totalCosts) }}</dd>\n                <dd class=\"text-xs\" :class=\"costVarianceClass\">{{ formatCurrency(kpiData.costVariance) }} vs budget</dd>\n              </dl>\n            </div>\n          </div>\n        </div>\n\n        <!-- Ricavi Potenziali -->\n        <div class=\"bg-white shadow rounded-lg p-6\">\n          <div class=\"flex items-center\">\n            <div class=\"flex-shrink-0\">\n              <svg class=\"h-8 w-8 text-green-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M13 7h8m0 0v8m0-8l-8 8-4-4-6 6\" />\n              </svg>\n            </div>\n            <div class=\"ml-5 w-0 flex-1\">\n              <dl>\n                <dt class=\"text-sm font-medium text-gray-500 truncate\">Ricavi Potenziali</dt>\n                <dd class=\"text-lg font-medium text-gray-900\">{{ formatCurrency(kpiData.potentialRevenue) }}</dd>\n                <dd class=\"text-xs text-gray-500\">{{ formatCurrency(kpiData.actualRevenue) }} fatturati</dd>\n              </dl>\n            </div>\n          </div>\n        </div>\n\n        <!-- Margine Progetto -->\n        <div class=\"bg-white shadow rounded-lg p-6\">\n          <div class=\"flex items-center\">\n            <div class=\"flex-shrink-0\">\n              <svg class=\"h-8 w-8 text-purple-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z\" />\n              </svg>\n            </div>\n            <div class=\"ml-5 w-0 flex-1\">\n              <dl>\n                <dt class=\"text-sm font-medium text-gray-500 truncate\">Margine</dt>\n                <dd class=\"text-lg font-medium text-gray-900\">{{ formatPercentage(kpiData.marginPercentage) }}</dd>\n                <dd class=\"text-xs\" :class=\"marginClass\">{{ marginStatus }}</dd>\n              </dl>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <!-- Performance Charts -->\n      <div class=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n        <!-- Budget Progress Chart -->\n        <div class=\"bg-white shadow rounded-lg p-6\">\n          <h4 class=\"text-lg font-medium text-gray-900 mb-4\">Andamento Budget</h4>\n          <div class=\"space-y-4\">\n            <div class=\"flex justify-between text-sm\">\n              <span class=\"text-gray-600\">Budget Totale</span>\n              <span class=\"font-medium\">{{ formatCurrency(project.budget || 0) }}</span>\n            </div>\n            <div class=\"w-full bg-gray-200 rounded-full h-3\">\n              <div\n                class=\"bg-blue-600 h-3 rounded-full transition-all duration-300\"\n                :style=\"{ width: budgetUsagePercentage + '%' }\"\n              ></div>\n            </div>\n            <div class=\"flex justify-between text-sm\">\n              <span class=\"text-gray-600\">Utilizzato: {{ formatCurrency(kpiData.totalCosts) }}</span>\n              <span class=\"font-medium\">{{ budgetUsagePercentage }}%</span>\n            </div>\n          </div>\n        </div>\n\n        <!-- Time Progress Chart -->\n        <div class=\"bg-white shadow rounded-lg p-6\">\n          <h4 class=\"text-lg font-medium text-gray-900 mb-4\">Andamento Tempo</h4>\n          <div class=\"space-y-4\">\n            <div class=\"flex justify-between text-sm\">\n              <span class=\"text-gray-600\">Ore Stimate</span>\n              <span class=\"font-medium\">{{ formatHours(project.estimated_hours || 0) }}</span>\n            </div>\n            <div class=\"w-full bg-gray-200 rounded-full h-3\">\n              <div\n                class=\"bg-green-600 h-3 rounded-full transition-all duration-300\"\n                :style=\"{ width: timeUsagePercentage + '%' }\"\n              ></div>\n            </div>\n            <div class=\"flex justify-between text-sm\">\n              <span class=\"text-gray-600\">Lavorate: {{ formatHours(kpiData.totalHours) }}</span>\n              <span class=\"font-medium\">{{ timeUsagePercentage }}%</span>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <!-- KPI Thresholds Configuration -->\n      <div class=\"bg-white shadow rounded-lg p-6\">\n        <div class=\"flex items-center justify-between mb-4\">\n          <h4 class=\"text-lg font-medium text-gray-900\">Soglie KPI</h4>\n          <button\n            @click=\"openKPIConfigModal\"\n            class=\"inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50\"\n          >\n            <svg class=\"w-4 h-4 mr-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z\" />\n              <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M15 12a3 3 0 11-6 0 3 3 0 016 0z\" />\n            </svg>\n            Configura KPI\n          </button>\n        </div>\n\n        <div class=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n          <div class=\"text-center p-4 border rounded-lg\">\n            <div class=\"text-2xl font-bold\" :class=\"budgetThresholdClass\">\n              {{ budgetUsagePercentage }}%\n            </div>\n            <div class=\"text-sm text-gray-600\">Budget Usage</div>\n            <div class=\"text-xs text-gray-500\">Soglia: {{ kpiThresholds.budget }}%</div>\n          </div>\n\n          <div class=\"text-center p-4 border rounded-lg\">\n            <div class=\"text-2xl font-bold\" :class=\"timeThresholdClass\">\n              {{ timeUsagePercentage }}%\n            </div>\n            <div class=\"text-sm text-gray-600\">Time Usage</div>\n            <div class=\"text-xs text-gray-500\">Soglia: {{ kpiThresholds.time }}%</div>\n          </div>\n\n          <div class=\"text-center p-4 border rounded-lg\">\n            <div class=\"text-2xl font-bold\" :class=\"marginThresholdClass\">\n              {{ formatPercentage(kpiData.marginPercentage) }}\n            </div>\n            <div class=\"text-sm text-gray-600\">Margine</div>\n            <div class=\"text-xs text-gray-500\">Soglia: {{ kpiThresholds.margin }}%</div>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <div v-else class=\"text-center py-8\">\n      <p class=\"text-gray-500\">Progetto non trovato</p>\n    </div>\n\n    <!-- KPI Configuration Modal -->\n    <div v-if=\"showKPIConfigModal\" class=\"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50\">\n      <div class=\"relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white\">\n        <div class=\"mt-3\">\n          <!-- Modal Header -->\n          <div class=\"flex items-center justify-between pb-4 border-b\">\n            <h3 class=\"text-lg font-medium text-gray-900\">Configurazione KPI Progetto</h3>\n            <button @click=\"closeKPIConfigModal\" class=\"text-gray-400 hover:text-gray-600\">\n              <svg class=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M6 18L18 6M6 6l12 12\" />\n              </svg>\n            </button>\n          </div>\n\n          <!-- Modal Content -->\n          <div class=\"mt-6 space-y-6\">\n            <!-- Project Info -->\n            <div class=\"bg-gray-50 p-4 rounded-lg\">\n              <h4 class=\"font-medium text-gray-900\">{{ project?.name }}</h4>\n              <p class=\"text-sm text-gray-600\">Tipo: {{ getProjectTypeLabel(project?.project_type) }}</p>\n            </div>\n\n            <!-- KPI Configuration Forms -->\n            <div class=\"space-y-6\">\n              <div v-for=\"kpi in availableKPIs\" :key=\"kpi.name\" class=\"border border-gray-200 rounded-lg p-4\">\n                <div class=\"flex items-center justify-between mb-4\">\n                  <div>\n                    <h5 class=\"font-medium text-gray-900\">{{ kpi.display_name }}</h5>\n                    <p class=\"text-sm text-gray-600\">{{ kpi.description }}</p>\n                  </div>\n                  <div class=\"flex items-center space-x-2\">\n                    <span class=\"text-xs text-gray-500\">{{ kpi.unit }}</span>\n                    <button\n                      @click=\"resetKPIToDefault(kpi.name)\"\n                      class=\"text-xs text-blue-600 hover:text-blue-800\"\n                      title=\"Reset ai valori di default\"\n                    >\n                      Reset\n                    </button>\n                  </div>\n                </div>\n\n                <div class=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n                  <div>\n                    <label class=\"block text-sm font-medium text-gray-700 mb-1\">Target Minimo</label>\n                    <input\n                      type=\"number\"\n                      step=\"0.1\"\n                      v-model=\"kpiConfigs[kpi.name].target_min\"\n                      @input=\"markKPIAsDirty(kpi.name)\"\n                      class=\"w-full border border-gray-300 rounded-md px-3 py-2 text-sm\"\n                    >\n                  </div>\n                  <div>\n                    <label class=\"block text-sm font-medium text-gray-700 mb-1\">Target Massimo</label>\n                    <input\n                      type=\"number\"\n                      step=\"0.1\"\n                      v-model=\"kpiConfigs[kpi.name].target_max\"\n                      @input=\"markKPIAsDirty(kpi.name)\"\n                      class=\"w-full border border-gray-300 rounded-md px-3 py-2 text-sm\"\n                    >\n                  </div>\n                  <div>\n                    <label class=\"block text-sm font-medium text-gray-700 mb-1\">Soglia Warning</label>\n                    <input\n                      type=\"number\"\n                      step=\"0.1\"\n                      v-model=\"kpiConfigs[kpi.name].warning_threshold\"\n                      @input=\"markKPIAsDirty(kpi.name)\"\n                      class=\"w-full border border-gray-300 rounded-md px-3 py-2 text-sm\"\n                    >\n                  </div>\n                </div>\n\n                <div class=\"mt-4\">\n                  <label class=\"block text-sm font-medium text-gray-700 mb-1\">Descrizione Personalizzata</label>\n                  <textarea\n                    v-model=\"kpiConfigs[kpi.name].custom_description\"\n                    @input=\"markKPIAsDirty(kpi.name)\"\n                    rows=\"2\"\n                    class=\"w-full border border-gray-300 rounded-md px-3 py-2 text-sm\"\n                    placeholder=\"Descrizione specifica per questo progetto...\"\n                  ></textarea>\n                </div>\n\n                <!-- Save Button per singolo KPI -->\n                <div class=\"mt-4 flex justify-end\">\n                  <button\n                    v-if=\"kpiConfigs[kpi.name]?.isDirty\"\n                    @click=\"saveKPIConfig(kpi.name)\"\n                    :disabled=\"savingKPI === kpi.name\"\n                    class=\"inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 disabled:opacity-50\"\n                  >\n                    <svg v-if=\"savingKPI === kpi.name\" class=\"animate-spin -ml-1 mr-2 h-4 w-4 text-white\" fill=\"none\" viewBox=\"0 0 24 24\">\n                      <circle class=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" stroke-width=\"4\"></circle>\n                      <path class=\"opacity-75\" fill=\"currentColor\" d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"></path>\n                    </svg>\n                    {{ savingKPI === kpi.name ? 'Salvataggio...' : 'Salva KPI' }}\n                  </button>\n                  <span v-else-if=\"kpiConfigs[kpi.name]?.isSaved\" class=\"text-sm text-green-600\">✓ Salvato</span>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          <!-- Modal Footer -->\n          <div class=\"mt-6 pt-4 border-t flex justify-between\">\n            <button\n              @click=\"resetAllKPIsToDefault\"\n              class=\"inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50\"\n            >\n              Reset Tutti\n            </button>\n            <div class=\"flex space-x-3\">\n              <button\n                @click=\"closeKPIConfigModal\"\n                class=\"inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50\"\n              >\n                Chiudi\n              </button>\n              <button\n                @click=\"saveAllKPIConfigs\"\n                :disabled=\"!hasUnsavedChanges\"\n                class=\"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 disabled:opacity-50\"\n              >\n                Salva Tutto\n              </button>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script setup>\nimport { ref, computed, onMounted, watch } from 'vue'\n\n// Props\nconst props = defineProps({\n  project: { type: Object, default: null },\n  loading: { type: Boolean, default: false }\n})\n\n// Emits\nconst emit = defineEmits(['refresh'])\n\n// Reactive data\nconst refreshing = ref(false)\nconst showKPIConfigModal = ref(false)\nconst savingKPI = ref(null)\nconst kpiConfigs = ref({})\nconst kpiData = ref({\n  totalHours: 0,\n  workDays: 0,\n  totalCosts: 0,\n  costVariance: 0,\n  potentialRevenue: 0,\n  actualRevenue: 0,\n  marginPercentage: 0\n})\n\nconst kpiThresholds = ref({\n  budget: 80,\n  time: 85,\n  margin: 15\n})\n\n// Computed properties\nconst budgetUsagePercentage = computed(() => {\n  if (!props.project?.budget || kpiData.value.totalCosts === 0) return 0\n  return Math.round((kpiData.value.totalCosts / props.project.budget) * 100)\n})\n\nconst timeUsagePercentage = computed(() => {\n  if (!props.project?.estimated_hours || kpiData.value.totalHours === 0) return 0\n  return Math.round((kpiData.value.totalHours / props.project.estimated_hours) * 100)\n})\n\nconst costVarianceClass = computed(() => {\n  const variance = kpiData.value.costVariance\n  if (variance > 0) return 'text-red-600'\n  if (variance < 0) return 'text-green-600'\n  return 'text-gray-600'\n})\n\nconst marginClass = computed(() => {\n  const margin = kpiData.value.marginPercentage\n  if (margin >= kpiThresholds.value.margin) return 'text-green-600'\n  if (margin >= kpiThresholds.value.margin * 0.7) return 'text-yellow-600'\n  return 'text-red-600'\n})\n\nconst marginStatus = computed(() => {\n  const margin = kpiData.value.marginPercentage\n  if (margin >= kpiThresholds.value.margin) return 'Ottimo'\n  if (margin >= kpiThresholds.value.margin * 0.7) return 'Accettabile'\n  return 'Critico'\n})\n\nconst budgetThresholdClass = computed(() => {\n  const usage = budgetUsagePercentage.value\n  if (usage >= kpiThresholds.value.budget) return 'text-red-600'\n  if (usage >= kpiThresholds.value.budget * 0.8) return 'text-yellow-600'\n  return 'text-green-600'\n})\n\nconst timeThresholdClass = computed(() => {\n  const usage = timeUsagePercentage.value\n  if (usage >= kpiThresholds.value.time) return 'text-red-600'\n  if (usage >= kpiThresholds.value.time * 0.8) return 'text-yellow-600'\n  return 'text-green-600'\n})\n\nconst marginThresholdClass = computed(() => {\n  const margin = kpiData.value.marginPercentage\n  if (margin >= kpiThresholds.value.margin) return 'text-green-600'\n  if (margin >= kpiThresholds.value.margin * 0.7) return 'text-yellow-600'\n  return 'text-red-600'\n})\n\n// Methods\nconst formatCurrency = (amount) => {\n  return new Intl.NumberFormat('it-IT', {\n    style: 'currency',\n    currency: 'EUR'\n  }).format(amount || 0)\n}\n\nconst formatHours = (hours) => {\n  return `${hours || 0}h`\n}\n\nconst formatPercentage = (percentage) => {\n  return `${(percentage || 0).toFixed(1)}%`\n}\n\nconst loadKPIData = async () => {\n  if (!props.project?.id) return\n\n  // API endpoint non esiste ancora, usa sempre calcoli fallback\n  calculateFallbackKPIs()\n}\n\nconst calculateFallbackKPIs = () => {\n  // Calculate basic KPIs from project data\n  const project = props.project\n  if (!project) return\n\n  // Basic calculations (these would normally come from API)\n  kpiData.value = {\n    totalHours: project.total_hours || 0,\n    workDays: Math.ceil((project.total_hours || 0) / 8),\n    totalCosts: (project.total_hours || 0) * 50, // Assuming 50€/hour average\n    costVariance: ((project.total_hours || 0) * 50) - (project.budget || 0),\n    potentialRevenue: project.budget || 0,\n    actualRevenue: project.invoiced_amount || 0,\n    marginPercentage: project.budget ?\n      (((project.budget - ((project.total_hours || 0) * 50)) / project.budget) * 100) : 0\n  }\n}\n\nconst refreshKPIs = async () => {\n  refreshing.value = true\n  try {\n    await loadKPIData()\n    emit('refresh')\n  } catch (error) {\n    console.error('Error refreshing KPIs:', error)\n  } finally {\n    refreshing.value = false\n  }\n}\n\n// KPI Configuration Modal Functions\nconst availableKPIs = computed(() => {\n  const projectType = props.project?.project_type || 'service'\n  return getKPITemplatesForProjectType(projectType)\n})\n\nconst getKPITemplatesForProjectType = (projectType) => {\n  const templates = {\n    service: [\n      {\n        name: 'margin_percentage',\n        display_name: 'Margine Netto %',\n        description: 'Percentuale di margine netto sul fatturato',\n        unit: '%',\n        target_min: 25,\n        target_max: 40,\n        warning_threshold: 15\n      },\n      {\n        name: 'utilization_rate',\n        display_name: 'Tasso di Utilizzo %',\n        description: 'Percentuale di utilizzo del team rispetto alla capacità teorica',\n        unit: '%',\n        target_min: 75,\n        target_max: 85,\n        warning_threshold: 60\n      },\n      {\n        name: 'cost_per_hour',\n        display_name: 'Costo per Ora',\n        description: 'Costo medio per ora di lavoro, inclusi tutti i costi',\n        unit: '€',\n        target_min: 30,\n        target_max: 50,\n        warning_threshold: 60\n      },\n      {\n        name: 'cost_revenue_ratio',\n        display_name: 'Rapporto C/R',\n        description: 'Rapporto tra costi sostenuti e ricavi generati',\n        unit: 'ratio',\n        target_min: 0.6,\n        target_max: 0.75,\n        warning_threshold: 0.85\n      }\n    ]\n  }\n\n  return templates[projectType] || templates.service\n}\n\nconst getProjectTypeLabel = (projectType) => {\n  const labels = {\n    service: '🔧 Servizio',\n    license: '📄 Licenza',\n    consulting: '💼 Consulenza',\n    product: '📦 Prodotto',\n    rd: '🔬 R&D',\n    internal: '🏢 Interno'\n  }\n  return labels[projectType] || 'Sconosciuto'\n}\n\nconst openKPIConfigModal = () => {\n  // Inizializza configurazioni KPI con valori di default\n  const templates = availableKPIs.value\n  templates.forEach(kpi => {\n    if (!kpiConfigs.value[kpi.name]) {\n      kpiConfigs.value[kpi.name] = {\n        target_min: kpi.target_min,\n        target_max: kpi.target_max,\n        warning_threshold: kpi.warning_threshold,\n        custom_description: '',\n        isDirty: false,\n        isSaved: false\n      }\n    }\n  })\n\n  showKPIConfigModal.value = true\n}\n\nconst closeKPIConfigModal = () => {\n  showKPIConfigModal.value = false\n}\n\nconst markKPIAsDirty = (kpiName) => {\n  if (kpiConfigs.value[kpiName]) {\n    kpiConfigs.value[kpiName].isDirty = true\n    kpiConfigs.value[kpiName].isSaved = false\n  }\n}\n\nconst resetKPIToDefault = (kpiName) => {\n  const template = availableKPIs.value.find(kpi => kpi.name === kpiName)\n  if (template && kpiConfigs.value[kpiName]) {\n    kpiConfigs.value[kpiName].target_min = template.target_min\n    kpiConfigs.value[kpiName].target_max = template.target_max\n    kpiConfigs.value[kpiName].warning_threshold = template.warning_threshold\n    kpiConfigs.value[kpiName].custom_description = ''\n    kpiConfigs.value[kpiName].isDirty = true\n    kpiConfigs.value[kpiName].isSaved = false\n  }\n}\n\nconst resetAllKPIsToDefault = () => {\n  if (confirm('Sei sicuro di voler ripristinare tutti i KPI ai valori di default?')) {\n    availableKPIs.value.forEach(kpi => {\n      resetKPIToDefault(kpi.name)\n    })\n  }\n}\n\nconst saveKPIConfig = async (kpiName) => {\n  if (!kpiConfigs.value[kpiName]) return\n\n  savingKPI.value = kpiName\n\n  try {\n    // TODO: Implementare chiamata API per salvare configurazione KPI\n    const config = kpiConfigs.value[kpiName]\n\n    // Simula chiamata API\n    await new Promise(resolve => setTimeout(resolve, 1000))\n\n    console.log('Saving KPI config:', {\n      project_id: props.project?.id,\n      kpi_name: kpiName,\n      target_min: config.target_min,\n      target_max: config.target_max,\n      warning_threshold: config.warning_threshold,\n      custom_description: config.custom_description\n    })\n\n    kpiConfigs.value[kpiName].isDirty = false\n    kpiConfigs.value[kpiName].isSaved = true\n\n    // Auto-hide saved status after 3 seconds\n    setTimeout(() => {\n      if (kpiConfigs.value[kpiName]) {\n        kpiConfigs.value[kpiName].isSaved = false\n      }\n    }, 3000)\n\n  } catch (error) {\n    console.error('Error saving KPI config:', error)\n    alert('Errore nel salvataggio della configurazione KPI')\n  } finally {\n    savingKPI.value = null\n  }\n}\n\nconst saveAllKPIConfigs = async () => {\n  const dirtyKPIs = availableKPIs.value.filter(kpi => kpiConfigs.value[kpi.name]?.isDirty)\n\n  for (const kpi of dirtyKPIs) {\n    await saveKPIConfig(kpi.name)\n  }\n}\n\nconst hasUnsavedChanges = computed(() => {\n  return availableKPIs.value.some(kpi => kpiConfigs.value[kpi.name]?.isDirty)\n})\n\n// Watchers\nwatch(() => props.project, (newProject) => {\n  if (newProject) {\n    loadKPIData()\n  }\n}, { immediate: true })\n\n// Lifecycle\nonMounted(() => {\n  if (props.project) {\n    loadKPIData()\n  }\n})\n</script>", "modifiedCode": "<template>\n  <div class=\"project-kpi\">\n    <div v-if=\"loading\" class=\"animate-pulse space-y-4\">\n      <div class=\"grid grid-cols-1 md:grid-cols-4 gap-4\">\n        <div v-for=\"i in 4\" :key=\"i\" class=\"bg-gray-200 rounded-lg h-24\"></div>\n      </div>\n      <div class=\"bg-gray-200 rounded-lg h-64\"></div>\n    </div>\n\n    <div v-else-if=\"project\" class=\"space-y-6\">\n      <!-- K<PERSON> Header -->\n      <div class=\"bg-white shadow rounded-lg p-6\">\n        <div class=\"flex items-center justify-between\">\n          <div>\n            <h3 class=\"text-lg font-medium text-gray-900\">KPI Progetto</h3>\n            <p class=\"text-sm text-gray-600\">Dashboard metriche e performance del progetto</p>\n          </div>\n          <button\n            @click=\"refreshKPIs\"\n            :disabled=\"refreshing\"\n            class=\"inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50\"\n          >\n            <svg class=\"w-4 h-4 mr-2\" :class=\"{ 'animate-spin': refreshing }\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15\" />\n            </svg>\n            Aggiorna\n          </button>\n        </div>\n      </div>\n\n      <!-- KPI Overview Cards -->\n      <div class=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\">\n        <!-- Ore Totali -->\n        <div class=\"bg-white shadow rounded-lg p-6\">\n          <div class=\"flex items-center\">\n            <div class=\"flex-shrink-0\">\n              <svg class=\"h-8 w-8 text-blue-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\" />\n              </svg>\n            </div>\n            <div class=\"ml-5 w-0 flex-1\">\n              <dl>\n                <dt class=\"text-sm font-medium text-gray-500 truncate\">Ore Totali</dt>\n                <dd class=\"text-lg font-medium text-gray-900\">{{ formatHours(kpiData.totalHours) }}</dd>\n                <dd class=\"text-xs text-gray-500\">{{ kpiData.workDays }} giorni lavorati</dd>\n              </dl>\n            </div>\n          </div>\n        </div>\n\n        <!-- Costi Totali -->\n        <div class=\"bg-white shadow rounded-lg p-6\">\n          <div class=\"flex items-center\">\n            <div class=\"flex-shrink-0\">\n              <svg class=\"h-8 w-8 text-red-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1\" />\n              </svg>\n            </div>\n            <div class=\"ml-5 w-0 flex-1\">\n              <dl>\n                <dt class=\"text-sm font-medium text-gray-500 truncate\">Costi Totali</dt>\n                <dd class=\"text-lg font-medium text-gray-900\">{{ formatCurrency(kpiData.totalCosts) }}</dd>\n                <dd class=\"text-xs\" :class=\"costVarianceClass\">{{ formatCurrency(kpiData.costVariance) }} vs budget</dd>\n              </dl>\n            </div>\n          </div>\n        </div>\n\n        <!-- Ricavi Potenziali -->\n        <div class=\"bg-white shadow rounded-lg p-6\">\n          <div class=\"flex items-center\">\n            <div class=\"flex-shrink-0\">\n              <svg class=\"h-8 w-8 text-green-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M13 7h8m0 0v8m0-8l-8 8-4-4-6 6\" />\n              </svg>\n            </div>\n            <div class=\"ml-5 w-0 flex-1\">\n              <dl>\n                <dt class=\"text-sm font-medium text-gray-500 truncate\">Ricavi Potenziali</dt>\n                <dd class=\"text-lg font-medium text-gray-900\">{{ formatCurrency(kpiData.potentialRevenue) }}</dd>\n                <dd class=\"text-xs text-gray-500\">{{ formatCurrency(kpiData.actualRevenue) }} fatturati</dd>\n              </dl>\n            </div>\n          </div>\n        </div>\n\n        <!-- Margine Progetto -->\n        <div class=\"bg-white shadow rounded-lg p-6\">\n          <div class=\"flex items-center\">\n            <div class=\"flex-shrink-0\">\n              <svg class=\"h-8 w-8 text-purple-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z\" />\n              </svg>\n            </div>\n            <div class=\"ml-5 w-0 flex-1\">\n              <dl>\n                <dt class=\"text-sm font-medium text-gray-500 truncate\">Margine</dt>\n                <dd class=\"text-lg font-medium text-gray-900\">{{ formatPercentage(kpiData.marginPercentage) }}</dd>\n                <dd class=\"text-xs\" :class=\"marginClass\">{{ marginStatus }}</dd>\n              </dl>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <!-- Performance Charts -->\n      <div class=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n        <!-- Budget Progress Chart -->\n        <div class=\"bg-white shadow rounded-lg p-6\">\n          <h4 class=\"text-lg font-medium text-gray-900 mb-4\">Andamento Budget</h4>\n          <div class=\"space-y-4\">\n            <div class=\"flex justify-between text-sm\">\n              <span class=\"text-gray-600\">Budget Totale</span>\n              <span class=\"font-medium\">{{ formatCurrency(project.budget || 0) }}</span>\n            </div>\n            <div class=\"w-full bg-gray-200 rounded-full h-3\">\n              <div\n                class=\"bg-blue-600 h-3 rounded-full transition-all duration-300\"\n                :style=\"{ width: budgetUsagePercentage + '%' }\"\n              ></div>\n            </div>\n            <div class=\"flex justify-between text-sm\">\n              <span class=\"text-gray-600\">Utilizzato: {{ formatCurrency(kpiData.totalCosts) }}</span>\n              <span class=\"font-medium\">{{ budgetUsagePercentage }}%</span>\n            </div>\n          </div>\n        </div>\n\n        <!-- Time Progress Chart -->\n        <div class=\"bg-white shadow rounded-lg p-6\">\n          <h4 class=\"text-lg font-medium text-gray-900 mb-4\">Andamento Tempo</h4>\n          <div class=\"space-y-4\">\n            <div class=\"flex justify-between text-sm\">\n              <span class=\"text-gray-600\">Ore Stimate</span>\n              <span class=\"font-medium\">{{ formatHours(project.estimated_hours || 0) }}</span>\n            </div>\n            <div class=\"w-full bg-gray-200 rounded-full h-3\">\n              <div\n                class=\"bg-green-600 h-3 rounded-full transition-all duration-300\"\n                :style=\"{ width: timeUsagePercentage + '%' }\"\n              ></div>\n            </div>\n            <div class=\"flex justify-between text-sm\">\n              <span class=\"text-gray-600\">Lavorate: {{ formatHours(kpiData.totalHours) }}</span>\n              <span class=\"font-medium\">{{ timeUsagePercentage }}%</span>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <!-- KPI Thresholds Configuration -->\n      <div class=\"bg-white shadow rounded-lg p-6\">\n        <div class=\"flex items-center justify-between mb-4\">\n          <h4 class=\"text-lg font-medium text-gray-900\">Soglie KPI</h4>\n          <button\n            @click=\"openKPIConfigModal\"\n            class=\"inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50\"\n          >\n            <svg class=\"w-4 h-4 mr-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z\" />\n              <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M15 12a3 3 0 11-6 0 3 3 0 016 0z\" />\n            </svg>\n            Configura KPI\n          </button>\n        </div>\n\n        <div class=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n          <div class=\"text-center p-4 border rounded-lg\">\n            <div class=\"text-2xl font-bold\" :class=\"budgetThresholdClass\">\n              {{ budgetUsagePercentage }}%\n            </div>\n            <div class=\"text-sm text-gray-600\">Budget Usage</div>\n            <div class=\"text-xs text-gray-500\">Soglia: {{ kpiThresholds.budget }}%</div>\n          </div>\n\n          <div class=\"text-center p-4 border rounded-lg\">\n            <div class=\"text-2xl font-bold\" :class=\"timeThresholdClass\">\n              {{ timeUsagePercentage }}%\n            </div>\n            <div class=\"text-sm text-gray-600\">Time Usage</div>\n            <div class=\"text-xs text-gray-500\">Soglia: {{ kpiThresholds.time }}%</div>\n          </div>\n\n          <div class=\"text-center p-4 border rounded-lg\">\n            <div class=\"text-2xl font-bold\" :class=\"marginThresholdClass\">\n              {{ formatPercentage(kpiData.marginPercentage) }}\n            </div>\n            <div class=\"text-sm text-gray-600\">Margine</div>\n            <div class=\"text-xs text-gray-500\">Soglia: {{ kpiThresholds.margin }}%</div>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <div v-else class=\"text-center py-8\">\n      <p class=\"text-gray-500\">Progetto non trovato</p>\n    </div>\n\n    <!-- KPI Configuration Modal -->\n    <div v-if=\"showKPIConfigModal\" class=\"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50\">\n      <div class=\"relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white\">\n        <div class=\"mt-3\">\n          <!-- Modal Header -->\n          <div class=\"flex items-center justify-between pb-4 border-b\">\n            <h3 class=\"text-lg font-medium text-gray-900\">Configurazione KPI Progetto</h3>\n            <button @click=\"closeKPIConfigModal\" class=\"text-gray-400 hover:text-gray-600\">\n              <svg class=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M6 18L18 6M6 6l12 12\" />\n              </svg>\n            </button>\n          </div>\n\n          <!-- Modal Content -->\n          <div class=\"mt-6 space-y-6\">\n            <!-- Project Info -->\n            <div class=\"bg-gray-50 p-4 rounded-lg\">\n              <h4 class=\"font-medium text-gray-900\">{{ project?.name }}</h4>\n              <p class=\"text-sm text-gray-600\">Tipo: {{ getProjectTypeLabel(project?.project_type) }}</p>\n            </div>\n\n            <!-- KPI Configuration Forms -->\n            <div class=\"space-y-6\">\n              <div v-for=\"kpi in availableKPIs\" :key=\"kpi.name\" class=\"border border-gray-200 rounded-lg p-4\">\n                <div class=\"flex items-center justify-between mb-4\">\n                  <div>\n                    <h5 class=\"font-medium text-gray-900\">{{ kpi.display_name }}</h5>\n                    <p class=\"text-sm text-gray-600\">{{ kpi.description }}</p>\n                  </div>\n                  <div class=\"flex items-center space-x-2\">\n                    <span class=\"text-xs text-gray-500\">{{ kpi.unit }}</span>\n                    <button\n                      @click=\"resetKPIToDefault(kpi.name)\"\n                      class=\"text-xs text-blue-600 hover:text-blue-800\"\n                      title=\"Reset ai valori di default\"\n                    >\n                      Reset\n                    </button>\n                  </div>\n                </div>\n\n                <div class=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n                  <div>\n                    <label class=\"block text-sm font-medium text-gray-700 mb-1\">Target Minimo</label>\n                    <input\n                      type=\"number\"\n                      step=\"0.1\"\n                      v-model=\"kpiConfigs[kpi.name].target_min\"\n                      @input=\"markKPIAsDirty(kpi.name)\"\n                      class=\"w-full border border-gray-300 rounded-md px-3 py-2 text-sm\"\n                    >\n                  </div>\n                  <div>\n                    <label class=\"block text-sm font-medium text-gray-700 mb-1\">Target Massimo</label>\n                    <input\n                      type=\"number\"\n                      step=\"0.1\"\n                      v-model=\"kpiConfigs[kpi.name].target_max\"\n                      @input=\"markKPIAsDirty(kpi.name)\"\n                      class=\"w-full border border-gray-300 rounded-md px-3 py-2 text-sm\"\n                    >\n                  </div>\n                  <div>\n                    <label class=\"block text-sm font-medium text-gray-700 mb-1\">Soglia Warning</label>\n                    <input\n                      type=\"number\"\n                      step=\"0.1\"\n                      v-model=\"kpiConfigs[kpi.name].warning_threshold\"\n                      @input=\"markKPIAsDirty(kpi.name)\"\n                      class=\"w-full border border-gray-300 rounded-md px-3 py-2 text-sm\"\n                    >\n                  </div>\n                </div>\n\n                <div class=\"mt-4\">\n                  <label class=\"block text-sm font-medium text-gray-700 mb-1\">Descrizione Personalizzata</label>\n                  <textarea\n                    v-model=\"kpiConfigs[kpi.name].custom_description\"\n                    @input=\"markKPIAsDirty(kpi.name)\"\n                    rows=\"2\"\n                    class=\"w-full border border-gray-300 rounded-md px-3 py-2 text-sm\"\n                    placeholder=\"Descrizione specifica per questo progetto...\"\n                  ></textarea>\n                </div>\n\n                <!-- Save Button per singolo KPI -->\n                <div class=\"mt-4 flex justify-end\">\n                  <button\n                    v-if=\"kpiConfigs[kpi.name]?.isDirty\"\n                    @click=\"saveKPIConfig(kpi.name)\"\n                    :disabled=\"savingKPI === kpi.name\"\n                    class=\"inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 disabled:opacity-50\"\n                  >\n                    <svg v-if=\"savingKPI === kpi.name\" class=\"animate-spin -ml-1 mr-2 h-4 w-4 text-white\" fill=\"none\" viewBox=\"0 0 24 24\">\n                      <circle class=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" stroke-width=\"4\"></circle>\n                      <path class=\"opacity-75\" fill=\"currentColor\" d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"></path>\n                    </svg>\n                    {{ savingKPI === kpi.name ? 'Salvataggio...' : 'Salva KPI' }}\n                  </button>\n                  <span v-else-if=\"kpiConfigs[kpi.name]?.isSaved\" class=\"text-sm text-green-600\">✓ Salvato</span>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          <!-- Modal Footer -->\n          <div class=\"mt-6 pt-4 border-t flex justify-between\">\n            <button\n              @click=\"resetAllKPIsToDefault\"\n              class=\"inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50\"\n            >\n              Reset Tutti\n            </button>\n            <div class=\"flex space-x-3\">\n              <button\n                @click=\"closeKPIConfigModal\"\n                class=\"inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50\"\n              >\n                Chiudi\n              </button>\n              <button\n                @click=\"saveAllKPIConfigs\"\n                :disabled=\"!hasUnsavedChanges\"\n                class=\"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 disabled:opacity-50\"\n              >\n                Salva Tutto\n              </button>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script setup>\nimport { ref, computed, onMounted, watch } from 'vue'\n\n// Props\nconst props = defineProps({\n  project: { type: Object, default: null },\n  loading: { type: Boolean, default: false }\n})\n\n// Emits\nconst emit = defineEmits(['refresh'])\n\n// Reactive data\nconst refreshing = ref(false)\nconst showKPIConfigModal = ref(false)\nconst savingKPI = ref(null)\nconst kpiConfigs = ref({})\nconst kpiData = ref({\n  totalHours: 0,\n  workDays: 0,\n  totalCosts: 0,\n  costVariance: 0,\n  potentialRevenue: 0,\n  actualRevenue: 0,\n  marginPercentage: 0\n})\n\nconst kpiThresholds = ref({\n  budget: 80,\n  time: 85,\n  margin: 15\n})\n\n// Computed properties\nconst budgetUsagePercentage = computed(() => {\n  if (!props.project?.budget || kpiData.value.totalCosts === 0) return 0\n  return Math.round((kpiData.value.totalCosts / props.project.budget) * 100)\n})\n\nconst timeUsagePercentage = computed(() => {\n  if (!props.project?.estimated_hours || kpiData.value.totalHours === 0) return 0\n  return Math.round((kpiData.value.totalHours / props.project.estimated_hours) * 100)\n})\n\nconst costVarianceClass = computed(() => {\n  const variance = kpiData.value.costVariance\n  if (variance > 0) return 'text-red-600'\n  if (variance < 0) return 'text-green-600'\n  return 'text-gray-600'\n})\n\nconst marginClass = computed(() => {\n  const margin = kpiData.value.marginPercentage\n  if (margin >= kpiThresholds.value.margin) return 'text-green-600'\n  if (margin >= kpiThresholds.value.margin * 0.7) return 'text-yellow-600'\n  return 'text-red-600'\n})\n\nconst marginStatus = computed(() => {\n  const margin = kpiData.value.marginPercentage\n  if (margin >= kpiThresholds.value.margin) return 'Ottimo'\n  if (margin >= kpiThresholds.value.margin * 0.7) return 'Accettabile'\n  return 'Critico'\n})\n\nconst budgetThresholdClass = computed(() => {\n  const usage = budgetUsagePercentage.value\n  if (usage >= kpiThresholds.value.budget) return 'text-red-600'\n  if (usage >= kpiThresholds.value.budget * 0.8) return 'text-yellow-600'\n  return 'text-green-600'\n})\n\nconst timeThresholdClass = computed(() => {\n  const usage = timeUsagePercentage.value\n  if (usage >= kpiThresholds.value.time) return 'text-red-600'\n  if (usage >= kpiThresholds.value.time * 0.8) return 'text-yellow-600'\n  return 'text-green-600'\n})\n\nconst marginThresholdClass = computed(() => {\n  const margin = kpiData.value.marginPercentage\n  if (margin >= kpiThresholds.value.margin) return 'text-green-600'\n  if (margin >= kpiThresholds.value.margin * 0.7) return 'text-yellow-600'\n  return 'text-red-600'\n})\n\n// Methods\nconst formatCurrency = (amount) => {\n  return new Intl.NumberFormat('it-IT', {\n    style: 'currency',\n    currency: 'EUR'\n  }).format(amount || 0)\n}\n\nconst formatHours = (hours) => {\n  if (!hours || hours === 0) return '0h'\n  return `${parseFloat(hours).toFixed(2)}h`\n}\n\nconst formatPercentage = (percentage) => {\n  return `${(percentage || 0).toFixed(1)}%`\n}\n\nconst loadKPIData = async () => {\n  if (!props.project?.id) return\n\n  // API endpoint non esiste ancora, usa sempre calcoli fallback\n  calculateFallbackKPIs()\n}\n\nconst calculateFallbackKPIs = () => {\n  // Calculate basic KPIs from project data\n  const project = props.project\n  if (!project) return\n\n  // Basic calculations (these would normally come from API)\n  kpiData.value = {\n    totalHours: project.total_hours || 0,\n    workDays: Math.ceil((project.total_hours || 0) / 8),\n    totalCosts: (project.total_hours || 0) * 50, // Assuming 50€/hour average\n    costVariance: ((project.total_hours || 0) * 50) - (project.budget || 0),\n    potentialRevenue: project.budget || 0,\n    actualRevenue: project.invoiced_amount || 0,\n    marginPercentage: project.budget ?\n      (((project.budget - ((project.total_hours || 0) * 50)) / project.budget) * 100) : 0\n  }\n}\n\nconst refreshKPIs = async () => {\n  refreshing.value = true\n  try {\n    await loadKPIData()\n    emit('refresh')\n  } catch (error) {\n    console.error('Error refreshing KPIs:', error)\n  } finally {\n    refreshing.value = false\n  }\n}\n\n// KPI Configuration Modal Functions\nconst availableKPIs = computed(() => {\n  const projectType = props.project?.project_type || 'service'\n  return getKPITemplatesForProjectType(projectType)\n})\n\nconst getKPITemplatesForProjectType = (projectType) => {\n  const templates = {\n    service: [\n      {\n        name: 'margin_percentage',\n        display_name: 'Margine Netto %',\n        description: 'Percentuale di margine netto sul fatturato',\n        unit: '%',\n        target_min: 25,\n        target_max: 40,\n        warning_threshold: 15\n      },\n      {\n        name: 'utilization_rate',\n        display_name: 'Tasso di Utilizzo %',\n        description: 'Percentuale di utilizzo del team rispetto alla capacità teorica',\n        unit: '%',\n        target_min: 75,\n        target_max: 85,\n        warning_threshold: 60\n      },\n      {\n        name: 'cost_per_hour',\n        display_name: 'Costo per Ora',\n        description: 'Costo medio per ora di lavoro, inclusi tutti i costi',\n        unit: '€',\n        target_min: 30,\n        target_max: 50,\n        warning_threshold: 60\n      },\n      {\n        name: 'cost_revenue_ratio',\n        display_name: 'Rapporto C/R',\n        description: 'Rapporto tra costi sostenuti e ricavi generati',\n        unit: 'ratio',\n        target_min: 0.6,\n        target_max: 0.75,\n        warning_threshold: 0.85\n      }\n    ]\n  }\n\n  return templates[projectType] || templates.service\n}\n\nconst getProjectTypeLabel = (projectType) => {\n  const labels = {\n    service: '🔧 Servizio',\n    license: '📄 Licenza',\n    consulting: '💼 Consulenza',\n    product: '📦 Prodotto',\n    rd: '🔬 R&D',\n    internal: '🏢 Interno'\n  }\n  return labels[projectType] || 'Sconosciuto'\n}\n\nconst openKPIConfigModal = () => {\n  // Inizializza configurazioni KPI con valori di default\n  const templates = availableKPIs.value\n  templates.forEach(kpi => {\n    if (!kpiConfigs.value[kpi.name]) {\n      kpiConfigs.value[kpi.name] = {\n        target_min: kpi.target_min,\n        target_max: kpi.target_max,\n        warning_threshold: kpi.warning_threshold,\n        custom_description: '',\n        isDirty: false,\n        isSaved: false\n      }\n    }\n  })\n\n  showKPIConfigModal.value = true\n}\n\nconst closeKPIConfigModal = () => {\n  showKPIConfigModal.value = false\n}\n\nconst markKPIAsDirty = (kpiName) => {\n  if (kpiConfigs.value[kpiName]) {\n    kpiConfigs.value[kpiName].isDirty = true\n    kpiConfigs.value[kpiName].isSaved = false\n  }\n}\n\nconst resetKPIToDefault = (kpiName) => {\n  const template = availableKPIs.value.find(kpi => kpi.name === kpiName)\n  if (template && kpiConfigs.value[kpiName]) {\n    kpiConfigs.value[kpiName].target_min = template.target_min\n    kpiConfigs.value[kpiName].target_max = template.target_max\n    kpiConfigs.value[kpiName].warning_threshold = template.warning_threshold\n    kpiConfigs.value[kpiName].custom_description = ''\n    kpiConfigs.value[kpiName].isDirty = true\n    kpiConfigs.value[kpiName].isSaved = false\n  }\n}\n\nconst resetAllKPIsToDefault = () => {\n  if (confirm('Sei sicuro di voler ripristinare tutti i KPI ai valori di default?')) {\n    availableKPIs.value.forEach(kpi => {\n      resetKPIToDefault(kpi.name)\n    })\n  }\n}\n\nconst saveKPIConfig = async (kpiName) => {\n  if (!kpiConfigs.value[kpiName]) return\n\n  savingKPI.value = kpiName\n\n  try {\n    // TODO: Implementare chiamata API per salvare configurazione KPI\n    const config = kpiConfigs.value[kpiName]\n\n    // Simula chiamata API\n    await new Promise(resolve => setTimeout(resolve, 1000))\n\n    console.log('Saving KPI config:', {\n      project_id: props.project?.id,\n      kpi_name: kpiName,\n      target_min: config.target_min,\n      target_max: config.target_max,\n      warning_threshold: config.warning_threshold,\n      custom_description: config.custom_description\n    })\n\n    kpiConfigs.value[kpiName].isDirty = false\n    kpiConfigs.value[kpiName].isSaved = true\n\n    // Auto-hide saved status after 3 seconds\n    setTimeout(() => {\n      if (kpiConfigs.value[kpiName]) {\n        kpiConfigs.value[kpiName].isSaved = false\n      }\n    }, 3000)\n\n  } catch (error) {\n    console.error('Error saving KPI config:', error)\n    alert('Errore nel salvataggio della configurazione KPI')\n  } finally {\n    savingKPI.value = null\n  }\n}\n\nconst saveAllKPIConfigs = async () => {\n  const dirtyKPIs = availableKPIs.value.filter(kpi => kpiConfigs.value[kpi.name]?.isDirty)\n\n  for (const kpi of dirtyKPIs) {\n    await saveKPIConfig(kpi.name)\n  }\n}\n\nconst hasUnsavedChanges = computed(() => {\n  return availableKPIs.value.some(kpi => kpiConfigs.value[kpi.name]?.isDirty)\n})\n\n// Watchers\nwatch(() => props.project, (newProject) => {\n  if (newProject) {\n    loadKPIData()\n  }\n}, { immediate: true })\n\n// Lifecycle\nonMounted(() => {\n  if (props.project) {\n    loadKPIData()\n  }\n})\n</script>"}