{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/views/projects/components/ProjectTeam.vue"}, "originalCode": "<template>\n  <div class=\"space-y-6\">\n    <div class=\"bg-white dark:bg-gray-800 shadow rounded-lg\">\n      <div class=\"px-6 py-4 border-b border-gray-200 dark:border-gray-700\">\n        <div class=\"flex items-center justify-between\">\n          <h3 class=\"text-lg font-medium text-gray-900 dark:text-white\">\n            Team del Progetto\n          </h3>\n          <button\n            @click=\"showAddMemberModal = true\"\n            class=\"inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500\"\n          >\n            <svg class=\"w-4 h-4 mr-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 6v6m0 0v6m0-6h6m-6 0H6\"></path>\n            </svg>\n            Aggiungi Membro\n          </button>\n        </div>\n      </div>\n\n      <!-- Team Statistics -->\n      <div class=\"p-6 border-b border-gray-200 dark:border-gray-700\">\n        <div class=\"grid grid-cols-1 md:grid-cols-4 gap-4\">\n          <div class=\"text-center\">\n            <div class=\"text-2xl font-bold text-primary-600 dark:text-primary-400\">{{ teamMembers.length }}</div>\n            <div class=\"text-sm text-gray-500 dark:text-gray-400\">Membri Totali</div>\n          </div>\n          <div class=\"text-center\">\n            <div class=\"text-2xl font-bold text-green-600\">{{ totalHoursWorked }}h</div>\n            <div class=\"text-sm text-gray-500 dark:text-gray-400\">Ore Totali</div>\n          </div>\n          <div class=\"text-center\">\n            <div class=\"text-2xl font-bold text-blue-600\">{{ averageHoursPerMember }}h</div>\n            <div class=\"text-sm text-gray-500 dark:text-gray-400\">Media per Membro</div>\n          </div>\n          <div class=\"text-center\">\n            <div class=\"text-2xl font-bold text-purple-600\">{{ activeMembersCount }}</div>\n            <div class=\"text-sm text-gray-500 dark:text-gray-400\">Membri Attivi</div>\n          </div>\n        </div>\n      </div>\n\n      <!-- Team Members List -->\n      <div class=\"p-6\">\n        <div class=\"space-y-4\">\n          <div \n            v-for=\"member in teamMembers\" \n            :key=\"member.id\" \n            class=\"border border-gray-200 dark:border-gray-700 rounded-lg p-4 hover:shadow-md transition-all duration-200\"\n          >\n            <div class=\"flex items-center justify-between\">\n              <div class=\"flex items-center space-x-4\">\n                <div class=\"flex-shrink-0\">\n                  <img\n                    v-if=\"member.profile_image\"\n                    :src=\"member.profile_image\"\n                    :alt=\"member.full_name\"\n                    class=\"w-12 h-12 rounded-full\"\n                  >\n                  <div v-else class=\"w-12 h-12 bg-gray-300 dark:bg-gray-600 rounded-full flex items-center justify-center\">\n                    <span class=\"text-sm font-medium text-gray-600 dark:text-gray-300\">{{ getInitials(member.full_name) }}</span>\n                  </div>\n                </div>\n                <div class=\"flex-1\">\n                  <div class=\"flex items-center space-x-2\">\n                    <h4 class=\"text-lg font-medium text-gray-900 dark:text-white\">{{ member.full_name }}</h4>\n                    <span \n                      v-if=\"member.id === project?.manager_id\"\n                      class=\"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200\"\n                    >\n                      Project Manager\n                    </span>\n                  </div>\n                  <p class=\"text-sm text-gray-600 dark:text-gray-400\">{{ member.role || 'Team Member' }}</p>\n                  <p class=\"text-xs text-gray-500 dark:text-gray-500\">{{ member.email }}</p>\n                </div>\n              </div>\n              \n              <div class=\"flex items-center space-x-4\">\n                <!-- Member stats -->\n                <div class=\"text-right\">\n                  <div class=\"text-sm font-medium text-gray-900 dark:text-white\">{{ formatHours(member.hours_worked || 0) }}h</div>\n                  <div class=\"text-xs text-gray-500 dark:text-gray-400\">ore lavorate</div>\n                </div>\n                <div class=\"text-right\">\n                  <div class=\"text-sm font-medium text-gray-900 dark:text-white\">{{ getAssignedTasksCount(member.id) }}</div>\n                  <div class=\"text-xs text-gray-500 dark:text-gray-400\">task assegnati</div>\n                </div>\n                <div class=\"text-right\">\n                  <div class=\"text-sm font-medium text-gray-900 dark:text-white\">{{ getCompletedTasksCount(member.id) }}</div>\n                  <div class=\"text-xs text-gray-500 dark:text-gray-400\">completati</div>\n                </div>\n                \n                <!-- Actions -->\n                <div class=\"flex items-center space-x-2\">\n                  <button\n                    @click=\"editMember(member)\"\n                    class=\"p-1 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300\"\n                    title=\"Modifica membro\"\n                  >\n                    <svg class=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                      <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z\"></path>\n                    </svg>\n                  </button>\n                  <button\n                    v-if=\"member.id !== project?.manager_id\"\n                    @click=\"removeMember(member)\"\n                    class=\"p-1 text-gray-400 hover:text-red-600\"\n                    title=\"Rimuovi dal progetto\"\n                  >\n                    <svg class=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                      <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16\"></path>\n                    </svg>\n                  </button>\n                </div>\n              </div>\n            </div>\n\n            <!-- Productivity meter -->\n            <div class=\"mt-4\">\n              <div class=\"flex items-center justify-between text-xs text-gray-500 dark:text-gray-400 mb-1\">\n                <span>Produttività</span>\n                <span>{{ getProductivityPercentage(member.id) }}%</span>\n              </div>\n              <div class=\"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2\">\n                <div \n                  class=\"h-2 rounded-full transition-all duration-300\"\n                  :class=\"getProductivityColor(member.id)\"\n                  :style=\"{ width: getProductivityPercentage(member.id) + '%' }\"\n                ></div>\n              </div>\n            </div>\n          </div>\n\n          <!-- Empty state -->\n          <div v-if=\"teamMembers.length === 0\" class=\"text-center py-8\">\n            <svg class=\"mx-auto h-12 w-12 text-gray-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z\"></path>\n            </svg>\n            <h3 class=\"mt-2 text-sm font-medium text-gray-900 dark:text-white\">Nessun membro del team</h3>\n            <p class=\"mt-1 text-sm text-gray-500 dark:text-gray-400\">Inizia aggiungendo membri al progetto.</p>\n            <div class=\"mt-6\">\n              <button\n                @click=\"showAddMemberModal = true\"\n                class=\"inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700\"\n              >\n                <svg class=\"-ml-1 mr-2 h-5 w-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 6v6m0 0v6m0-6h6m-6 0H6\"></path>\n                </svg>\n                Aggiungi primo membro\n              </button>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- Add Member Modal -->\n    <div v-if=\"showAddMemberModal\" class=\"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50\" @click=\"closeAddMemberModal\">\n      <div class=\"relative top-20 mx-auto p-5 border w-[500px] shadow-lg rounded-md bg-white dark:bg-gray-800\" @click.stop>\n        <div class=\"mt-3\">\n          <h3 class=\"text-lg font-medium text-gray-900 dark:text-white mb-4\">\n            Aggiungi Membro al Team\n          </h3>\n          \n          <form @submit.prevent=\"addMember\">\n            <div class=\"space-y-4\">\n              <div>\n                <label class=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">Utente</label>\n                <select \n                  v-model=\"newMemberForm.user_id\"\n                  required\n                  class=\"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500\"\n                >\n                  <option value=\"\">Seleziona utente</option>\n                  <option \n                    v-for=\"user in availableUsers\" \n                    :key=\"user.id\" \n                    :value=\"user.id\"\n                  >\n                    {{ user.full_name }} ({{ user.email }})\n                  </option>\n                </select>\n              </div>\n\n              <div>\n                <label class=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">Ruolo</label>\n                <select \n                  v-model=\"newMemberForm.role\"\n                  required\n                  class=\"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500\"\n                >\n                  <option value=\"\">Seleziona ruolo</option>\n                  <option value=\"Team Member\">Team Member</option>\n                  <option value=\"Developer\">Developer</option>\n                  <option value=\"Designer\">Designer</option>\n                  <option value=\"QA Tester\">QA Tester</option>\n                  <option value=\"Business Analyst\">Business Analyst</option>\n                  <option value=\"Technical Lead\">Technical Lead</option>\n                </select>\n              </div>\n            </div>\n\n            <div class=\"flex justify-end space-x-3 mt-6\">\n              <button \n                type=\"button\" \n                @click=\"closeAddMemberModal\"\n                class=\"px-4 py-2 text-sm font-medium text-gray-700 bg-gray-200 hover:bg-gray-300 rounded-md\"\n              >\n                Annulla\n              </button>\n              <button \n                type=\"submit\"\n                :disabled=\"adding\"\n                class=\"px-4 py-2 text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 rounded-md disabled:opacity-50\"\n              >\n                {{ adding ? 'Aggiungendo...' : 'Aggiungi' }}\n              </button>\n            </div>\n          </form>\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script setup>\nimport { ref, computed, onMounted, watch } from 'vue'\nimport { useAuthStore } from '@/stores/auth'\n\nconst props = defineProps({\n  project: {\n    type: Object,\n    default: null\n  },\n  loading: {\n    type: Boolean,\n    default: false\n  }\n})\n\nconst authStore = useAuthStore()\n\n// State\nconst showAddMemberModal = ref(false)\nconst availableUsers = ref([])\nconst adding = ref(false)\nconst newMemberForm = ref({\n  user_id: '',\n  role: ''\n})\n\n// Computed\nconst teamMembers = computed(() => {\n  return props.project?.team_members || []\n})\n\nconst totalHoursWorked = computed(() => {\n  return teamMembers.value.reduce((total, member) => total + (member.hours_worked || 0), 0)\n})\n\nconst averageHoursPerMember = computed(() => {\n  if (teamMembers.value.length === 0) return 0\n  return Math.round(totalHoursWorked.value / teamMembers.value.length)\n})\n\nconst activeMembersCount = computed(() => {\n  return teamMembers.value.filter(member => (member.hours_worked || 0) > 0).length\n})\n\n// Methods\nconst getInitials = (fullName) => {\n  if (!fullName) return '??'\n  return fullName\n    .split(' ')\n    .map(name => name.charAt(0).toUpperCase())\n    .slice(0, 2)\n    .join('')\n}\n\nconst getAssignedTasksCount = (memberId) => {\n  const tasks = props.project?.tasks || []\n  return tasks.filter(task => task.assignee_id === memberId).length\n}\n\nconst getCompletedTasksCount = (memberId) => {\n  const tasks = props.project?.tasks || []\n  return tasks.filter(task => task.assignee_id === memberId && task.status === 'done').length\n}\n\nconst getProductivityPercentage = (memberId) => {\n  const assigned = getAssignedTasksCount(memberId)\n  const completed = getCompletedTasksCount(memberId)\n  if (assigned === 0) return 0\n  return Math.round((completed / assigned) * 100)\n}\n\nconst getProductivityColor = (memberId) => {\n  const percentage = getProductivityPercentage(memberId)\n  if (percentage >= 80) return 'bg-green-600'\n  if (percentage >= 60) return 'bg-yellow-600'\n  if (percentage >= 40) return 'bg-orange-600'\n  return 'bg-red-600'\n}\n\nconst formatHours = (hours) => {\n  if (!hours || hours === 0) return '0.00'\n  return parseFloat(hours).toFixed(2)\n}\n\nconst loadAvailableUsers = async () => {\n  try {\n    const response = await fetch('/api/personnel/', {\n      headers: {\n        'Content-Type': 'application/json',\n        'X-CSRFToken': authStore.csrfToken\n      }\n    })\n\n    if (response.ok) {\n      const result = await response.json()\n      // Filtra utenti già nel team\n      const currentMemberIds = teamMembers.value.map(m => m.id)\n      availableUsers.value = result.data ? result.data.filter(user => !currentMemberIds.includes(user.id)) : []\n    }\n  } catch (error) {\n    console.error('Errore nel caricamento utenti:', error)\n    availableUsers.value = []\n  }\n}\n\nconst addMember = async () => {\n  adding.value = true\n  \n  try {\n    const response = await fetch(`/api/projects/${props.project.id}/team`, {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json',\n        'X-CSRFToken': authStore.csrfToken\n      },\n      body: JSON.stringify(newMemberForm.value)\n    })\n\n    if (response.ok) {\n      // Ricarica progetto per aggiornare team\n      emit('refresh')\n      closeAddMemberModal()\n    } else {\n      const error = await response.json()\n      alert(error.message || 'Errore nell\\'aggiunta del membro')\n    }\n  } catch (error) {\n    alert('Errore nell\\'aggiunta del membro')\n  } finally {\n    adding.value = false\n  }\n}\n\nconst editMember = (member) => {\n  // TODO: Implementare modifica membro\n  console.log('Edit member:', member)\n}\n\nconst removeMember = async (member) => {\n  if (!confirm(`Rimuovere ${member.full_name} dal progetto?`)) return\n  \n  try {\n    const response = await fetch(`/api/projects/${props.project.id}/team/${member.id}`, {\n      method: 'DELETE',\n      headers: {\n        'Content-Type': 'application/json',\n        'X-CSRFToken': authStore.csrfToken\n      }\n    })\n\n    if (response.ok) {\n      emit('refresh')\n    } else {\n      const error = await response.json()\n      alert(error.message || 'Errore nella rimozione del membro')\n    }\n  } catch (error) {\n    alert('Errore nella rimozione del membro')\n  }\n}\n\nconst closeAddMemberModal = () => {\n  showAddMemberModal.value = false\n  newMemberForm.value = {\n    user_id: '',\n    role: ''\n  }\n}\n\n// Events\nconst emit = defineEmits(['refresh'])\n\n// Lifecycle\nonMounted(() => {\n  loadAvailableUsers()\n})\n\n// Watchers\nwatch(() => showAddMemberModal.value, (newVal) => {\n  if (newVal) {\n    loadAvailableUsers()\n  }\n})\n\nwatch(() => props.project?.team_members, () => {\n  if (showAddMemberModal.value) {\n    loadAvailableUsers()\n  }\n})\n\n// Expose methods\ndefineExpose({\n  refresh: loadAvailableUsers\n})\n</script>", "modifiedCode": "<template>\n  <div class=\"space-y-6\">\n    <div class=\"bg-white dark:bg-gray-800 shadow rounded-lg\">\n      <div class=\"px-6 py-4 border-b border-gray-200 dark:border-gray-700\">\n        <div class=\"flex items-center justify-between\">\n          <h3 class=\"text-lg font-medium text-gray-900 dark:text-white\">\n            Team del Progetto\n          </h3>\n          <button\n            @click=\"showAddMemberModal = true\"\n            class=\"inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500\"\n          >\n            <svg class=\"w-4 h-4 mr-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 6v6m0 0v6m0-6h6m-6 0H6\"></path>\n            </svg>\n            Aggiungi Membro\n          </button>\n        </div>\n      </div>\n\n      <!-- Team Statistics -->\n      <div class=\"p-6 border-b border-gray-200 dark:border-gray-700\">\n        <div class=\"grid grid-cols-1 md:grid-cols-4 gap-4\">\n          <div class=\"text-center\">\n            <div class=\"text-2xl font-bold text-primary-600 dark:text-primary-400\">{{ teamMembers.length }}</div>\n            <div class=\"text-sm text-gray-500 dark:text-gray-400\">Membri Totali</div>\n          </div>\n          <div class=\"text-center\">\n            <div class=\"text-2xl font-bold text-green-600\">{{ totalHoursWorked }}h</div>\n            <div class=\"text-sm text-gray-500 dark:text-gray-400\">Ore Totali</div>\n          </div>\n          <div class=\"text-center\">\n            <div class=\"text-2xl font-bold text-blue-600\">{{ averageHoursPerMember }}h</div>\n            <div class=\"text-sm text-gray-500 dark:text-gray-400\">Media per Membro</div>\n          </div>\n          <div class=\"text-center\">\n            <div class=\"text-2xl font-bold text-purple-600\">{{ activeMembersCount }}</div>\n            <div class=\"text-sm text-gray-500 dark:text-gray-400\">Membri Attivi</div>\n          </div>\n        </div>\n      </div>\n\n      <!-- Team Members List -->\n      <div class=\"p-6\">\n        <div class=\"space-y-4\">\n          <div \n            v-for=\"member in teamMembers\" \n            :key=\"member.id\" \n            class=\"border border-gray-200 dark:border-gray-700 rounded-lg p-4 hover:shadow-md transition-all duration-200\"\n          >\n            <div class=\"flex items-center justify-between\">\n              <div class=\"flex items-center space-x-4\">\n                <div class=\"flex-shrink-0\">\n                  <img\n                    v-if=\"member.profile_image\"\n                    :src=\"member.profile_image\"\n                    :alt=\"member.full_name\"\n                    class=\"w-12 h-12 rounded-full\"\n                  >\n                  <div v-else class=\"w-12 h-12 bg-gray-300 dark:bg-gray-600 rounded-full flex items-center justify-center\">\n                    <span class=\"text-sm font-medium text-gray-600 dark:text-gray-300\">{{ getInitials(member.full_name) }}</span>\n                  </div>\n                </div>\n                <div class=\"flex-1\">\n                  <div class=\"flex items-center space-x-2\">\n                    <h4 class=\"text-lg font-medium text-gray-900 dark:text-white\">{{ member.full_name }}</h4>\n                    <span \n                      v-if=\"member.id === project?.manager_id\"\n                      class=\"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200\"\n                    >\n                      Project Manager\n                    </span>\n                  </div>\n                  <p class=\"text-sm text-gray-600 dark:text-gray-400\">{{ member.role || 'Team Member' }}</p>\n                  <p class=\"text-xs text-gray-500 dark:text-gray-500\">{{ member.email }}</p>\n                </div>\n              </div>\n              \n              <div class=\"flex items-center space-x-4\">\n                <!-- Member stats -->\n                <div class=\"text-right\">\n                  <div class=\"text-sm font-medium text-gray-900 dark:text-white\">{{ formatHours(member.hours_worked || 0) }}h</div>\n                  <div class=\"text-xs text-gray-500 dark:text-gray-400\">ore lavorate</div>\n                </div>\n                <div class=\"text-right\">\n                  <div class=\"text-sm font-medium text-gray-900 dark:text-white\">{{ getAssignedTasksCount(member.id) }}</div>\n                  <div class=\"text-xs text-gray-500 dark:text-gray-400\">task assegnati</div>\n                </div>\n                <div class=\"text-right\">\n                  <div class=\"text-sm font-medium text-gray-900 dark:text-white\">{{ getCompletedTasksCount(member.id) }}</div>\n                  <div class=\"text-xs text-gray-500 dark:text-gray-400\">completati</div>\n                </div>\n                \n                <!-- Actions -->\n                <div class=\"flex items-center space-x-2\">\n                  <button\n                    @click=\"editMember(member)\"\n                    class=\"p-1 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300\"\n                    title=\"Modifica membro\"\n                  >\n                    <svg class=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                      <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z\"></path>\n                    </svg>\n                  </button>\n                  <button\n                    v-if=\"member.id !== project?.manager_id\"\n                    @click=\"removeMember(member)\"\n                    class=\"p-1 text-gray-400 hover:text-red-600\"\n                    title=\"Rimuovi dal progetto\"\n                  >\n                    <svg class=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                      <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16\"></path>\n                    </svg>\n                  </button>\n                </div>\n              </div>\n            </div>\n\n            <!-- Productivity meter -->\n            <div class=\"mt-4\">\n              <div class=\"flex items-center justify-between text-xs text-gray-500 dark:text-gray-400 mb-1\">\n                <span>Produttività</span>\n                <span>{{ getProductivityPercentage(member.id) }}%</span>\n              </div>\n              <div class=\"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2\">\n                <div \n                  class=\"h-2 rounded-full transition-all duration-300\"\n                  :class=\"getProductivityColor(member.id)\"\n                  :style=\"{ width: getProductivityPercentage(member.id) + '%' }\"\n                ></div>\n              </div>\n            </div>\n          </div>\n\n          <!-- Empty state -->\n          <div v-if=\"teamMembers.length === 0\" class=\"text-center py-8\">\n            <svg class=\"mx-auto h-12 w-12 text-gray-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z\"></path>\n            </svg>\n            <h3 class=\"mt-2 text-sm font-medium text-gray-900 dark:text-white\">Nessun membro del team</h3>\n            <p class=\"mt-1 text-sm text-gray-500 dark:text-gray-400\">Inizia aggiungendo membri al progetto.</p>\n            <div class=\"mt-6\">\n              <button\n                @click=\"showAddMemberModal = true\"\n                class=\"inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700\"\n              >\n                <svg class=\"-ml-1 mr-2 h-5 w-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 6v6m0 0v6m0-6h6m-6 0H6\"></path>\n                </svg>\n                Aggiungi primo membro\n              </button>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- Add Member Modal -->\n    <div v-if=\"showAddMemberModal\" class=\"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50\" @click=\"closeAddMemberModal\">\n      <div class=\"relative top-20 mx-auto p-5 border w-[500px] shadow-lg rounded-md bg-white dark:bg-gray-800\" @click.stop>\n        <div class=\"mt-3\">\n          <h3 class=\"text-lg font-medium text-gray-900 dark:text-white mb-4\">\n            Aggiungi Membro al Team\n          </h3>\n          \n          <form @submit.prevent=\"addMember\">\n            <div class=\"space-y-4\">\n              <div>\n                <label class=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">Utente</label>\n                <select \n                  v-model=\"newMemberForm.user_id\"\n                  required\n                  class=\"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500\"\n                >\n                  <option value=\"\">Seleziona utente</option>\n                  <option \n                    v-for=\"user in availableUsers\" \n                    :key=\"user.id\" \n                    :value=\"user.id\"\n                  >\n                    {{ user.full_name }} ({{ user.email }})\n                  </option>\n                </select>\n              </div>\n\n              <div>\n                <label class=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">Ruolo</label>\n                <select \n                  v-model=\"newMemberForm.role\"\n                  required\n                  class=\"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500\"\n                >\n                  <option value=\"\">Seleziona ruolo</option>\n                  <option value=\"Team Member\">Team Member</option>\n                  <option value=\"Developer\">Developer</option>\n                  <option value=\"Designer\">Designer</option>\n                  <option value=\"QA Tester\">QA Tester</option>\n                  <option value=\"Business Analyst\">Business Analyst</option>\n                  <option value=\"Technical Lead\">Technical Lead</option>\n                </select>\n              </div>\n            </div>\n\n            <div class=\"flex justify-end space-x-3 mt-6\">\n              <button \n                type=\"button\" \n                @click=\"closeAddMemberModal\"\n                class=\"px-4 py-2 text-sm font-medium text-gray-700 bg-gray-200 hover:bg-gray-300 rounded-md\"\n              >\n                Annulla\n              </button>\n              <button \n                type=\"submit\"\n                :disabled=\"adding\"\n                class=\"px-4 py-2 text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 rounded-md disabled:opacity-50\"\n              >\n                {{ adding ? 'Aggiungendo...' : 'Aggiungi' }}\n              </button>\n            </div>\n          </form>\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script setup>\nimport { ref, computed, onMounted, watch } from 'vue'\nimport { useAuthStore } from '@/stores/auth'\n\nconst props = defineProps({\n  project: {\n    type: Object,\n    default: null\n  },\n  loading: {\n    type: Boolean,\n    default: false\n  }\n})\n\nconst authStore = useAuthStore()\n\n// State\nconst showAddMemberModal = ref(false)\nconst availableUsers = ref([])\nconst adding = ref(false)\nconst newMemberForm = ref({\n  user_id: '',\n  role: ''\n})\n\n// Computed\nconst teamMembers = computed(() => {\n  return props.project?.team_members || []\n})\n\nconst totalHoursWorked = computed(() => {\n  return teamMembers.value.reduce((total, member) => total + (member.hours_worked || 0), 0)\n})\n\nconst averageHoursPerMember = computed(() => {\n  if (teamMembers.value.length === 0) return 0\n  return Math.round(totalHoursWorked.value / teamMembers.value.length)\n})\n\nconst activeMembersCount = computed(() => {\n  return teamMembers.value.filter(member => (member.hours_worked || 0) > 0).length\n})\n\n// Methods\nconst getInitials = (fullName) => {\n  if (!fullName) return '??'\n  return fullName\n    .split(' ')\n    .map(name => name.charAt(0).toUpperCase())\n    .slice(0, 2)\n    .join('')\n}\n\nconst getAssignedTasksCount = (memberId) => {\n  const tasks = props.project?.tasks || []\n  return tasks.filter(task => task.assignee_id === memberId).length\n}\n\nconst getCompletedTasksCount = (memberId) => {\n  const tasks = props.project?.tasks || []\n  return tasks.filter(task => task.assignee_id === memberId && task.status === 'done').length\n}\n\nconst getProductivityPercentage = (memberId) => {\n  const assigned = getAssignedTasksCount(memberId)\n  const completed = getCompletedTasksCount(memberId)\n  if (assigned === 0) return 0\n  return Math.round((completed / assigned) * 100)\n}\n\nconst getProductivityColor = (memberId) => {\n  const percentage = getProductivityPercentage(memberId)\n  if (percentage >= 80) return 'bg-green-600'\n  if (percentage >= 60) return 'bg-yellow-600'\n  if (percentage >= 40) return 'bg-orange-600'\n  return 'bg-red-600'\n}\n\nconst formatHours = (hours) => {\n  if (!hours || hours === 0) return '0.00'\n  return parseFloat(hours).toFixed(2)\n}\n\nconst loadAvailableUsers = async () => {\n  try {\n    const response = await fetch('/api/personnel/', {\n      headers: {\n        'Content-Type': 'application/json',\n        'X-CSRFToken': authStore.csrfToken\n      }\n    })\n\n    if (response.ok) {\n      const result = await response.json()\n      // Filtra utenti già nel team\n      const currentMemberIds = teamMembers.value.map(m => m.id)\n      availableUsers.value = result.data ? result.data.filter(user => !currentMemberIds.includes(user.id)) : []\n    }\n  } catch (error) {\n    console.error('Errore nel caricamento utenti:', error)\n    availableUsers.value = []\n  }\n}\n\nconst addMember = async () => {\n  adding.value = true\n  \n  try {\n    const response = await fetch(`/api/projects/${props.project.id}/team`, {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json',\n        'X-CSRFToken': authStore.csrfToken\n      },\n      body: JSON.stringify(newMemberForm.value)\n    })\n\n    if (response.ok) {\n      // Ricarica progetto per aggiornare team\n      emit('refresh')\n      closeAddMemberModal()\n    } else {\n      const error = await response.json()\n      alert(error.message || 'Errore nell\\'aggiunta del membro')\n    }\n  } catch (error) {\n    alert('Errore nell\\'aggiunta del membro')\n  } finally {\n    adding.value = false\n  }\n}\n\nconst editMember = (member) => {\n  // TODO: Implementare modifica membro\n  console.log('Edit member:', member)\n}\n\nconst removeMember = async (member) => {\n  if (!confirm(`Rimuovere ${member.full_name} dal progetto?`)) return\n  \n  try {\n    const response = await fetch(`/api/projects/${props.project.id}/team/${member.id}`, {\n      method: 'DELETE',\n      headers: {\n        'Content-Type': 'application/json',\n        'X-CSRFToken': authStore.csrfToken\n      }\n    })\n\n    if (response.ok) {\n      emit('refresh')\n    } else {\n      const error = await response.json()\n      alert(error.message || 'Errore nella rimozione del membro')\n    }\n  } catch (error) {\n    alert('Errore nella rimozione del membro')\n  }\n}\n\nconst closeAddMemberModal = () => {\n  showAddMemberModal.value = false\n  newMemberForm.value = {\n    user_id: '',\n    role: ''\n  }\n}\n\n// Events\nconst emit = defineEmits(['refresh'])\n\n// Lifecycle\nonMounted(() => {\n  loadAvailableUsers()\n})\n\n// Watchers\nwatch(() => showAddMemberModal.value, (newVal) => {\n  if (newVal) {\n    loadAvailableUsers()\n  }\n})\n\nwatch(() => props.project?.team_members, () => {\n  if (showAddMemberModal.value) {\n    loadAvailableUsers()\n  }\n})\n\n// Expose methods\ndefineExpose({\n  refresh: loadAvailableUsers\n})\n</script>"}