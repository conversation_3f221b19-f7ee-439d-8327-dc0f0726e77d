{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/views/user/Profile.vue"}, "modifiedCode": "<template>\n  <div class=\"max-w-4xl mx-auto\">\n    <div class=\"bg-white shadow rounded-lg\">\n      <div class=\"px-6 py-4 border-b border-gray-200\">\n        <h1 class=\"text-xl font-semibold text-gray-900\">Il tuo Profilo</h1>\n        <p class=\"mt-1 text-sm text-gray-600\">Gestisci le informazioni del tuo account</p>\n      </div>\n\n      <div v-if=\"loading\" class=\"flex items-center justify-center h-64\">\n        <div class=\"animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600\"></div>\n      </div>\n\n      <form v-else @submit.prevent=\"saveProfile\" class=\"p-6 space-y-6\">\n        <!-- Avatar e Info Base -->\n        <div class=\"flex items-center space-x-6\">\n          <div class=\"flex-shrink-0\">\n            <div class=\"h-20 w-20 rounded-full bg-primary-100 flex items-center justify-center\">\n              <span class=\"text-2xl font-medium text-primary-700\">\n                {{ userInitials }}\n              </span>\n            </div>\n          </div>\n          <div class=\"flex-1\">\n            <h3 class=\"text-lg font-medium text-gray-900\">{{ user?.username }}</h3>\n            <p class=\"text-sm text-gray-500\">{{ user?.email }}</p>\n            <p class=\"text-sm text-gray-500\">Ruolo: {{ roleLabel }}</p>\n          </div>\n        </div>\n\n        <!-- Informazioni Personali -->\n        <div class=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n          <div>\n            <label for=\"first_name\" class=\"block text-sm font-medium text-gray-700\">Nome</label>\n            <input\n              id=\"first_name\"\n              v-model=\"form.first_name\"\n              type=\"text\"\n              class=\"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500\"\n            />\n          </div>\n          <div>\n            <label for=\"last_name\" class=\"block text-sm font-medium text-gray-700\">Cognome</label>\n            <input\n              id=\"last_name\"\n              v-model=\"form.last_name\"\n              type=\"text\"\n              class=\"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500\"\n            />\n          </div>\n        </div>\n\n        <!-- Email e Telefono -->\n        <div class=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n          <div>\n            <label for=\"email\" class=\"block text-sm font-medium text-gray-700\">Email</label>\n            <input\n              id=\"email\"\n              v-model=\"form.email\"\n              type=\"email\"\n              class=\"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500\"\n            />\n          </div>\n          <div>\n            <label for=\"phone\" class=\"block text-sm font-medium text-gray-700\">Telefono</label>\n            <input\n              id=\"phone\"\n              v-model=\"form.phone\"\n              type=\"tel\"\n              class=\"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500\"\n            />\n          </div>\n        </div>\n\n        <!-- Posizione e Dipartimento -->\n        <div class=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n          <div>\n            <label for=\"position\" class=\"block text-sm font-medium text-gray-700\">Posizione</label>\n            <input\n              id=\"position\"\n              v-model=\"form.position\"\n              type=\"text\"\n              class=\"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500\"\n            />\n          </div>\n          <div>\n            <label for=\"department\" class=\"block text-sm font-medium text-gray-700\">Dipartimento</label>\n            <input\n              id=\"department\"\n              v-model=\"form.department\"\n              type=\"text\"\n              readonly\n              class=\"mt-1 block w-full rounded-md border-gray-300 bg-gray-50 shadow-sm\"\n            />\n          </div>\n        </div>\n\n        <!-- Bio -->\n        <div>\n          <label for=\"bio\" class=\"block text-sm font-medium text-gray-700\">Bio</label>\n          <textarea\n            id=\"bio\"\n            v-model=\"form.bio\"\n            rows=\"3\"\n            class=\"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500\"\n            placeholder=\"Racconta qualcosa di te...\"\n          ></textarea>\n        </div>\n\n        <!-- Preferenze -->\n        <div class=\"border-t border-gray-200 pt-6\">\n          <h3 class=\"text-lg font-medium text-gray-900 mb-4\">Preferenze</h3>\n          <div class=\"flex items-center\">\n            <input\n              id=\"dark_mode\"\n              v-model=\"form.dark_mode\"\n              type=\"checkbox\"\n              class=\"h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded\"\n            />\n            <label for=\"dark_mode\" class=\"ml-2 block text-sm text-gray-900\">\n              Modalità scura\n            </label>\n          </div>\n        </div>\n\n        <!-- Azioni -->\n        <div class=\"flex justify-end space-x-3 pt-6 border-t border-gray-200\">\n          <button\n            type=\"button\"\n            @click=\"resetForm\"\n            class=\"px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500\"\n          >\n            Ripristina\n          </button>\n          <button\n            type=\"submit\"\n            :disabled=\"saving\"\n            class=\"px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50\"\n          >\n            {{ saving ? 'Salvataggio...' : 'Salva Profilo' }}\n          </button>\n        </div>\n      </form>\n    </div>\n  </div>\n</template>\n\n<script setup>\nimport { ref, computed, onMounted } from 'vue'\nimport { useAuthStore } from '@/stores/auth'\n\nconst authStore = useAuthStore()\n\n// State\nconst loading = ref(true)\nconst saving = ref(false)\nconst user = ref(null)\nconst form = ref({\n  first_name: '',\n  last_name: '',\n  email: '',\n  phone: '',\n  position: '',\n  department: '',\n  bio: '',\n  dark_mode: false\n})\n\n// Computed\nconst userInitials = computed(() => {\n  if (!user.value) return 'U'\n  const firstName = user.value.first_name || ''\n  const lastName = user.value.last_name || ''\n  return (firstName.charAt(0) + lastName.charAt(0)).toUpperCase() || user.value.username?.charAt(0).toUpperCase() || 'U'\n})\n\nconst roleLabel = computed(() => {\n  const roles = {\n    admin: 'Amministratore',\n    manager: 'Manager',\n    employee: 'Dipendente',\n    human_resources: 'Risorse Umane',\n    sales: 'Vendite'\n  }\n  return roles[user.value?.role] || user.value?.role || 'Utente'\n})\n\n// Methods\nconst loadProfile = async () => {\n  try {\n    // Usa i dati dell'authStore se disponibili\n    if (authStore.user) {\n      user.value = authStore.user\n      populateForm()\n    } else {\n      // Altrimenti carica dal server\n      const response = await fetch('/api/auth/profile', {\n        headers: {\n          'Content-Type': 'application/json',\n          'X-CSRFToken': authStore.csrfToken\n        }\n      })\n      \n      if (response.ok) {\n        const result = await response.json()\n        user.value = result.data.user\n        populateForm()\n      }\n    }\n  } catch (error) {\n    console.error('Error loading profile:', error)\n  } finally {\n    loading.value = false\n  }\n}\n\nconst populateForm = () => {\n  if (user.value) {\n    form.value = {\n      first_name: user.value.first_name || '',\n      last_name: user.value.last_name || '',\n      email: user.value.email || '',\n      phone: user.value.phone || '',\n      position: user.value.position || '',\n      department: user.value.department || '',\n      bio: user.value.bio || '',\n      dark_mode: user.value.dark_mode || false\n    }\n  }\n}\n\nconst resetForm = () => {\n  populateForm()\n}\n\nconst saveProfile = async () => {\n  saving.value = true\n  \n  try {\n    const response = await fetch('/api/auth/profile', {\n      method: 'PUT',\n      headers: {\n        'Content-Type': 'application/json',\n        'X-CSRFToken': authStore.csrfToken\n      },\n      body: JSON.stringify(form.value)\n    })\n\n    if (!response.ok) {\n      throw new Error('Errore nel salvataggio del profilo')\n    }\n\n    const result = await response.json()\n    user.value = result.data.user\n    \n    // Aggiorna anche l'authStore\n    await authStore.refreshUser()\n    \n    alert('Profilo aggiornato con successo!')\n  } catch (error) {\n    console.error('Error saving profile:', error)\n    alert('Errore nel salvataggio del profilo')\n  } finally {\n    saving.value = false\n  }\n}\n\n// Lifecycle\nonMounted(() => {\n  loadProfile()\n})\n</script>\n"}