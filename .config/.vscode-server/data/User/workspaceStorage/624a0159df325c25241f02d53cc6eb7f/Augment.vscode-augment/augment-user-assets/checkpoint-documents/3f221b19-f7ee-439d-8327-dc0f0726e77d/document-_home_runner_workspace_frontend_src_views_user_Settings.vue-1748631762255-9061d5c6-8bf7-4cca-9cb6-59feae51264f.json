{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/views/user/Settings.vue"}, "modifiedCode": "<template>\n  <div class=\"max-w-4xl mx-auto space-y-6\">\n    <!-- Impostazioni Account -->\n    <div class=\"bg-white shadow rounded-lg\">\n      <div class=\"px-6 py-4 border-b border-gray-200\">\n        <h2 class=\"text-lg font-semibold text-gray-900\">Impostazioni Account</h2>\n        <p class=\"mt-1 text-sm text-gray-600\">Gestisci le impostazioni del tuo account</p>\n      </div>\n\n      <div class=\"p-6 space-y-6\">\n        <!-- Cambio Password -->\n        <div>\n          <h3 class=\"text-md font-medium text-gray-900 mb-4\">Cambia Password</h3>\n          <form @submit.prevent=\"changePassword\" class=\"space-y-4\">\n            <div>\n              <label for=\"current_password\" class=\"block text-sm font-medium text-gray-700\">Password Attuale</label>\n              <input\n                id=\"current_password\"\n                v-model=\"passwordForm.current_password\"\n                type=\"password\"\n                required\n                class=\"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500\"\n              />\n            </div>\n            <div>\n              <label for=\"new_password\" class=\"block text-sm font-medium text-gray-700\">Nuova Password</label>\n              <input\n                id=\"new_password\"\n                v-model=\"passwordForm.new_password\"\n                type=\"password\"\n                required\n                minlength=\"8\"\n                class=\"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500\"\n              />\n            </div>\n            <div>\n              <label for=\"confirm_password\" class=\"block text-sm font-medium text-gray-700\">Conferma Password</label>\n              <input\n                id=\"confirm_password\"\n                v-model=\"passwordForm.confirm_password\"\n                type=\"password\"\n                required\n                class=\"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500\"\n              />\n            </div>\n            <button\n              type=\"submit\"\n              :disabled=\"changingPassword\"\n              class=\"px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50\"\n            >\n              {{ changingPassword ? 'Aggiornamento...' : 'Cambia Password' }}\n            </button>\n          </form>\n        </div>\n      </div>\n    </div>\n\n    <!-- Preferenze Interfaccia -->\n    <div class=\"bg-white shadow rounded-lg\">\n      <div class=\"px-6 py-4 border-b border-gray-200\">\n        <h2 class=\"text-lg font-semibold text-gray-900\">Preferenze Interfaccia</h2>\n        <p class=\"mt-1 text-sm text-gray-600\">Personalizza l'aspetto dell'applicazione</p>\n      </div>\n\n      <div class=\"p-6 space-y-6\">\n        <!-- Tema -->\n        <div>\n          <h3 class=\"text-md font-medium text-gray-900 mb-4\">Tema</h3>\n          <div class=\"space-y-3\">\n            <div class=\"flex items-center\">\n              <input\n                id=\"theme_light\"\n                v-model=\"preferences.theme\"\n                type=\"radio\"\n                value=\"light\"\n                class=\"h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300\"\n              />\n              <label for=\"theme_light\" class=\"ml-2 block text-sm text-gray-900\">\n                🌞 Modalità Chiara\n              </label>\n            </div>\n            <div class=\"flex items-center\">\n              <input\n                id=\"theme_dark\"\n                v-model=\"preferences.theme\"\n                type=\"radio\"\n                value=\"dark\"\n                class=\"h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300\"\n              />\n              <label for=\"theme_dark\" class=\"ml-2 block text-sm text-gray-900\">\n                🌙 Modalità Scura\n              </label>\n            </div>\n            <div class=\"flex items-center\">\n              <input\n                id=\"theme_auto\"\n                v-model=\"preferences.theme\"\n                type=\"radio\"\n                value=\"auto\"\n                class=\"h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300\"\n              />\n              <label for=\"theme_auto\" class=\"ml-2 block text-sm text-gray-900\">\n                🔄 Automatico (segue il sistema)\n              </label>\n            </div>\n          </div>\n        </div>\n\n        <!-- Lingua -->\n        <div>\n          <h3 class=\"text-md font-medium text-gray-900 mb-4\">Lingua</h3>\n          <select\n            v-model=\"preferences.language\"\n            class=\"block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500\"\n          >\n            <option value=\"it\">🇮🇹 Italiano</option>\n            <option value=\"en\">🇺🇸 English</option>\n            <option value=\"fr\">🇫🇷 Français</option>\n            <option value=\"de\">🇩🇪 Deutsch</option>\n          </select>\n        </div>\n\n        <!-- Sidebar -->\n        <div>\n          <h3 class=\"text-md font-medium text-gray-900 mb-4\">Sidebar</h3>\n          <div class=\"flex items-center\">\n            <input\n              id=\"sidebar_collapsed\"\n              v-model=\"preferences.sidebar_collapsed\"\n              type=\"checkbox\"\n              class=\"h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded\"\n            />\n            <label for=\"sidebar_collapsed\" class=\"ml-2 block text-sm text-gray-900\">\n              Mantieni sidebar collassata di default\n            </label>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- Notifiche -->\n    <div class=\"bg-white shadow rounded-lg\">\n      <div class=\"px-6 py-4 border-b border-gray-200\">\n        <h2 class=\"text-lg font-semibold text-gray-900\">Notifiche</h2>\n        <p class=\"mt-1 text-sm text-gray-600\">Gestisci le tue preferenze di notifica</p>\n      </div>\n\n      <div class=\"p-6 space-y-6\">\n        <div class=\"space-y-4\">\n          <div class=\"flex items-center justify-between\">\n            <div>\n              <h4 class=\"text-sm font-medium text-gray-900\">Notifiche Email</h4>\n              <p class=\"text-sm text-gray-500\">Ricevi notifiche via email</p>\n            </div>\n            <input\n              v-model=\"notifications.email\"\n              type=\"checkbox\"\n              class=\"h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded\"\n            />\n          </div>\n\n          <div class=\"flex items-center justify-between\">\n            <div>\n              <h4 class=\"text-sm font-medium text-gray-900\">Notifiche Progetti</h4>\n              <p class=\"text-sm text-gray-500\">Aggiornamenti sui progetti a cui partecipi</p>\n            </div>\n            <input\n              v-model=\"notifications.projects\"\n              type=\"checkbox\"\n              class=\"h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded\"\n            />\n          </div>\n\n          <div class=\"flex items-center justify-between\">\n            <div>\n              <h4 class=\"text-sm font-medium text-gray-900\">Notifiche Task</h4>\n              <p class=\"text-sm text-gray-500\">Quando ti vengono assegnati nuovi task</p>\n            </div>\n            <input\n              v-model=\"notifications.tasks\"\n              type=\"checkbox\"\n              class=\"h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded\"\n            />\n          </div>\n\n          <div class=\"flex items-center justify-between\">\n            <div>\n              <h4 class=\"text-sm font-medium text-gray-900\">Notifiche Sistema</h4>\n              <p class=\"text-sm text-gray-500\">Aggiornamenti e manutenzioni del sistema</p>\n            </div>\n            <input\n              v-model=\"notifications.system\"\n              type=\"checkbox\"\n              class=\"h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded\"\n            />\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- Azioni -->\n    <div class=\"flex justify-end space-x-3\">\n      <button\n        @click=\"resetSettings\"\n        class=\"px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500\"\n      >\n        Ripristina Default\n      </button>\n      <button\n        @click=\"saveSettings\"\n        :disabled=\"saving\"\n        class=\"px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50\"\n      >\n        {{ saving ? 'Salvataggio...' : 'Salva Impostazioni' }}\n      </button>\n    </div>\n  </div>\n</template>\n\n<script setup>\nimport { ref, onMounted } from 'vue'\nimport { useAuthStore } from '@/stores/auth'\nimport { useDarkMode } from '@/composables/useDarkMode'\n\nconst authStore = useAuthStore()\nconst { isDarkMode, toggleDarkMode } = useDarkMode()\n\n// State\nconst saving = ref(false)\nconst changingPassword = ref(false)\n\nconst passwordForm = ref({\n  current_password: '',\n  new_password: '',\n  confirm_password: ''\n})\n\nconst preferences = ref({\n  theme: 'light',\n  language: 'it',\n  sidebar_collapsed: false\n})\n\nconst notifications = ref({\n  email: true,\n  projects: true,\n  tasks: true,\n  system: false\n})\n\n// Methods\nconst loadSettings = async () => {\n  try {\n    const response = await fetch('/api/auth/settings', {\n      headers: {\n        'Content-Type': 'application/json',\n        'X-CSRFToken': authStore.csrfToken\n      }\n    })\n    \n    if (response.ok) {\n      const result = await response.json()\n      const settings = result.data.settings\n      \n      preferences.value = {\n        theme: settings.theme || 'light',\n        language: settings.language || 'it',\n        sidebar_collapsed: settings.sidebar_collapsed || false\n      }\n      \n      notifications.value = {\n        email: settings.notifications?.email !== false,\n        projects: settings.notifications?.projects !== false,\n        tasks: settings.notifications?.tasks !== false,\n        system: settings.notifications?.system || false\n      }\n    }\n  } catch (error) {\n    console.error('Error loading settings:', error)\n  }\n}\n\nconst saveSettings = async () => {\n  saving.value = true\n  \n  try {\n    const response = await fetch('/api/auth/settings', {\n      method: 'PUT',\n      headers: {\n        'Content-Type': 'application/json',\n        'X-CSRFToken': authStore.csrfToken\n      },\n      body: JSON.stringify({\n        preferences: preferences.value,\n        notifications: notifications.value\n      })\n    })\n\n    if (!response.ok) {\n      throw new Error('Errore nel salvataggio delle impostazioni')\n    }\n\n    alert('Impostazioni salvate con successo!')\n  } catch (error) {\n    console.error('Error saving settings:', error)\n    alert('Errore nel salvataggio delle impostazioni')\n  } finally {\n    saving.value = false\n  }\n}\n\nconst changePassword = async () => {\n  if (passwordForm.value.new_password !== passwordForm.value.confirm_password) {\n    alert('Le password non coincidono')\n    return\n  }\n  \n  changingPassword.value = true\n  \n  try {\n    const response = await fetch('/api/auth/change-password', {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json',\n        'X-CSRFToken': authStore.csrfToken\n      },\n      body: JSON.stringify({\n        current_password: passwordForm.value.current_password,\n        new_password: passwordForm.value.new_password\n      })\n    })\n\n    if (!response.ok) {\n      throw new Error('Errore nel cambio password')\n    }\n\n    alert('Password cambiata con successo!')\n    passwordForm.value = {\n      current_password: '',\n      new_password: '',\n      confirm_password: ''\n    }\n  } catch (error) {\n    console.error('Error changing password:', error)\n    alert('Errore nel cambio password')\n  } finally {\n    changingPassword.value = false\n  }\n}\n\nconst resetSettings = () => {\n  preferences.value = {\n    theme: 'light',\n    language: 'it',\n    sidebar_collapsed: false\n  }\n  \n  notifications.value = {\n    email: true,\n    projects: true,\n    tasks: true,\n    system: false\n  }\n}\n\n// Lifecycle\nonMounted(() => {\n  loadSettings()\n})\n</script>\n"}