{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "specs/personnel_migration_plan.md"}, "modifiedCode": "# Personnel/HR Module - Vue.js Migration Plan\n\n## Analisi Sistema Legacy\n\n### ✅ Funzionalità Esistenti Identificate\n\n**1. Personnel Management (Core)**\n- Lista dipendenti con filtri (dipartimento, competenze, ricerca)\n- Profilo dipendente dettagliato con tab (progetti, task, competenze, timesheet, CV)\n- Modifica profilo con informazioni HR estese\n- Directory aziendale con ricerca avanzata\n- Gestione competenze (skills) con livelli di proficiency\n\n**2. Department Management**\n- Lista dipartimenti con gerarchia\n- Dashboard dipartimento con statistiche\n- Creazione/modifica/eliminazione dipartimenti\n- Gestione dipendenti per dipartimento\n- Trasferimento dipendenti tra dipartimenti\n\n**3. Organization Chart**\n- Visualizzazione organigramma aziendale\n- Struttura gerarchica dipartimenti\n- Filtri per dipartimento e ruolo\n\n**4. Skills Management**\n- Matrice competenze (utenti vs skills)\n- Gestione competenze individuali\n- Livelli di proficiency (1-5)\n- Certificazioni e anni di esperienza\n\n### 📋 Modelli Dati Esistenti\n\n**User Model (già disponibile):**\n- Campi base: username, email, first_name, last_name, role\n- Campi HR: department_id, position, hire_date, phone, bio\n- Relazioni: department_obj, detailed_skills, profile\n\n**UserProfile Model (già disponibile):**\n- employee_id, job_title, birth_date, address\n- emergency_contact_name, emergency_contact_phone\n- employment_type, work_location, weekly_hours\n- profile_completion (calcolato)\n\n**Department Model (già disponibile):**\n- name, description, parent_id, manager_id, budget\n- Relazioni: parent, subdepartments, employees, manager\n\n**Skill & UserSkill Models (già disponibili):**\n- Skill: name, category, description\n- UserSkill: proficiency_level, years_experience, is_certified\n\n## Piano di Migrazione Vue.js\n\n### 🎯 Fase 1: Struttura Base e Routing\n\n**1.1 Routing Vue.js**\n```javascript\n// Aggiungi a router/index.js\n{\n  path: '/app/personnel',\n  component: () => import('@/views/personnel/PersonnelLayout.vue'),\n  children: [\n    { path: '', name: 'PersonnelList', component: () => import('@/views/personnel/PersonnelList.vue') },\n    { path: 'directory', name: 'PersonnelDirectory', component: () => import('@/views/personnel/PersonnelDirectory.vue') },\n    { path: 'orgchart', name: 'PersonnelOrgChart', component: () => import('@/views/personnel/PersonnelOrgChart.vue') },\n    { path: 'departments', name: 'DepartmentList', component: () => import('@/views/personnel/DepartmentList.vue') },\n    { path: 'departments/create', name: 'DepartmentCreate', component: () => import('@/views/personnel/DepartmentCreate.vue') },\n    { path: 'departments/:id', name: 'DepartmentView', component: () => import('@/views/personnel/DepartmentView.vue') },\n    { path: 'departments/:id/edit', name: 'DepartmentEdit', component: () => import('@/views/personnel/DepartmentEdit.vue') },\n    { path: 'skills', name: 'SkillsMatrix', component: () => import('@/views/personnel/SkillsMatrix.vue') },\n    { path: ':id', name: 'PersonnelProfile', component: () => import('@/views/personnel/PersonnelProfile.vue') }\n  ]\n}\n```\n\n**1.2 Store Pinia**\n```javascript\n// stores/personnel.js\nexport const usePersonnelStore = defineStore('personnel', {\n  state: () => ({\n    users: [],\n    departments: [],\n    skills: [],\n    currentUser: null,\n    loading: false,\n    filters: {\n      search: '',\n      department: null,\n      skill: null,\n      role: null\n    }\n  }),\n  actions: {\n    async fetchUsers(params = {}),\n    async fetchUser(id),\n    async updateUser(id, data),\n    async fetchDepartments(),\n    async createDepartment(data),\n    async updateDepartment(id, data),\n    async deleteDepartment(id),\n    async fetchSkills(),\n    async updateUserSkill(userId, skillData)\n  }\n})\n```\n\n### 🎯 Fase 2: Componenti Core\n\n**2.1 PersonnelList.vue (Isofunctional Port)**\n- Grid layout 3 colonne responsive\n- Filtri: dipartimento, competenze, ricerca\n- Card dipendente con avatar, info base, competenze\n- Paginazione\n- Azioni: profilo, telefono, email\n\n**2.2 PersonnelProfile.vue (Isofunctional Port)**\n- Layout 2 colonne: info personali + tab attività\n- Tab: Progetti, Task, Competenze, Timesheet, CV\n- Gestione competenze con livelli proficiency\n- Informazioni HR estese\n- Progress bar completamento profilo\n\n**2.3 PersonnelDirectory.vue**\n- Lista compatta con ricerca avanzata\n- Filtri: dipartimento, ruolo, location\n- Ordinamento multiplo\n- Export funzionalità\n\n### 🎯 Fase 3: Department Management\n\n**3.1 DepartmentList.vue**\n- Tabella dipartimenti con gerarchia\n- Statistiche: totale dipartimenti, manager, dipendenti\n- Filtri: ricerca, livello gerarchia\n- Azioni: dashboard, modifica, elimina\n\n**3.2 DepartmentView.vue**\n- Dashboard dipartimento con KPI\n- Lista dipendenti del dipartimento\n- Sottodepartimenti\n- Distribuzione competenze\n- Gestione dipendenti (assegna/trasferisci)\n\n**3.3 DepartmentCreate/Edit.vue**\n- Form completo dipartimento\n- Selezione parent department\n- Assegnazione manager\n- Budget e descrizione\n\n### 🎯 Fase 4: Organization Chart & Skills\n\n**4.1 PersonnelOrgChart.vue**\n- Visualizzazione albero gerarchico\n- Componenti ricorsivi per nodi\n- Filtri dipartimento e ruolo\n- Statistiche organizzazione\n\n**4.2 SkillsMatrix.vue**\n- Tabella utenti vs competenze\n- Heatmap livelli proficiency\n- Filtri categoria e dipartimento\n- Export matrice competenze\n\n## API Endpoints da Implementare\n\n### ✅ Già Disponibili\n- `GET /api/personnel/users` - Lista utenti con filtri\n- `GET /api/personnel/users/:id` - Dettaglio utente\n- `PUT /api/personnel/users/:id` - Aggiorna utente\n\n### 🔄 Da Implementare/Estendere\n- `GET /api/personnel/departments` - Lista dipartimenti\n- `POST /api/personnel/departments` - Crea dipartimento\n- `PUT /api/personnel/departments/:id` - Aggiorna dipartimento\n- `DELETE /api/personnel/departments/:id` - Elimina dipartimento\n- `GET /api/personnel/departments/:id/employees` - Dipendenti dipartimento\n- `POST /api/personnel/departments/:id/transfer` - Trasferisci dipendente\n- `GET /api/personnel/skills` - Lista competenze\n- `POST /api/personnel/users/:id/skills` - Aggiungi competenza\n- `PUT /api/personnel/users/:id/skills/:skillId` - Aggiorna competenza\n- `DELETE /api/personnel/users/:id/skills/:skillId` - Rimuovi competenza\n- `GET /api/personnel/orgchart` - Dati organigramma\n- `GET /api/personnel/skills-matrix` - Matrice competenze\n\n## Priorità Implementazione\n\n### 🚀 Sprint 1 (Alta Priorità)\n1. PersonnelList.vue - Lista dipendenti base\n2. PersonnelProfile.vue - Profilo dipendente\n3. Store personnel.js - Gestione stato\n4. API departments base\n\n### 🚀 Sprint 2 (Media Priorità)\n1. DepartmentList.vue - Gestione dipartimenti\n2. DepartmentView.vue - Dashboard dipartimento\n3. PersonnelDirectory.vue - Directory aziendale\n\n### 🚀 Sprint 3 (Bassa Priorità)\n1. PersonnelOrgChart.vue - Organigramma\n2. SkillsMatrix.vue - Matrice competenze\n3. Funzionalità avanzate (CV, export, etc.)\n\n## Note Tecniche\n\n**Mantenimento Parità Funzionale:**\n- Stessi filtri e ordinamenti del legacy\n- Stessa struttura dati e validazioni\n- Stessi permessi RBAC\n- Stessa UX e layout responsive\n\n**Miglioramenti Vue.js:**\n- SPA navigation senza reload\n- Caching intelligente nello store\n- Componenti riutilizzabili\n- Performance ottimizzate\n"}