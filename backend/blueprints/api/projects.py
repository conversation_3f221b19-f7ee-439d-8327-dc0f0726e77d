"""
API RESTful per la gestione dei progetti.
"""
from flask import Blueprint, request, jsonify, current_app
from flask_login import login_required, current_user
from sqlalchemy import desc
from models import Project, Task, ProjectResource, ProjectKPI, User
from utils.api_utils import (
    api_response, handle_api_error, get_pagination_params,
    format_pagination, api_permission_required
)
from utils.permissions import (
    PERMISSION_VIEW_ALL_PROJECTS, PERMISSION_EDIT_PROJECT,
    PERMISSION_DELETE_PROJECT
)
from utils.db import db

# Crea il blueprint per le API dei progetti
api_projects = Blueprint('api_projects', __name__)

@api_projects.route('/', methods=['GET'])
@login_required
def get_projects():
    """
    Ottiene la lista dei progetti con supporto per filtri e paginazione.
    ---
    tags:
      - projects
    parameters:
      - $ref: '#/components/parameters/pageParam'
      - $ref: '#/components/parameters/perPageParam'
      - name: status
        in: query
        description: Filtra per stato del progetto
        schema:
          type: string
          enum: [planning, active, completed, on-hold]
      - name: client_id
        in: query
        description: Filtra per ID cliente
        schema:
          type: integer
      - name: search
        in: query
        description: Cerca nei nomi e nelle descrizioni dei progetti
        schema:
          type: string
    responses:
      200:
        description: Lista di progetti
        content:
          application/json:
            schema:
              type: object
              properties:
                success:
                  type: boolean
                  example: true
                data:
                  type: object
                  properties:
                    projects:
                      type: array
                      items:
                        $ref: '#/components/schemas/Project'
                pagination:
                  $ref: '#/components/schemas/Pagination'
      401:
        $ref: '#/components/responses/Unauthorized'
    """
    try:
        # Ottieni parametri di paginazione
        page, per_page = get_pagination_params()

        # Inizia la query
        query = Project.query

        # Applica filtri
        status = request.args.get('status')
        if status:
            query = query.filter(Project.status == status)

        client_id = request.args.get('client_id', type=int)
        if client_id:
            query = query.filter(Project.client_id == client_id)

        search = request.args.get('search')
        if search:
            search_term = f"%{search}%"
            query = query.filter(
                (Project.name.ilike(search_term)) |
                (Project.description.ilike(search_term))
            )

        # Applica ordinamento
        query = query.order_by(desc(Project.updated_at))

        # Esegui query con paginazione
        pagination = query.paginate(page=page, per_page=per_page)

        # Prepara i dati dei progetti
        projects_data = []
        for project in pagination.items:
            # Serializza i dati del cliente se presente
            client_data = None
            if project.client:
                client_data = {
                    'id': project.client.id,
                    'name': project.client.name,
                    'industry': project.client.industry
                }

            projects_data.append({
                'id': project.id,
                'name': project.name,
                'description': project.description,
                'client_id': project.client_id,
                'client': client_data,
                'project_type': project.project_type,
                'is_billable': project.is_billable,
                'client_daily_rate': float(project.client_daily_rate) if project.client_daily_rate else None,
                'markup_percentage': float(project.markup_percentage) if project.markup_percentage else None,
                'start_date': project.start_date.isoformat() if project.start_date else None,
                'end_date': project.end_date.isoformat() if project.end_date else None,
                'status': project.status,
                'budget': float(project.budget) if project.budget else None,
                'expenses': float(project.expenses) if project.expenses else None,
                'created_at': project.created_at.isoformat(),
                'updated_at': project.updated_at.isoformat()
            })

        # Restituisci risposta
        return api_response(
            data={'projects': projects_data},
            pagination=format_pagination(pagination)
        )
    except Exception as e:
        current_app.logger.error(f"Error in get_projects: {str(e)}")
        return handle_api_error(e)

@api_projects.route('/<int:project_id>', methods=['GET'])
@login_required
def get_project(project_id):
    """
    Ottiene i dettagli di un progetto specifico.
    ---
    tags:
      - projects
    parameters:
      - name: project_id
        in: path
        required: true
        description: ID del progetto
        schema:
          type: integer
    responses:
      200:
        description: Dettagli del progetto
        content:
          application/json:
            schema:
              type: object
              properties:
                success:
                  type: boolean
                  example: true
                data:
                  type: object
                  properties:
                    project:
                      $ref: '#/components/schemas/Project'
      404:
        $ref: '#/components/responses/NotFound'
      401:
        $ref: '#/components/responses/Unauthorized'
    """
    try:
        project = Project.query.get_or_404(project_id)

        # Serializza i dati del cliente se presente
        client_data = None
        if project.client:
            client_data = {
                'id': project.client.id,
                'name': project.client.name,
                'industry': project.client.industry
            }

        # Calcola dati aggregati
        tasks = project.tasks.all()
        task_count = len(tasks)
        completed_tasks = len([t for t in tasks if t.status == 'completed'])

        # Serializza task
        tasks_data = []
        for task in tasks:
            assignee_data = None
            if task.assignee_id:
                assignee = next((m for m in project.team_members if m.id == task.assignee_id), None)
                if assignee:
                    assignee_data = {
                        'id': assignee.id,
                        'full_name': assignee.full_name
                    }

            tasks_data.append({
                'id': task.id,
                'name': task.name,
                'description': task.description,
                'status': task.status,
                'priority': task.priority,
                'assignee_id': task.assignee_id,
                'assignee': assignee_data,
                'start_date': task.start_date.isoformat() if task.start_date else None,
                'due_date': task.due_date.isoformat() if task.due_date else None,
                'estimated_hours': task.estimated_hours,
                'actual_hours': task.actual_hours,
                'created_at': task.created_at.isoformat() if task.created_at else None
            })

        # Calcola ore totali dai timesheet
        total_hours = 0
        for entry in project.timesheet_entries:
            total_hours += entry.hours if entry.hours else 0

        # Serializza team members
        team_members_data = []
        for member in project.team_members:
            # Calcola ore lavorate per questo membro
            member_hours = 0
            for entry in project.timesheet_entries:
                if entry.user_id == member.id:
                    member_hours += entry.hours if entry.hours else 0

            team_members_data.append({
                'id': member.id,
                'full_name': member.full_name,
                'email': member.email,
                'profile_image': member.profile_image,
                'role': 'Team Member',  # TODO: Get actual role from project_team table
                'hours_worked': member_hours
            })

        # Prepara i dati del progetto
        project_data = {
            'id': project.id,
            'name': project.name,
            'description': project.description,
            'client_id': project.client_id,
            'client': client_data,
            'project_type': project.project_type,
            'is_billable': project.is_billable,
            'client_daily_rate': float(project.client_daily_rate) if project.client_daily_rate else None,
            'markup_percentage': float(project.markup_percentage) if project.markup_percentage else None,
            'start_date': project.start_date.isoformat() if project.start_date else None,
            'end_date': project.end_date.isoformat() if project.end_date else None,
            'status': project.status,
            'budget': float(project.budget) if project.budget else None,
            'expenses': float(project.expenses) if project.expenses else None,
            'created_at': project.created_at.isoformat(),
            'updated_at': project.updated_at.isoformat(),
            # Dati aggregati per l'overview
            'task_count': task_count,
            'completed_tasks': completed_tasks,
            'team_count': len(project.team_members),
            'team_members': team_members_data,
            'tasks': tasks_data,
            'total_hours': total_hours,
            'estimated_hours': sum(t.estimated_hours or 0 for t in tasks),
            'remaining_budget': float(project.budget - project.expenses) if project.budget and project.expenses else None
        }

        return api_response(data={'project': project_data})
    except Exception as e:
        current_app.logger.error(f"Error in get_project: {str(e)}")
        return handle_api_error(e)

@api_projects.route('/<int:project_id>/team', methods=['POST'])
@login_required
def add_team_member(project_id):
    """Aggiunge un membro al team del progetto."""
    try:
        project = Project.query.get_or_404(project_id)
        data = request.get_json()

        user_id = data.get('user_id')
        role = data.get('role', 'Team Member')

        if not user_id:
            return api_response(False, 'user_id richiesto', status_code=400)

        user = User.query.get_or_404(user_id)

        # Verifica se l'utente è già nel team
        if user in project.team_members:
            return api_response(False, 'Utente già nel team', status_code=400)

        # Aggiungi al team
        project.team_members.append(user)
        db.session.commit()

        return api_response(data={}, message='Membro aggiunto al team con successo')

    except Exception as e:
        db.session.rollback()
        return handle_api_error(e)


@api_projects.route('/<int:project_id>/team/<int:user_id>', methods=['DELETE'])
@login_required
def remove_team_member(project_id, user_id):
    """Rimuove un membro dal team del progetto."""
    try:
        project = Project.query.get_or_404(project_id)
        user = User.query.get_or_404(user_id)

        # Non rimuovere il project manager
        if user_id == project.manager_id:
            return api_response(False, 'Non è possibile rimuovere il project manager', status_code=400)

        # Verifica se l'utente è nel team
        if user not in project.team_members:
            return api_response(False, 'Utente non nel team', status_code=400)

        # Rimuovi dal team
        project.team_members.remove(user)
        db.session.commit()

        return api_response(data={}, message='Membro rimosso dal team con successo')

    except Exception as e:
        db.session.rollback()
        return handle_api_error(e)


@api_projects.route('/', methods=['POST'])
@login_required
@api_permission_required(PERMISSION_EDIT_PROJECT)
def create_project():
    """
    Crea un nuovo progetto.
    ---
    tags:
      - projects
    requestBody:
      required: true
      content:
        application/json:
          schema:
            type: object
            required:
              - name
            properties:
              name:
                type: string
                description: Nome del progetto
              description:
                type: string
                description: Descrizione del progetto
              client_id:
                type: integer
                description: ID del cliente associato
              client:
                type: string
                description: Nome del cliente (alternativo a client_id)
              project_type:
                type: string
                enum: [service, license, consulting, product, rd, internal]
                default: service
                description: Tipologia del progetto
              is_billable:
                type: boolean
                default: true
                description: Se il progetto è fatturabile
              client_daily_rate:
                type: number
                description: Tariffa giornaliera del cliente
              markup_percentage:
                type: number
                description: Percentuale di markup applicata
              start_date:
                type: string
                format: date
                description: Data di inizio (YYYY-MM-DD)
              end_date:
                type: string
                format: date
                description: Data di fine (YYYY-MM-DD)
              status:
                type: string
                enum: [planning, active, completed, on-hold, cancelled]
                default: planning
                description: Stato del progetto
              budget:
                type: number
                description: Budget del progetto
              team_members:
                type: array
                items:
                  type: integer
                description: Lista di ID utenti da assegnare al progetto
    responses:
      201:
        description: Progetto creato con successo
        content:
          application/json:
            schema:
              type: object
              properties:
                success:
                  type: boolean
                  example: true
                data:
                  type: object
                  properties:
                    project:
                      $ref: '#/components/schemas/Project'
                    message:
                      type: string
                      example: Progetto creato con successo
      400:
        description: Dati non validi
        content:
          application/json:
            schema:
              type: object
              properties:
                success:
                  type: boolean
                  example: false
                message:
                  type: string
                  example: Nome del progetto obbligatorio
      401:
        $ref: '#/components/responses/Unauthorized'
      403:
        $ref: '#/components/responses/Forbidden'
    """
    try:
        # Ottieni i dati dalla richiesta
        data = request.json

        # Validazione dei dati
        if not data or not data.get('name'):
            return api_response(
                message="Nome del progetto obbligatorio",
                status_code=400
            )

        # Crea il nuovo progetto
        new_project = Project(
            name=data.get('name'),
            description=data.get('description', ''),
            client_id=data.get('client_id'),
            project_type=data.get('project_type', 'service'),
            is_billable=data.get('is_billable', True),
            client_daily_rate=data.get('client_daily_rate'),
            markup_percentage=data.get('markup_percentage'),
            start_date=data.get('start_date'),
            end_date=data.get('end_date'),
            status=data.get('status', 'planning'),
            budget=data.get('budget')
        )

        # Aggiungi il progetto al database
        db.session.add(new_project)
        db.session.flush()  # Per ottenere l'ID del progetto

        # Aggiungi i membri del team se specificati
        team_members = data.get('team_members', [])
        if team_members:
            for user_id in team_members:
                user = User.query.get(user_id)
                if user:
                    new_project.team_members.append(user)

        # Aggiungi l'utente corrente al team se non è già incluso
        if current_user.id not in team_members:
            new_project.team_members.append(current_user)

        # Commit delle modifiche
        db.session.commit()

        # Serializza i dati del cliente se presente
        client_data = None
        if new_project.client:
            client_data = {
                'id': new_project.client.id,
                'name': new_project.client.name,
                'industry': new_project.client.industry
            }

        # Prepara i dati del progetto per la risposta
        project_data = {
            'id': new_project.id,
            'name': new_project.name,
            'description': new_project.description,
            'client_id': new_project.client_id,
            'client': client_data,
            'project_type': new_project.project_type,
            'is_billable': new_project.is_billable,
            'client_daily_rate': float(new_project.client_daily_rate) if new_project.client_daily_rate else None,
            'markup_percentage': float(new_project.markup_percentage) if new_project.markup_percentage else None,
            'start_date': new_project.start_date.isoformat() if new_project.start_date else None,
            'end_date': new_project.end_date.isoformat() if new_project.end_date else None,
            'status': new_project.status,
            'budget': float(new_project.budget) if new_project.budget else None,
            'expenses': float(new_project.expenses) if new_project.expenses else None,
            'created_at': new_project.created_at.isoformat(),
            'updated_at': new_project.updated_at.isoformat()
        }

        return api_response(
            data={'project': project_data},
            message="Progetto creato con successo",
            status_code=201
        )
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Error in create_project: {str(e)}")
        return handle_api_error(e)

@api_projects.route('/<int:project_id>', methods=['PUT'])
@login_required
@api_permission_required(PERMISSION_EDIT_PROJECT)
def update_project(project_id):
    """
    Aggiorna un progetto esistente.
    ---
    tags:
      - projects
    parameters:
      - name: project_id
        in: path
        required: true
        description: ID del progetto da aggiornare
        schema:
          type: integer
    requestBody:
      required: true
      content:
        application/json:
          schema:
            type: object
            properties:
              name:
                type: string
                description: Nome del progetto
              description:
                type: string
                description: Descrizione del progetto
              client_id:
                type: integer
                description: ID del cliente associato
              start_date:
                type: string
                format: date
                description: Data di inizio (YYYY-MM-DD)
              end_date:
                type: string
                format: date
                description: Data di fine (YYYY-MM-DD)
              status:
                type: string
                enum: [planning, active, completed, on-hold]
                description: Stato del progetto
              budget:
                type: number
                description: Budget del progetto
              team_members:
                type: array
                items:
                  type: integer
                description: Lista di ID utenti da assegnare al progetto
    responses:
      200:
        description: Progetto aggiornato con successo
        content:
          application/json:
            schema:
              type: object
              properties:
                success:
                  type: boolean
                  example: true
                data:
                  type: object
                  properties:
                    project:
                      $ref: '#/components/schemas/Project'
                    message:
                      type: string
                      example: Progetto aggiornato con successo
      400:
        description: Dati non validi
        content:
          application/json:
            schema:
              type: object
              properties:
                success:
                  type: boolean
                  example: false
                message:
                  type: string
                  example: Dati non validi
      404:
        $ref: '#/components/responses/NotFound'
      401:
        $ref: '#/components/responses/Unauthorized'
      403:
        $ref: '#/components/responses/Forbidden'
    """
    try:
        # Ottieni il progetto dal database
        project = Project.query.get_or_404(project_id)

        # Ottieni i dati dalla richiesta
        data = request.json
        if not data:
            return api_response(
                message="Nessun dato fornito per l'aggiornamento",
                status_code=400
            )

        # Aggiorna i campi del progetto
        if 'name' in data:
            project.name = data['name']
        if 'description' in data:
            project.description = data['description']
        if 'client_id' in data:
            project.client_id = data['client_id']
        if 'project_type' in data:
            project.project_type = data['project_type']
        if 'is_billable' in data:
            project.is_billable = data['is_billable']
        if 'client_daily_rate' in data:
            project.client_daily_rate = data['client_daily_rate']
        if 'markup_percentage' in data:
            project.markup_percentage = data['markup_percentage']
        if 'start_date' in data:
            project.start_date = data['start_date']
        if 'end_date' in data:
            project.end_date = data['end_date']
        if 'status' in data:
            project.status = data['status']
        if 'budget' in data:
            project.budget = data['budget']

        # Aggiorna i membri del team se specificati
        if 'team_members' in data:
            # Rimuovi tutti i membri attuali
            project.team_members = []

            # Aggiungi i nuovi membri
            for user_id in data['team_members']:
                user = User.query.get(user_id)
                if user:
                    project.team_members.append(user)

            # Assicurati che l'utente corrente sia nel team
            if current_user.id not in data['team_members']:
                project.team_members.append(current_user)

        # Commit delle modifiche
        db.session.commit()

        # Serializza i dati del cliente se presente
        client_data = None
        if project.client:
            client_data = {
                'id': project.client.id,
                'name': project.client.name,
                'industry': project.client.industry
            }

        # Prepara i dati del progetto per la risposta
        project_data = {
            'id': project.id,
            'name': project.name,
            'description': project.description,
            'client_id': project.client_id,
            'client': client_data,
            'project_type': project.project_type,
            'is_billable': project.is_billable,
            'client_daily_rate': float(project.client_daily_rate) if project.client_daily_rate else None,
            'markup_percentage': float(project.markup_percentage) if project.markup_percentage else None,
            'start_date': project.start_date.isoformat() if project.start_date else None,
            'end_date': project.end_date.isoformat() if project.end_date else None,
            'status': project.status,
            'budget': float(project.budget) if project.budget else None,
            'expenses': float(project.expenses) if project.expenses else None,
            'created_at': project.created_at.isoformat(),
            'updated_at': project.updated_at.isoformat()
        }

        return api_response(
            data={'project': project_data},
            message="Progetto aggiornato con successo"
        )
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Error in update_project: {str(e)}")
        return handle_api_error(e)

@api_projects.route('/<int:project_id>', methods=['DELETE'])
@login_required
@api_permission_required(PERMISSION_DELETE_PROJECT)
def delete_project(project_id):
    """
    Elimina un progetto esistente.
    ---
    tags:
      - projects
    parameters:
      - name: project_id
        in: path
        required: true
        description: ID del progetto da eliminare
        schema:
          type: integer
    responses:
      200:
        description: Progetto eliminato con successo
        content:
          application/json:
            schema:
              type: object
              properties:
                success:
                  type: boolean
                  example: true
                message:
                  type: string
                  example: "Progetto eliminato con successo"
      403:
        description: Permessi insufficienti
      404:
        description: Progetto non trovato
      500:
        description: Errore interno del server
    """
    try:
        project = Project.query.get_or_404(project_id)

        # Verifica permessi aggiuntivi se necessario
        # (il decoratore già controlla PERMISSION_DELETE_PROJECT)

        # Elimina il progetto (cascade eliminerà automaticamente task, timesheet, etc.)
        db.session.delete(project)
        db.session.commit()

        current_app.logger.info(f"Progetto {project_id} eliminato da utente {current_user.id}")

        return api_response(
            message=f"Progetto '{project.name}' eliminato con successo"
        )

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Errore nell'eliminazione progetto {project_id}: {str(e)}")
        return handle_api_error(e)


@api_projects.route('/batch', methods=['POST'])
@login_required
@api_permission_required(PERMISSION_EDIT_PROJECT)
def batch_projects():
    """
    Esegue operazioni batch sui progetti.
    ---
    tags:
      - projects
    requestBody:
      required: true
      content:
        application/json:
          schema:
            type: object
            required:
              - operations
            properties:
              operations:
                type: array
                items:
                  type: object
                  required:
                    - operation
                  properties:
                    operation:
                      type: string
                      enum: [create, update, delete]
                      description: Tipo di operazione da eseguire
                    project_id:
                      type: integer
                      description: ID del progetto (richiesto per update e delete)
                    data:
                      type: object
                      description: Dati del progetto (richiesto per create e update)
    responses:
      200:
        description: Operazioni batch eseguite con successo
        content:
          application/json:
            schema:
              type: object
              properties:
                success:
                  type: boolean
                  example: true
                data:
                  type: object
                  properties:
                    results:
                      type: array
                      items:
                        type: object
                        properties:
                          operation:
                            type: string
                            example: create
                          success:
                            type: boolean
                            example: true
                          project_id:
                            type: integer
                            example: 1
                          message:
                            type: string
                            example: Progetto creato con successo
                    message:
                      type: string
                      example: Operazioni batch eseguite con successo
      400:
        description: Dati non validi
        content:
          application/json:
            schema:
              type: object
              properties:
                success:
                  type: boolean
                  example: false
                message:
                  type: string
                  example: Dati non validi
      401:
        $ref: '#/components/responses/Unauthorized'
      403:
        $ref: '#/components/responses/Forbidden'
    """
    try:
        # Ottieni i dati dalla richiesta
        data = request.json

        # Validazione dei dati
        if not data or 'operations' not in data or not isinstance(data['operations'], list):
            return api_response(
                message="Operazioni batch non valide",
                status_code=400
            )

        # Risultati delle operazioni
        results = []

        # Esegui le operazioni
        for operation_data in data['operations']:
            # Validazione dell'operazione
            if 'operation' not in operation_data:
                results.append({
                    'operation': 'unknown',
                    'success': False,
                    'message': "Tipo di operazione non specificato"
                })
                continue

            operation = operation_data['operation']

            # Operazione CREATE
            if operation == 'create':
                if 'data' not in operation_data or not operation_data['data'].get('name'):
                    results.append({
                        'operation': 'create',
                        'success': False,
                        'message': "Nome del progetto obbligatorio"
                    })
                    continue

                try:
                    # Crea il nuovo progetto
                    project_data = operation_data['data']
                    new_project = Project(
                        name=project_data.get('name'),
                        description=project_data.get('description', ''),
                        client_id=project_data.get('client_id'),
                        start_date=project_data.get('start_date'),
                        end_date=project_data.get('end_date'),
                        status=project_data.get('status', 'planning'),
                        budget=project_data.get('budget')
                    )

                    # Aggiungi il progetto al database
                    db.session.add(new_project)
                    db.session.flush()  # Per ottenere l'ID del progetto

                    # Aggiungi i membri del team se specificati
                    team_members = project_data.get('team_members', [])
                    if team_members:
                        for user_id in team_members:
                            user = User.query.get(user_id)
                            if user:
                                new_project.team_members.append(user)

                    # Aggiungi l'utente corrente al team se non è già incluso
                    if current_user.id not in team_members:
                        new_project.team_members.append(current_user)

                    results.append({
                        'operation': 'create',
                        'success': True,
                        'project_id': new_project.id,
                        'message': "Progetto creato con successo"
                    })
                except Exception as e:
                    results.append({
                        'operation': 'create',
                        'success': False,
                        'message': f"Errore durante la creazione del progetto: {str(e)}"
                    })

            # Operazione UPDATE
            elif operation == 'update':
                if 'project_id' not in operation_data or 'data' not in operation_data:
                    results.append({
                        'operation': 'update',
                        'success': False,
                        'message': "ID progetto e dati obbligatori"
                    })
                    continue

                try:
                    # Ottieni il progetto dal database
                    project = Project.query.get(operation_data['project_id'])
                    if not project:
                        results.append({
                            'operation': 'update',
                            'success': False,
                            'project_id': operation_data['project_id'],
                            'message': "Progetto non trovato"
                        })
                        continue

                    # Aggiorna i campi del progetto
                    project_data = operation_data['data']
                    if 'name' in project_data:
                        project.name = project_data['name']
                    if 'description' in project_data:
                        project.description = project_data['description']
                    if 'client_id' in project_data:
                        project.client_id = project_data['client_id']
                    if 'start_date' in project_data:
                        project.start_date = project_data['start_date']
                    if 'end_date' in project_data:
                        project.end_date = project_data['end_date']
                    if 'status' in project_data:
                        project.status = project_data['status']
                    if 'budget' in project_data:
                        project.budget = project_data['budget']

                    # Aggiorna i membri del team se specificati
                    if 'team_members' in project_data:
                        # Rimuovi tutti i membri attuali
                        project.team_members = []

                        # Aggiungi i nuovi membri
                        for user_id in project_data['team_members']:
                            user = User.query.get(user_id)
                            if user:
                                project.team_members.append(user)

                        # Assicurati che l'utente corrente sia nel team
                        if current_user.id not in project_data['team_members']:
                            project.team_members.append(current_user)

                    results.append({
                        'operation': 'update',
                        'success': True,
                        'project_id': project.id,
                        'message': "Progetto aggiornato con successo"
                    })
                except Exception as e:
                    results.append({
                        'operation': 'update',
                        'success': False,
                        'project_id': operation_data.get('project_id'),
                        'message': f"Errore durante l'aggiornamento del progetto: {str(e)}"
                    })

            # Operazione DELETE
            elif operation == 'delete':
                if 'project_id' not in operation_data:
                    results.append({
                        'operation': 'delete',
                        'success': False,
                        'message': "ID progetto obbligatorio"
                    })
                    continue

                try:
                    # Ottieni il progetto dal database
                    project = Project.query.get(operation_data['project_id'])
                    if not project:
                        results.append({
                            'operation': 'delete',
                            'success': False,
                            'project_id': operation_data['project_id'],
                            'message': "Progetto non trovato"
                        })
                        continue

                    # Verifica se ci sono task associati
                    tasks = Task.query.filter_by(project_id=project.id).count()
                    if tasks > 0:
                        results.append({
                            'operation': 'delete',
                            'success': False,
                            'project_id': project.id,
                            'message': f"Impossibile eliminare il progetto: ci sono {tasks} task associati"
                        })
                        continue

                    # Verifica se ci sono risorse associate
                    resources = ProjectResource.query.filter_by(project_id=project.id).count()
                    if resources > 0:
                        results.append({
                            'operation': 'delete',
                            'success': False,
                            'project_id': project.id,
                            'message': f"Impossibile eliminare il progetto: ci sono {resources} risorse associate"
                        })
                        continue

                    # Verifica se ci sono KPI associati
                    kpis = ProjectKPI.query.filter_by(project_id=project.id).count()
                    if kpis > 0:
                        results.append({
                            'operation': 'delete',
                            'success': False,
                            'project_id': project.id,
                            'message': f"Impossibile eliminare il progetto: ci sono {kpis} KPI associati"
                        })
                        continue

                    # Elimina il progetto
                    db.session.delete(project)

                    results.append({
                        'operation': 'delete',
                        'success': True,
                        'project_id': operation_data['project_id'],
                        'message': "Progetto eliminato con successo"
                    })
                except Exception as e:
                    results.append({
                        'operation': 'delete',
                        'success': False,
                        'project_id': operation_data.get('project_id'),
                        'message': f"Errore durante l'eliminazione del progetto: {str(e)}"
                    })

            # Operazione non supportata
            else:
                results.append({
                    'operation': operation,
                    'success': False,
                    'message': f"Operazione '{operation}' non supportata"
                })

        # Commit delle modifiche
        db.session.commit()

        return api_response(
            data={'results': results},
            message="Operazioni batch eseguite con successo"
        )
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Error in batch_projects: {str(e)}")
        return handle_api_error(e)

@api_projects.route('/<int:project_id>', methods=['DELETE'])
@login_required
@api_permission_required(PERMISSION_DELETE_PROJECT)
def delete_project(project_id):
    """
    Elimina un progetto esistente.
    ---
    tags:
      - projects
    parameters:
      - name: project_id
        in: path
        required: true
        description: ID del progetto da eliminare
        schema:
          type: integer
    responses:
      200:
        description: Progetto eliminato con successo
        content:
          application/json:
            schema:
              type: object
              properties:
                success:
                  type: boolean
                  example: true
                message:
                  type: string
                  example: Progetto eliminato con successo
      404:
        $ref: '#/components/responses/NotFound'
      401:
        $ref: '#/components/responses/Unauthorized'
      403:
        $ref: '#/components/responses/Forbidden'
    """
    try:
        # Ottieni il progetto dal database
        project = Project.query.get_or_404(project_id)

        # Salva il nome del progetto per il messaggio di risposta
        project_name = project.name

        # Elimina il progetto
        db.session.delete(project)
        db.session.commit()

        return api_response(
            message=f"Progetto '{project_name}' eliminato con successo"
        )
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Error in delete_project: {str(e)}")
        return handle_api_error(e)