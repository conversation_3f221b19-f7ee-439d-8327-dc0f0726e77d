"""
Blueprint per funzionalità amministrative
"""
from flask import Blueprint, render_template, request, jsonify, flash, redirect, url_for
from flask_login import login_required, current_user
from models import ProjectKPITemplate, ProjectKPITarget, Project, User
from extensions import db
from utils.permissions import user_has_permission, PERMISSION_ADMIN
from utils.cost_calculator import get_default_kpi_templates
from datetime import datetime
import json

admin_bp = Blueprint('admin', __name__, url_prefix='/admin')

def admin_required(f):
    """Decorator per richiedere permessi admin"""
    def decorated_function(*args, **kwargs):
        if not current_user.is_authenticated:
            flash('Accesso negato', 'error')
            return redirect(url_for('auth.login'))

        if not user_has_permission(current_user.role, PERMISSION_ADMIN):
            flash('Permessi insufficienti', 'error')
            return redirect(url_for('dashboard.index'))

        return f(*args, **kwargs)
    decorated_function.__name__ = f.__name__
    return decorated_function

@admin_bp.route('/kpi-templates')
@login_required
@admin_required
def kpi_templates():
    """Gestione template KPI"""
    templates = ProjectKPITemplate.query.order_by(
        ProjectKPITemplate.project_type,
        ProjectKPITemplate.kpi_name
    ).all()

    # Raggruppa per tipologia e serializza
    templates_by_type = {}
    for template in templates:
        if template.project_type not in templates_by_type:
            templates_by_type[template.project_type] = []

        # Serializza il template per JSON
        template_data = {
            'id': template.id,
            'project_type': template.project_type,
            'kpi_name': template.kpi_name,
            'display_name': template.display_name,
            'target_min': float(template.target_min) if template.target_min else 0,
            'target_max': float(template.target_max) if template.target_max else 0,
            'warning_threshold': float(template.warning_threshold) if template.warning_threshold else 0,
            'unit': template.unit or '%',
            'description': template.description or '',
            'is_active': template.is_active
        }
        templates_by_type[template.project_type].append(template_data)

    project_types = [
        {'key': 'service', 'name': '🔧 Servizio', 'description': 'Sviluppo software, manutenzione, supporto'},
        {'key': 'license', 'name': '📄 Licenza', 'description': 'Vendita software, abbonamenti, SaaS'},
        {'key': 'consulting', 'name': '💼 Consulenza', 'description': 'Advisory, formazione, audit'},
        {'key': 'product', 'name': '📦 Prodotto', 'description': 'Hardware, dispositivi fisici'},
        {'key': 'rd', 'name': '🔬 R&D', 'description': 'Ricerca, innovazione, prototipi'},
        {'key': 'internal', 'name': '🏢 Interno', 'description': 'Progetti interni aziendali'}
    ]

    return render_template('admin/kpi_templates.html',
                         templates_by_type=templates_by_type,
                         project_types=project_types)

@admin_bp.route('/kpi-templates', methods=['POST'])
@login_required
@admin_required
def create_kpi_template():
    """Crea nuovo template KPI"""
    try:
        data = request.get_json()

        # Validazione
        required_fields = ['project_type', 'kpi_name', 'target_min', 'target_max', 'warning_threshold']
        for field in required_fields:
            if field not in data:
                return jsonify({'error': f'Campo {field} obbligatorio'}), 400

        # Controlla se template già esiste
        existing = ProjectKPITemplate.query.filter_by(
            project_type=data['project_type'],
            kpi_name=data['kpi_name']
        ).first()

        if existing:
            return jsonify({'error': 'Template già esistente per questa tipologia'}), 400

        # Crea nuovo template
        template = ProjectKPITemplate(
            project_type=data['project_type'],
            kpi_name=data['kpi_name'],
            target_min=float(data['target_min']),
            target_max=float(data['target_max']),
            warning_threshold=float(data['warning_threshold']),
            unit=data.get('unit', '%'),
            description=data.get('description', ''),
            is_active=True
        )

        db.session.add(template)
        db.session.commit()

        return jsonify({
            'success': True,
            'message': 'Template KPI creato con successo',
            'template': {
                'id': template.id,
                'display_name': template.display_name,
                'target_min': template.target_min,
                'target_max': template.target_max,
                'unit': template.unit
            }
        })

    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

@admin_bp.route('/kpi-templates/<int:template_id>', methods=['PUT'])
@login_required
@admin_required
def update_kpi_template(template_id):
    """Aggiorna template KPI"""
    try:
        template = ProjectKPITemplate.query.get_or_404(template_id)
        data = request.get_json()

        # Aggiorna campi
        if 'target_min' in data:
            template.target_min = float(data['target_min'])
        if 'target_max' in data:
            template.target_max = float(data['target_max'])
        if 'warning_threshold' in data:
            template.warning_threshold = float(data['warning_threshold'])
        if 'unit' in data:
            template.unit = data['unit']
        if 'description' in data:
            template.description = data['description']
        if 'is_active' in data:
            template.is_active = bool(data['is_active'])

        template.updated_at = datetime.utcnow()
        db.session.commit()

        return jsonify({
            'success': True,
            'message': 'Template aggiornato con successo'
        })

    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

@admin_bp.route('/kpi-templates/<int:template_id>', methods=['DELETE'])
@login_required
@admin_required
def delete_kpi_template(template_id):
    """Elimina template KPI"""
    try:
        template = ProjectKPITemplate.query.get_or_404(template_id)

        # Controlla se è utilizzato in progetti
        projects_using = ProjectKPITarget.query.filter_by(kpi_name=template.kpi_name).count()
        if projects_using > 0:
            return jsonify({
                'error': f'Template utilizzato in {projects_using} progetti. Disattivalo invece di eliminarlo.'
            }), 400

        db.session.delete(template)
        db.session.commit()

        return jsonify({
            'success': True,
            'message': 'Template eliminato con successo'
        })

    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

@admin_bp.route('/kpi-suggestions', methods=['POST'])
@login_required
@admin_required
def get_kpi_suggestions():
    """Ottieni suggerimenti AI per KPI"""
    try:
        data = request.get_json()
        project_type = data.get('project_type')
        kpi_name = data.get('kpi_name')

        # Per ora restituiamo suggerimenti statici
        # TODO: Integrare con AI service
        default_templates = get_default_kpi_templates()

        if project_type in default_templates and kpi_name in default_templates[project_type]:
            config = default_templates[project_type][kpi_name]
            suggestion = f"Per progetti {project_type}, il {kpi_name} dovrebbe essere tra {config['target_min']}-{config['target_max']}{config['unit']}. Soglia warning: {config['warning_threshold']}{config['unit']}."
        else:
            suggestion = "Nessun suggerimento disponibile per questa combinazione."

        return jsonify({
            'suggestion': suggestion,
            'confidence': 0.8
        })

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@admin_bp.route('/reset-kpi-templates', methods=['POST'])
@login_required
@admin_required
def reset_kpi_templates():
    """Reset template KPI ai valori di default"""
    try:
        # Elimina template esistenti
        ProjectKPITemplate.query.delete()

        # Ricrea template di default
        default_templates = get_default_kpi_templates()

        for project_type, kpis in default_templates.items():
            for kpi_name, config in kpis.items():
                template = ProjectKPITemplate(
                    project_type=project_type,
                    kpi_name=kpi_name,
                    target_min=config['target_min'],
                    target_max=config['target_max'],
                    warning_threshold=config['warning_threshold'],
                    unit=config['unit'],
                    description=f"Template {kpi_name} per progetti {project_type}",
                    is_active=True
                )
                db.session.add(template)

        db.session.commit()

        return jsonify({
            'success': True,
            'message': 'Template KPI ripristinati ai valori di default'
        })

    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500
