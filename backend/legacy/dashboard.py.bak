from flask import Blueprint, render_template, redirect, url_for, flash, request, jsonify, current_app
from flask_login import login_required, current_user
from sqlalchemy import func, desc
from datetime import datetime, timedelta

from app import db
from models import Project, Task, Timesheet, News, Event, KPI, Client, Document, FundingOpportunity, User, AdminLog, Notification
from utils.decorators import permission_required
from utils.permissions import PERMISSION_MANAGE_USERS, PERMISSION_VIEW_DASHBOARD, PERMISSION_VIEW_ALL_PROJECTS, PERMISSION_VIEW_REPORTS, PERMISSION_VIEW_CRM, user_has_permission, ROLE_MANAGER

dashboard_bp = Blueprint('dashboard', __name__, url_prefix='/dashboard')

@dashboard_bp.route('/')
@login_required
@permission_required(PERMISSION_VIEW_DASHBOARD)
def index():
    # Get key metrics for dashboard
    now = datetime.utcnow()
    week_ahead = now + timedelta(days=7)

    # Active projects count - filtrato per permessi
    if user_has_permission(current_user.role, PERMISSION_VIEW_ALL_PROJECTS):
        active_projects_count = Project.query.filter_by(status='active').count()
    else:
        # Solo progetti a cui l'utente è assegnato
        active_projects_count = Project.query.filter_by(
            status='active'
        ).filter(
            Project.team_members.any(id=current_user.id)
        ).count()

    # Tasks due in the next week - filtrato per permessi
    if user_has_permission(current_user.role, PERMISSION_VIEW_ALL_PROJECTS):
        upcoming_tasks = Task.query.filter(
            Task.due_date.between(now.date(), week_ahead.date()),
            Task.status != 'done'
        ).order_by(Task.due_date).limit(5).all()
    else:
        upcoming_tasks = Task.query.filter(
            Task.due_date.between(now.date(), week_ahead.date()),
            Task.status != 'done',
            Task.assignee_id == current_user.id
        ).order_by(Task.due_date).limit(5).all()

    # Recent timesheet entries - già filtrato per l'utente corrente
    recent_timesheets = Timesheet.query.filter_by(
        user_id=current_user.id
    ).order_by(desc(Timesheet.date)).limit(5).all()

    # Latest news - tutti vedono le news pubblicate
    latest_news = News.query.filter_by(
        is_published=True
    ).order_by(desc(News.created_at)).limit(3).all()

    # Upcoming events - filtro eventi per team/department se necessario
    if user_has_permission(current_user.role, PERMISSION_VIEW_ALL_PROJECTS):
        upcoming_events = Event.query.filter(
            Event.start_time >= now
        ).order_by(Event.start_time).limit(5).all()
    else:
        # Mostra solo gli eventi creati dall'utente o associati ai suoi progetti
        upcoming_events = Event.query.filter(
            Event.start_time >= now,
            (Event.created_by == current_user.id) |
            Event.project_id.in_(
                db.session.query(Project.id).join(
                    Project.team_members
                ).filter(User.id == current_user.id)
            )
        ).order_by(Event.start_time).limit(5).all()

    # Key performance indicators - solo specifici per dipartimento se non admin/manager
    if user_has_permission(current_user.role, PERMISSION_VIEW_REPORTS):
        kpis = KPI.query.order_by(KPI.category).limit(6).all()
    else:
        # Mostra tutti i KPI disponibili per gli utenti non admin/manager
        kpis = KPI.query.order_by(KPI.category).limit(6).all()

    # Client count - visibile solo a chi ha permesso di CRM
    if user_has_permission(current_user.role, PERMISSION_VIEW_CRM):
        client_count = Client.query.count()
    else:
        client_count = 0

    # Recent documents - filtro per visibilità/dipartimento
    if user_has_permission(current_user.role, PERMISSION_VIEW_ALL_PROJECTS):
        recent_documents = Document.query.order_by(desc(Document.created_at)).limit(5).all()
    else:
        # Mostra solo i documenti caricati dall'utente
        recent_documents = Document.query.filter(
            Document.uploaded_by == current_user.id
        ).order_by(desc(Document.created_at)).limit(5).all()

    # Task status distribution
    if user_has_permission(current_user.role, PERMISSION_VIEW_ALL_PROJECTS):
        task_status = db.session.query(
            Task.status, func.count(Task.id)
        ).group_by(Task.status).all()
    else:
        task_status = db.session.query(
            Task.status, func.count(Task.id)
        ).filter(Task.assignee_id == current_user.id).group_by(Task.status).all()

    task_status_data = {
        'labels': [status for status, _ in task_status],
        'data': [count for _, count in task_status]
    }

    # Funding opportunities - visibili a tutti (ma potremmo filtrare per dipartimento)
    funding_opportunities = FundingOpportunity.query.filter_by(
        status='open'
    ).order_by(FundingOpportunity.application_deadline).limit(3).all()

    # Projects by status - filtrato per permessi
    if user_has_permission(current_user.role, PERMISSION_VIEW_ALL_PROJECTS):
        project_status = db.session.query(
            Project.status, func.count(Project.id)
        ).group_by(Project.status).all()
    else:
        project_status = db.session.query(
            Project.status, func.count(Project.id)
        ).filter(Project.team_members.any(id=current_user.id)).group_by(Project.status).all()

    project_status_data = {
        'labels': [status for status, _ in project_status],
        'data': [count for _, count in project_status]
    }

    return render_template(
        'dashboard/index.html',
        active_projects_count=active_projects_count,
        upcoming_tasks=upcoming_tasks,
        recent_timesheets=recent_timesheets,
        latest_news=latest_news,
        upcoming_events=upcoming_events,
        kpis=kpis,
        client_count=client_count,
        recent_documents=recent_documents,
        task_status_data=task_status_data,
        project_status_data=project_status_data,
        funding_opportunities=funding_opportunities
    )

@dashboard_bp.route('/admin')
@login_required
@permission_required(PERMISSION_MANAGE_USERS)
def admin():
    """Dashboard di amministrazione per la gestione degli utenti."""
    # Recupera tutti gli utenti
    users = User.query.all()

    # Prepara i dati utente per il template
    users_data = []
    for user in users:
        # Crea una versione sicura per evitare accesso a None
        if user:
            name = f"{user.first_name or ''} {user.last_name or ''}".strip()
            if not name:
                name = user.username or user.email or "Utente"

            users_data.append({
                'id': user.id,
                'name': name,
                'email': user.email or "",
                'role': user.role or "employee",
                'is_active': user.is_active if user.is_active is not None else True,
                'created_at': user.created_at.isoformat() if user.created_at else None
            })

    # Debug dei dati utente
    print(f"Numero utenti elaborati: {len(users_data)}")
    if users_data:
        print(f"Primo utente: {users_data[0]}")

    # Recupera i log delle attività amministrative
    admin_logs = AdminLog.query.order_by(AdminLog.timestamp.desc()).limit(10).all()

    # Prepara i dati di log per il template
    log_data = []
    for log in admin_logs:
        # Trova l'admin e l'utente target
        admin = User.query.get(log.admin_id)
        target = User.query.get(log.target_user_id)

        # Determina la classe CSS per l'azione
        action_class = ""
        if "update" in log.action.lower():
            action_class = "text-blue-600 dark:text-blue-400"
        elif "create" in log.action.lower():
            action_class = "text-green-600 dark:text-green-400"
        elif "delete" in log.action.lower() or "disable" in log.action.lower():
            action_class = "text-red-600 dark:text-red-400"
        else:
            action_class = "text-gray-600 dark:text-gray-400"

        log_data.append({
            'admin_name': f"{admin.first_name} {admin.last_name}" if admin else "Sistema",
            'action': log.action,
            'action_class': action_class,
            'target_user': f"{target.first_name} {target.last_name}" if target else "N/A",
            'timestamp': log.timestamp
        })

    return render_template('dashboard/admin.html', users=users_data, admin_logs=log_data)

@dashboard_bp.route('/admin/update-role/<int:user_id>', methods=['POST'])
@login_required
@permission_required(PERMISSION_MANAGE_USERS)
def update_user_role(user_id):
    """Aggiorna il ruolo di un utente."""
    user = User.query.get_or_404(user_id)
    role = request.form.get('role')

    if role not in ['admin', 'manager', 'employee', 'sales']:
        flash('Ruolo non valido', 'error')
        return redirect(url_for('dashboard.admin'))

    # Salva il ruolo precedente per il log
    old_role = user.role

    # Aggiorna il ruolo
    user.role = role

    # Registra l'azione nel log
    log = AdminLog(
        admin_id=current_user.id,
        action=f"Modificato ruolo da '{old_role}' a '{role}'",
        target_user_id=user.id,
        timestamp=datetime.utcnow()
    )

    db.session.add(log)
    db.session.commit()

    flash(f'Ruolo di {user.first_name} {user.last_name} aggiornato a {role}', 'success')
    return redirect(url_for('dashboard.admin'))

@dashboard_bp.route('/admin/toggle-status/<int:user_id>', methods=['POST'])
@login_required
@permission_required(PERMISSION_MANAGE_USERS)
def toggle_user_status(user_id):
    """Attiva o disattiva un account utente."""
    user = User.query.get_or_404(user_id)

    # Non permettere di disattivare il proprio account
    if user.id == current_user.id:
        flash('Non puoi disattivare il tuo account', 'error')
        return redirect(url_for('dashboard.admin'))

    # Inverti lo stato attuale
    user.is_active = not user.is_active

    # Registra l'azione nel log
    action = "Attivato account" if user.is_active else "Disattivato account"
    log = AdminLog(
        admin_id=current_user.id,
        action=action,
        target_user_id=user.id,
        timestamp=datetime.utcnow()
    )

    db.session.add(log)
    db.session.commit()

    status = "attivato" if user.is_active else "disattivato"
    flash(f'Account di {user.first_name} {user.last_name} {status}', 'success')
    return redirect(url_for('dashboard.admin'))

@dashboard_bp.route('/admin/create-user', methods=['POST'])
@login_required
@permission_required(PERMISSION_MANAGE_USERS)
def create_user():
    """Crea un nuovo utente."""
    # Ottenere i dati dal form
    first_name = request.form.get('first_name')
    last_name = request.form.get('last_name')
    email = request.form.get('email')
    username = request.form.get('username')
    password = request.form.get('password')
    role = request.form.get('role')

    # Validazione base
    if not all([first_name, last_name, email, username, password, role]):
        flash('Tutti i campi sono obbligatori.', 'error')
        return redirect(url_for('dashboard.admin'))

    # Verificare che l'email e lo username non siano già in uso
    existing_email = User.query.filter_by(email=email).first()
    existing_username = User.query.filter_by(username=username).first()

    if existing_email:
        flash('Email già in uso. Scegli un\'altra email.', 'error')
        return redirect(url_for('dashboard.admin'))

    if existing_username:
        flash('Username già in uso. Scegli un altro username.', 'error')
        return redirect(url_for('dashboard.admin'))

    # Creare il nuovo utente
    new_user = User(
        first_name=first_name,
        last_name=last_name,
        email=email,
        username=username,
        role=role,
        is_active=True,
        created_at=datetime.utcnow()
    )
    new_user.set_password(password)

    # Aggiungere l'utente al database
    db.session.add(new_user)

    # Registrare l'azione nel log
    log = AdminLog(
        admin_id=current_user.id,
        action=f"Creato nuovo utente '{username}' con ruolo '{role}'",
        target_user_id=None,  # Verrà aggiornato dopo il commit
        timestamp=datetime.utcnow()
    )
    db.session.add(log)
    db.session.commit()

    # Aggiornare l'ID dell'utente target nel log
    log.target_user_id = new_user.id
    db.session.commit()

    # Creare una notifica per il nuovo utente
    notification = Notification(
        user_id=new_user.id,
        title="Benvenuto in DatPortal",
        message=f"Benvenuto {first_name}! Il tuo account è stato creato da {current_user.first_name} {current_user.last_name}.",
        type="success",
        is_read=False,
        created_at=datetime.utcnow()
    )
    db.session.add(notification)
    db.session.commit()

    flash(f'Utente {username} creato con successo.', 'success')
    return redirect(url_for('dashboard.admin'))

@dashboard_bp.route('/api/users/edit', methods=['POST'])
@login_required
@permission_required(PERMISSION_MANAGE_USERS)
def api_edit_user():
    """API endpoint per modificare un utente."""
    try:
        data = request.json
        user_id = data.get('id')
        name = data.get('name', '')
        role = data.get('role')
        is_active = data.get('is_active', True)
        password = data.get('password')

        user = User.query.get(user_id)
        if not user:
            return jsonify({'success': False, 'message': 'Utente non trovato.'}), 404

        # Dividiamo il nome in nome e cognome (assumendo formato "Nome Cognome")
        name_parts = name.split(maxsplit=1)
        first_name = name_parts[0]
        last_name = name_parts[1] if len(name_parts) > 1 else ""

        user.first_name = first_name
        user.last_name = last_name
        user.role = role
        user.is_active = is_active

        # Cambiamo la password solo se fornita
        if password and password.strip():
            user.set_password(password)

        # Registrare l'azione nel log
        log = AdminLog(
            admin_id=current_user.id,
            action=f"Modificato utente '{user.username}' (ID: {user.id})",
            target_user_id=user.id,
            timestamp=datetime.utcnow()
        )
        db.session.add(log)

        db.session.commit()

        # Formattiamo il nome completo
        name = f"{user.first_name} {user.last_name}".strip()
        if not name:
            name = user.username or user.email or "Utente"

        return jsonify({
            'success': True,
            'message': 'Utente aggiornato con successo.',
            'user': {
                'id': user.id,
                'name': name,
                'email': user.email,
                'role': user.role,
                'is_active': user.is_active,
                'created_at': user.created_at.isoformat() if user.created_at else None
            }
        })
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Errore durante l'aggiornamento dell'utente: {str(e)}")
        return jsonify({'success': False, 'message': f'Errore: {str(e)}'}), 500

@dashboard_bp.route('/api/users/add', methods=['POST'])
@login_required
@permission_required(PERMISSION_MANAGE_USERS)
def api_add_user():
    """API endpoint per aggiungere un nuovo utente."""
    try:
        data = request.json
        name = data.get('name', '')
        email = data.get('email')
        role = data.get('role')
        password = data.get('password')

        if not all([name, email, role, password]):
            return jsonify({'success': False, 'message': 'Tutti i campi sono obbligatori.'}), 400

        # Verifica che l'email non sia già in uso
        existing_email = User.query.filter_by(email=email).first()
        if existing_email:
            return jsonify({'success': False, 'message': 'Email già in uso.'}), 400

        # Dividiamo il nome in nome e cognome (assumendo formato "Nome Cognome")
        name_parts = name.split(maxsplit=1)
        first_name = name_parts[0]
        last_name = name_parts[1] if len(name_parts) > 1 else ""

        # Creare username dall'email
        username = email.split('@')[0]

        # Verificare che lo username non sia già in uso e generarne uno unico se necessario
        base_username = username
        count = 1
        while User.query.filter_by(username=username).first():
            username = f"{base_username}{count}"
            count += 1

        # Creare il nuovo utente
        new_user = User(
            first_name=first_name,
            last_name=last_name,
            email=email,
            username=username,
            role=role,
            is_active=True,
            created_at=datetime.utcnow()
        )
        new_user.set_password(password)

        db.session.add(new_user)

        # Registrare l'azione nel log
        log = AdminLog(
            admin_id=current_user.id,
            action=f"Creato nuovo utente '{username}' con ruolo '{role}'",
            target_user_id=None,
            timestamp=datetime.utcnow()
        )
        db.session.add(log)
        db.session.flush()  # Per ottenere l'ID del nuovo utente

        # Aggiornare l'ID dell'utente target nel log
        log.target_user_id = new_user.id

        # Creare una notifica per il nuovo utente
        notification = Notification(
            user_id=new_user.id,
            title="Benvenuto in DatPortal",
            message=f"Benvenuto {first_name}! Il tuo account è stato creato da {current_user.first_name} {current_user.last_name}.",
            type="success",
            is_read=False,
            created_at=datetime.utcnow()
        )
        db.session.add(notification)

        db.session.commit()

        # Formatta il nome completo
        name = f"{new_user.first_name} {new_user.last_name}".strip()
        if not name:
            name = new_user.username or new_user.email or "Utente"

        return jsonify({
            'success': True,
            'message': 'Utente creato con successo.',
            'user': {
                'id': new_user.id,
                'name': name,
                'email': new_user.email,
                'role': new_user.role,
                'is_active': new_user.is_active,
                'created_at': new_user.created_at.isoformat() if new_user.created_at else None
            }
        })
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Errore durante la creazione dell'utente: {str(e)}")
        return jsonify({'success': False, 'message': f'Errore: {str(e)}'}), 500
