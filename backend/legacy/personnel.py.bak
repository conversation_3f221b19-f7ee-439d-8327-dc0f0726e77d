from flask import Blueprint, render_template, redirect, url_for, request, flash, current_app, send_from_directory, make_response
from flask_login import login_required, current_user
from datetime import datetime
from werkzeug.security import generate_password_hash
from werkzeug.utils import secure_filename
from sqlalchemy import desc
import os
import json
from reportlab.lib.pagesizes import A4
from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import inch
from reportlab.lib import colors
from io import BytesIO

from app import db
from models import User, Skill, Project, Task, Timesheet, Department, UserProfile, UserSkill, project_team
# Importazioni per RBAC
from utils.decorators import permission_required, admin_required
from utils.permissions import PERMISSION_VIEW_PERSONNEL_DATA, PERMISSION_MANAGE_USERS, PERMISSION_ASSIGN_ROLES, ALL_ROLES, user_has_permission, ROLE_ADMIN, ROLE_MANAGER
# Importazioni per CV
from utils.cv_parser import extract_text_from_cv, is_valid_cv_file, get_file_size_mb
from ai_services import extract_skills_from_cv, generate_cv_html
# Importazioni per immagini profilo
from utils.image_utils import save_profile_image, remove_profile_image, get_profile_image_url
# Importazioni per CSRF
from extensions import csrf

personnel_bp = Blueprint('personnel', __name__, url_prefix='/personnel')

@personnel_bp.route('/')
@login_required
@permission_required(PERMISSION_VIEW_PERSONNEL_DATA)
def index():
    # Parametri paginazione
    page = request.args.get('page', 1, type=int)
    per_page = 12  # 12 utenti per pagina (3x4 grid)

    # Filtri di ricerca
    search_query = request.args.get('q', '').strip()
    skill_filter = request.args.get('skill', None)
    department_filter = request.args.get('department', None)

    # Query base con controllo permessi
    users_query = User.query.filter_by(is_active=True)

    # Controllo permessi per ruolo
    if current_user.role not in [ROLE_ADMIN, ROLE_MANAGER, 'human_resources']:
        if current_user.department:
            users_query = users_query.filter(User.department == current_user.department)
        else:
            users_query = users_query.filter(User.id == current_user.id)

    # Filtro ricerca testuale (nome, cognome, email)
    if search_query:
        search_filter = db.or_(
            User.first_name.ilike(f'%{search_query}%'),
            User.last_name.ilike(f'%{search_query}%'),
            User.email.ilike(f'%{search_query}%'),
            User.position.ilike(f'%{search_query}%')
        )
        users_query = users_query.filter(search_filter)

    # Filtro dipartimento
    if department_filter:
        # Controllo permessi per dipartimento
        if current_user.role in [ROLE_ADMIN, ROLE_MANAGER, 'human_resources']:
            users_query = users_query.filter(User.department == department_filter)
        else:
            if current_user.department == department_filter:
                users_query = users_query.filter(User.department == department_filter)
            else:
                flash("Non hai il permesso di visualizzare questo dipartimento.", "danger")
                return redirect(url_for('dashboard.index'))

    # Filtro competenza (più complesso, richiede join)
    if skill_filter:
        skill = Skill.query.get(skill_filter)
        if skill:
            # Join con UserSkill per filtrare per competenza
            users_query = users_query.join(UserSkill).filter(UserSkill.skill_id == skill_filter)

    # Ordina per cognome, nome
    users_query = users_query.order_by(User.last_name, User.first_name)

    # Applica paginazione
    pagination = users_query.paginate(
        page=page,
        per_page=per_page,
        error_out=False
    )
    users_list = pagination.items

    # Get all skills for filtering dropdown
    skills = Skill.query.order_by(Skill.category, Skill.name).all()

    # Get unique departments for filtering dropdown
    if current_user.role in [ROLE_ADMIN, ROLE_MANAGER, 'human_resources']:
        departments = db.session.query(User.department).distinct().filter(User.department.isnot(None)).all()
        departments = [d[0] for d in departments]
    else:
        # Per ruoli diversi da admin/manager/HR, mostra solo il proprio dipartimento
        if current_user.department:
            departments = [current_user.department]
        else:
            departments = []

    active_skill_obj = Skill.query.get(skill_filter) if skill_filter else None

    return render_template(
        'personnel/index.html',
        users_list=users_list,
        pagination=pagination,
        skills=skills,
        departments=departments,
        active_skill=active_skill_obj,
        active_department=department_filter,
        search_query=search_query
    )

@personnel_bp.route('/profile/<int:user_id>')
@login_required
def view_profile(user_id):
    user_to_view = User.query.get_or_404(user_id)

    # Controllo permesso: l'utente può vedere il proprio profilo OPPURE ha il permesso generale
    if user_to_view.id != current_user.id and not user_has_permission(current_user.role, PERMISSION_VIEW_PERSONNEL_DATA):
        flash("Non hai il permesso di visualizzare questo profilo.", "danger")
        # Reindirizza alla lista del personale se l'utente ha il permesso per vederla, altrimenti alla dashboard.
        if user_has_permission(current_user.role, PERMISSION_VIEW_PERSONNEL_DATA):
            return redirect(url_for('personnel.index'))
        else:
            return redirect(url_for('dashboard.index'))

    projects = user_to_view.projects
    tasks = Task.query.filter_by(assignee_id=user_id).all()
    recent_timesheets = Timesheet.query.filter_by(
        user_id=user_id
    ).order_by(desc(Timesheet.date)).limit(10).all()

    # Prepara dati timesheet mensile (mese corrente)
    from datetime import datetime, date
    import calendar

    current_date = date.today()
    current_month = current_date.month
    current_year = current_date.year

    # Ottieni tutti i timesheet del mese corrente per l'utente
    month_timesheets = Timesheet.query.filter(
        Timesheet.user_id == user_id,
        db.extract('month', Timesheet.date) == current_month,
        db.extract('year', Timesheet.date) == current_year
    ).all()

    # Organizza i dati per task e giorni
    timesheet_data = {}
    daily_totals = {}
    month_total = 0
    worked_days = set()

    for entry in month_timesheets:
        day = entry.date.day
        task_key = f"{entry.task.name if entry.task else 'Nessun task'} - {entry.project.name if entry.project else 'Nessun progetto'}"

        if task_key not in timesheet_data:
            timesheet_data[task_key] = {
                'task_name': entry.task.name if entry.task else 'Nessun task',
                'project_name': entry.project.name if entry.project else 'Nessun progetto',
                'daily_hours': {},
                'total_hours': 0
            }

        # Aggiungi ore al giorno specifico
        if day not in timesheet_data[task_key]['daily_hours']:
            timesheet_data[task_key]['daily_hours'][day] = 0
        timesheet_data[task_key]['daily_hours'][day] += entry.hours
        timesheet_data[task_key]['total_hours'] += entry.hours

        # Aggiorna totali giornalieri
        if day not in daily_totals:
            daily_totals[day] = 0
        daily_totals[day] += entry.hours

        # Aggiorna totale mensile e giorni lavorati
        month_total += entry.hours
        worked_days.add(day)

    # Converti in lista per il template
    timesheet_data_list = list(timesheet_data.values())

    # Calcola giorni nel mese
    days_in_month = calendar.monthrange(current_year, current_month)[1]

    # Ottieni competenze disponibili per il modal (escludi quelle già possedute nel nuovo sistema)
    user_skill_ids = [user_skill.skill_id for user_skill in user_to_view.detailed_skills]
    available_skills = Skill.query.filter(~Skill.id.in_(user_skill_ids)).order_by(Skill.category, Skill.name).all()

    # Ottieni dipartimenti per il modal di trasferimento
    all_departments = Department.query.filter_by(is_active=True).order_by(Department.name).all()

    return render_template(
        'personnel/profile.html',
        user=user_to_view, # Rinominato per chiarezza
        projects=projects,
        tasks=tasks,
        recent_timesheets=recent_timesheets,
        timesheet_data=timesheet_data_list,
        daily_totals=daily_totals,
        month_total=month_total,
        worked_days=len(worked_days),
        days_in_month=days_in_month,
        current_month=current_month,
        current_year=current_year,
        available_skills=available_skills,
        all_departments=all_departments,
        is_self=(user_id == current_user.id)
    )

@personnel_bp.route('/skills')
@login_required
def skills():
    page = request.args.get('page', 1, type=int)
    per_page = 20

    skills_pagination = Skill.query.order_by(Skill.category, Skill.name).paginate(
        page=page, per_page=per_page, error_out=False
    )

    categories = db.session.query(Skill.category).distinct().all()
    categories = [c[0] for c in categories if c[0]]

    return render_template(
        'personnel/skills.html',
        skills=skills_pagination, # Passato l'oggetto paginazione
        categories=categories
    )

@personnel_bp.route('/skills/matrix')
@login_required
@permission_required(PERMISSION_VIEW_PERSONNEL_DATA)
def skills_matrix():
    """Vista Skill Matrix - tabella utenti vs competenze"""
    # Filtri
    department_filter = request.args.get('department')
    category_filter = request.args.get('category')
    skill_filter = request.args.get('skill')

    # Query utenti con filtro dipartimento
    users_query = User.query.filter(User.is_active == True)
    if department_filter:
        users_query = users_query.filter(User.department == department_filter)

    # Controllo permessi per dipartimenti
    if current_user.role not in [ROLE_ADMIN, ROLE_MANAGER, 'human_resources']:
        if current_user.department:
            users_query = users_query.filter(User.department == current_user.department)
        else:
            users_query = users_query.filter(User.id == current_user.id)

    users = users_query.order_by(User.last_name, User.first_name).all()

    # Query competenze con filtri
    skills_query = Skill.query
    if category_filter:
        skills_query = skills_query.filter(Skill.category == category_filter)
    if skill_filter:
        skills_query = skills_query.filter(Skill.id == skill_filter)

    skills = skills_query.order_by(Skill.category, Skill.name).all()

    # Costruisci matrice competenze
    matrix_data = {}
    for user in users:
        matrix_data[user.id] = {}
        for user_skill in user.detailed_skills:
            matrix_data[user.id][user_skill.skill_id] = {
                'proficiency_level': user_skill.proficiency_level,
                'years_experience': user_skill.years_experience,
                'is_certified': user_skill.is_certified,
                'certification_name': user_skill.certification_name
            }

    # Dati per filtri
    departments = db.session.query(User.department).distinct().filter(User.department.isnot(None)).all()
    departments = [d[0] for d in departments]

    categories = db.session.query(Skill.category).distinct().filter(Skill.category.isnot(None)).all()
    categories = [c[0] for c in categories]

    all_skills = Skill.query.order_by(Skill.name).all()

    return render_template(
        'personnel/skills_matrix.html',
        users=users,
        skills=skills,
        matrix_data=matrix_data,
        departments=departments,
        categories=categories,
        all_skills=all_skills,
        active_department=department_filter,
        active_category=category_filter,
        active_skill=skill_filter
    )


@personnel_bp.route('/profile/<int:user_id>/skills/add', methods=['POST'])
@login_required
def add_user_skill(user_id):
    """Aggiungi competenza a un utente specifico con livelli"""
    user_to_edit = User.query.get_or_404(user_id)

    # Controllo permessi: solo il proprio profilo o admin/manager/HR
    if user_to_edit.id != current_user.id and not user_has_permission(current_user.role, PERMISSION_MANAGE_USERS) and current_user.role != 'human_resources':
        flash("Non hai il permesso di modificare le competenze di questo utente.", "danger")
        return redirect(url_for('personnel.view_profile', user_id=user_id))

    skill_id = request.form.get('skill_id')
    proficiency_level = request.form.get('proficiency_level', 1, type=int)
    years_experience = request.form.get('years_experience', 0.0, type=float)
    is_certified = request.form.get('is_certified') == 'on'
    certification_name = request.form.get('certification_name', '').strip()
    notes = request.form.get('notes', '').strip()

    if not skill_id:
        flash('Competenza non selezionata', 'error')
        return redirect(url_for('personnel.view_profile', user_id=user_id) + '#skills')

    skill_to_add = Skill.query.get_or_404(skill_id)

    # Verifica se l'utente ha già questa competenza nel nuovo sistema
    existing_user_skill = UserSkill.query.filter_by(user_id=user_id, skill_id=skill_id).first()
    if existing_user_skill:
        flash('L\'utente ha già questa competenza', 'warning')
        return redirect(url_for('personnel.view_profile', user_id=user_id) + '#skills')

    # Crea nuova UserSkill con livelli
    new_user_skill = UserSkill(
        user_id=user_id,
        skill_id=skill_id,
        proficiency_level=max(1, min(5, proficiency_level)),  # Assicura range 1-5
        years_experience=max(0, years_experience),
        is_certified=is_certified,
        certification_name=certification_name if certification_name else None,
        notes=notes if notes else None,
        self_assessed=True,
        manager_assessed=False
    )

    try:
        db.session.add(new_user_skill)
        db.session.commit()
        flash('Competenza aggiunta con successo', 'success')
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Errore aggiunta competenza: {str(e)}")
        flash('Errore durante l\'aggiunta della competenza', 'error')

    return redirect(url_for('personnel.view_profile', user_id=user_id) + '#skills')

@personnel_bp.route('/profile/<int:user_id>/skills/remove/<int:skill_id>', methods=['POST'])
@login_required
def remove_user_skill(user_id, skill_id):
    """Rimuovi competenza da un utente specifico"""
    user_to_edit = User.query.get_or_404(user_id)

    # Controllo permessi: solo il proprio profilo o admin/manager/HR
    if user_to_edit.id != current_user.id and not user_has_permission(current_user.role, PERMISSION_MANAGE_USERS) and current_user.role != 'human_resources':
        flash("Non hai il permesso di modificare le competenze di questo utente.", "danger")
        return redirect(url_for('personnel.view_profile', user_id=user_id))

    # Trova e rimuovi dalla tabella UserSkill
    user_skill_to_remove = UserSkill.query.filter_by(user_id=user_id, skill_id=skill_id).first()

    if user_skill_to_remove:
        try:
            db.session.delete(user_skill_to_remove)
            db.session.commit()
            flash('Competenza rimossa con successo', 'success')
        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"Errore rimozione competenza: {str(e)}")
            flash('Errore durante la rimozione della competenza', 'error')
    else:
        flash('Competenza non trovata nel profilo dell\'utente', 'error')

    return redirect(url_for('personnel.view_profile', user_id=user_id) + '#skills')

@personnel_bp.route('/skills/create', methods=['POST'])
@login_required
@admin_required
def create_skill():
    # Il controllo if current_user.role != 'admin' non è più necessario grazie al decoratore
    name = request.form.get('name')
    category = request.form.get('category')
    description = request.form.get('description')

    if not name or not category:
        flash('Nome e categoria sono obbligatori', 'error')
        return redirect(url_for('personnel.skills'))

    existing_skill = Skill.query.filter_by(name=name).first()
    if existing_skill:
        flash('Una competenza con questo nome esiste già', 'error')
        return redirect(url_for('personnel.skills'))

    new_skill_obj = Skill(
        name=name,
        category=category,
        description=description
    )

    try:
        db.session.add(new_skill_obj)
        db.session.commit()
        flash('Competenza creata con successo', 'success')
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Skill creation error: {str(e)}")
        flash('Si è verificato un errore durante la creazione della competenza', 'error')

    return redirect(url_for('personnel.skills'))

@personnel_bp.route('/skills/edit/<int:skill_id>', methods=['GET', 'POST'])
@login_required
@admin_required
def edit_skill(skill_id):
    """Modifica una competenza esistente (solo admin)"""
    skill = Skill.query.get_or_404(skill_id)

    if request.method == 'POST':
        name = request.form.get('name', '').strip()
        category = request.form.get('category', '').strip()
        description = request.form.get('description', '').strip()

        if not name or not category:
            flash('Nome e categoria sono obbligatori', 'error')
            return redirect(url_for('personnel.edit_skill', skill_id=skill_id))

        # Verifica che non esista già una skill con lo stesso nome (escludendo quella corrente)
        existing_skill = Skill.query.filter(
            Skill.name == name,
            Skill.id != skill_id
        ).first()

        if existing_skill:
            flash('Una competenza con questo nome esiste già', 'error')
            return redirect(url_for('personnel.edit_skill', skill_id=skill_id))

        try:
            skill.name = name
            skill.category = category
            skill.description = description

            db.session.commit()
            flash('Competenza aggiornata con successo', 'success')
            return redirect(url_for('personnel.skills'))

        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"Skill update error: {str(e)}")
            flash('Si è verificato un errore durante l\'aggiornamento della competenza', 'error')

    # GET request - mostra form di modifica
    categories = db.session.query(Skill.category).distinct().all()
    categories = [c[0] for c in categories if c[0]]

    return render_template(
        'personnel/edit_skill.html',
        skill=skill,
        categories=categories
    )

@personnel_bp.route('/skills/delete/<int:skill_id>', methods=['POST'])
@login_required
@admin_required
def delete_skill(skill_id):
    """Elimina una competenza (solo admin)"""
    skill = Skill.query.get_or_404(skill_id)

    # Verifica se la skill è utilizzata da qualche utente
    users_with_skill = User.query.filter(User.skills.contains(skill)).count()

    if users_with_skill > 0:
        flash(f'Impossibile eliminare la competenza "{skill.name}": è utilizzata da {users_with_skill} utenti. Rimuovila prima dai profili utente.', 'error')
        return redirect(url_for('personnel.skills'))

    try:
        skill_name = skill.name
        db.session.delete(skill)
        db.session.commit()
        flash(f'Competenza "{skill_name}" eliminata con successo', 'success')

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Skill deletion error: {str(e)}")
        flash('Si è verificato un errore durante l\'eliminazione della competenza', 'error')

    return redirect(url_for('personnel.skills'))

@personnel_bp.route('/admin')
@login_required
@permission_required(PERMISSION_MANAGE_USERS)
def admin():
    # Il controllo if current_user.role != 'admin' non è più necessario
    users_list_admin = User.query.all()
    return render_template('personnel/admin.html', users=users_list_admin)

@personnel_bp.route('/admin/toggle-activation/<int:user_id>', methods=['POST'])
@login_required
@permission_required(PERMISSION_MANAGE_USERS)
def toggle_activation(user_id):
    # Il controllo if current_user.role != 'admin' non è più necessario
    user_to_toggle = User.query.get_or_404(user_id)

    if user_to_toggle.id == current_user.id:
        flash('Non puoi disattivare il tuo account', 'error')
        return redirect(url_for('personnel.admin'))

    user_to_toggle.is_active = not user_to_toggle.is_active
    db.session.commit()

    status_msg = "attivato" if user_to_toggle.is_active else "disattivato"
    flash(f'Account di {user_to_toggle.username} {status_msg} con successo', 'success')

    return redirect(url_for('personnel.admin'))

@personnel_bp.route('/admin/change-role/<int:user_id>', methods=['POST'])
@login_required
@permission_required(PERMISSION_ASSIGN_ROLES)
def change_role(user_id):
    # Il controllo if current_user.role != 'admin' non è più necessario
    user_to_change = User.query.get_or_404(user_id)
    new_role_form = request.form.get('role')

    # Usa ALL_ROLES da utils.permissions per validare
    if new_role_form not in ALL_ROLES:
        flash(f'Ruolo "{new_role_form}" non valido. Ruoli validi: {", ".join(ALL_ROLES)}', 'error')
        return redirect(url_for('personnel.admin'))

    # Non permettere di cambiare il proprio ruolo a un ruolo inferiore se si è l'unico admin? (Logica più complessa, per ora omessa)
    # Non permettere di declassare l'ultimo admin?

    user_to_change.role = new_role_form
    db.session.commit()

    flash(f'Ruolo di {user_to_change.username} aggiornato a {new_role_form}', 'success')

    return redirect(url_for('personnel.admin'))

# ===== SUBTASK 7.2: Employee Profile Management =====

@personnel_bp.route('/profile/<int:user_id>/edit', methods=['GET', 'POST'])
@login_required
def edit_profile(user_id):
    """Modifica profilo utente (proprio o di altri se admin/manager)"""
    user_to_edit = User.query.get_or_404(user_id)

    # Controllo permessi: solo il proprio profilo o admin/manager
    if user_to_edit.id != current_user.id and not user_has_permission(current_user.role, PERMISSION_MANAGE_USERS):
        flash("Non hai il permesso di modificare questo profilo.", "danger")
        return redirect(url_for('personnel.view_profile', user_id=user_id))

    # Ottieni o crea il profilo HR
    profile = user_to_edit.profile
    if not profile:
        profile = UserProfile(user_id=user_to_edit.id)
        db.session.add(profile)
        db.session.flush()  # Per ottenere l'ID

    if request.method == 'POST':
        try:
            # Aggiorna dati base utente
            user_to_edit.first_name = request.form.get('first_name', '').strip()
            user_to_edit.last_name = request.form.get('last_name', '').strip()
            user_to_edit.email = request.form.get('email', '').strip()
            user_to_edit.phone = request.form.get('phone', '').strip()
            user_to_edit.position = request.form.get('position', '').strip()
            user_to_edit.bio = request.form.get('bio', '').strip()

            # Aggiorna dipartimento
            department_id = request.form.get('department_id')
            if department_id and department_id.isdigit():
                user_to_edit.department_id = int(department_id)

            # Data di assunzione
            hire_date_str = request.form.get('hire_date')
            if hire_date_str:
                user_to_edit.hire_date = datetime.strptime(hire_date_str, '%Y-%m-%d').date()

            # Aggiorna profilo HR
            profile.employee_id = request.form.get('employee_id', '').strip()
            profile.job_title = request.form.get('job_title', '').strip()
            profile.address = request.form.get('address', '').strip()
            profile.emergency_contact_name = request.form.get('emergency_contact_name', '').strip()
            profile.emergency_contact_phone = request.form.get('emergency_contact_phone', '').strip()
            profile.emergency_contact_relationship = request.form.get('emergency_contact_relationship', '').strip()
            profile.employment_type = request.form.get('employment_type', 'full_time')
            profile.work_location = request.form.get('work_location', '').strip()

            # Data di nascita
            birth_date_str = request.form.get('birth_date')
            if birth_date_str:
                profile.birth_date = datetime.strptime(birth_date_str, '%Y-%m-%d').date()

            # Capacità lavorativa
            weekly_hours = request.form.get('weekly_hours')
            if weekly_hours:
                profile.weekly_hours = float(weekly_hours)

            daily_hours = request.form.get('daily_hours')
            if daily_hours:
                profile.daily_hours = float(daily_hours)

            # Solo admin/manager possono modificare dati sensibili
            if user_has_permission(current_user.role, PERMISSION_MANAGE_USERS):
                salary = request.form.get('salary')
                if salary:
                    profile.salary = float(salary)

                profile.notes = request.form.get('notes', '').strip()

                # Date contrattuali
                probation_end_str = request.form.get('probation_end_date')
                if probation_end_str:
                    profile.probation_end_date = datetime.strptime(probation_end_str, '%Y-%m-%d').date()

                contract_end_str = request.form.get('contract_end_date')
                if contract_end_str:
                    profile.contract_end_date = datetime.strptime(contract_end_str, '%Y-%m-%d').date()

            # Gestione upload immagine profilo
            profile_image = request.files.get('profile_image')
            remove_image = request.form.get('remove_image') == 'true'

            if remove_image and user_to_edit.profile_image:
                # Rimuovi immagine esistente
                # Estrai il percorso relativo dall'URL
                old_path = user_to_edit.profile_image.replace('/static/uploads/', '') if user_to_edit.profile_image.startswith('/static/uploads/') else user_to_edit.profile_image
                if remove_profile_image(old_path):
                    user_to_edit.profile_image = None
                    flash('Foto profilo rimossa con successo!', 'success')
                else:
                    flash('Errore durante la rimozione della foto profilo.', 'warning')

            elif profile_image and profile_image.filename:
                # Upload nuova immagine
                success, file_path, error_msg = save_profile_image(profile_image, user_to_edit.id)

                if success:
                    # Rimuovi immagine precedente se esiste
                    if user_to_edit.profile_image:
                        # Estrai il percorso relativo dall'URL
                        old_path = user_to_edit.profile_image.replace('/static/uploads/', '') if user_to_edit.profile_image.startswith('/static/uploads/') else user_to_edit.profile_image
                        remove_profile_image(old_path)

                    # Aggiorna percorso immagine
                    user_to_edit.profile_image = get_profile_image_url(file_path)
                    flash('Foto profilo aggiornata con successo!', 'success')
                else:
                    flash(f'Errore upload foto profilo: {error_msg}', 'error')

            # Calcola completamento profilo
            profile.calculate_completion()

            db.session.commit()
            flash('Profilo aggiornato con successo!', 'success')
            return redirect(url_for('personnel.view_profile', user_id=user_id))

        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"Errore aggiornamento profilo: {str(e)}")
            flash('Errore durante l\'aggiornamento del profilo.', 'error')

    # Ottieni dipartimenti per il dropdown
    departments = Department.query.filter_by(is_active=True).order_by(Department.name).all()

    return render_template(
        'personnel/edit_profile.html',
        user=user_to_edit,
        profile=profile,
        departments=departments,
        is_self=(user_id == current_user.id),
        can_edit_sensitive=user_has_permission(current_user.role, PERMISSION_MANAGE_USERS)
    )

@personnel_bp.route('/directory')
@login_required
@permission_required(PERMISSION_VIEW_PERSONNEL_DATA)
def directory():
    """Directory aziendale con ricerca avanzata"""
    page = request.args.get('page', 1, type=int)
    per_page = 20

    # Filtri
    search_query = request.args.get('q', '')
    department_filter = request.args.get('department', '')
    role_filter = request.args.get('role', '')
    location_filter = request.args.get('location', '')

    # Query base
    users_query = User.query.filter_by(is_active=True)

    # Applica filtri di permesso
    if current_user.role not in [ROLE_ADMIN, ROLE_MANAGER, 'human_resources']:
        if current_user.department:
            users_query = users_query.filter(User.department == current_user.department)
        else:
            users_query = users_query.filter(User.id == current_user.id)

    # Applica filtri di ricerca
    if search_query:
        search_term = f"%{search_query}%"
        users_query = users_query.filter(
            db.or_(
                User.first_name.ilike(search_term),
                User.last_name.ilike(search_term),
                User.email.ilike(search_term),
                User.position.ilike(search_term)
            )
        )

    if department_filter:
        if department_filter.isdigit():
            users_query = users_query.filter(User.department_id == int(department_filter))
        else:
            users_query = users_query.filter(User.department == department_filter)

    if role_filter:
        users_query = users_query.filter(User.role == role_filter)

    if location_filter:
        users_query = users_query.join(UserProfile).filter(UserProfile.work_location.ilike(f"%{location_filter}%"))

    # Ordinamento
    sort_by = request.args.get('sort', 'name')
    if sort_by == 'name':
        users_query = users_query.order_by(User.first_name, User.last_name)
    elif sort_by == 'department':
        users_query = users_query.order_by(User.department, User.first_name)
    elif sort_by == 'role':
        users_query = users_query.order_by(User.role, User.first_name)
    elif sort_by == 'hire_date':
        users_query = users_query.order_by(User.hire_date.desc().nullslast())

    # Paginazione
    pagination = users_query.paginate(page=page, per_page=per_page, error_out=False)

    # Dati per filtri
    departments = Department.query.filter_by(is_active=True).order_by(Department.name).all()
    roles = ['employee', 'manager', 'admin']
    locations = db.session.query(UserProfile.work_location).distinct().filter(UserProfile.work_location.isnot(None)).all()
    locations = [loc[0] for loc in locations if loc[0]]

    return render_template(
        'personnel/directory.html',
        pagination=pagination,
        departments=departments,
        roles=roles,
        locations=locations,
        current_filters={
            'q': search_query,
            'department': department_filter,
            'role': role_filter,
            'location': location_filter,
            'sort': sort_by
        }
    )

@personnel_bp.route('/orgchart')
@login_required
@permission_required(PERMISSION_VIEW_PERSONNEL_DATA)
def orgchart():
    """Visualizzazione organigramma aziendale con filtri"""

    # Filtri
    search_query = request.args.get('q', '').strip()
    department_filter = request.args.get('department', '')
    role_filter = request.args.get('role', '')

    # Query base per dipartimenti
    departments_query = Department.query.filter_by(is_active=True)

    # Applica filtro dipartimento se specificato
    if department_filter:
        departments_query = departments_query.filter(Department.id == department_filter)

    # Costruisci struttura gerarchica con filtri
    def build_department_tree(parent_id=None):
        tree = []
        dept_query = departments_query.filter_by(parent_id=parent_id)

        for dept in dept_query.order_by(Department.name):
            # Query dipendenti con filtri
            employees_query = User.query.filter_by(department_id=dept.id, is_active=True)

            # Applica filtri ricerca
            if search_query:
                search_term = f"%{search_query}%"
                employees_query = employees_query.filter(
                    db.or_(
                        User.first_name.ilike(search_term),
                        User.last_name.ilike(search_term),
                        User.email.ilike(search_term),
                        User.position.ilike(search_term)
                    )
                )

            # Applica filtro ruolo
            if role_filter:
                employees_query = employees_query.filter(User.role == role_filter)

            employees = employees_query.all()

            # Include dipartimento solo se ha dipendenti (quando ci sono filtri) o sempre
            if not search_query and not role_filter or employees:
                dept_data = {
                    'id': dept.id,
                    'name': dept.name,
                    'description': dept.description,
                    'manager': dept.manager,
                    'employees': employees,
                    'employee_count': len(employees),
                    'subdepartments': build_department_tree(dept.id)
                }
                tree.append(dept_data)

        return tree

    # Costruisci albero dei dipartimenti
    department_tree = build_department_tree()

    # Ottieni utenti senza dipartimento con filtri
    users_without_dept_query = User.query.filter_by(department_id=None, is_active=True)

    if search_query:
        search_term = f"%{search_query}%"
        users_without_dept_query = users_without_dept_query.filter(
            db.or_(
                User.first_name.ilike(search_term),
                User.last_name.ilike(search_term),
                User.email.ilike(search_term),
                User.position.ilike(search_term)
            )
        )

    if role_filter:
        users_without_dept_query = users_without_dept_query.filter(User.role == role_filter)

    users_without_dept = users_without_dept_query.all()

    # Statistiche (sempre totali, non filtrate)
    total_employees = User.query.filter_by(is_active=True).count()
    total_departments = Department.query.filter_by(is_active=True).count()
    total_managers = User.query.filter_by(role='manager', is_active=True).count()

    # Dati per filtri
    all_departments = Department.query.filter_by(is_active=True).order_by(Department.name).all()
    roles = ['employee', 'manager', 'admin', 'human_resources']

    return render_template(
        'personnel/orgchart.html',
        department_tree=department_tree,
        users_without_dept=users_without_dept,
        all_departments=all_departments,
        roles=roles,
        current_filters={
            'q': search_query,
            'department': department_filter,
            'role': role_filter
        },
        stats={
            'total_employees': total_employees,
            'total_departments': total_departments,
            'total_managers': total_managers,
            'users_without_dept': len(users_without_dept)
        }
    )

@personnel_bp.route('/profile-completion')
@login_required
@permission_required(PERMISSION_VIEW_PERSONNEL_DATA)
def profile_completion():
    """Report completamento profili"""

    # Ottieni tutti gli utenti attivi con i loro profili
    users_query = User.query.filter_by(is_active=True)

    # Applica filtri di permesso
    if current_user.role not in [ROLE_ADMIN, ROLE_MANAGER, 'human_resources']:
        if current_user.department:
            users_query = users_query.filter(User.department == current_user.department)
        else:
            users_query = users_query.filter(User.id == current_user.id)

    users = users_query.all()

    # Calcola statistiche completamento
    completion_stats = {
        'total_users': len(users),
        'with_profile': 0,
        'without_profile': 0,
        'avg_completion': 0,
        'completion_ranges': {
            '0-25': 0,
            '26-50': 0,
            '51-75': 0,
            '76-100': 0
        }
    }

    total_completion = 0
    users_with_completion = []

    for user in users:
        if user.profile:
            completion_stats['with_profile'] += 1
            completion = user.profile.calculate_completion()
            total_completion += completion

            # Categorizza per range
            if completion <= 25:
                completion_stats['completion_ranges']['0-25'] += 1
            elif completion <= 50:
                completion_stats['completion_ranges']['26-50'] += 1
            elif completion <= 75:
                completion_stats['completion_ranges']['51-75'] += 1
            else:
                completion_stats['completion_ranges']['76-100'] += 1

            users_with_completion.append({
                'user': user,
                'completion': completion
            })
        else:
            completion_stats['without_profile'] += 1
            users_with_completion.append({
                'user': user,
                'completion': 0
            })

    if completion_stats['with_profile'] > 0:
        completion_stats['avg_completion'] = total_completion / completion_stats['with_profile']

    # Ordina per completamento (dal più basso al più alto)
    users_with_completion.sort(key=lambda x: x['completion'])

    return render_template(
        'personnel/profile_completion.html',
        users_with_completion=users_with_completion,
        stats=completion_stats
    )

@personnel_bp.route('/profile/<int:user_id>/cv/upload', methods=['POST'])
@login_required
def upload_cv(user_id):
    """Upload CV per un utente"""
    user_to_edit = User.query.get_or_404(user_id)

    # Controllo permessi: solo il proprio profilo o admin/manager/HR
    if user_to_edit.id != current_user.id and not user_has_permission(current_user.role, PERMISSION_MANAGE_USERS) and current_user.role != 'human_resources':
        flash("Non hai il permesso di modificare il CV di questo utente.", "danger")
        return redirect(url_for('personnel.view_profile', user_id=user_id))

    file = request.files.get('cv_file')
    analyze_skills = 'analyze_skills' in request.form

    if not file or file.filename == '':
        flash('Nessun file selezionato', 'error')
        return redirect(url_for('personnel.view_profile', user_id=user_id) + '#cv')

    if not is_valid_cv_file(file.filename):
        flash('Formato file non supportato. Usa PDF, DOCX o TXT.', 'error')
        return redirect(url_for('personnel.view_profile', user_id=user_id) + '#cv')

    try:
        # Salva il file
        filename = secure_filename(file.filename)
        upload_dir = os.path.join(current_app.config['UPLOAD_FOLDER'], 'cv')
        os.makedirs(upload_dir, exist_ok=True)

        # Nome file unico
        file_path = os.path.join(
            'cv',
            f"{user_id}_{datetime.now().strftime('%Y%m%d%H%M%S')}_{filename}"
        )
        full_path = os.path.join(current_app.config['UPLOAD_FOLDER'], file_path)
        file.save(full_path)

        # Verifica dimensione file
        if get_file_size_mb(full_path) > 10:  # Max 10MB
            os.remove(full_path)
            flash('File troppo grande. Massimo 10MB.', 'error')
            return redirect(url_for('personnel.view_profile', user_id=user_id) + '#cv')

        # Ottieni o crea profilo
        profile = user_to_edit.profile
        if not profile:
            profile = UserProfile(user_id=user_to_edit.id)
            db.session.add(profile)
            db.session.flush()

        # Rimuovi CV precedente se esiste
        if profile.current_cv_path:
            old_cv_path = os.path.join(current_app.config['UPLOAD_FOLDER'], profile.current_cv_path)
            if os.path.exists(old_cv_path):
                os.remove(old_cv_path)

        # Aggiorna profilo
        profile.current_cv_path = file_path
        profile.cv_last_updated = datetime.utcnow()

        # Analisi AI se richiesta
        if analyze_skills:
            cv_text = extract_text_from_cv(full_path)
            if cv_text:
                try:
                    analysis_result = extract_skills_from_cv(cv_text)
                    if 'error' not in analysis_result:
                        profile.cv_analysis_data = json.dumps(analysis_result)

                        # Auto-aggiungi competenze se non esistono già
                        if 'skills' in analysis_result:
                            added_skills = []
                            for skill_data in analysis_result['skills']:
                                skill_name = skill_data.get('name', '').strip()
                                skill_category = skill_data.get('category', 'Generale')

                                if skill_name:
                                    # Cerca skill esistente
                                    existing_skill = Skill.query.filter_by(name=skill_name).first()
                                    if not existing_skill:
                                        # Crea nuova skill
                                        existing_skill = Skill(
                                            name=skill_name,
                                            category=skill_category,
                                            description=f"Competenza estratta automaticamente dal CV"
                                        )
                                        db.session.add(existing_skill)
                                        db.session.flush()

                                    # Aggiungi all'utente se non ce l'ha già
                                    if existing_skill not in user_to_edit.skills:
                                        user_to_edit.skills.append(existing_skill)
                                        added_skills.append(skill_name)

                            if added_skills:
                                flash(f'Aggiunte automaticamente {len(added_skills)} competenze dal CV', 'success')

                        flash('CV caricato e analizzato con successo!', 'success')
                    else:
                        flash(f'CV caricato ma analisi AI fallita: {analysis_result.get("error", "Errore sconosciuto")}', 'warning')
                except Exception as ai_error:
                    current_app.logger.warning(f"AI analysis failed: {str(ai_error)}")
                    flash('CV caricato ma analisi AI non disponibile', 'warning')
            else:
                flash('CV caricato ma impossibile estrarre il testo per l\'analisi', 'warning')
        else:
            flash('CV caricato con successo!', 'success')

        db.session.commit()

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Errore upload CV: {str(e)}")
        flash('Errore durante il caricamento del CV', 'error')

    return redirect(url_for('personnel.view_profile', user_id=user_id) + '#cv')

@personnel_bp.route('/profile/<int:user_id>/cv/download')
@login_required
def download_cv(user_id):
    """Download CV di un utente"""
    user_to_view = User.query.get_or_404(user_id)

    # Controllo permessi
    if user_to_view.id != current_user.id and not user_has_permission(current_user.role, PERMISSION_VIEW_PERSONNEL_DATA):
        flash("Non hai il permesso di scaricare questo CV.", "danger")
        return redirect(url_for('personnel.view_profile', user_id=user_id))

    if not user_to_view.profile or not user_to_view.profile.current_cv_path:
        flash("Nessun CV disponibile per questo utente.", "warning")
        return redirect(url_for('personnel.view_profile', user_id=user_id))

    try:
        file_directory, filename = os.path.split(user_to_view.profile.current_cv_path)
        return send_from_directory(
            os.path.join(current_app.config['UPLOAD_FOLDER'], file_directory),
            filename,
            as_attachment=True
        )
    except Exception as e:
        current_app.logger.error(f"Errore download CV: {str(e)}")
        flash("Errore durante il download del CV.", "error")
        return redirect(url_for('personnel.view_profile', user_id=user_id))

@personnel_bp.route('/profile/<int:user_id>/cv/generate')
@login_required
def generate_cv(user_id):
    """Genera CV standard usando AI"""
    user_to_view = User.query.get_or_404(user_id)

    # Controllo permessi
    if user_to_view.id != current_user.id and not user_has_permission(current_user.role, PERMISSION_VIEW_PERSONNEL_DATA):
        flash("Non hai il permesso di generare il CV per questo utente.", "danger")
        return redirect(url_for('personnel.view_profile', user_id=user_id))

    try:
        # Prepara dati utente
        user_data = {
            "name": f"{user_to_view.first_name} {user_to_view.last_name}",
            "email": user_to_view.email,
            "phone": user_to_view.phone,  # phone è nel modello User
            "address": user_to_view.profile.address if user_to_view.profile else None,
            "position": user_to_view.profile.job_title if user_to_view.profile else user_to_view.position,  # job_title è in UserProfile
        }

        # Prepara dati competenze
        skills_data = {}
        if user_to_view.skills:
            skills_by_category = {}
            for skill in user_to_view.skills:
                category = skill.category or 'Generale'
                if category not in skills_by_category:
                    skills_by_category[category] = []
                skills_by_category[category].append(skill.name)
            skills_data = skills_by_category

        # Prova a generare HTML CV con AI, altrimenti usa template fallback
        cv_html = None
        try:
            cv_html = generate_cv_html(user_data, skills_data)
            if cv_html and not cv_html.startswith('<p>Errore'):
                flash('CV generato con AI!', 'success')
            else:
                cv_html = None
                flash('AI non disponibile, usando template standard', 'info')
        except Exception as ai_error:
            current_app.logger.warning(f"AI CV generation failed: {str(ai_error)}")
            cv_html = None
            flash('AI non disponibile, usando template standard', 'info')

        return render_template(
            'personnel/generated_cv.html',
            user=user_to_view,
            cv_html=cv_html,
            user_data=user_data,
            skills_data=skills_data
        )

    except Exception as e:
        current_app.logger.error(f"Errore generazione CV: {str(e)}")
        flash(f"Errore durante la generazione del CV: {str(e)}", "error")
        return redirect(url_for('personnel.view_profile', user_id=user_id))

@personnel_bp.route('/profile/<int:user_id>/cv/delete', methods=['POST'])
@login_required
def delete_cv(user_id):
    """Cancella CV di un utente"""
    user_to_edit = User.query.get_or_404(user_id)

    # Controllo permessi: solo il proprio profilo o admin/manager/HR
    if user_to_edit.id != current_user.id and not user_has_permission(current_user.role, PERMISSION_MANAGE_USERS) and current_user.role != 'human_resources':
        flash("Non hai il permesso di cancellare il CV di questo utente.", "danger")
        return redirect(url_for('personnel.view_profile', user_id=user_id))

    if not user_to_edit.profile or not user_to_edit.profile.current_cv_path:
        flash("Nessun CV da cancellare.", "warning")
        return redirect(url_for('personnel.view_profile', user_id=user_id) + '#cv')

    try:
        # Rimuovi file fisico
        cv_path = os.path.join(current_app.config['UPLOAD_FOLDER'], user_to_edit.profile.current_cv_path)
        if os.path.exists(cv_path):
            os.remove(cv_path)

        # Pulisci database
        user_to_edit.profile.current_cv_path = None
        user_to_edit.profile.cv_last_updated = None
        user_to_edit.profile.cv_analysis_data = None

        db.session.commit()
        flash('CV cancellato con successo!', 'success')

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Errore cancellazione CV: {str(e)}")
        flash('Errore durante la cancellazione del CV', 'error')

    return redirect(url_for('personnel.view_profile', user_id=user_id) + '#cv')

@personnel_bp.route('/profile/<int:user_id>/cv/download-pdf')
@login_required
def download_cv_pdf(user_id):
    """Genera e scarica CV in formato PDF"""
    user_to_view = User.query.get_or_404(user_id)

    # Controllo permessi
    if user_to_view.id != current_user.id and not user_has_permission(current_user.role, PERMISSION_VIEW_PERSONNEL_DATA):
        flash("Non hai il permesso di scaricare il CV di questo utente.", "danger")
        return redirect(url_for('personnel.view_profile', user_id=user_id))

    try:
        # Prepara dati utente
        user_data = {
            "name": f"{user_to_view.first_name} {user_to_view.last_name}",
            "email": user_to_view.email,
            "phone": user_to_view.phone,
            "address": user_to_view.profile.address if user_to_view.profile else None,
            "position": user_to_view.profile.job_title if user_to_view.profile else user_to_view.position,
        }

        # Prepara dati competenze
        skills_data = {}
        if user_to_view.skills:
            skills_by_category = {}
            for skill in user_to_view.skills:
                category = skill.category or 'Generale'
                if category not in skills_by_category:
                    skills_by_category[category] = []
                skills_by_category[category].append(skill.name)
            skills_data = skills_by_category

        # Genera PDF con ReportLab
        buffer = BytesIO()
        doc = SimpleDocTemplate(buffer, pagesize=A4, rightMargin=72, leftMargin=72, topMargin=72, bottomMargin=18)

        # Stili
        styles = getSampleStyleSheet()
        title_style = ParagraphStyle(
            'CustomTitle',
            parent=styles['Heading1'],
            fontSize=24,
            spaceAfter=30,
            alignment=1,  # Center
            textColor=colors.HexColor('#1e40af')
        )

        heading_style = ParagraphStyle(
            'CustomHeading',
            parent=styles['Heading2'],
            fontSize=16,
            spaceAfter=12,
            textColor=colors.HexColor('#1e40af')
        )

        # Contenuto PDF
        story = []

        # Titolo
        story.append(Paragraph(user_data['name'], title_style))
        if user_data['position']:
            story.append(Paragraph(user_data['position'], styles['Heading3']))
        story.append(Spacer(1, 12))

        # Contatti
        contact_info = []
        if user_data['email']:
            contact_info.append(f"📧 {user_data['email']}")
        if user_data['phone']:
            contact_info.append(f"📱 {user_data['phone']}")
        if user_data['address']:
            contact_info.append(f"📍 {user_data['address']}")

        if contact_info:
            story.append(Paragraph(" | ".join(contact_info), styles['Normal']))
            story.append(Spacer(1, 20))

        # Competenze
        if skills_data:
            story.append(Paragraph("🎯 Competenze Tecniche", heading_style))
            for category, skills in skills_data.items():
                story.append(Paragraph(f"<b>{category}:</b>", styles['Heading4']))
                skills_text = ", ".join(skills)
                story.append(Paragraph(skills_text, styles['Normal']))
                story.append(Spacer(1, 8))
            story.append(Spacer(1, 12))

        # Esperienza
        story.append(Paragraph("💼 Esperienza Professionale", heading_style))
        if user_data['position']:
            story.append(Paragraph(f"<b>{user_data['position']}</b>", styles['Heading4']))
            story.append(Paragraph("DatVinci - Posizione attuale", styles['Normal']))
        else:
            story.append(Paragraph("Esperienza professionale da completare", styles['Italic']))
        story.append(Spacer(1, 12))

        # Formazione
        story.append(Paragraph("🎓 Formazione", heading_style))
        story.append(Paragraph("Formazione da completare nel profilo utente", styles['Italic']))

        # Genera PDF
        doc.build(story)
        pdf_data = buffer.getvalue()
        buffer.close()

        # Crea response
        response = make_response(pdf_data)
        response.headers['Content-Type'] = 'application/pdf'
        response.headers['Content-Disposition'] = f'attachment; filename="CV_{user_to_view.first_name}_{user_to_view.last_name}.pdf"'

        return response

    except Exception as e:
        current_app.logger.error(f"Errore generazione PDF: {str(e)}")
        flash("Errore durante la generazione del PDF.", "error")
        return redirect(url_for('personnel.view_profile', user_id=user_id))

# ============================================================================
# DEPARTMENT MANAGEMENT ROUTES
# ============================================================================

@personnel_bp.route('/departments')
@login_required
@permission_required(PERMISSION_MANAGE_USERS)
def departments():
    """Lista dipartimenti con gerarchia"""
    page = request.args.get('page', 1, type=int)
    per_page = 20

    # Filtri
    search_query = request.args.get('q', '').strip()
    parent_filter = request.args.get('parent', '')

    # Query base
    departments_query = Department.query.filter_by(is_active=True)

    # Applica filtri
    if search_query:
        departments_query = departments_query.filter(
            db.or_(
                Department.name.ilike(f'%{search_query}%'),
                Department.description.ilike(f'%{search_query}%')
            )
        )

    if parent_filter:
        if parent_filter == 'root':
            departments_query = departments_query.filter_by(parent_id=None)
        else:
            departments_query = departments_query.filter_by(parent_id=parent_filter)

    # Ordinamento
    departments_query = departments_query.order_by(Department.name)

    # Paginazione
    pagination = departments_query.paginate(
        page=page, per_page=per_page, error_out=False
    )

    # Dati per filtri
    all_departments = Department.query.filter_by(is_active=True).order_by(Department.name).all()

    # Statistiche
    stats = {
        'total_departments': Department.query.filter_by(is_active=True).count(),
        'root_departments': Department.query.filter_by(is_active=True, parent_id=None).count(),
        'departments_with_manager': Department.query.filter(Department.manager_id.isnot(None), Department.is_active == True).count(),
        'total_employees': User.query.filter_by(is_active=True).count()
    }

    return render_template(
        'personnel/departments/index.html',
        pagination=pagination,
        all_departments=all_departments,
        current_filters={
            'q': search_query,
            'parent': parent_filter
        },
        stats=stats
    )

@personnel_bp.route('/departments/create', methods=['GET', 'POST'])
@login_required
@permission_required(PERMISSION_MANAGE_USERS)
def create_department():
    """Crea nuovo dipartimento"""
    if request.method == 'POST':
        name = request.form.get('name', '').strip()
        description = request.form.get('description', '').strip()
        parent_id = request.form.get('parent_id') or None
        manager_id = request.form.get('manager_id') or None
        budget = request.form.get('budget', 0.0, type=float)

        # Validazioni
        if not name:
            flash('Il nome del dipartimento è obbligatorio', 'error')
            return redirect(url_for('personnel.create_department'))

        # Verifica nome univoco
        existing_dept = Department.query.filter_by(name=name, is_active=True).first()
        if existing_dept:
            flash('Un dipartimento con questo nome esiste già', 'error')
            return redirect(url_for('personnel.create_department'))

        # Verifica parent valido
        if parent_id:
            parent_dept = Department.query.get(parent_id)
            if not parent_dept or not parent_dept.is_active:
                flash('Dipartimento padre non valido', 'error')
                return redirect(url_for('personnel.create_department'))

        # Verifica manager valido
        if manager_id:
            manager = User.query.get(manager_id)
            if not manager or not manager.is_active:
                flash('Manager non valido', 'error')
                return redirect(url_for('personnel.create_department'))

        try:
            new_department = Department(
                name=name,
                description=description,
                parent_id=parent_id,
                manager_id=manager_id,
                budget=budget
            )

            db.session.add(new_department)
            db.session.commit()

            flash(f'Dipartimento "{name}" creato con successo', 'success')
            return redirect(url_for('personnel.departments'))

        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"Errore creazione dipartimento: {str(e)}")
            flash('Errore durante la creazione del dipartimento', 'error')

    # GET request - mostra form
    parent_departments = Department.query.filter_by(is_active=True).order_by(Department.name).all()
    potential_managers = User.query.filter_by(is_active=True).filter(
        db.or_(User.role == 'manager', User.role == 'admin')
    ).order_by(User.first_name, User.last_name).all()

    # Converti in dizionari per JSON serialization
    parent_departments_data = [
        {
            'id': dept.id,
            'name': dept.name,
            'parent_name': dept.parent.name if dept.parent else None
        }
        for dept in parent_departments
    ]

    potential_managers_data = [
        {
            'id': manager.id,
            'full_name': manager.full_name,
            'position': manager.position,
            'role': manager.role
        }
        for manager in potential_managers
    ]

    return render_template(
        'personnel/departments/create.html',
        parent_departments=parent_departments,
        potential_managers=potential_managers,
        parent_departments_data=parent_departments_data,
        potential_managers_data=potential_managers_data
    )

@personnel_bp.route('/departments/<int:dept_id>/edit', methods=['GET', 'POST'])
@login_required
@permission_required(PERMISSION_MANAGE_USERS)
def edit_department(dept_id):
    """Modifica dipartimento esistente"""
    department = Department.query.get_or_404(dept_id)

    if not department.is_active:
        flash('Dipartimento non trovato', 'error')
        return redirect(url_for('personnel.departments'))

    if request.method == 'POST':
        name = request.form.get('name', '').strip()
        description = request.form.get('description', '').strip()
        parent_id = request.form.get('parent_id') or None
        manager_id = request.form.get('manager_id') or None
        budget = request.form.get('budget', 0.0, type=float)

        # Validazioni
        if not name:
            flash('Il nome del dipartimento è obbligatorio', 'error')
            return redirect(url_for('personnel.edit_department', dept_id=dept_id))

        # Verifica nome univoco (escludendo il dipartimento corrente)
        existing_dept = Department.query.filter(
            Department.name == name,
            Department.id != dept_id,
            Department.is_active == True
        ).first()
        if existing_dept:
            flash('Un dipartimento con questo nome esiste già', 'error')
            return redirect(url_for('personnel.edit_department', dept_id=dept_id))

        # Verifica parent valido (non può essere se stesso o un suo figlio)
        if parent_id:
            parent_dept = Department.query.get(parent_id)
            if not parent_dept or not parent_dept.is_active:
                flash('Dipartimento padre non valido', 'error')
                return redirect(url_for('personnel.edit_department', dept_id=dept_id))

            # Verifica che non sia se stesso
            if int(parent_id) == dept_id:
                flash('Un dipartimento non può essere padre di se stesso', 'error')
                return redirect(url_for('personnel.edit_department', dept_id=dept_id))

            # Verifica che non sia un suo figlio (evita cicli)
            def is_descendant(dept, potential_parent_id):
                for child in dept.subdepartments:
                    if child.id == potential_parent_id:
                        return True
                    if is_descendant(child, potential_parent_id):
                        return True
                return False

            if is_descendant(department, int(parent_id)):
                flash('Non è possibile creare una gerarchia circolare', 'error')
                return redirect(url_for('personnel.edit_department', dept_id=dept_id))

        # Verifica manager valido
        if manager_id:
            manager = User.query.get(manager_id)
            if not manager or not manager.is_active:
                flash('Manager non valido', 'error')
                return redirect(url_for('personnel.edit_department', dept_id=dept_id))

        try:
            department.name = name
            department.description = description
            department.parent_id = parent_id
            department.manager_id = manager_id
            department.budget = budget

            db.session.commit()

            flash(f'Dipartimento "{name}" aggiornato con successo', 'success')
            return redirect(url_for('personnel.departments'))

        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"Errore aggiornamento dipartimento: {str(e)}")
            flash('Errore durante l\'aggiornamento del dipartimento', 'error')

    # GET request - mostra form
    parent_departments = Department.query.filter(
        Department.is_active == True,
        Department.id != dept_id  # Esclude se stesso
    ).order_by(Department.name).all()

    potential_managers = User.query.filter_by(is_active=True).filter(
        db.or_(User.role == 'manager', User.role == 'admin')
    ).order_by(User.first_name, User.last_name).all()

    return render_template(
        'personnel/departments/edit.html',
        department=department,
        parent_departments=parent_departments,
        potential_managers=potential_managers
    )

@personnel_bp.route('/departments/<int:dept_id>/delete', methods=['POST'])
@login_required
@permission_required(PERMISSION_MANAGE_USERS)
def delete_department(dept_id):
    """Elimina dipartimento (soft delete)"""
    department = Department.query.get_or_404(dept_id)

    if not department.is_active:
        flash('Dipartimento già eliminato', 'warning')
        return redirect(url_for('personnel.departments'))

    # Verifica se ha dipendenti
    if department.employee_count > 0:
        flash(f'Impossibile eliminare il dipartimento "{department.name}": ha {department.employee_count} dipendenti assegnati', 'error')
        return redirect(url_for('personnel.departments'))

    # Verifica se ha sottodepartimenti
    active_subdepts = department.subdepartments.filter_by(is_active=True).count()
    if active_subdepts > 0:
        flash(f'Impossibile eliminare il dipartimento "{department.name}": ha {active_subdepts} sottodepartimenti attivi', 'error')
        return redirect(url_for('personnel.departments'))

    try:
        # Soft delete
        department.is_active = False
        db.session.commit()

        flash(f'Dipartimento "{department.name}" eliminato con successo', 'success')

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Errore eliminazione dipartimento: {str(e)}")
        flash('Errore durante l\'eliminazione del dipartimento', 'error')

    return redirect(url_for('personnel.departments'))

@personnel_bp.route('/departments/<int:dept_id>')
@login_required
@permission_required(PERMISSION_VIEW_PERSONNEL_DATA)
def department_dashboard(dept_id):
    """Dashboard dipartimento con statistiche e dipendenti"""
    department = Department.query.get_or_404(dept_id)

    if not department.is_active:
        flash('Dipartimento non trovato', 'error')
        return redirect(url_for('personnel.departments'))

    # Statistiche dipartimento
    employees = department.employees.filter_by(is_active=True).all()
    subdepartments = department.subdepartments.filter_by(is_active=True).all()

    # Calcola statistiche
    stats = {
        'employee_count': len(employees),
        'subdepartment_count': len(subdepartments),
        'manager': department.manager,
        'budget': department.budget,
        'budget_used': 0.0,  # TODO: calcolare da progetti/spese
        'avg_salary': 0.0,
        'total_projects': 0,
        'active_projects': 0
    }

    # Calcola stipendio medio se disponibile
    salaries = [emp.profile.salary for emp in employees if emp.profile and emp.profile.salary]
    if salaries:
        stats['avg_salary'] = sum(salaries) / len(salaries)

    # Conta progetti (se il dipartimento ha dipendenti coinvolti)
    from models import Project
    if employees:
        employee_ids = [emp.id for emp in employees]
        # Progetti dove almeno un membro del team è del dipartimento
        projects = Project.query.join(project_team).filter(
            project_team.c.user_id.in_(employee_ids)
        ).distinct().all()

        stats['total_projects'] = len(projects)
        stats['active_projects'] = len([p for p in projects if p.status == 'active'])

    # Distribuzione competenze
    skills_distribution = {}
    for employee in employees:
        for user_skill in employee.detailed_skills:
            skill_name = user_skill.skill.name
            if skill_name not in skills_distribution:
                skills_distribution[skill_name] = {
                    'count': 0,
                    'avg_proficiency': 0,
                    'levels': []
                }
            skills_distribution[skill_name]['count'] += 1
            skills_distribution[skill_name]['levels'].append(user_skill.proficiency_level)

    # Calcola proficiency media per skill
    for skill_name, data in skills_distribution.items():
        if data['levels']:
            data['avg_proficiency'] = sum(data['levels']) / len(data['levels'])

    # Ordina per numero di persone che hanno la skill
    skills_distribution = dict(sorted(
        skills_distribution.items(),
        key=lambda x: x[1]['count'],
        reverse=True
    ))

    return render_template(
        'personnel/departments/dashboard.html',
        department=department,
        employees=employees,
        subdepartments=subdepartments,
        stats=stats,
        skills_distribution=skills_distribution
    )

@personnel_bp.route('/departments/<int:dept_id>/manage-employees', methods=['GET', 'POST'])
@login_required
@permission_required(PERMISSION_MANAGE_USERS)
def manage_department_employees(dept_id):
    """Gestisci dipendenti del dipartimento"""
    department = Department.query.get_or_404(dept_id)

    if not department.is_active:
        flash('Dipartimento non trovato', 'error')
        return redirect(url_for('personnel.departments'))

    if request.method == 'POST':
        action = request.form.get('action')

        if action == 'assign_employees':
            # Assegna dipendenti selezionati al dipartimento
            employee_ids = request.form.getlist('employee_ids')
            if employee_ids:
                try:
                    users_to_assign = User.query.filter(User.id.in_(employee_ids), User.is_active == True).all()
                    for user in users_to_assign:
                        old_dept = user.department_obj.name if user.department_obj else "Nessun dipartimento"
                        user.department_id = dept_id

                    db.session.commit()
                    flash(f'{len(users_to_assign)} dipendenti assegnati a {department.name}', 'success')

                except Exception as e:
                    db.session.rollback()
                    current_app.logger.error(f"Errore assegnazione dipendenti: {str(e)}")
                    flash('Errore durante l\'assegnazione dei dipendenti', 'error')

        elif action == 'remove_employees':
            # Rimuovi dipendenti selezionati dal dipartimento
            employee_ids = request.form.getlist('employee_ids')
            if employee_ids:
                try:
                    users_to_remove = User.query.filter(
                        User.id.in_(employee_ids),
                        User.department_id == dept_id,
                        User.is_active == True
                    ).all()
                    for user in users_to_remove:
                        user.department_id = None

                    db.session.commit()
                    flash(f'{len(users_to_remove)} dipendenti rimossi da {department.name}', 'success')

                except Exception as e:
                    db.session.rollback()
                    current_app.logger.error(f"Errore rimozione dipendenti: {str(e)}")
                    flash('Errore durante la rimozione dei dipendenti', 'error')

        return redirect(url_for('personnel.manage_department_employees', dept_id=dept_id))

    # GET request - mostra interfaccia gestione
    current_employees = department.employees.filter_by(is_active=True).all()

    # Dipendenti disponibili (senza dipartimento o di altri dipartimenti)
    available_employees = User.query.filter(
        User.is_active == True,
        db.or_(User.department_id == None, User.department_id != dept_id)
    ).order_by(User.first_name, User.last_name).all()

    # Tutti i dipartimenti per trasferimenti
    all_departments = Department.query.filter(
        Department.is_active == True,
        Department.id != dept_id
    ).order_by(Department.name).all()

    return render_template(
        'personnel/departments/manage_employees.html',
        department=department,
        current_employees=current_employees,
        available_employees=available_employees,
        all_departments=all_departments
    )

@personnel_bp.route('/transfer-employee', methods=['POST'])
@login_required
@permission_required(PERMISSION_MANAGE_USERS)
def transfer_employee():
    """Trasferisci singolo dipendente tra dipartimenti"""
    user_id = request.form.get('user_id')
    new_department_id = request.form.get('new_department_id') or None
    reason = request.form.get('reason', '').strip()

    if not user_id:
        flash('Dipendente non specificato', 'error')
        return redirect(request.referrer or url_for('personnel.index'))

    user = User.query.get_or_404(user_id)
    old_department = user.department_obj

    # Verifica nuovo dipartimento se specificato
    new_department = None
    if new_department_id:
        new_department = Department.query.get(new_department_id)
        if not new_department or not new_department.is_active:
            flash('Dipartimento di destinazione non valido', 'error')
            return redirect(request.referrer or url_for('personnel.index'))

    try:
        # Aggiorna dipartimento
        user.department_id = new_department_id

        # Log del trasferimento (opzionale - per audit trail)
        transfer_log = {
            'user_id': user.id,
            'from_department': old_department.name if old_department else None,
            'to_department': new_department.name if new_department else None,
            'reason': reason,
            'transferred_by': current_user.id,
            'timestamp': datetime.utcnow().isoformat()
        }
        current_app.logger.info(f"Employee transfer: {transfer_log}")

        db.session.commit()

        # Messaggio di successo
        if new_department:
            flash(f'{user.full_name} trasferito/a a {new_department.name}', 'success')
        else:
            flash(f'{user.full_name} rimosso/a dal dipartimento', 'success')

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Errore trasferimento dipendente: {str(e)}")
        flash('Errore durante il trasferimento', 'error')

    return redirect(request.referrer or url_for('personnel.index'))