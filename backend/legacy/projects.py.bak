from flask import Blueprint, render_template, redirect, url_for, request, flash, current_app, jsonify
from flask_login import login_required, current_user
from datetime import datetime, timedelta, date
from sqlalchemy import desc, asc, or_, and_
import json
import calendar

from app import db
from models import Project, Task, Timesheet, Event, Client, User, Notification, PersonnelRate, ProjectExpense, ProjectKPITemplate, ProjectKPITarget
from ai_services import analyze_project_requirements
from utils.cost_calculator import calculate_project_profitability, calculate_project_kpis, get_kpi_status, get_user_daily_rate_for_date
# Importazioni per RBAC
from utils.decorators import permission_required
from utils.permissions import (
    PERMISSION_VIEW_ALL_PROJECTS, PERMISSION_CREATE_PROJECT, PERMISSION_EDIT_PROJECT,
    PERMISSION_SUBMIT_TIMESHEET, PERMISSION_MANAGE_PROJECT_TASKS, PERMISSION_VIEW_OWN_TIMESHEETS,
    PERMISSION_APPROVE_TIMESHEETS, user_has_permission, ROLE_ADMIN, ROLE_MANAGER
)

projects_bp = Blueprint('projects', __name__, url_prefix='/projects')

@projects_bp.route('/')
@login_required
def index():
    status_filter = request.args.get('status', None)
    client_filter = request.args.get('client', None)
    query_param = request.args.get('q', None) # Rinominato per evitare shadowing

    projects_query = Project.query.options(
        db.joinedload(Project.client),
        db.joinedload(Project.team_members)
    )

    # Filtro RBAC: mostra tutti i progetti se si ha il permesso, altrimenti solo quelli a cui l'utente è assegnato
    if not user_has_permission(current_user.role, PERMISSION_VIEW_ALL_PROJECTS):
        projects_query = projects_query.join(Project.team_members).filter(User.id == current_user.id)

    if status_filter:
        projects_query = projects_query.filter(Project.status == status_filter)

    if client_filter:
        projects_query = projects_query.filter(Project.client_id == client_filter)

    if query_param:
        projects_query = projects_query.filter(
            or_(
                Project.name.ilike(f'%{query_param}%'),
                Project.description.ilike(f'%{query_param}%')
            )
        )

    sort_by = request.args.get('sort_by', 'created_at')
    sort_order = request.args.get('sort_order', 'desc')

    # Assicurarsi che sort_by sia un attributo valido di Project per evitare errori
    if hasattr(Project, sort_by):
        if sort_order == 'desc':
            projects_query = projects_query.order_by(desc(getattr(Project, sort_by)))
        else:
            projects_query = projects_query.order_by(asc(getattr(Project, sort_by)))
    else:
        projects_query = projects_query.order_by(desc(Project.created_at)) # Fallback default sorting

    page = request.args.get('page', 1, type=int)
    per_page = 10
    projects_pagination = projects_query.paginate(page=page, per_page=per_page, error_out=False)

    clients = Client.query.all()
    statuses = ['planning', 'active', 'completed', 'on-hold']

    # Serializza i progetti per il frontend
    projects_data = []
    for project in projects_pagination.items:
        project_dict = {
            'id': project.id,
            'name': project.name,
            'description': project.description,
            'status': project.status,
            'budget': project.budget,
            'expenses': project.expenses,
            'start_date': project.start_date.isoformat() if project.start_date else None,
            'end_date': project.end_date.isoformat() if project.end_date else None,
            'created_at': project.created_at.isoformat() if project.created_at else None,
            'client_id': project.client_id,
            'client_name': project.client.name if project.client else None,
            'team_members': [
                {
                    'id': member.id,
                    'full_name': member.full_name,
                    'profile_image': member.profile_image
                } for member in project.team_members
            ]
        }
        projects_data.append(project_dict)

    # Crea un oggetto pagination serializzabile
    pagination_data = {
        'items': projects_data,
        'page': projects_pagination.page,
        'per_page': projects_pagination.per_page,
        'total': projects_pagination.total,
        'pages': projects_pagination.pages
    }

    return render_template(
        'projects/index.html',
        projects=pagination_data,
        clients=clients,
        statuses=statuses,
        current_status=status_filter,
        current_client=client_filter,
        current_query=query_param,
        current_sort_by=sort_by,
        current_sort_order=sort_order
    )

@projects_bp.route('/<int:project_id>')
@login_required
def view(project_id):
    project_to_view = Project.query.get_or_404(project_id)

    # Controllo RBAC
    if not user_has_permission(current_user.role, PERMISSION_VIEW_ALL_PROJECTS) and current_user not in project_to_view.team_members:
        flash("Non hai il permesso di visualizzare questo progetto.", "danger")
        # Reindirizza alla lista progetti se l'utente ha il permesso per vederla (filtrata per i suoi), altrimenti alla dashboard.
        return redirect(url_for('projects.index'))

    tasks = Task.query.filter_by(project_id=project_id).all()
    team_members = project_to_view.team_members
    recent_timesheets = Timesheet.query.filter_by(
        project_id=project_id
    ).order_by(desc(Timesheet.date)).limit(10).all()

    now = datetime.utcnow()
    upcoming_events = Event.query.filter(
        Event.project_id == project_id,
        Event.start_time >= now
    ).order_by(Event.start_time).all()

    task_status_counts = {}
    for task_item in tasks: # Rinominato per evitare shadowing
        if task_item.status in task_status_counts:
            task_status_counts[task_item.status] += 1
        else:
            task_status_counts[task_item.status] = 1

    # Dati per timesheet mensile con navigazione
    from calendar import monthrange

    # Gestisci navigazione mesi (parametri URL)
    view_month = request.args.get('month', type=int) or now.month
    view_year = request.args.get('year', type=int) or now.year

    # Assicurati che il mese sia valido
    if view_month < 1 or view_month > 12:
        view_month = now.month
        view_year = now.year

    days_in_month = monthrange(view_year, view_month)[1]

    # Ottieni timesheet del mese selezionato per questo progetto
    month_start = datetime(view_year, view_month, 1).date()
    month_end = datetime(view_year, view_month, days_in_month).date()

    monthly_timesheets = Timesheet.query.filter(
        Timesheet.project_id == project_id,
        Timesheet.date >= month_start,
        Timesheet.date <= month_end
    ).all()

    # Calcola mese precedente e successivo per navigazione
    if view_month == 1:
        prev_month, prev_year = 12, view_year - 1
    else:
        prev_month, prev_year = view_month - 1, view_year

    if view_month == 12:
        next_month, next_year = 1, view_year + 1
    else:
        next_month, next_year = view_month + 1, view_year

    # Dati per tabella aggregata con filtri temporali
    filter_period = request.args.get('period', 'month')  # month, 6months, all
    member_id_filter = request.args.get('member_id', None, type=int)

    if filter_period == '6months':
        # Ultimi 6 mesi
        six_months_ago = (now - timedelta(days=180)).date()
        aggregated_timesheets = Timesheet.query.filter(
            Timesheet.project_id == project_id,
            Timesheet.date >= six_months_ago
        ).all()
        period_label = "Ultimi 6 mesi"
    elif filter_period == 'all':
        # Tutto il progetto
        aggregated_timesheets = Timesheet.query.filter(
            Timesheet.project_id == project_id
        ).all()
        period_label = "Tutto il progetto"
    else:
        # Solo mese corrente (default)
        aggregated_timesheets = monthly_timesheets
        period_label = f"{view_month}/{view_year}"

    # Calcola statistiche per team members (per risolvere problema Jinja2)
    team_stats = []
    for member in team_members:
        member_timesheets = [ts for ts in aggregated_timesheets if ts.user_id == member.id]
        member_total_hours = sum(ts.hours for ts in member_timesheets)
        member_days_worked = len(set(ts.date for ts in member_timesheets))
        member_avg_hours = member_total_hours / member_days_worked if member_days_worked > 0 else 0

        # Calcola costo giornaliero e totale per la risorsa (solo per admin/manager)
        daily_rate = None
        total_cost = None
        rate_period = None

        if user_has_permission(current_user.role, PERMISSION_VIEW_ALL_PROJECTS) or current_user.id == project_to_view.created_by:
            # Ottieni il costo giornaliero più recente per questo utente
            latest_rate = PersonnelRate.query.filter_by(user_id=member.id).order_by(desc(PersonnelRate.valid_from)).first()
            if latest_rate:
                daily_rate = latest_rate.daily_rate
                rate_period = f"Dal {latest_rate.valid_from.strftime('%d/%m/%Y')}"
                if latest_rate.valid_to:
                    rate_period += f" al {latest_rate.valid_to.strftime('%d/%m/%Y')}"

                # Calcola costo totale basato sulle ore lavorate
                total_days = member_total_hours / 8 if member_total_hours > 0 else 0  # Assumiamo 8 ore per giorno
                total_cost = daily_rate * total_days

        team_stats.append({
            'member': member,
            'total_hours': member_total_hours,
            'days_worked': member_days_worked,
            'avg_hours_per_day': member_avg_hours,
            'daily_rate': daily_rate,
            'total_cost': total_cost,
            'rate_period': rate_period
        })

    # Calcola totali progetto
    project_total_hours = sum(ts.hours for ts in aggregated_timesheets)
    project_total_days = len(set(ts.date for ts in aggregated_timesheets))
    project_avg_hours = project_total_hours / project_total_days if project_total_days > 0 else 0

    # Ordina team stats per ore totali
    team_stats.sort(key=lambda x: x['total_hours'], reverse=True)

    # Conta task per stato per KPI
    task_status_counts = {}
    for task in tasks:
        status = task.status
        task_status_counts[status] = task_status_counts.get(status, 0) + 1

    # Calcola dati per tabella task dettaglio (per risolvere problema Jinja2)
    task_daily_data = {}
    task_totals = {}
    daily_totals = {}

    # Filtra timesheet per persona se specificato
    filtered_timesheets = monthly_timesheets
    if member_id_filter:
        filtered_timesheets = [ts for ts in monthly_timesheets if ts.user_id == member_id_filter]

    for task in tasks:
        task_daily_data[task.id] = {}
        task_totals[task.id] = 0

        for day in range(1, days_in_month + 1):
            day_hours = sum(ts.hours for ts in filtered_timesheets
                          if ts.task_id == task.id and ts.date.day == day)
            task_daily_data[task.id][day] = day_hours
            task_totals[task.id] += day_hours

            # Accumula totali giornalieri
            if day not in daily_totals:
                daily_totals[day] = 0
            daily_totals[day] += day_hours

    grand_total = sum(daily_totals.values())

    # Converti task in dizionari per JSON serialization
    tasks_json = []
    for task in tasks:
        task_dict = {
            'id': task.id,
            'name': task.name,
            'description': task.description,
            'status': task.status,
            'priority': task.priority,
            'start_date': task.start_date.isoformat() if task.start_date else None,
            'due_date': task.due_date.isoformat() if task.due_date else None,
            'estimated_hours': float(task.estimated_hours) if task.estimated_hours else None,
            'actual_hours': task.actual_hours,
            'hours_efficiency': task.hours_efficiency,
            'hours_variance': task.hours_variance,
            'duration_days': task.duration_days,
            'assignee': {
                'id': task.assignee.id,
                'first_name': task.assignee.first_name,
                'last_name': task.assignee.last_name
            } if task.assignee else None,
            'assignee_id': task.assignee_id,  # Aggiungiamo anche l'ID per i form
            'dependencies': []  # TODO: implementare dipendenze
        }
        tasks_json.append(task_dict)

    # Calcola analisi costi/ricavi per KPI
    try:
        project_profitability = calculate_project_profitability(project_id)
        project_kpis = calculate_project_kpis(project_id)
    except Exception as e:
        current_app.logger.error(f"Error calculating project profitability: {str(e)}")
        project_profitability = None
        project_kpis = {}

    # Calcola totali spese per il tab expenses
    project_expenses = ProjectExpense.query.filter_by(project_id=project_id).all()
    expenses_total = sum(exp.amount for exp in project_expenses)
    expenses_billable = sum(exp.amount for exp in project_expenses if exp.billing_type == 'billable')
    expenses_non_billable = sum(exp.amount for exp in project_expenses if exp.billing_type == 'non-billable')

    # Converti expenses in dizionari serializzabili per JSON (come nella pagina dedicata)
    expenses_data = []
    for expense in project_expenses:
        expense_dict = {
            'id': expense.id,
            'category': expense.category,
            'description': expense.description,
            'amount': float(expense.amount),
            'billing_type': expense.billing_type,
            'date': expense.date.isoformat(),
            'status': expense.status,
            'user_id': expense.user_id,
            'user_name': f"{expense.user.first_name} {expense.user.last_name}",
            'created_at': expense.created_at.isoformat() if expense.created_at else None
        }
        expenses_data.append(expense_dict)

    # Categorie e stati per filtri
    expense_categories = ['licenses', 'travel', 'meals', 'equipment', 'external', 'other']
    expense_statuses = ['pending', 'approved', 'rejected']

    # Verifica se l'utente può vedere i costi delle risorse
    can_view_costs = user_has_permission(current_user.role, PERMISSION_VIEW_ALL_PROJECTS) or current_user.id == project_to_view.created_by

    # Debug: stampa informazioni sui timesheet
    print(f"DEBUG - Progetto {project_id}:")
    print(f"  - Task: {len(tasks)}")
    print(f"  - Team members: {len(team_members)}")
    print(f"  - Monthly timesheets: {len(monthly_timesheets)}")
    print(f"  - Aggregated timesheets: {len(aggregated_timesheets)}")
    print(f"  - Ore totali progetto: {project_total_hours}")
    print(f"  - Grand total tabella: {grand_total}")
    for stat in team_stats:
        print(f"    - {stat['member'].first_name}: {stat['total_hours']}h in {stat['days_worked']} giorni")
    if project_profitability:
        print(f"  - Costi totali: €{project_profitability['profitability']['total_costs']:.2f}")
        print(f"  - Ricavi potenziali: €{project_profitability['revenue']['potential']:.2f}")
        print(f"  - Margine netto: {project_profitability['profitability']['net_margin_percentage']:.1f}%")

    return render_template(
        'projects/view.html',
        project=project_to_view,
        tasks=tasks,
        tasks_json=tasks_json,
        team_members=team_members,
        recent_timesheets=recent_timesheets,
        upcoming_events=upcoming_events,
        task_status_counts=task_status_counts,
        monthly_timesheets=monthly_timesheets,
        aggregated_timesheets=aggregated_timesheets,
        team_stats=team_stats,
        project_total_hours=project_total_hours,
        project_total_days=project_total_days,
        project_avg_hours=project_avg_hours,
        task_daily_data=task_daily_data,
        task_totals=task_totals,
        daily_totals=daily_totals,
        grand_total=grand_total,
        member_id_filter=member_id_filter,
        view_month=view_month,
        view_year=view_year,
        days_in_month=days_in_month,
        prev_month=prev_month,
        prev_year=prev_year,
        next_month=next_month,
        next_year=next_year,
        filter_period=filter_period,
        period_label=period_label,
        project_profitability=project_profitability,
        project_kpis=project_kpis,
        get_kpi_status=get_kpi_status,
        expenses_total=expenses_total,
        expenses_billable=expenses_billable,
        expenses_non_billable=expenses_non_billable,
        expenses_data=expenses_data,
        expense_categories=expense_categories,
        expense_statuses=expense_statuses,
        can_view_costs=can_view_costs,
        today=date.today()
    )

@projects_bp.route('/create', methods=['GET', 'POST'])
@login_required
@permission_required(PERMISSION_CREATE_PROJECT)
def create():
    # Il controllo if current_user.role not in ['admin', 'manager'] è ora gestito dal decoratore
    if request.method == 'POST':
        name = request.form.get('name')
        description = request.form.get('description')
        client_id = request.form.get('client_id') or None
        start_date_str = request.form.get('start_date') # Rinominato per evitare shadowing
        end_date_str = request.form.get('end_date') # Rinominato per evitare shadowing
        status = request.form.get('status')
        budget_str = request.form.get('budget') or 0 # Rinominato
        team_member_ids = request.form.getlist('team_members') # Rinominato

        # Nuovi campi fatturazione e tipologia
        project_type = request.form.get('project_type', 'service')
        is_billable = request.form.get('is_billable') == 'on'
        client_daily_rate_str = request.form.get('client_daily_rate')
        markup_percentage_str = request.form.get('markup_percentage')

        if not name:
            flash('Il nome del progetto è obbligatorio', 'error')
            return redirect(url_for('projects.create'))

        start_date_obj, end_date_obj = None, None # Rinominato
        try:
            if start_date_str: start_date_obj = datetime.strptime(start_date_str, '%Y-%m-%d').date()
            if end_date_str: end_date_obj = datetime.strptime(end_date_str, '%Y-%m-%d').date()
        except ValueError:
            flash('Formato data non valido', 'error')
            return redirect(url_for('projects.create'))

        budget_float = 0.0 # Rinominato
        try:
            budget_float = float(budget_str) if budget_str else 0.0
        except ValueError:
            flash('Valore budget non valido', 'error')
            return redirect(url_for('projects.create'))

        # Parsing campi fatturazione
        client_daily_rate_float = None
        markup_percentage_float = 0.0

        try:
            if client_daily_rate_str:
                client_daily_rate_float = float(client_daily_rate_str)
        except ValueError:
            flash('Valore tariffa cliente non valido', 'error')
            return redirect(url_for('projects.create'))

        try:
            if markup_percentage_str:
                markup_percentage_float = float(markup_percentage_str)
        except ValueError:
            flash('Valore markup non valido', 'error')
            return redirect(url_for('projects.create'))

        new_project_obj = Project( # Rinominato
            name=name,
            description=description,
            client_id=client_id,
            start_date=start_date_obj,
            end_date=end_date_obj,
            status=status,
            budget=budget_float,
            project_type=project_type,
            is_billable=is_billable,
            client_daily_rate=client_daily_rate_float,
            markup_percentage=markup_percentage_float
        )

        try:
            db.session.add(new_project_obj)
            db.session.flush()

            for user_id_str in team_member_ids: # Rinominato
                user_obj = User.query.get(user_id_str) # Rinominato
                if user_obj:
                    new_project_obj.team_members.append(user_obj)
                    notification = Notification(
                        user_id=user_obj.id,
                        title="Nuovo progetto assegnato",
                        message=f"Sei stato assegnato al progetto '{name}'",
                        link=f"/projects/{new_project_obj.id}"
                    )
                    db.session.add(notification)

            if str(current_user.id) not in team_member_ids:
                new_project_obj.team_members.append(current_user)

            db.session.commit()
            flash('Progetto creato con successo', 'success')

            if description and len(description) > 50:
                try:
                    insights = analyze_project_requirements(description)
                    if 'error' not in insights:
                        flash('Analisi requisiti di progetto generata con AI', 'info')
                except Exception as e:
                    current_app.logger.error(f"AI analysis error: {str(e)}")

            return redirect(url_for('projects.view', project_id=new_project_obj.id))
        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"Project creation error: {str(e)}")
            flash('Si è verificato un errore durante la creazione del progetto', 'error')
            return redirect(url_for('projects.create'))

    clients_list = Client.query.all() # Rinominato
    users_list = User.query.filter_by(is_active=True).all() # Rinominato

    return render_template(
        'projects/create.html',
        clients=clients_list,
        users=users_list,
        statuses=['planning', 'active', 'completed', 'on-hold']
    )

@projects_bp.route('/<int:project_id>/edit', methods=['GET', 'POST'])
@login_required
@permission_required(PERMISSION_EDIT_PROJECT) # Applicato il permesso più restrittivo come discusso
def edit(project_id):
    project_to_edit = Project.query.get_or_404(project_id) # Rinominato

    # Il controllo precedente (current_user.role not in ['admin', 'manager'] and current_user not in project.team_members)
    # è ora gestito in modo più stringente dal decoratore PERMISSION_EDIT_PROJECT.
    # Se volessimo permettere ai membri del team di modificare, dovremmo rivedere questa logica
    # e potenzialmente creare un permesso più granulare o controlli interni alla funzione.

    if request.method == 'POST':
        project_to_edit.name = request.form.get('name')
        project_to_edit.description = request.form.get('description')
        project_to_edit.client_id = request.form.get('client_id') or None
        project_to_edit.status = request.form.get('status')

        # Aggiorna tipologia e campi fatturazione
        project_to_edit.project_type = request.form.get('project_type', 'service')
        project_to_edit.is_billable = request.form.get('is_billable') == 'on'

        start_date_str_edit = request.form.get('start_date') # Rinominato
        end_date_str_edit = request.form.get('end_date') # Rinominato
        try:
            if start_date_str_edit: project_to_edit.start_date = datetime.strptime(start_date_str_edit, '%Y-%m-%d').date()
            else: project_to_edit.start_date = None
            if end_date_str_edit: project_to_edit.end_date = datetime.strptime(end_date_str_edit, '%Y-%m-%d').date()
            else: project_to_edit.end_date = None
        except ValueError:
            flash('Formato data non valido', 'error')
            return redirect(url_for('projects.edit', project_id=project_id))

        budget_str_edit = request.form.get('budget') # Rinominato
        try:
            project_to_edit.budget = float(budget_str_edit) if budget_str_edit else 0.0
        except ValueError:
            flash('Valore budget non valido', 'error')
            return redirect(url_for('projects.edit', project_id=project_id))

        # Aggiorna campi fatturazione numerici
        client_daily_rate_str_edit = request.form.get('client_daily_rate')
        markup_percentage_str_edit = request.form.get('markup_percentage')

        try:
            if client_daily_rate_str_edit:
                project_to_edit.client_daily_rate = float(client_daily_rate_str_edit)
            else:
                project_to_edit.client_daily_rate = None
        except ValueError:
            flash('Valore tariffa cliente non valido', 'error')
            return redirect(url_for('projects.edit', project_id=project_id))

        try:
            if markup_percentage_str_edit:
                project_to_edit.markup_percentage = float(markup_percentage_str_edit)
            else:
                project_to_edit.markup_percentage = 0.0
        except ValueError:
            flash('Valore markup non valido', 'error')
            return redirect(url_for('projects.edit', project_id=project_id))

        new_team_member_ids_edit = request.form.getlist('team_members') # Rinominato

        project_to_edit.team_members = [] # Clear existing
        for user_id_str_edit in new_team_member_ids_edit:
            user_obj_edit = User.query.get(user_id_str_edit)
            if user_obj_edit:
                project_to_edit.team_members.append(user_obj_edit)

        try:
            db.session.commit()
            flash('Progetto aggiornato con successo', 'success')
            return redirect(url_for('projects.view', project_id=project_to_edit.id))
        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"Project edit error: {str(e)}")
            flash('Si è verificato un errore durante l\'aggiornamento del progetto', 'error')
            return redirect(url_for('projects.edit', project_id=project_id))

    clients_list_edit = Client.query.all() # Rinominato
    users_list_edit = User.query.filter_by(is_active=True).all() # Rinominato

    return render_template(
        'projects/edit.html',
        project=project_to_edit,
        clients=clients_list_edit,
        users=users_list_edit,
        statuses=['planning', 'active', 'completed', 'on-hold']
    )

@projects_bp.route('/timesheet')
@login_required
@permission_required(PERMISSION_VIEW_OWN_TIMESHEETS) # Permesso base per vedere i propri timesheet
def timesheet():
    # Filtrare i dati visualizzabili in base ai permessi
    user_id_filter = request.args.get('user_id', None, type=int)
    project_id_filter = request.args.get('project_id', None, type=int)
    status_filter = request.args.get('status', None)
    start_date_filter = request.args.get('start_date', None)
    end_date_filter = request.args.get('end_date', None)

    # Imposta date predefinite se non specificate
    if not start_date_filter or not end_date_filter:
        today = datetime.today()
        start_of_month = today.replace(day=1)

        if not start_date_filter:
            start_date_filter = start_of_month.strftime('%Y-%m-%d')
        if not end_date_filter:
            end_date_filter = today.strftime('%Y-%m-%d')

    # Costruisci la query di base
    timesheet_query = Timesheet.query

    # Applica filtri RBAC
    has_approve_permission = user_has_permission(current_user.role, PERMISSION_APPROVE_TIMESHEETS)

    # Se l'utente non può approvare timesheet, può vedere solo i propri
    if not has_approve_permission:
        timesheet_query = timesheet_query.filter(Timesheet.user_id == current_user.id)
        # Se è stato specificato un ID utente ma non è il proprio, ignora
        if user_id_filter and user_id_filter != current_user.id:
            flash("Non hai il permesso di visualizzare i timesheet di altri utenti.", "warning")
            user_id_filter = current_user.id

    # Applica filtri da URL
    if user_id_filter:
        timesheet_query = timesheet_query.filter(Timesheet.user_id == user_id_filter)

    if project_id_filter:
        # Verifica se l'utente ha accesso al progetto
        project = Project.query.get(project_id_filter)
        if project:
            if has_approve_permission or user_has_permission(current_user.role, PERMISSION_VIEW_ALL_PROJECTS) or current_user in project.team_members:
                timesheet_query = timesheet_query.filter(Timesheet.project_id == project_id_filter)
            else:
                flash("Non hai accesso ai timesheet per questo progetto.", "warning")
                return redirect(url_for('projects.timesheet'))
        else:
            flash("Progetto non trovato.", "error")
            return redirect(url_for('projects.timesheet'))

    if status_filter:
        timesheet_query = timesheet_query.filter(Timesheet.status == status_filter)

    try:
        if start_date_filter:
            start_date = datetime.strptime(start_date_filter, '%Y-%m-%d').date()
            timesheet_query = timesheet_query.filter(Timesheet.date >= start_date)

        if end_date_filter:
            end_date = datetime.strptime(end_date_filter, '%Y-%m-%d').date()
            timesheet_query = timesheet_query.filter(Timesheet.date <= end_date)
    except ValueError:
        flash('Formato data non valido', 'error')

    timesheet_entries = timesheet_query.order_by(desc(Timesheet.date)).all()

    # Filtra gli utenti disponibili in base ai permessi
    if has_approve_permission:
        users = User.query.filter(User.is_active == True).all()
    else:
        users = [current_user]

    # Filtra i progetti disponibili in base ai permessi
    if user_has_permission(current_user.role, PERMISSION_VIEW_ALL_PROJECTS):
        projects = Project.query.all()
    else:
        projects = current_user.projects

    statuses = ['pending', 'approved', 'rejected']

    return render_template(
        'projects/timesheet.html',
        timesheet_entries=timesheet_entries,
        users=users,
        projects=projects,
        statuses=statuses,
        current_user_id=user_id_filter or current_user.id,
        current_project_id=project_id_filter,
        current_status=status_filter,
        current_start_date=start_date_filter,
        current_end_date=end_date_filter,
        has_approve_permission=has_approve_permission
    )

@projects_bp.route('/timesheet/save', methods=['POST'])
@login_required
@permission_required(PERMISSION_SUBMIT_TIMESHEET)
def save_timesheet():
    form_data = request.form
    try:
        for key, hours_str in form_data.items():
            if key.startswith('hours_'):
                parts = key.split('_')
                date_str = parts[1]
                project_id_str = parts[2]
                task_id_str = parts[3] if len(parts) > 3 and parts[3] != 'none' else None

                date_obj = datetime.strptime(date_str, '%Y-%m-%d').date()
                project_id = int(project_id_str)
                task_id = int(task_id_str) if task_id_str else None
                hours = float(hours_str) if hours_str else 0

                # Non permettere di registrare ore su progetti a cui l'utente non è assegnato
                project_check = db.session.query(Project).join(Project.team_members).filter(Project.id == project_id, User.id == current_user.id).first()
                if not project_check:
                    flash(f"Non puoi registrare ore per il progetto ID {project_id} a cui non sei assegnato.", "warning")
                    continue

                existing_entry = Timesheet.query.filter_by(
                    user_id=current_user.id,
                    project_id=project_id,
                    task_id=task_id,
                    date=date_obj
                ).first()

                if hours > 0:
                    if existing_entry:
                        existing_entry.hours = hours
                    else:
                        new_entry = Timesheet(
                            user_id=current_user.id,
                            project_id=project_id,
                            task_id=task_id,
                            date=date_obj,
                            hours=hours,
                            status='pending' # O 'submitted'
                        )
                        db.session.add(new_entry)
                elif existing_entry and hours == 0:
                    db.session.delete(existing_entry)

        db.session.commit()
        flash('Timesheet salvato con successo', 'success')
    except ValueError:
        db.session.rollback()
        flash('Errore nel formato dei dati del timesheet.', 'error')
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Timesheet save error: {str(e)}")
        flash('Si è verificato un errore durante il salvataggio del timesheet.', 'error')

    return redirect(url_for('projects.timesheet', date=request.form.get('selected_date')))

@projects_bp.route('/calendar')
@login_required
def calendar_view(): # Rinominata per evitare conflitto con modulo calendar
    # Filtro gli eventi in base ai permessi
    project_id_filter = request.args.get('project_id', None, type=int)

    # Costruisci la query di base
    events_query = Event.query

    # Base del filtro RBAC:
    # - Se l'utente può vedere tutti i progetti, mostra tutti gli eventi di progetto
    # - Altrimenti, mostra solo gli eventi dei progetti a cui è assegnato l'utente
    if not user_has_permission(current_user.role, PERMISSION_VIEW_ALL_PROJECTS):
        # Ottieni i progetti a cui l'utente è assegnato
        user_project_ids = [project.id for project in current_user.projects]

        # Filtra gli eventi basati sui progetti a cui l'utente ha accesso
        if user_project_ids:
            # Se ha progetti assegnati, mostra:
            # 1. Eventi dei suoi progetti
            # 2. Eventi personali (creati da lui ma non legati a progetti)
            events_query = events_query.filter(
                or_(
                    Event.project_id.in_(user_project_ids),
                    and_(
                        Event.project_id.is_(None),
                        Event.created_by == current_user.id
                    )
                )
            )
        else:
            # Se non ha progetti assegnati, mostra solo gli eventi personali
            events_query = events_query.filter(
                and_(
                    Event.project_id.is_(None),
                    Event.created_by == current_user.id
                )
            )

    # Applica il filtro per progetto se specificato
    if project_id_filter:
        # Verifica se l'utente ha accesso al progetto
        project = Project.query.get(project_id_filter)
        if project:
            # Se ha accesso generale o fa parte del team, può vedere gli eventi
            if user_has_permission(current_user.role, PERMISSION_VIEW_ALL_PROJECTS) or current_user in project.team_members:
                events_query = events_query.filter(Event.project_id == project_id_filter)
            else:
                flash("Non hai accesso agli eventi di questo progetto.", "warning")
                return redirect(url_for('projects.calendar_view'))
        else:
            flash("Progetto non trovato.", "error")
            return redirect(url_for('projects.calendar_view'))

    # Ottieni tutti gli eventi filtrati
    events = events_query.all()

    # Formatta gli eventi per il calendario
    events_data = []
    for event in events:
        start_time_str = event.start_time.strftime('%Y-%m-%d %H:%M:%S')
        end_time_str = event.end_time.strftime('%Y-%m-%d %H:%M:%S')

        event_data = {
            'id': event.id,
            'title': event.title,
            'start': start_time_str,
            'end': end_time_str,
            'location': event.location or '',
            'type': event.event_type or 'general',
            'project_id': event.project_id
        }

        # Colore diverso in base al tipo di evento
        if event.event_type == 'meeting':
            event_data['backgroundColor'] = '#4285F4'  # Blue
            event_data['borderColor'] = '#2A56C6'
        elif event.event_type == 'deadline':
            event_data['backgroundColor'] = '#EA4335'  # Red
            event_data['borderColor'] = '#B31412'
        elif event.event_type == 'milestone':
            event_data['backgroundColor'] = '#FBBC04'  # Yellow
            event_data['borderColor'] = '#E37400'
        else:
            event_data['backgroundColor'] = '#34A853'  # Green
            event_data['borderColor'] = '#188038'

        events_data.append(event_data)

    # Filtra i progetti disponibili in base ai permessi
    if user_has_permission(current_user.role, PERMISSION_VIEW_ALL_PROJECTS):
        projects = Project.query.order_by(Project.name).all()
    else:
        projects = current_user.projects

    return render_template(
        'projects/calendar.html',
        events_json=json.dumps(events_data),
        projects=projects,
        current_project_id=project_id_filter
    )

@projects_bp.route('/calendar/event/create', methods=['POST'])
@login_required
@permission_required(PERMISSION_MANAGE_PROJECT_TASKS) # Assumiamo questo permesso per ora
def create_event():
    # TODO RBAC: Verificare se l'utente può creare eventi, possibilmente per un progetto specifico.
    #             Se legato a un progetto, verificare i permessi su quel progetto.
    #             PERMISSION_MANAGE_PROJECT_TASKS potrebbe essere un buon candidato se l'evento è specifico del progetto.
    title = request.form.get('event_title')
    description = request.form.get('event_description')
    project_id_event = request.form.get('event_project_id') or None # Rinominato
    start_time_str = request.form.get('event_start_time')
    end_time_str = request.form.get('event_end_time')
    location = request.form.get('event_location')
    event_type = request.form.get('event_type')

    if not title or not start_time_str or not end_time_str:
        flash('Titolo, ora di inizio e ora di fine sono obbligatori per l\'evento', 'error')
        return redirect(url_for('projects.calendar_view', month=request.form.get('current_month'), year=request.form.get('current_year')))

    try:
        start_time_obj = datetime.strptime(start_time_str, '%Y-%m-%dT%H:%M') # Rinominato
        end_time_obj = datetime.strptime(end_time_str, '%Y-%m-%dT%H:%M') # Rinominato
    except ValueError:
        flash('Formato data/ora evento non valido. Usare YYYY-MM-GGTHH:MM', 'error')
        return redirect(url_for('projects.calendar_view', month=request.form.get('current_month'), year=request.form.get('current_year')))

    if end_time_obj <= start_time_obj:
        flash('L\'ora di fine dell\'evento deve essere successiva all\'ora di inizio', 'error')
        return redirect(url_for('projects.calendar_view', month=request.form.get('current_month'), year=request.form.get('current_year')))

    if project_id_event:
        project_for_event = Project.query.get(project_id_event)
        if not project_for_event:
            flash("Progetto specificato per l'evento non trovato.", "error")
            return redirect(url_for('projects.calendar_view', month=request.form.get('current_month'), year=request.form.get('current_year')))

        # L'utente deve essere membro del progetto o avere il permesso di vedere tutti i progetti per aggiungere un evento specifico al progetto
        # (Oltre al permesso PERMISSION_MANAGE_PROJECT_TASKS già controllato dal decoratore)
        if not user_has_permission(current_user.role, PERMISSION_VIEW_ALL_PROJECTS) and current_user not in project_for_event.team_members:
            flash("Non puoi aggiungere eventi a questo progetto.", "error")
            return redirect(url_for('projects.calendar_view', month=request.form.get('current_month'), year=request.form.get('current_year')))
    # Se project_id_event è None, l'evento è globale e il permesso PERMISSION_MANAGE_PROJECT_TASKS è sufficiente.

    new_event_obj = Event( # Rinominato
        title=title,
        description=description,
        project_id=project_id_event,
        start_time=start_time_obj,
        end_time=end_time_obj,
        location=location,
        event_type=event_type,
        created_by=current_user.id
    )

    try:
        db.session.add(new_event_obj)
        db.session.commit()
        flash('Evento creato con successo', 'success')
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Event creation error: {str(e)}")
        flash('Si è verificato un errore durante la creazione dell\'evento', 'error')

    return redirect(url_for('projects.calendar_view', month=start_time_obj.month, year=start_time_obj.year))

@projects_bp.route('/tasks/create', methods=['POST'])
@login_required
@permission_required(PERMISSION_MANAGE_PROJECT_TASKS) # Assumendo che questo permesso sia appropriato
def create_task():
    project_id_task = request.form.get('project_id') # Rinominato
    name = request.form.get('name')
    description = request.form.get('description')
    assignee_id = request.form.get('assignee_id') or None
    start_date_str_task = request.form.get('start_date') # Nuovo campo
    due_date_str_task = request.form.get('due_date') # Rinominato
    estimated_hours_str = request.form.get('estimated_hours') # Nuovo campo
    priority = request.form.get('priority', 'medium')
    status_task = request.form.get('status', 'todo') # Rinominato

    if not name or not project_id_task:
        flash('Nome del task e ID progetto sono obbligatori', 'error')
        return redirect(request.referrer or url_for('projects.view', project_id=project_id_task if project_id_task else 0))

    # TODO RBAC: Verificare che l'utente (Manager/Admin con PERMISSION_MANAGE_PROJECT_TASKS)
    # possa effettivamente creare task per il project_id_task specificato.
    # project_check = Project.query.get(project_id_task)
    # if not project_check or (current_user not in project_check.team_members and not user_has_permission(current_user.role, PERMISSION_VIEW_ALL_PROJECTS)):
    #     flash("Progetto non valido o non hai i permessi per aggiungere task.", "error")
    #     return redirect(request.referrer or url_for('projects.index'))

    # Parsing start_date
    start_date_obj_task = None
    if start_date_str_task:
        try:
            start_date_obj_task = datetime.strptime(start_date_str_task, '%Y-%m-%d').date()
        except ValueError:
            flash('Formato data di inizio task non valido', 'error')
            return redirect(request.referrer or url_for('projects.view', project_id=project_id_task))

    # Parsing due_date
    due_date_obj_task = None # Rinominato
    if due_date_str_task:
        try:
            due_date_obj_task = datetime.strptime(due_date_str_task, '%Y-%m-%d').date()
        except ValueError:
            flash('Formato data di scadenza task non valido', 'error')
            return redirect(request.referrer or url_for('projects.view', project_id=project_id_task))

    # Parsing estimated_hours
    estimated_hours_obj = None
    if estimated_hours_str:
        try:
            estimated_hours_obj = float(estimated_hours_str)
            if estimated_hours_obj < 0:
                flash('Le ore stimate devono essere positive', 'error')
                return redirect(request.referrer or url_for('projects.view', project_id=project_id_task))
        except ValueError:
            flash('Formato ore stimate non valido', 'error')
            return redirect(request.referrer or url_for('projects.view', project_id=project_id_task))

    # Validazione logica date
    if start_date_obj_task and due_date_obj_task and start_date_obj_task > due_date_obj_task:
        flash('La data di inizio non può essere successiva alla data di scadenza', 'error')
        return redirect(request.referrer or url_for('projects.view', project_id=project_id_task))

    new_task_obj = Task( # Rinominato
        name=name,
        description=description,
        project_id=project_id_task,
        assignee_id=assignee_id,
        start_date=start_date_obj_task,
        due_date=due_date_obj_task,
        estimated_hours=estimated_hours_obj,
        priority=priority,
        status=status_task
    )

    try:
        db.session.add(new_task_obj)
        db.session.commit()
        flash('Task creato con successo', 'success')
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Task creation error: {str(e)}")
        flash('Si è verificato un errore durante la creazione del task', 'error')

    return redirect(url_for('projects.view', project_id=project_id_task) + '#tasks')

@projects_bp.route('/tasks/<int:task_id>/update-status', methods=['POST'])
@login_required
def update_task_status(task_id):
    task_to_update = Task.query.get_or_404(task_id)
    new_status = request.form.get('status')

    project_of_task = task_to_update.project
    can_update = False

    if task_to_update.assignee_id == current_user.id:
        can_update = True

    if not can_update and project_of_task:
        # Verifica se l'utente ha il permesso di gestire i task del progetto
        has_manage_perm = user_has_permission(current_user.role, PERMISSION_MANAGE_PROJECT_TASKS)
        # Verifica se l'utente può vedere il progetto (necessario per gestirne i task)
        can_see_project = user_has_permission(current_user.role, PERMISSION_VIEW_ALL_PROJECTS) or \
                          (current_user in project_of_task.team_members)

        if has_manage_perm and can_see_project:
            can_update = True

    if not can_update:
        flash("Non hai i permessi per aggiornare lo stato di questo task.", "error")
        return redirect(url_for('projects.view', project_id=task_to_update.project_id))

    if new_status not in ['todo', 'in-progress', 'review', 'done']:
        flash('Stato del task non valido', 'error')
    else:
        task_to_update.status = new_status
        task_to_update.updated_at = datetime.utcnow()
        try:
            db.session.commit()
            flash(f'Stato del task "{task_to_update.name}" aggiornato a "{new_status}"', 'success')
        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"Task status update error: {str(e)}")
            flash('Errore durante l\'aggiornamento dello stato del task', 'error')

    return redirect(url_for('projects.view', project_id=task_to_update.project_id))

@projects_bp.route('/tasks/<int:task_id>/status', methods=['POST'])
@login_required
@permission_required(PERMISSION_EDIT_PROJECT)
def update_task_status_ajax(task_id):
    """Aggiorna rapidamente lo stato di un task via AJAX"""
    task = Task.query.get_or_404(task_id)

    # Verifica che l'utente abbia accesso al progetto
    if not user_has_permission(current_user.role, PERMISSION_VIEW_ALL_PROJECTS) and current_user not in task.project.team_members:
        return jsonify({'error': 'Non hai accesso a questo task'}), 403

    data = request.get_json()
    new_status = data.get('status')

    if new_status not in ['todo', 'in-progress', 'completed', 'blocked']:
        return jsonify({'error': 'Stato non valido'}), 400

    try:
        task.status = new_status
        db.session.commit()
        return jsonify({'success': True, 'status': new_status})
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Task status update error: {str(e)}")
        return jsonify({'error': 'Errore durante l\'aggiornamento'}), 500

@projects_bp.route('/tasks/<int:task_id>/priority', methods=['POST'])
@login_required
@permission_required(PERMISSION_EDIT_PROJECT)
def update_task_priority_ajax(task_id):
    """Aggiorna rapidamente la priorità di un task via AJAX"""
    task = Task.query.get_or_404(task_id)

    # Verifica che l'utente abbia accesso al progetto
    if not user_has_permission(current_user.role, PERMISSION_VIEW_ALL_PROJECTS) and current_user not in task.project.team_members:
        return jsonify({'error': 'Non hai accesso a questo task'}), 403

    data = request.get_json()
    new_priority = data.get('priority')

    if new_priority not in ['low', 'medium', 'high']:
        return jsonify({'error': 'Priorità non valida'}), 400

    try:
        task.priority = new_priority
        db.session.commit()
        return jsonify({'success': True, 'priority': new_priority})
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Task priority update error: {str(e)}")
        return jsonify({'error': 'Errore durante l\'aggiornamento'}), 500

@projects_bp.route('/tasks/<int:task_id>/dates', methods=['POST'])
@login_required
@permission_required(PERMISSION_MANAGE_PROJECT_TASKS)
def update_task_dates_ajax(task_id):
    """Aggiorna le date di un task via AJAX (per Gantt Chart)"""
    current_app.logger.info(f"Updating task {task_id} dates - User: {current_user.id}")

    task = Task.query.get_or_404(task_id)

    # Verifica che l'utente abbia accesso al progetto
    if not user_has_permission(current_user.role, PERMISSION_VIEW_ALL_PROJECTS) and current_user not in task.project.team_members:
        current_app.logger.warning(f"User {current_user.id} denied access to task {task_id}")
        return jsonify({'error': 'Non hai accesso a questo task'}), 403

    data = request.get_json()
    if not data:
        current_app.logger.error("No JSON data received")
        return jsonify({'error': 'Dati non ricevuti'}), 400

    start_date_str = data.get('start_date')
    due_date_str = data.get('due_date')

    current_app.logger.info(f"Received dates: start={start_date_str}, due={due_date_str}")

    try:
        # Aggiorna start_date se fornita
        if start_date_str:
            try:
                start_date = datetime.strptime(start_date_str, '%Y-%m-%d').date()
                task.start_date = start_date
                current_app.logger.info(f"Updated task {task_id} start_date to {start_date}")
            except ValueError as e:
                current_app.logger.error(f"Invalid start_date format: {start_date_str} - {str(e)}")
                return jsonify({'error': 'Formato start_date non valido'}), 400

        # Aggiorna due_date se fornita
        if due_date_str:
            try:
                due_date = datetime.strptime(due_date_str, '%Y-%m-%d').date()
                task.due_date = due_date
                current_app.logger.info(f"Updated task {task_id} due_date to {due_date}")
            except ValueError as e:
                current_app.logger.error(f"Invalid due_date format: {due_date_str} - {str(e)}")
                return jsonify({'error': 'Formato due_date non valido'}), 400

        db.session.commit()
        current_app.logger.info(f"Successfully updated task {task_id} dates")

        return jsonify({
            'success': True,
            'start_date': start_date_str,
            'due_date': due_date_str
        })
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Task dates update error: {str(e)}")
        return jsonify({'error': 'Errore durante l\'aggiornamento'}), 500

# ===== GESTIONE SPESE PROGETTO =====

@projects_bp.route('/<int:project_id>/expenses')
@login_required
def expenses(project_id):
    """Lista spese del progetto"""
    project = Project.query.get_or_404(project_id)

    # Controllo RBAC
    if not user_has_permission(current_user.role, PERMISSION_VIEW_ALL_PROJECTS) and current_user not in project.team_members:
        flash("Non hai il permesso di visualizzare questo progetto.", "danger")
        return redirect(url_for('projects.index'))

    # Filtri
    category_filter = request.args.get('category')
    status_filter = request.args.get('status')
    user_filter = request.args.get('user_id', type=int)

    expenses_query = ProjectExpense.query.filter_by(project_id=project_id)

    if category_filter:
        expenses_query = expenses_query.filter_by(category=category_filter)
    if status_filter:
        expenses_query = expenses_query.filter_by(status=status_filter)
    if user_filter:
        expenses_query = expenses_query.filter_by(user_id=user_filter)

    expenses = expenses_query.order_by(desc(ProjectExpense.date)).all()

    # Converti expenses in dizionari serializzabili per JSON
    expenses_data = []
    for expense in expenses:
        expense_dict = {
            'id': expense.id,
            'category': expense.category,
            'description': expense.description,
            'amount': float(expense.amount),
            'billing_type': expense.billing_type,
            'date': expense.date.isoformat(),
            'status': expense.status,
            'user_id': expense.user_id,
            'user_name': f"{expense.user.first_name} {expense.user.last_name}",
            'created_at': expense.created_at.isoformat() if expense.created_at else None
        }
        expenses_data.append(expense_dict)

    # Calcola totali
    total_amount = sum(exp.amount for exp in expenses)
    billable_amount = sum(exp.amount for exp in expenses if exp.billing_type == 'billable')
    non_billable_amount = sum(exp.amount for exp in expenses if exp.billing_type == 'non-billable')

    # Categorie e stati per filtri
    categories = ['licenses', 'travel', 'meals', 'equipment', 'external', 'other']
    statuses = ['pending', 'approved', 'rejected']

    return render_template('projects/expenses.html',
                         project=project,
                         expenses=expenses_data,  # Passa i dati serializzabili
                         categories=categories,
                         statuses=statuses,
                         total_amount=total_amount,
                         billable_amount=billable_amount,
                         non_billable_amount=non_billable_amount,
                         current_category=category_filter,
                         current_status=status_filter,
                         current_user_filter=user_filter)

@projects_bp.route('/<int:project_id>/expenses/create', methods=['POST'])
@login_required
def create_expense(project_id):
    """Crea nuova spesa"""
    project = Project.query.get_or_404(project_id)

    # Controllo RBAC - solo team members possono aggiungere spese
    if not user_has_permission(current_user.role, PERMISSION_VIEW_ALL_PROJECTS) and current_user not in project.team_members:
        return jsonify({'error': 'Permessi insufficienti'}), 403

    try:
        data = request.get_json()

        # Validazione
        required_fields = ['category', 'description', 'amount', 'date']
        for field in required_fields:
            if field not in data or not data[field]:
                return jsonify({'error': f'Campo {field} obbligatorio'}), 400

        # Parsing data
        try:
            expense_date = datetime.strptime(data['date'], '%Y-%m-%d').date()
        except ValueError:
            return jsonify({'error': 'Formato data non valido'}), 400

        # Parsing amount
        try:
            amount = float(data['amount'])
            if amount <= 0:
                return jsonify({'error': 'Importo deve essere positivo'}), 400
        except ValueError:
            return jsonify({'error': 'Importo non valido'}), 400

        # Crea spesa
        expense = ProjectExpense(
            project_id=project_id,
            user_id=current_user.id,
            category=data['category'],
            description=data['description'],
            amount=amount,
            billing_type=data.get('billing_type', 'billable'),
            date=expense_date,
            status='pending'
        )

        db.session.add(expense)
        db.session.commit()

        return jsonify({
            'success': True,
            'message': 'Spesa creata con successo',
            'expense': {
                'id': expense.id,
                'category': expense.category,
                'description': expense.description,
                'amount': expense.amount,
                'billing_type': expense.billing_type,
                'date': expense.date.isoformat(),
                'status': expense.status,
                'user_name': current_user.full_name
            }
        })

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Expense creation error: {str(e)}")
        return jsonify({'error': 'Errore durante la creazione'}), 500

@projects_bp.route('/<int:project_id>/expenses/<int:expense_id>', methods=['PUT'])
@login_required
def update_expense(project_id, expense_id):
    """Aggiorna spesa"""
    expense = ProjectExpense.query.filter_by(id=expense_id, project_id=project_id).first_or_404()

    # Solo chi ha creato la spesa o admin/manager possono modificare
    if (expense.user_id != current_user.id and
        not user_has_permission(current_user.role, PERMISSION_EDIT_PROJECT)):
        return jsonify({'error': 'Permessi insufficienti'}), 403

    try:
        data = request.get_json()

        # Aggiorna campi se presenti
        if 'category' in data:
            expense.category = data['category']
        if 'description' in data:
            expense.description = data['description']
        if 'amount' in data:
            try:
                amount = float(data['amount'])
                if amount <= 0:
                    return jsonify({'error': 'Importo deve essere positivo'}), 400
                expense.amount = amount
            except ValueError:
                return jsonify({'error': 'Importo non valido'}), 400
        if 'billing_type' in data:
            expense.billing_type = data['billing_type']
        if 'date' in data:
            try:
                expense.date = datetime.strptime(data['date'], '%Y-%m-%d').date()
            except ValueError:
                return jsonify({'error': 'Formato data non valido'}), 400

        # Solo admin/manager possono cambiare status
        if 'status' in data and user_has_permission(current_user.role, PERMISSION_EDIT_PROJECT):
            expense.status = data['status']

        db.session.commit()

        return jsonify({
            'success': True,
            'message': 'Spesa aggiornata con successo'
        })

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Expense update error: {str(e)}")
        return jsonify({'error': 'Errore durante l\'aggiornamento'}), 500

@projects_bp.route('/<int:project_id>/expenses/<int:expense_id>', methods=['DELETE'])
@login_required
def delete_expense(project_id, expense_id):
    """Elimina spesa"""
    expense = ProjectExpense.query.filter_by(id=expense_id, project_id=project_id).first_or_404()

    # Solo chi ha creato la spesa o admin/manager possono eliminare
    if (expense.user_id != current_user.id and
        not user_has_permission(current_user.role, PERMISSION_EDIT_PROJECT)):
        return jsonify({'error': 'Permessi insufficienti'}), 403

    try:
        db.session.delete(expense)
        db.session.commit()

        return jsonify({
            'success': True,
            'message': 'Spesa eliminata con successo'
        })

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Expense deletion error: {str(e)}")
        return jsonify({'error': 'Errore durante l\'eliminazione'}), 500

# ===== GESTIONE KPI PROGETTO =====

@projects_bp.route('/<int:project_id>/kpi-config')
@login_required
def kpi_config(project_id):
    """Configurazione KPI del progetto"""
    project = Project.query.get_or_404(project_id)

    # Controllo RBAC - solo admin o project owner
    if (not user_has_permission(current_user.role, PERMISSION_VIEW_ALL_PROJECTS) and
        current_user not in project.team_members):
        flash("Non hai il permesso di configurare i KPI di questo progetto.", "danger")
        return redirect(url_for('projects.view', project_id=project_id))

    # Ottieni template KPI per la tipologia del progetto
    templates = ProjectKPITemplate.query.filter_by(
        project_type=project.project_type,
        is_active=True
    ).all()

    # Ottieni target personalizzati esistenti
    existing_targets = ProjectKPITarget.query.filter_by(project_id=project_id).all()
    existing_targets_dict = {target.kpi_name: target for target in existing_targets}

    # Combina template e target personalizzati
    kpi_configs = []
    for template in templates:
        existing_target = existing_targets_dict.get(template.kpi_name)

        kpi_config = {
            'template': template,
            'target': existing_target,
            'current_value': existing_target.target_value if existing_target else template.target_max,
            'warning_threshold': existing_target.warning_threshold if existing_target else template.warning_threshold,
            'is_customized': existing_target is not None
        }
        kpi_configs.append(kpi_config)

    return render_template('projects/kpi_config.html',
                         project=project,
                         kpi_configs=kpi_configs,
                         templates=templates)

@projects_bp.route('/<int:project_id>/kpi-config', methods=['POST'])
@login_required
def save_kpi_config(project_id):
    """Salva configurazione KPI personalizzata"""
    project = Project.query.get_or_404(project_id)

    # Controllo RBAC
    if (not user_has_permission(current_user.role, PERMISSION_EDIT_PROJECT) and
        current_user not in project.team_members):
        return jsonify({'error': 'Permessi insufficienti'}), 403

    try:
        data = request.get_json()
        kpi_name = data.get('kpi_name')
        target_value = data.get('target_value')
        warning_threshold = data.get('warning_threshold')
        custom_description = data.get('custom_description', '')

        # Validazione
        if not kpi_name or target_value is None:
            return jsonify({'error': 'Dati KPI incompleti'}), 400

        # Verifica che esista un template per questo KPI
        template = ProjectKPITemplate.query.filter_by(
            project_type=project.project_type,
            kpi_name=kpi_name,
            is_active=True
        ).first()

        if not template:
            return jsonify({'error': 'Template KPI non trovato'}), 404

        # Cerca target esistente o creane uno nuovo
        target = ProjectKPITarget.query.filter_by(
            project_id=project_id,
            kpi_name=kpi_name
        ).first()

        if not target:
            target = ProjectKPITarget(
                project_id=project_id,
                kpi_name=kpi_name,
                created_by=current_user.id
            )
            db.session.add(target)

        # Aggiorna valori
        target.target_value = float(target_value)
        target.warning_threshold = float(warning_threshold) if warning_threshold else None
        target.custom_description = custom_description
        target.updated_at = datetime.utcnow()

        db.session.commit()

        return jsonify({
            'success': True,
            'message': f'Configurazione {template.display_name} salvata con successo'
        })

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"KPI config save error: {str(e)}")
        return jsonify({'error': 'Errore durante il salvataggio'}), 500

@projects_bp.route('/<int:project_id>/kpi-config/<kpi_name>', methods=['DELETE'])
@login_required
def reset_kpi_config(project_id, kpi_name):
    """Reset KPI ai valori di default del template"""
    project = Project.query.get_or_404(project_id)

    # Controllo RBAC
    if (not user_has_permission(current_user.role, PERMISSION_EDIT_PROJECT) and
        current_user not in project.team_members):
        return jsonify({'error': 'Permessi insufficienti'}), 403

    try:
        # Elimina target personalizzato
        target = ProjectKPITarget.query.filter_by(
            project_id=project_id,
            kpi_name=kpi_name
        ).first()

        if target:
            db.session.delete(target)
            db.session.commit()

        return jsonify({
            'success': True,
            'message': 'KPI ripristinato ai valori di default'
        })

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"KPI reset error: {str(e)}")
        return jsonify({'error': 'Errore durante il reset'}), 500

@projects_bp.route('/tasks/<int:task_id>/delete', methods=['DELETE'])
@login_required
@permission_required(PERMISSION_MANAGE_PROJECT_TASKS)
def delete_task(task_id):
    """Elimina un task esistente"""
    task = Task.query.get_or_404(task_id)

    # Verifica che l'utente abbia accesso al progetto
    if not user_has_permission(current_user.role, PERMISSION_VIEW_ALL_PROJECTS) and current_user not in task.project.team_members:
        return jsonify({'error': 'Non hai accesso a questo task'}), 403

    try:
        # Salva il nome del task per il messaggio di risposta
        task_name = task.name
        project_id = task.project_id

        # Elimina il task
        db.session.delete(task)
        db.session.commit()

        return jsonify({
            'success': True,
            'message': f'Task "{task_name}" eliminato con successo'
        })
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Error deleting task {task_id}: {str(e)}")
        return jsonify({'error': 'Errore durante l\'eliminazione del task'}), 500

@projects_bp.route('/tasks/<int:task_id>/edit', methods=['POST'])
@login_required
@permission_required(PERMISSION_MANAGE_PROJECT_TASKS)
def edit_task(task_id):
    """Modifica un task esistente"""
    task = Task.query.get_or_404(task_id)

    # Verifica che l'utente abbia accesso al progetto
    if not user_has_permission(current_user.role, PERMISSION_VIEW_ALL_PROJECTS) and current_user not in task.project.team_members:
        flash('Non hai accesso a questo task', 'error')
        return redirect(url_for('projects.view', project_id=task.project_id))

    # Ottieni i dati dal form
    name = request.form.get('name')
    description = request.form.get('description')
    assignee_id = request.form.get('assignee_id') or None
    priority = request.form.get('priority', 'medium')
    status = request.form.get('status', 'todo')
    start_date_str = request.form.get('start_date')
    due_date_str = request.form.get('due_date')
    estimated_hours_str = request.form.get('estimated_hours')

    if not name:
        flash('Il nome del task è obbligatorio', 'error')
        return redirect(url_for('projects.view', project_id=task.project_id))

    try:
        # Parsing start_date
        start_date_obj = None
        if start_date_str:
            try:
                start_date_obj = datetime.strptime(start_date_str, '%Y-%m-%d').date()
            except ValueError:
                flash('Formato data di inizio non valido', 'error')
                return redirect(url_for('projects.view', project_id=task.project_id))

        # Parsing due_date
        due_date_obj = None
        if due_date_str:
            try:
                due_date_obj = datetime.strptime(due_date_str, '%Y-%m-%d').date()
            except ValueError:
                flash('Formato data di scadenza non valido', 'error')
                return redirect(url_for('projects.view', project_id=task.project_id))

        # Parsing estimated_hours
        estimated_hours_obj = None
        if estimated_hours_str:
            try:
                estimated_hours_obj = float(estimated_hours_str)
                if estimated_hours_obj < 0:
                    flash('Le ore stimate devono essere positive', 'error')
                    return redirect(url_for('projects.view', project_id=task.project_id))
            except ValueError:
                flash('Formato ore stimate non valido', 'error')
                return redirect(url_for('projects.view', project_id=task.project_id))

        # Validazione logica date
        if start_date_obj and due_date_obj and start_date_obj > due_date_obj:
            flash('La data di inizio non può essere successiva alla data di scadenza', 'error')
            return redirect(url_for('projects.view', project_id=task.project_id))

        # Aggiorna il task
        task.name = name
        task.description = description
        task.assignee_id = assignee_id
        task.priority = priority
        task.status = status
        task.start_date = start_date_obj
        task.due_date = due_date_obj
        task.estimated_hours = estimated_hours_obj

        db.session.commit()
        flash('Task aggiornato con successo', 'success')

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Task edit error: {str(e)}")
        flash('Errore durante l\'aggiornamento del task', 'error')

    return redirect(url_for('projects.view', project_id=task.project_id))