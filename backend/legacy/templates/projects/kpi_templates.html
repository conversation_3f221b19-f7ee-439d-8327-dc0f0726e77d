{% extends "base.html" %}

{% block title %}Gestione Template KPI{% endblock %}

{% block content %}
<div x-data="kpiTemplateManager()" x-init="init()">
    <!-- Header -->
    <div class="flex justify-between items-center mb-6">
        <div>
            <h1 class="text-2xl font-bold text-gray-900 dark:text-white">Template KPI</h1>
            <p class="text-gray-600 dark:text-gray-400">Configura KPI di default per tipologie progetto</p>
        </div>
        <div class="flex space-x-3">
            <button @click="showCreateModal = true"
                    class="bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-md text-sm font-medium">
                + Nuovo Template
            </button>
            <button @click="resetToDefaults()"
                    class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-md text-sm font-medium">
                🔄 Reset Default
            </button>
        </div>
    </div>

    <!-- Filtri -->
    <div class="mb-6">
        <div class="flex space-x-4 mb-3">
            <select x-model="selectedType" @change="filterTemplates()"
                    class="border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500">
                <option value="">Tutte le tipologie</option>
                <template x-for="type in projectTypes" :key="type.key">
                    <option :value="type.key" x-text="type.name"></option>
                </template>
            </select>

            <select x-model="selectedKPI" @change="filterTemplates()"
                    class="border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500">
                <option value="">Tutti i KPI</option>
                <option value="margin_percentage">Margine Netto</option>
                <option value="utilization_rate">Utilization Rate</option>
                <option value="cost_per_hour">Costo per Ora</option>
                <option value="cost_revenue_ratio">Rapporto C/R</option>
            </select>

            <!-- Reset filtri -->
            <button @click="resetFilters()"
                    x-show="selectedType || selectedKPI"
                    class="px-3 py-2 text-sm text-gray-600 hover:text-gray-800 border border-gray-300 rounded-md hover:bg-gray-50">
                🗑️ Reset
            </button>
        </div>

        <!-- Indicatore risultati -->
        <div class="text-sm text-gray-600">
            <span x-text="filteredProjectTypes.length"></span> tipologie mostrate
            <span x-show="selectedType || selectedKPI" class="text-blue-600">
                (filtrate)
            </span>
        </div>
    </div>

    <!-- Griglia template per tipologia -->
    <div class="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
        <template x-for="type in filteredProjectTypes" :key="type.key">
            <div class="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
                <div class="flex items-center justify-between mb-4">
                    <h3 x-text="type.name" class="text-lg font-semibold text-gray-900 dark:text-white"></h3>
                    <span class="text-xs text-gray-500 dark:text-gray-400" x-text="type.description"></span>
                </div>

                <!-- Lista KPI per questa tipologia -->
                <div class="space-y-3">
                    <template x-for="kpi in getFilteredKPIsForType(type.key)" :key="kpi.id">
                        <div class="border border-gray-200 dark:border-gray-600 rounded-lg p-3">
                            <div class="flex justify-between items-start mb-2">
                                <span x-text="kpi.display_name" class="font-medium text-gray-900 dark:text-white"></span>
                                <div class="flex space-x-1">
                                    <button @click="editKPI(kpi)"
                                            class="text-blue-600 hover:text-blue-800 text-sm">
                                        ✏️
                                    </button>
                                    <button @click="toggleKPI(kpi)"
                                            :class="kpi.is_active ? 'text-green-600' : 'text-gray-400'"
                                            class="text-sm">
                                        <span x-text="kpi.is_active ? '✅' : '❌'"></span>
                                    </button>
                                </div>
                            </div>
                            <div class="text-sm text-gray-600 dark:text-gray-400">
                                Target: <span x-text="kpi.target_min"></span>-<span x-text="kpi.target_max"></span><span x-text="kpi.unit"></span>
                                <br>
                                Warning: <span x-text="kpi.warning_threshold"></span><span x-text="kpi.unit"></span>
                            </div>
                            <div class="text-xs text-gray-500 dark:text-gray-400 mt-1" x-text="kpi.description"></div>
                        </div>
                    </template>

                    <!-- Aggiungi KPI -->
                    <button @click="addKPIToType(type.key)"
                            class="w-full text-center py-3 border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg text-gray-500 dark:text-gray-400 hover:border-primary-400 hover:text-primary-600 transition-colors">
                        + Aggiungi KPI
                    </button>
                </div>
            </div>
        </template>
    </div>

    <!-- Modal KPI -->
    <div x-show="showKPIModal"
         x-transition:enter="ease-out duration-300"
         x-transition:enter-start="opacity-0"
         x-transition:enter-end="opacity-100"
         x-transition:leave="ease-in duration-200"
         x-transition:leave-start="opacity-100"
         x-transition:leave-end="opacity-0"
         class="fixed inset-0 z-50 overflow-y-auto"
         style="display: none;">
        <div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity"></div>

            <div class="inline-block align-bottom bg-white dark:bg-gray-800 rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
                <form @submit.prevent="saveKPI()">
                    <div class="bg-white dark:bg-gray-800 px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                        <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4"
                            x-text="editingKPI ? 'Modifica KPI' : 'Nuovo KPI'"></h3>

                        <!-- Tipologia progetto -->
                        <div class="mb-4">
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                Tipologia Progetto
                            </label>
                            <select x-model="kpiForm.project_type" required
                                    class="w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white">
                                <template x-for="type in projectTypes" :key="type.key">
                                    <option :value="type.key" x-text="type.name"></option>
                                </template>
                            </select>
                        </div>

                        <!-- Nome KPI -->
                        <div class="mb-4">
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                Nome KPI
                            </label>
                            <select x-model="kpiForm.kpi_name" required
                                    class="w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white">
                                <option value="margin_percentage">Margine Netto (%)</option>
                                <option value="utilization_rate">Utilization Rate (%)</option>
                                <option value="cost_per_hour">Costo per Ora (€)</option>
                                <option value="cost_revenue_ratio">Rapporto C/R</option>
                            </select>
                        </div>

                        <!-- Valori target -->
                        <div class="grid grid-cols-3 gap-4 mb-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                    Minimo
                                </label>
                                <input type="number" x-model="kpiForm.target_min" step="0.1" required
                                       class="w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                    Ottimale
                                </label>
                                <input type="number" x-model="kpiForm.target_max" step="0.1" required
                                       class="w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                    Warning
                                </label>
                                <input type="number" x-model="kpiForm.warning_threshold" step="0.1" required
                                       class="w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white">
                            </div>
                        </div>

                        <!-- Unità di misura -->
                        <div class="mb-4">
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                Unità
                            </label>
                            <select x-model="kpiForm.unit"
                                    class="w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white">
                                <option value="%">Percentuale (%)</option>
                                <option value="€">Euro (€)</option>
                                <option value="ratio">Rapporto</option>
                                <option value="giorni">Giorni</option>
                                <option value="ore">Ore</option>
                            </select>
                        </div>

                        <!-- Descrizione -->
                        <div class="mb-4">
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                Descrizione
                            </label>
                            <textarea x-model="kpiForm.description" rows="3"
                                      class="w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"></textarea>
                        </div>

                        <!-- Suggerimenti AI -->
                        <div class="mb-4">
                            <button type="button" @click="getAISuggestions()"
                                    class="bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 rounded text-sm">
                                🤖 Suggerimenti AI
                            </button>
                            <div x-show="aiSuggestions" x-transition class="mt-2 p-3 bg-blue-50 dark:bg-blue-900/20 rounded">
                                <p class="text-sm text-blue-800 dark:text-blue-200" x-text="aiSuggestions"></p>
                            </div>
                        </div>
                    </div>

                    <!-- Azioni modal -->
                    <div class="bg-gray-50 dark:bg-gray-700 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                        <button type="submit"
                                class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-primary-600 text-base font-medium text-white hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 sm:ml-3 sm:w-auto sm:text-sm">
                            Salva KPI
                        </button>
                        <button type="button" @click="closeKPIModal()"
                                class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm dark:bg-gray-600 dark:text-white dark:border-gray-500 dark:hover:bg-gray-700">
                            Annulla
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
function kpiTemplateManager() {
    return {
        templates: {{ templates_by_type|tojson|safe }},
        projectTypes: {{ project_types|tojson|safe }},
        filteredProjectTypes: [],
        selectedType: '',
        selectedKPI: '',
        showKPIModal: false,
        showCreateModal: false,
        editingKPI: null,
        aiSuggestions: '',
        kpiForm: {
            project_type: 'service',
            kpi_name: 'margin_percentage',
            target_min: '',
            target_max: '',
            warning_threshold: '',
            unit: '%',
            description: ''
        },

        init() {
            this.filteredProjectTypes = this.projectTypes;
            console.log('KPI Templates initialized:', this.templates);
            console.log('Project Types:', this.projectTypes);
        },

        filterTemplates() {
            console.log('Filtering with:', { selectedType: this.selectedType, selectedKPI: this.selectedKPI });

            // Forza l'aggiornamento nel prossimo tick
            this.$nextTick(() => {
                this.filteredProjectTypes = this.projectTypes.filter(type => {
                    // Filtro per tipologia progetto
                    if (this.selectedType && type.key !== this.selectedType) {
                        return false;
                    }

                    // Filtro per KPI specifico
                    if (this.selectedKPI) {
                        const kpis = this.getKPIsForType(type.key);
                        console.log(`KPIs for ${type.key}:`, kpis);
                        // Mostra solo tipologie che hanno il KPI selezionato
                        const hasKPI = kpis.length > 0 && kpis.some(kpi => kpi.kpi_name === this.selectedKPI);
                        console.log(`Type ${type.key} has KPI ${this.selectedKPI}:`, hasKPI);
                        return hasKPI;
                    }

                    // Se nessun filtro KPI, mostra tutte le tipologie (filtrate per tipo se selezionato)
                    return true;
                });

                console.log('Filtered project types:', this.filteredProjectTypes);
            });
        },

        getKPIsForType(projectType) {
            return this.templates[projectType] || [];
        },

        getFilteredKPIsForType(projectType) {
            const kpis = this.getKPIsForType(projectType);

            // Se c'è un filtro KPI specifico, mostra solo quello
            if (this.selectedKPI) {
                return kpis.filter(kpi => kpi.kpi_name === this.selectedKPI);
            }

            // Altrimenti mostra tutti i KPI per questa tipologia
            return kpis;
        },

        toggleKPI(kpi) {
            // Toggle dello stato attivo/inattivo del KPI
            kpi.is_active = !kpi.is_active;

            // Salva la modifica
            this.updateKPIStatus(kpi);
        },

        async updateKPIStatus(kpi) {
            try {
                const response = await fetch(`/admin/kpi-templates/${kpi.id}`, {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRFToken': document.querySelector('meta[name=csrf-token]')?.getAttribute('content')
                    },
                    body: JSON.stringify({
                        is_active: kpi.is_active
                    })
                });

                const data = await response.json();
                if (!data.success) {
                    // Ripristina lo stato precedente in caso di errore
                    kpi.is_active = !kpi.is_active;
                    alert('Errore nell\'aggiornamento: ' + data.error);
                }
            } catch (error) {
                // Ripristina lo stato precedente in caso di errore
                kpi.is_active = !kpi.is_active;
                alert('Errore di rete: ' + error.message);
            }
        },

        addKPIToType(projectType) {
            this.kpiForm.project_type = projectType;
            this.editingKPI = null;
            this.showKPIModal = true;
        },

        editKPI(kpi) {
            this.editingKPI = kpi;
            this.kpiForm = {
                project_type: kpi.project_type,
                kpi_name: kpi.kpi_name,
                target_min: kpi.target_min,
                target_max: kpi.target_max,
                warning_threshold: kpi.warning_threshold,
                unit: kpi.unit,
                description: kpi.description
            };
            this.showKPIModal = true;
        },

        async saveKPI() {
            try {
                const url = this.editingKPI
                    ? `/admin/kpi-templates/${this.editingKPI.id}`
                    : '/admin/kpi-templates';
                const method = this.editingKPI ? 'PUT' : 'POST';

                const response = await fetch(url, {
                    method: method,
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRFToken': document.querySelector('meta[name=csrf-token]')?.getAttribute('content')
                    },
                    body: JSON.stringify(this.kpiForm)
                });

                const data = await response.json();

                if (data.success) {
                    location.reload(); // Ricarica la pagina per aggiornare i dati
                } else {
                    alert('Errore: ' + data.error);
                }
            } catch (error) {
                alert('Errore di rete: ' + error.message);
            }
        },

        async getAISuggestions() {
            try {
                const response = await fetch('/admin/kpi-suggestions', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        project_type: this.kpiForm.project_type,
                        kpi_name: this.kpiForm.kpi_name
                    })
                });

                const data = await response.json();
                this.aiSuggestions = data.suggestion;
            } catch (error) {
                this.aiSuggestions = 'Errore nel recupero suggerimenti';
            }
        },

        async resetToDefaults() {
            if (confirm('Sei sicuro di voler ripristinare tutti i template ai valori di default?')) {
                try {
                    const response = await fetch('/admin/reset-kpi-templates', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        }
                    });

                    const data = await response.json();
                    if (data.success) {
                        location.reload();
                    } else {
                        alert('Errore: ' + data.error);
                    }
                } catch (error) {
                    alert('Errore di rete: ' + error.message);
                }
            }
        },

        closeKPIModal() {
            this.showKPIModal = false;
            this.editingKPI = null;
            this.aiSuggestions = '';
        },

        resetFilters() {
            this.selectedType = '';
            this.selectedKPI = '';
            this.filterTemplates();
        }
    }
}
</script>
{% endblock %}
