import{c as a,j as e,a as o,i as s,b as n,o as i,m as d}from"./vendor.js";const l={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6"},p={class:"mt-4"},u={__name:"DepartmentCreate",setup(m){return(x,t)=>{const r=n("router-link");return i(),a("div",null,[t[2]||(t[2]=e("h1",{class:"text-2xl font-bold text-gray-900 dark:text-white mb-6"},"➕ Nuovo Dipartimento",-1)),e("div",l,[t[1]||(t[1]=e("p",{class:"text-gray-600 dark:text-gray-400"},"Creazione dipartimento in fase di migrazione...",-1)),e("div",p,[o(r,{to:"/app/personnel/departments",class:"text-primary-600 dark:text-primary-400 hover:underline"},{default:s(()=>t[0]||(t[0]=[d(" ← Torna ai Dipartimenti ")])),_:1,__:[0]})])])])}}};export{u as default};
