import{c as r,j as e,a as o,i,b as s,o as n,l as d}from"./vendor.js";const l={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6"},p={class:"mt-4"},c={__name:"DepartmentEdit",setup(m){return(x,t)=>{const a=s("router-link");return n(),r("div",null,[t[2]||(t[2]=e("h1",{class:"text-2xl font-bold text-gray-900 dark:text-white mb-6"},"✏️ Modifica Dipartimento",-1)),e("div",l,[t[1]||(t[1]=e("p",{class:"text-gray-600 dark:text-gray-400"},"Modifica dipartimento in fase di migrazione...",-1)),e("div",p,[o(a,{to:"/app/personnel/departments",class:"text-primary-600 dark:text-primary-400 hover:underline"},{default:i(()=>t[0]||(t[0]=[d(" ← Torna ai Dipartimenti ")])),_:1,__:[0]})])])])}}};export{c as default};
