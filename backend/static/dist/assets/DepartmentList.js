import{c as r,j as e,a as o,i as s,b as n,o as i,l}from"./vendor.js";const d={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6"},p={class:"mt-4"},u={__name:"DepartmentList",setup(m){return(x,t)=>{const a=n("router-link");return i(),r("div",null,[t[2]||(t[2]=e("h1",{class:"text-2xl font-bold text-gray-900 dark:text-white mb-6"},"🏢 Gestione Dipartimenti",-1)),e("div",d,[t[1]||(t[1]=e("p",{class:"text-gray-600 dark:text-gray-400"},"Gestione dipartimenti in fase di migrazione...",-1)),e("div",p,[o(a,{to:"/app/personnel",class:"text-primary-600 dark:text-primary-400 hover:underline"},{default:s(()=>t[0]||(t[0]=[l(" ← Torna al Team ")])),_:1,__:[0]})])])])}}};export{u as default};
