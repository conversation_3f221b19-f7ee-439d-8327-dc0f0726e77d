import{c as o,j as t,a,i as n,b as s,o as i,m as l}from"./vendor.js";const d={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6"},m={class:"mt-4"},u={__name:"PersonnelAdmin",setup(p){return(x,e)=>{const r=s("router-link");return i(),o("div",null,[e[2]||(e[2]=t("h1",{class:"text-2xl font-bold text-gray-900 dark:text-white mb-6"},"⚙️ Amministrazione Personnel",-1)),t("div",d,[e[1]||(e[1]=t("p",{class:"text-gray-600 dark:text-gray-400"},"Amministrazione personnel in fase di migrazione...",-1)),t("div",m,[a(r,{to:"/app/personnel",class:"text-primary-600 dark:text-primary-400 hover:underline"},{default:n(()=>e[0]||(e[0]=[l(" ← Torna al Team ")])),_:1,__:[0]})])])])}}};export{u as default};
