import{c as r,j as t,a,i as s,b as n,o as l,l as i}from"./vendor.js";const d={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6"},p={class:"mt-4"},u={__name:"PersonnelProfile",setup(m){return(x,e)=>{const o=n("router-link");return l(),r("div",null,[e[2]||(e[2]=t("h1",{class:"text-2xl font-bold text-gray-900 dark:text-white mb-6"},"👤 Profilo Dipendente",-1)),t("div",d,[e[1]||(e[1]=t("p",{class:"text-gray-600 dark:text-gray-400"},"Profilo dipendente in fase di migrazione...",-1)),t("div",p,[a(o,{to:"/app/personnel",class:"text-primary-600 dark:text-primary-400 hover:underline"},{default:s(()=>e[0]||(e[0]=[i(" ← Torna al Team ")])),_:1,__:[0]})])])])}}};export{u as default};
