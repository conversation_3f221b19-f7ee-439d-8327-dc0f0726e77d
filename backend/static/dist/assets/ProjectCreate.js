import{r as n,A as v,c as d,j as o,s as x,v as s,x as a,H as u,F as w,k,I as j,C,t as g,l as S,o as p}from"./vendor.js";import{u as z}from"./app.js";const P={class:"max-w-4xl mx-auto"},V={class:"bg-white shadow rounded-lg"},T=["value"],U={class:"grid grid-cols-1 md:grid-cols-2 gap-4"},D={class:"grid grid-cols-1 md:grid-cols-2 gap-4"},E={class:"grid grid-cols-1 md:grid-cols-2 gap-4"},F={class:"flex items-center"},I={class:"flex justify-end space-x-3 pt-6 border-t border-gray-200"},M=["disabled"],R={__name:"ProjectCreate",setup(N){const b=S(),m=z(),i=n(!1),c=n([]),r=n({name:"",description:"",client_id:"",start_date:"",end_date:"",project_type:"service",budget:"",status:"planning",is_billable:!0}),f=async()=>{var l;try{const e=await fetch("/api/clients",{headers:{"Content-Type":"application/json","X-CSRFToken":m.csrfToken}});if(e.ok){const t=await e.json();c.value=((l=t.data)==null?void 0:l.clients)||[]}}catch(e){console.error("Error loading clients:",e)}},y=async()=>{i.value=!0;try{const l=await fetch("/api/projects",{method:"POST",headers:{"Content-Type":"application/json","X-CSRFToken":m.csrfToken},body:JSON.stringify(r.value)});if(!l.ok)throw new Error("Errore nella creazione del progetto");const e=await l.json();b.push(`/app/projects/${e.data.project.id}`)}catch(l){console.error("Error creating project:",l),alert("Errore nella creazione del progetto")}finally{i.value=!1}};return v(()=>{f()}),(l,e)=>(p(),d("div",P,[o("div",V,[e[22]||(e[22]=o("div",{class:"px-6 py-4 border-b border-gray-200"},[o("h1",{class:"text-xl font-semibold text-gray-900"},"Nuovo Progetto"),o("p",{class:"mt-1 text-sm text-gray-600"},"Crea un nuovo progetto per la tua organizzazione")],-1)),o("form",{onSubmit:x(y,["prevent"]),class:"p-6 space-y-6"},[o("div",null,[e[10]||(e[10]=o("label",{for:"name",class:"block text-sm font-medium text-gray-700"},"Nome Progetto *",-1)),s(o("input",{id:"name","onUpdate:modelValue":e[0]||(e[0]=t=>r.value.name=t),type:"text",required:"",class:"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500",placeholder:"Inserisci il nome del progetto"},null,512),[[a,r.value.name]])]),o("div",null,[e[11]||(e[11]=o("label",{for:"description",class:"block text-sm font-medium text-gray-700"},"Descrizione",-1)),s(o("textarea",{id:"description","onUpdate:modelValue":e[1]||(e[1]=t=>r.value.description=t),rows:"3",class:"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500",placeholder:"Descrivi il progetto..."},null,512),[[a,r.value.description]])]),o("div",null,[e[13]||(e[13]=o("label",{for:"client_id",class:"block text-sm font-medium text-gray-700"},"Cliente",-1)),s(o("select",{id:"client_id","onUpdate:modelValue":e[2]||(e[2]=t=>r.value.client_id=t),class:"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"},[e[12]||(e[12]=o("option",{value:""},"Seleziona cliente",-1)),(p(!0),d(w,null,k(c.value,t=>(p(),d("option",{key:t.id,value:t.id},g(t.name),9,T))),128))],512),[[u,r.value.client_id]])]),o("div",U,[o("div",null,[e[14]||(e[14]=o("label",{for:"start_date",class:"block text-sm font-medium text-gray-700"},"Data Inizio",-1)),s(o("input",{id:"start_date","onUpdate:modelValue":e[3]||(e[3]=t=>r.value.start_date=t),type:"date",class:"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"},null,512),[[a,r.value.start_date]])]),o("div",null,[e[15]||(e[15]=o("label",{for:"end_date",class:"block text-sm font-medium text-gray-700"},"Data Fine",-1)),s(o("input",{id:"end_date","onUpdate:modelValue":e[4]||(e[4]=t=>r.value.end_date=t),type:"date",class:"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"},null,512),[[a,r.value.end_date]])])]),o("div",D,[o("div",null,[e[17]||(e[17]=o("label",{for:"project_type",class:"block text-sm font-medium text-gray-700"},"Tipo Progetto",-1)),s(o("select",{id:"project_type","onUpdate:modelValue":e[5]||(e[5]=t=>r.value.project_type=t),class:"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"},e[16]||(e[16]=[j('<option value="service">🔧 Servizio</option><option value="license">📄 Licenza</option><option value="consulting">💼 Consulenza</option><option value="product">📦 Prodotto</option><option value="rd">🔬 R&amp;D</option><option value="internal">🏢 Interno</option>',6)]),512),[[u,r.value.project_type]])]),o("div",null,[e[18]||(e[18]=o("label",{for:"budget",class:"block text-sm font-medium text-gray-700"},"Budget (€)",-1)),s(o("input",{id:"budget","onUpdate:modelValue":e[6]||(e[6]=t=>r.value.budget=t),type:"number",step:"0.01",min:"0",class:"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500",placeholder:"0.00"},null,512),[[a,r.value.budget]])])]),o("div",E,[o("div",null,[e[20]||(e[20]=o("label",{for:"status",class:"block text-sm font-medium text-gray-700"},"Stato",-1)),s(o("select",{id:"status","onUpdate:modelValue":e[7]||(e[7]=t=>r.value.status=t),class:"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"},e[19]||(e[19]=[o("option",{value:"planning"},"📋 Pianificazione",-1),o("option",{value:"active"},"🚀 Attivo",-1),o("option",{value:"on-hold"},"⏸️ In Pausa",-1),o("option",{value:"completed"},"✅ Completato",-1)]),512),[[u,r.value.status]])]),o("div",F,[s(o("input",{id:"is_billable","onUpdate:modelValue":e[8]||(e[8]=t=>r.value.is_billable=t),type:"checkbox",class:"h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"},null,512),[[C,r.value.is_billable]]),e[21]||(e[21]=o("label",{for:"is_billable",class:"ml-2 block text-sm text-gray-900"}," Progetto fatturabile ",-1))])]),o("div",I,[o("button",{type:"button",onClick:e[9]||(e[9]=t=>l.$router.go(-1)),class:"px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"}," Annulla "),o("button",{type:"submit",disabled:i.value,class:"px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50"},g(i.value?"Salvataggio...":"Crea Progetto"),9,M)])],32)])]))}};export{R as default};
