import{r as n,A as _,c as i,g as h,j as t,t as g,v as l,x as d,H as b,F as C,k as E,I as S,C as T,s as P,u as V,m as z,o as u}from"./vendor.js";import{u as U}from"./app.js";const D={class:"max-w-4xl mx-auto"},F={key:0,class:"flex items-center justify-center h-64"},M={key:1,class:"bg-white shadow rounded-lg"},R={class:"px-6 py-4 border-b border-gray-200"},$={class:"mt-1 text-sm text-gray-600"},N=["value"],A={class:"grid grid-cols-1 md:grid-cols-2 gap-4"},B={class:"grid grid-cols-1 md:grid-cols-2 gap-4"},I={class:"grid grid-cols-1 md:grid-cols-2 gap-4"},X={class:"flex items-center"},L={class:"flex justify-between pt-6 border-t border-gray-200"},q={class:"flex space-x-3"},H=["disabled"],G={__name:"ProjectEdit",setup(J){const p=V(),f=z(),m=U(),v=n(!0),c=n(!1),a=n(null),y=n([]),r=n({name:"",description:"",client_id:"",start_date:"",end_date:"",project_type:"service",budget:"",status:"planning",is_billable:!0}),x=async()=>{try{const s=await fetch(`/api/projects/${p.params.id}`,{headers:{"Content-Type":"application/json","X-CSRFToken":m.csrfToken}});if(!s.ok)throw new Error("Progetto non trovato");const e=await s.json();a.value=e.data.project,r.value={name:a.value.name||"",description:a.value.description||"",client_id:a.value.client_id||"",start_date:a.value.start_date||"",end_date:a.value.end_date||"",project_type:a.value.project_type||"service",budget:a.value.budget||"",status:a.value.status||"planning",is_billable:a.value.is_billable!==!1}}catch(s){console.error("Error loading project:",s),f.push("/app/projects")}finally{v.value=!1}},w=async()=>{var s;try{const e=await fetch("/api/clients",{headers:{"Content-Type":"application/json","X-CSRFToken":m.csrfToken}});if(e.ok){const o=await e.json();y.value=((s=o.data)==null?void 0:s.clients)||[]}}catch(e){console.error("Error loading clients:",e)}},k=async()=>{c.value=!0;try{if(!(await fetch(`/api/projects/${p.params.id}`,{method:"PUT",headers:{"Content-Type":"application/json","X-CSRFToken":m.csrfToken},body:JSON.stringify(r.value)})).ok)throw new Error("Errore nel salvataggio del progetto");f.push(`/app/projects/${p.params.id}`)}catch(s){console.error("Error saving project:",s),alert("Errore nel salvataggio del progetto")}finally{c.value=!1}},j=async()=>{if(confirm("Sei sicuro di voler eliminare questo progetto? Questa azione non può essere annullata."))try{if(!(await fetch(`/api/projects/${p.params.id}`,{method:"DELETE",headers:{"Content-Type":"application/json","X-CSRFToken":m.csrfToken}})).ok)throw new Error("Errore nell'eliminazione del progetto");f.push("/app/projects")}catch(s){console.error("Error deleting project:",s),alert("Errore nell'eliminazione del progetto")}};return _(()=>{x(),w()}),(s,e)=>(u(),i("div",D,[v.value?(u(),i("div",F,e[10]||(e[10]=[t("div",{class:"animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"},null,-1)]))):a.value?(u(),i("div",M,[t("div",R,[e[11]||(e[11]=t("h1",{class:"text-xl font-semibold text-gray-900"},"Modifica Progetto",-1)),t("p",$,g(a.value.name),1)]),t("form",{onSubmit:P(k,["prevent"]),class:"p-6 space-y-6"},[t("div",null,[e[12]||(e[12]=t("label",{for:"name",class:"block text-sm font-medium text-gray-700"},"Nome Progetto *",-1)),l(t("input",{id:"name","onUpdate:modelValue":e[0]||(e[0]=o=>r.value.name=o),type:"text",required:"",class:"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"},null,512),[[d,r.value.name]])]),t("div",null,[e[13]||(e[13]=t("label",{for:"description",class:"block text-sm font-medium text-gray-700"},"Descrizione",-1)),l(t("textarea",{id:"description","onUpdate:modelValue":e[1]||(e[1]=o=>r.value.description=o),rows:"3",class:"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"},null,512),[[d,r.value.description]])]),t("div",null,[e[15]||(e[15]=t("label",{for:"client_id",class:"block text-sm font-medium text-gray-700"},"Cliente",-1)),l(t("select",{id:"client_id","onUpdate:modelValue":e[2]||(e[2]=o=>r.value.client_id=o),class:"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"},[e[14]||(e[14]=t("option",{value:""},"Seleziona cliente",-1)),(u(!0),i(C,null,E(y.value,o=>(u(),i("option",{key:o.id,value:o.id},g(o.name),9,N))),128))],512),[[b,r.value.client_id]])]),t("div",A,[t("div",null,[e[16]||(e[16]=t("label",{for:"start_date",class:"block text-sm font-medium text-gray-700"},"Data Inizio",-1)),l(t("input",{id:"start_date","onUpdate:modelValue":e[3]||(e[3]=o=>r.value.start_date=o),type:"date",class:"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"},null,512),[[d,r.value.start_date]])]),t("div",null,[e[17]||(e[17]=t("label",{for:"end_date",class:"block text-sm font-medium text-gray-700"},"Data Fine",-1)),l(t("input",{id:"end_date","onUpdate:modelValue":e[4]||(e[4]=o=>r.value.end_date=o),type:"date",class:"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"},null,512),[[d,r.value.end_date]])])]),t("div",B,[t("div",null,[e[19]||(e[19]=t("label",{for:"project_type",class:"block text-sm font-medium text-gray-700"},"Tipo Progetto",-1)),l(t("select",{id:"project_type","onUpdate:modelValue":e[5]||(e[5]=o=>r.value.project_type=o),class:"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"},e[18]||(e[18]=[S('<option value="service">🔧 Servizio</option><option value="license">📄 Licenza</option><option value="consulting">💼 Consulenza</option><option value="product">📦 Prodotto</option><option value="rd">🔬 R&amp;D</option><option value="internal">🏢 Interno</option>',6)]),512),[[b,r.value.project_type]])]),t("div",null,[e[20]||(e[20]=t("label",{for:"budget",class:"block text-sm font-medium text-gray-700"},"Budget (€)",-1)),l(t("input",{id:"budget","onUpdate:modelValue":e[6]||(e[6]=o=>r.value.budget=o),type:"number",step:"0.01",min:"0",class:"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"},null,512),[[d,r.value.budget]])])]),t("div",I,[t("div",null,[e[22]||(e[22]=t("label",{for:"status",class:"block text-sm font-medium text-gray-700"},"Stato",-1)),l(t("select",{id:"status","onUpdate:modelValue":e[7]||(e[7]=o=>r.value.status=o),class:"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"},e[21]||(e[21]=[t("option",{value:"planning"},"📋 Pianificazione",-1),t("option",{value:"active"},"🚀 Attivo",-1),t("option",{value:"on-hold"},"⏸️ In Pausa",-1),t("option",{value:"completed"},"✅ Completato",-1)]),512),[[b,r.value.status]])]),t("div",X,[l(t("input",{id:"is_billable","onUpdate:modelValue":e[8]||(e[8]=o=>r.value.is_billable=o),type:"checkbox",class:"h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"},null,512),[[T,r.value.is_billable]]),e[23]||(e[23]=t("label",{for:"is_billable",class:"ml-2 block text-sm text-gray-900"}," Progetto fatturabile ",-1))])]),t("div",L,[t("button",{type:"button",onClick:j,class:"px-4 py-2 border border-red-300 rounded-md shadow-sm text-sm font-medium text-red-700 bg-white hover:bg-red-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"}," Elimina Progetto "),t("div",q,[t("button",{type:"button",onClick:e[9]||(e[9]=o=>s.$router.go(-1)),class:"px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"}," Annulla "),t("button",{type:"submit",disabled:c.value,class:"px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50"},g(c.value?"Salvataggio...":"Salva Modifiche"),9,H)])])],32)])):h("",!0)]))}};export{G as default};
