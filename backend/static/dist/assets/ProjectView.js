import{c as s,o as t,j as e,t as o,n as H,g as D,l as X,a as re,i as ye,b as fe,F,k as K,h as de,D as ve,f as B,z as ee,r as V,w as Z,A as se,v as U,H as J,I as ae,x as W,s as oe,N as he,p as le,u as be,O as ke,m as we}from"./vendor.js";import{_ as ue,u as ne,a as _e,b as xe}from"./app.js";const $e={class:"project-header bg-white shadow-sm rounded-lg p-6 mb-6"},je={key:0,class:"animate-pulse"},Ce={key:1,class:"flex flex-col sm:flex-row sm:items-center sm:justify-between"},Me={class:"flex-1"},Te={class:"flex items-center space-x-3 mb-2"},Pe={class:"text-2xl font-bold text-gray-900"},Se={class:"flex flex-wrap items-center gap-4 text-sm text-gray-500"},De={key:0},Ve={key:1},ze={key:2},Ie={key:3},Ae={class:"mt-4 sm:mt-0 flex space-x-3"},Be={key:2,class:"text-center py-8"},Ee={__name:"ProjectHeader",props:{project:{type:Object,default:null},loading:{type:Boolean,default:!1}},emits:["edit","delete"],setup(x){const P=p=>({planning:"bg-yellow-100 text-yellow-800",active:"bg-green-100 text-green-800",on_hold:"bg-orange-100 text-orange-800",completed:"bg-blue-100 text-blue-800",cancelled:"bg-red-100 text-red-800"})[p]||"bg-gray-100 text-gray-800",C=p=>({planning:"Pianificazione",active:"Attivo",on_hold:"In Pausa",completed:"Completato",cancelled:"Annullato"})[p]||p,M=p=>p?new Date(p).toLocaleDateString("it-IT"):"",$=p=>p?new Intl.NumberFormat("it-IT",{style:"currency",currency:"EUR"}).format(p):"";return(p,v)=>{const m=fe("router-link");return t(),s("div",$e,[x.loading?(t(),s("div",je,v[1]||(v[1]=[e("div",{class:"h-8 bg-gray-200 rounded w-1/3 mb-2"},null,-1),e("div",{class:"h-4 bg-gray-200 rounded w-1/2"},null,-1)]))):x.project?(t(),s("div",Ce,[e("div",Me,[e("div",Te,[e("h1",Pe,o(x.project.name),1),e("span",{class:H(["inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium",P(x.project.status)])},o(C(x.project.status)),3)]),e("div",Se,[x.project.client?(t(),s("span",De,[v[2]||(v[2]=e("svg",{class:"w-4 h-4 inline mr-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-4m-5 0H3m2 0h3M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"})],-1)),X(" Cliente: "+o(x.project.client.name),1)])):D("",!0),x.project.start_date?(t(),s("span",Ve,[v[3]||(v[3]=e("svg",{class:"w-4 h-4 inline mr-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"})],-1)),X(" Inizio: "+o(M(x.project.start_date)),1)])):D("",!0),x.project.end_date?(t(),s("span",ze,[v[4]||(v[4]=e("svg",{class:"w-4 h-4 inline mr-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"})],-1)),X(" Fine: "+o(M(x.project.end_date)),1)])):D("",!0),x.project.budget?(t(),s("span",Ie,[v[5]||(v[5]=e("svg",{class:"w-4 h-4 inline mr-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"})],-1)),X(" Budget: "+o($(x.project.budget)),1)])):D("",!0)])]),e("div",Ae,[re(m,{to:`/app/projects/${x.project.id}/edit`,class:"inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"},{default:ye(()=>v[6]||(v[6]=[e("svg",{class:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"})],-1),X(" Modifica ")])),_:1,__:[6]},8,["to"]),e("button",{onClick:v[0]||(v[0]=u=>p.$emit("delete")),class:"inline-flex items-center px-3 py-2 border border-red-300 shadow-sm text-sm leading-4 font-medium rounded-md text-red-700 bg-white hover:bg-red-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"},v[7]||(v[7]=[e("svg",{class:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"})],-1),X(" Elimina ")]))])])):(t(),s("div",Be,v[8]||(v[8]=[e("p",{class:"text-gray-500"},"Progetto non trovato",-1)])))])}}},Ue=ue(Ee,[["__scopeId","data-v-6f1b5cc9"]]),He={class:"tab-navigation"},Oe={class:"border-b border-gray-200"},Re={class:"-mb-px flex space-x-8","aria-label":"Tabs"},Le=["onClick","aria-current"],Fe={key:1,class:"ml-2 inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-600"},Ke={__name:"TabNavigation",props:{modelValue:{type:String,required:!0},tabs:{type:Array,required:!0,validator:x=>x.every(P=>typeof P=="object"&&P.id&&P.label)}},emits:["update:modelValue"],setup(x,{emit:P}){const C=x,M=P,$=m=>C.modelValue===m,p=m=>{M("update:modelValue",m)},v=m=>{const u={"chart-bar":{template:`<svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
      </svg>`},"clipboard-list":{template:`<svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01" />
      </svg>`},users:{template:`<svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a4 4 0 11-8 0 4 4 0 018 0z" />
      </svg>`},folder:{template:`<svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-6l-2-2H5a2 2 0 00-2 2z" />
      </svg>`},"trending-up":{template:`<svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
      </svg>`},calendar:{template:`<svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
      </svg>`},clock:{template:`<svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
      </svg>`}};return u[m]||u["chart-bar"]};return(m,u)=>(t(),s("div",He,[e("div",Oe,[e("nav",Re,[(t(!0),s(F,null,K(x.tabs,g=>(t(),s("button",{key:g.id,onClick:A=>p(g.id),class:H(["whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm flex items-center space-x-2",$(g.id)?"border-primary-500 text-primary-600":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"]),"aria-current":$(g.id)?"page":void 0},[g.icon?(t(),de(ve(v(g.icon)),{key:0,class:"w-4 h-4"})):D("",!0),e("span",null,o(g.label),1),g.count!==void 0?(t(),s("span",Fe,o(g.count),1)):D("",!0)],10,Le))),128))])])]))}},qe=ue(Ke,[["__scopeId","data-v-c205976e"]]),Ne={class:"project-overview"},Xe={key:0,class:"animate-pulse space-y-4"},We={key:1,class:"space-y-6"},Ye={class:"bg-white shadow rounded-lg p-6"},Ge={key:0,class:"text-gray-600"},Je={key:1,class:"text-gray-400 italic"},Qe={class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4"},Ze={class:"bg-white shadow rounded-lg p-6"},et={class:"flex items-center"},tt={class:"ml-5 w-0 flex-1"},st={class:"text-lg font-medium text-gray-900"},rt={class:"bg-white shadow rounded-lg p-6"},ot={class:"flex items-center"},at={class:"ml-5 w-0 flex-1"},nt={class:"text-lg font-medium text-gray-900"},it={class:"bg-white shadow rounded-lg p-6"},lt={class:"flex items-center"},dt={class:"ml-5 w-0 flex-1"},ut={class:"text-lg font-medium text-gray-900"},ct={class:"bg-white shadow rounded-lg p-6"},gt={class:"flex items-center"},mt={class:"ml-5 w-0 flex-1"},pt={class:"text-lg font-medium text-gray-900"},vt={class:"bg-white shadow rounded-lg p-6"},xt={class:"w-full bg-gray-200 rounded-full h-2.5"},yt={class:"text-sm text-gray-500 mt-2"},ft={class:"bg-white shadow rounded-lg p-6"},ht={class:"space-y-4"},bt={class:"flex justify-between items-center"},kt={class:"text-sm font-medium"},wt={class:"flex justify-between items-center"},_t={class:"text-sm font-medium"},$t={class:"w-full bg-gray-200 rounded-full h-3"},jt={class:"flex justify-between items-center text-sm"},Ct={class:"bg-white shadow rounded-lg p-6"},Mt={class:"space-y-3"},Tt={class:"flex-shrink-0"},Pt=["src","alt"],St={key:1,class:"w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center"},Dt={class:"text-xs font-medium text-gray-600"},Vt={class:"flex-1"},zt={class:"text-sm font-medium text-gray-900"},It={class:"text-xs text-gray-500"},At={class:"text-right"},Bt={class:"text-xs text-gray-500"},Et={key:0,class:"text-center py-4"},Ut={class:"bg-white shadow rounded-lg p-6"},Ht={class:"space-y-3"},Ot={class:"flex-shrink-0"},Rt={class:"flex-1"},Lt={class:"text-sm text-gray-900"},Ft={class:"flex items-center space-x-2 mt-1"},Kt={class:"text-xs text-gray-500"},qt={class:"text-xs text-gray-500"},Nt={key:0,class:"text-center py-4"},Xt={key:2,class:"text-center py-8"},Wt={__name:"ProjectOverview",props:{project:{type:Object,default:null},loading:{type:Boolean,default:!1}},setup(x){const P=x,C=B(()=>{if(!P.project||!P.project.task_count)return 0;const c=P.project.completed_tasks||0,a=P.project.task_count||1;return Math.round(c/a*100)}),M=B(()=>{var c;return((c=P.project)==null?void 0:c.team_members)||[]}),$=B(()=>{var S,z,k;if((S=P.project)!=null&&S.expenses)return P.project.expenses;const c=((z=P.project)==null?void 0:z.total_hours)||0,a=(k=P.project)!=null&&k.client_daily_rate?P.project.client_daily_rate/8:50;return c*a}),p=B(()=>{var a;return(((a=P.project)==null?void 0:a.budget)||0)-$.value}),v=B(()=>{var a;const c=((a=P.project)==null?void 0:a.budget)||1;return Math.min(Math.round($.value/c*100),100)}),m=B(()=>{const c=v.value;return c>=90?"bg-red-600":c>=75?"bg-yellow-600":"bg-green-600"}),u=B(()=>{var a;const c=p.value;return c<0?"text-red-600":c<(((a=P.project)==null?void 0:a.budget)||0)*.1?"text-yellow-600":"text-green-600"}),g=B(()=>{var c;return(c=P.project)!=null&&c.tasks?[...P.project.tasks].sort((a,S)=>new Date(S.updated_at)-new Date(a.updated_at)).slice(0,5).map(a=>{var S;return{id:a.id,description:`Task "${a.name}" ${A(a.status)}`,created_at:a.updated_at,user_name:((S=a.assignee)==null?void 0:S.full_name)||"Non assegnato",type:R(a.status)}}):[]}),A=c=>({todo:"creato","in-progress":"in corso",review:"in revisione",done:"completato"})[c]||c,R=c=>({todo:"task_created","in-progress":"task_updated",review:"task_updated",done:"task_completed"})[c]||"task_updated",E=c=>c?new Intl.NumberFormat("it-IT",{style:"currency",currency:"EUR"}).format(c):"Non specificato",I=c=>c?new Date(c).toLocaleDateString("it-IT",{day:"numeric",month:"short",hour:"2-digit",minute:"2-digit"}):"",f=c=>c?c.split(" ").map(a=>a.charAt(0).toUpperCase()).slice(0,2).join(""):"??",N=c=>{const a={task_created:"bg-blue-600",task_completed:"bg-green-600",task_updated:"bg-yellow-600",comment_added:"bg-purple-600",file_uploaded:"bg-indigo-600",member_added:"bg-pink-600",default:"bg-gray-600"};return a[c]||a.default};return(c,a)=>(t(),s("div",Ne,[x.loading?(t(),s("div",Xe,a[0]||(a[0]=[e("div",{class:"h-4 bg-gray-200 rounded w-3/4"},null,-1),e("div",{class:"h-4 bg-gray-200 rounded w-1/2"},null,-1),e("div",{class:"h-32 bg-gray-200 rounded"},null,-1)]))):x.project?(t(),s("div",We,[e("div",Ye,[a[1]||(a[1]=e("h3",{class:"text-lg font-medium text-gray-900 mb-4"},"Descrizione Progetto",-1)),x.project.description?(t(),s("p",Ge,o(x.project.description),1)):(t(),s("p",Je,"Nessuna descrizione disponibile"))]),e("div",Qe,[e("div",Ze,[e("div",et,[a[3]||(a[3]=e("div",{class:"flex-shrink-0"},[e("svg",{class:"h-8 w-8 text-blue-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01"})])],-1)),e("div",tt,[e("dl",null,[a[2]||(a[2]=e("dt",{class:"text-sm font-medium text-gray-500 truncate"},"Task Totali",-1)),e("dd",st,o(x.project.task_count||0),1)])])])]),e("div",rt,[e("div",ot,[a[5]||(a[5]=e("div",{class:"flex-shrink-0"},[e("svg",{class:"h-8 w-8 text-green-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"})])],-1)),e("div",at,[e("dl",null,[a[4]||(a[4]=e("dt",{class:"text-sm font-medium text-gray-500 truncate"},"Task Completati",-1)),e("dd",nt,o(x.project.completed_tasks||0),1)])])])]),e("div",it,[e("div",lt,[a[7]||(a[7]=e("div",{class:"flex-shrink-0"},[e("svg",{class:"h-8 w-8 text-purple-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"})])],-1)),e("div",dt,[e("dl",null,[a[6]||(a[6]=e("dt",{class:"text-sm font-medium text-gray-500 truncate"},"Membri Team",-1)),e("dd",ut,o(x.project.team_count||0),1)])])])]),e("div",ct,[e("div",gt,[a[9]||(a[9]=e("div",{class:"flex-shrink-0"},[e("svg",{class:"h-8 w-8 text-yellow-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"})])],-1)),e("div",mt,[e("dl",null,[a[8]||(a[8]=e("dt",{class:"text-sm font-medium text-gray-500 truncate"},"Budget",-1)),e("dd",pt,o(E(x.project.budget)),1)])])])])]),e("div",vt,[a[10]||(a[10]=e("h3",{class:"text-lg font-medium text-gray-900 mb-4"},"Progresso Progetto",-1)),e("div",xt,[e("div",{class:"bg-blue-600 h-2.5 rounded-full transition-all duration-300",style:ee({width:`${C.value}%`})},null,4)]),e("p",yt,o(C.value)+"% completato",1)]),e("div",ft,[a[15]||(a[15]=e("h3",{class:"text-lg font-medium text-gray-900 mb-4"},"Budget vs Spese",-1)),e("div",ht,[e("div",bt,[a[11]||(a[11]=e("span",{class:"text-sm text-gray-600"},"Budget Totale",-1)),e("span",kt,o(E(x.project.budget)),1)]),a[14]||(a[14]=e("div",{class:"w-full bg-gray-200 rounded-full h-3"},[e("div",{class:"bg-blue-600 h-3 rounded-full transition-all duration-300",style:{width:"100%"}})],-1)),e("div",wt,[a[12]||(a[12]=e("span",{class:"text-sm text-gray-600"},"Spese Sostenute",-1)),e("span",_t,o(E($.value)),1)]),e("div",$t,[e("div",{class:H(["h-3 rounded-full transition-all duration-300",m.value]),style:ee({width:v.value+"%"})},null,6)]),e("div",jt,[a[13]||(a[13]=e("span",{class:"text-gray-600"},"Rimanente",-1)),e("span",{class:H(["font-medium",u.value])},o(E(p.value)),3)])])]),e("div",Ct,[a[17]||(a[17]=e("div",{class:"flex items-center justify-between mb-4"},[e("h3",{class:"text-lg font-medium text-gray-900"},"Team Members"),e("button",{class:"text-sm text-blue-600 hover:text-blue-800"},"Visualizza tutti")],-1)),e("div",Mt,[(t(!0),s(F,null,K(M.value,S=>(t(),s("div",{key:S.id,class:"flex items-center space-x-3"},[e("div",Tt,[S.profile_image?(t(),s("img",{key:0,src:S.profile_image,alt:S.full_name,class:"w-8 h-8 rounded-full"},null,8,Pt)):(t(),s("div",St,[e("span",Dt,o(f(S.full_name)),1)]))]),e("div",Vt,[e("p",zt,o(S.full_name),1),e("p",It,o(S.role||"Team Member"),1)]),e("div",At,[e("p",Bt,o(S.hours_worked||0)+"h",1)])]))),128)),M.value.length===0?(t(),s("div",Et,a[16]||(a[16]=[e("p",{class:"text-gray-500"},"Nessun membro del team assegnato",-1)]))):D("",!0)])]),e("div",Ut,[a[20]||(a[20]=e("div",{class:"flex items-center justify-between mb-4"},[e("h3",{class:"text-lg font-medium text-gray-900"},"Attività Recenti"),e("button",{class:"text-sm text-blue-600 hover:text-blue-800"},"Visualizza tutte")],-1)),e("div",Ht,[(t(!0),s(F,null,K(g.value,S=>(t(),s("div",{key:S.id,class:"flex items-start space-x-3"},[e("div",Ot,[e("div",{class:H(["w-2 h-2 rounded-full mt-2",N(S.type)])},null,2)]),e("div",Rt,[e("p",Lt,o(S.description),1),e("div",Ft,[e("p",Kt,o(I(S.created_at)),1),a[18]||(a[18]=e("span",{class:"text-xs text-gray-400"},"•",-1)),e("p",qt,o(S.user_name),1)])])]))),128)),g.value.length===0?(t(),s("div",Nt,a[19]||(a[19]=[e("p",{class:"text-gray-500"},"Nessuna attività recente",-1)]))):D("",!0)])])])):(t(),s("div",Xt,a[21]||(a[21]=[e("p",{class:"text-gray-500"},"Progetto non trovato",-1)])))]))}},me=ue(Wt,[["__scopeId","data-v-16274846"]]),Yt={class:"space-y-6"},Gt={class:"bg-white dark:bg-gray-800 shadow rounded-lg"},Jt={class:"px-6 py-4 border-b border-gray-200 dark:border-gray-700"},Qt={class:"flex items-center justify-between"},Zt={class:"mt-4 grid grid-cols-1 md:grid-cols-4 gap-4"},es=["value"],ts={class:"mt-4 flex items-center justify-between"},ss={class:"flex items-center space-x-4"},rs={class:"text-sm text-gray-500 dark:text-gray-400"},os={key:0,class:"flex justify-center py-8"},as={key:1,class:"bg-red-50 border border-red-200 rounded-md p-4"},ns={class:"text-red-600"},is={key:2,class:"bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden"},ls={class:"min-w-full divide-y divide-gray-200 dark:divide-gray-700"},ds={class:"col-span-4"},us={class:"text-sm font-medium text-gray-900 dark:text-white"},cs={key:0,class:"text-sm text-gray-500 dark:text-gray-400 truncate"},gs={class:"col-span-2"},ms={key:0,class:"flex items-center"},ps={class:"h-8 w-8 rounded-full bg-gray-300 flex items-center justify-center text-sm font-medium text-gray-700"},vs={class:"ml-2"},xs={class:"text-sm font-medium text-gray-900 dark:text-white"},ys={key:1,class:"text-sm text-gray-500 dark:text-gray-400"},fs={class:"col-span-1"},hs={class:"col-span-1"},bs={class:"col-span-2"},ks={key:0,class:"text-sm text-gray-900 dark:text-white"},ws={key:1,class:"text-sm text-gray-500 dark:text-gray-400"},_s={class:"col-span-1"},$s={class:"text-sm text-gray-900 dark:text-white"},js={key:0,class:"text-gray-500"},Cs={class:"col-span-1"},Ms={class:"flex items-center space-x-2"},Ts=["onClick"],Ps={key:0,class:"px-6 py-12 text-center"},Ss={key:3,class:"grid grid-cols-1 md:grid-cols-4 gap-6"},Ds={class:"flex items-center justify-between mb-4"},Vs={class:"font-medium text-gray-900 dark:text-white"},zs={class:"bg-gray-200 dark:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-full px-2 py-1 text-xs"},Is={class:"space-y-3"},As=["onClick"],Bs={class:"font-medium text-sm text-gray-900 dark:text-white mb-1"},Es={key:0,class:"text-xs text-gray-500 dark:text-gray-400 mb-2 line-clamp-2"},Us={class:"flex items-center justify-between"},Hs={key:0,class:"h-6 w-6 rounded-full bg-gray-300 flex items-center justify-center text-xs font-medium text-gray-700"},Os={class:"mt-3"},Rs={class:"text-lg font-medium text-gray-900 dark:text-white mb-4"},Ls={class:"grid grid-cols-1 gap-4"},Fs={class:"grid grid-cols-2 gap-4"},Ks=["value"],qs={class:"grid grid-cols-2 gap-4"},Ns={class:"flex justify-end space-x-3 mt-6"},Xs=["disabled"],Ws={__name:"ProjectTasks",props:{project:{type:Object,default:null},loading:{type:Boolean,default:!1}},setup(x,{expose:P}){const C=x,M=ne(),{hasPermission:$}=_e(),p=V([]),v=V(!1),m=V(""),u=V("list"),g=V(!1),A=V({status:"",priority:"",assignee_id:"",search:""}),R=V(!1),E=V(!1),I=V(null),f=V({name:"",description:"",status:"todo",priority:"medium",assignee_id:"",due_date:"",estimated_hours:null}),N=B(()=>$.value("manage_project_tasks")),c=[{value:"todo",label:"Da fare"},{value:"in-progress",label:"In corso"},{value:"review",label:"In revisione"},{value:"done",label:"Completato"}],a=async()=>{var T,l;if((T=C.project)!=null&&T.id){v.value=!0,m.value="";try{const Q=new URLSearchParams({project_id:C.project.id,...A.value}),te=await fetch(`/api/tasks?${Q}`,{headers:{"Content-Type":"application/json","X-CSRFToken":M.csrfToken}});if(!te.ok)throw new Error("Errore nel caricamento dei task");const y=await te.json();p.value=((l=y.data)==null?void 0:l.tasks)||y.tasks||[]}catch(Q){m.value=Q.message}finally{v.value=!1}}},S=async()=>{g.value=!0;try{const T=E.value?`/api/tasks/${I.value.id}`:"/api/tasks",l=E.value?"PUT":"POST",Q={...f.value,project_id:C.project.id};if(!(await fetch(T,{method:l,headers:{"Content-Type":"application/json","X-CSRFToken":M.csrfToken},body:JSON.stringify(Q)})).ok)throw new Error("Errore nel salvataggio del task");await a(),k()}catch(T){m.value=T.message}finally{g.value=!1}},z=T=>{I.value=T,f.value={name:T.name,description:T.description||"",status:T.status,priority:T.priority,assignee_id:T.assignee_id||"",due_date:T.due_date?T.due_date.split("T")[0]:"",estimated_hours:T.estimated_hours},E.value=!0},k=()=>{R.value=!1,E.value=!1,I.value=null,f.value={name:"",description:"",status:"todo",priority:"medium",assignee_id:"",due_date:"",estimated_hours:null}},j=T=>p.value.filter(l=>l.status===T),_=T=>({todo:"bg-gray-100 text-gray-800","in-progress":"bg-blue-100 text-blue-800",review:"bg-yellow-100 text-yellow-800",done:"bg-green-100 text-green-800"})[T]||"bg-gray-100 text-gray-800",L=T=>({todo:"Da fare","in-progress":"In corso",review:"In revisione",done:"Completato"})[T]||T,h=T=>({low:"bg-green-100 text-green-800",medium:"bg-yellow-100 text-yellow-800",high:"bg-orange-100 text-orange-800",urgent:"bg-red-100 text-red-800"})[T]||"bg-gray-100 text-gray-800",r=T=>({low:"Bassa",medium:"Media",high:"Alta",urgent:"Urgente"})[T]||T,i=(T,l)=>`${(T==null?void 0:T.charAt(0))||""}${(l==null?void 0:l.charAt(0))||""}`.toUpperCase(),b=T=>new Date(T).toLocaleDateString("it-IT");let w;const q=()=>{clearTimeout(w),w=setTimeout(()=>{a()},300)};return Z(()=>{var T;return(T=C.project)==null?void 0:T.id},T=>{T&&a()}),se(()=>{var T;(T=C.project)!=null&&T.id&&a()}),P({refresh:a}),(T,l)=>{var Q,te;return t(),s("div",Yt,[e("div",Gt,[e("div",Jt,[e("div",Qt,[l[16]||(l[16]=e("h3",{class:"text-lg font-medium text-gray-900 dark:text-white"},"Task del Progetto",-1)),N.value?(t(),s("button",{key:0,onClick:l[0]||(l[0]=y=>R.value=!0),class:"inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"},l[15]||(l[15]=[e("svg",{class:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 6v6m0 0v6m0-6h6m-6 0H6"})],-1),X(" Nuovo Task ")]))):D("",!0)]),e("div",Zt,[e("div",null,[l[18]||(l[18]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Stato",-1)),U(e("select",{"onUpdate:modelValue":l[1]||(l[1]=y=>A.value.status=y),onChange:a,class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"},l[17]||(l[17]=[ae('<option value="">Tutti gli stati</option><option value="todo">Da fare</option><option value="in-progress">In corso</option><option value="review">In revisione</option><option value="done">Completato</option>',5)]),544),[[J,A.value.status]])]),e("div",null,[l[20]||(l[20]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Priorità",-1)),U(e("select",{"onUpdate:modelValue":l[2]||(l[2]=y=>A.value.priority=y),onChange:a,class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"},l[19]||(l[19]=[ae('<option value="">Tutte le priorità</option><option value="low">Bassa</option><option value="medium">Media</option><option value="high">Alta</option><option value="urgent">Urgente</option>',5)]),544),[[J,A.value.priority]])]),e("div",null,[l[22]||(l[22]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Assegnatario",-1)),U(e("select",{"onUpdate:modelValue":l[3]||(l[3]=y=>A.value.assignee_id=y),onChange:a,class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"},[l[21]||(l[21]=e("option",{value:""},"Tutti",-1)),(t(!0),s(F,null,K(((Q=x.project)==null?void 0:Q.team_members)||[],y=>(t(),s("option",{key:y.id,value:y.id},o(y.first_name)+" "+o(y.last_name),9,es))),128))],544),[[J,A.value.assignee_id]])]),e("div",null,[l[23]||(l[23]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Ricerca",-1)),U(e("input",{"onUpdate:modelValue":l[4]||(l[4]=y=>A.value.search=y),onInput:q,type:"text",placeholder:"Cerca task...",class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"},null,544),[[W,A.value.search]])])]),e("div",ts,[e("div",ss,[l[24]||(l[24]=e("span",{class:"text-sm text-gray-500 dark:text-gray-400"},"Vista:",-1)),e("button",{onClick:l[5]||(l[5]=y=>u.value="list"),class:H([u.value==="list"?"bg-primary-100 text-primary-700":"text-gray-500 hover:text-gray-700","px-3 py-1 rounded-md text-sm font-medium"])}," Lista ",2),e("button",{onClick:l[6]||(l[6]=y=>u.value="kanban"),class:H([u.value==="kanban"?"bg-primary-100 text-primary-700":"text-gray-500 hover:text-gray-700","px-3 py-1 rounded-md text-sm font-medium"])}," Kanban ",2)]),e("div",rs,o(p.value.length)+" task trovati ",1)])])]),v.value?(t(),s("div",os,l[25]||(l[25]=[e("div",{class:"animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"},null,-1)]))):D("",!0),m.value?(t(),s("div",as,[e("p",ns,o(m.value),1)])):D("",!0),!v.value&&u.value==="list"?(t(),s("div",is,[e("div",ls,[l[27]||(l[27]=ae('<div class="bg-gray-50 dark:bg-gray-700 px-6 py-3 grid grid-cols-12 gap-4 text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"><div class="col-span-4">Task</div><div class="col-span-2">Assegnatario</div><div class="col-span-1">Stato</div><div class="col-span-1">Priorità</div><div class="col-span-2">Scadenza</div><div class="col-span-1">Ore</div><div class="col-span-1">Azioni</div></div>',1)),(t(!0),s(F,null,K(p.value,y=>(t(),s("div",{key:y.id,class:"px-6 py-4 grid grid-cols-12 gap-4 items-center hover:bg-gray-50 dark:hover:bg-gray-700"},[e("div",ds,[e("div",us,o(y.name),1),y.description?(t(),s("div",cs,o(y.description),1)):D("",!0)]),e("div",gs,[y.assignee?(t(),s("div",ms,[e("div",ps,o(i(y.assignee.first_name,y.assignee.last_name)),1),e("div",vs,[e("div",xs,o(y.assignee.first_name)+" "+o(y.assignee.last_name),1)])])):(t(),s("span",ys,"Non assegnato"))]),e("div",fs,[e("span",{class:H([_(y.status),"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"])},o(L(y.status)),3)]),e("div",hs,[e("span",{class:H([h(y.priority),"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"])},o(r(y.priority)),3)]),e("div",bs,[y.due_date?(t(),s("div",ks,o(b(y.due_date)),1)):(t(),s("span",ws,"-"))]),e("div",_s,[e("div",$s,[X(o(y.actual_hours||0)+"h ",1),y.estimated_hours?(t(),s("span",js,"/ "+o(y.estimated_hours)+"h",1)):D("",!0)])]),e("div",Cs,[e("div",Ms,[e("button",{onClick:n=>z(y),class:"text-primary-600 hover:text-primary-900 text-sm"}," Modifica ",8,Ts)])])]))),128)),p.value.length===0?(t(),s("div",Ps,l[26]||(l[26]=[e("p",{class:"text-gray-500 dark:text-gray-400"},"Nessun task trovato",-1)]))):D("",!0)])])):D("",!0),!v.value&&u.value==="kanban"?(t(),s("div",Ss,[(t(),s(F,null,K(c,y=>e("div",{key:y.value,class:"bg-gray-50 dark:bg-gray-700 rounded-lg p-4"},[e("div",Ds,[e("h4",Vs,o(y.label),1),e("span",zs,o(j(y.value).length),1)]),e("div",Is,[(t(!0),s(F,null,K(j(y.value),n=>(t(),s("div",{key:n.id,class:"bg-white dark:bg-gray-800 p-3 rounded-lg shadow-sm border border-gray-200 dark:border-gray-600 cursor-pointer hover:shadow-md transition-shadow",onClick:d=>z(n)},[e("div",Bs,o(n.name),1),n.description?(t(),s("div",Es,o(n.description),1)):D("",!0),e("div",Us,[e("span",{class:H([h(n.priority),"inline-flex items-center px-2 py-0.5 rounded text-xs font-medium"])},o(r(n.priority)),3),n.assignee?(t(),s("div",Hs,o(i(n.assignee.first_name,n.assignee.last_name)),1)):D("",!0)])],8,As))),128))])])),64))])):D("",!0),R.value||E.value?(t(),s("div",{key:4,class:"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50",onClick:k},[e("div",{class:"relative top-20 mx-auto p-5 border w-[500px] shadow-lg rounded-md bg-white dark:bg-gray-800",onClick:l[14]||(l[14]=oe(()=>{},["stop"]))},[e("div",Os,[e("h3",Rs,o(E.value?"Modifica Task":"Nuovo Task"),1),e("form",{onSubmit:oe(S,["prevent"])},[e("div",Ls,[e("div",null,[l[28]||(l[28]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Nome",-1)),U(e("input",{"onUpdate:modelValue":l[7]||(l[7]=y=>f.value.name=y),type:"text",required:"",class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"},null,512),[[W,f.value.name]])]),e("div",null,[l[29]||(l[29]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Descrizione",-1)),U(e("textarea",{"onUpdate:modelValue":l[8]||(l[8]=y=>f.value.description=y),rows:"3",class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"},null,512),[[W,f.value.description]])]),e("div",Fs,[e("div",null,[l[31]||(l[31]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Stato",-1)),U(e("select",{"onUpdate:modelValue":l[9]||(l[9]=y=>f.value.status=y),class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"},l[30]||(l[30]=[e("option",{value:"todo"},"Da fare",-1),e("option",{value:"in-progress"},"In corso",-1),e("option",{value:"review"},"In revisione",-1),e("option",{value:"done"},"Completato",-1)]),512),[[J,f.value.status]])]),e("div",null,[l[33]||(l[33]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Priorità",-1)),U(e("select",{"onUpdate:modelValue":l[10]||(l[10]=y=>f.value.priority=y),class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"},l[32]||(l[32]=[e("option",{value:"low"},"Bassa",-1),e("option",{value:"medium"},"Media",-1),e("option",{value:"high"},"Alta",-1),e("option",{value:"urgent"},"Urgente",-1)]),512),[[J,f.value.priority]])])]),e("div",null,[l[35]||(l[35]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Assegnatario",-1)),U(e("select",{"onUpdate:modelValue":l[11]||(l[11]=y=>f.value.assignee_id=y),class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"},[l[34]||(l[34]=e("option",{value:""},"Non assegnato",-1)),(t(!0),s(F,null,K(((te=x.project)==null?void 0:te.team_members)||[],y=>(t(),s("option",{key:y.id,value:y.id},o(y.first_name)+" "+o(y.last_name),9,Ks))),128))],512),[[J,f.value.assignee_id]])]),e("div",qs,[e("div",null,[l[36]||(l[36]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Scadenza",-1)),U(e("input",{"onUpdate:modelValue":l[12]||(l[12]=y=>f.value.due_date=y),type:"date",class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"},null,512),[[W,f.value.due_date]])]),e("div",null,[l[37]||(l[37]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Ore stimate",-1)),U(e("input",{"onUpdate:modelValue":l[13]||(l[13]=y=>f.value.estimated_hours=y),type:"number",step:"0.5",min:"0",class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"},null,512),[[W,f.value.estimated_hours]])])])]),e("div",Ns,[e("button",{type:"button",onClick:k,class:"px-4 py-2 text-sm font-medium text-gray-700 bg-gray-200 hover:bg-gray-300 rounded-md"}," Annulla "),e("button",{type:"submit",disabled:g.value,class:"px-4 py-2 text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 rounded-md disabled:opacity-50"},o(g.value?"Salvataggio...":E.value?"Aggiorna":"Crea"),9,Xs)])],32)])])])):D("",!0)])}}},Ys={class:"space-y-6"},Gs={class:"bg-white dark:bg-gray-800 shadow rounded-lg"},Js={class:"px-6 py-4 border-b border-gray-200 dark:border-gray-700"},Qs={class:"flex items-center justify-between"},Zs={class:"p-6 border-b border-gray-200 dark:border-gray-700"},er={class:"grid grid-cols-1 md:grid-cols-4 gap-4"},tr={class:"text-center"},sr={class:"text-2xl font-bold text-primary-600 dark:text-primary-400"},rr={class:"text-center"},or={class:"text-2xl font-bold text-green-600"},ar={class:"text-center"},nr={class:"text-2xl font-bold text-blue-600"},ir={class:"text-center"},lr={class:"text-2xl font-bold text-purple-600"},dr={class:"p-6"},ur={class:"space-y-4"},cr={class:"flex items-center justify-between"},gr={class:"flex items-center space-x-4"},mr={class:"flex-shrink-0"},pr=["src","alt"],vr={key:1,class:"w-12 h-12 bg-gray-300 dark:bg-gray-600 rounded-full flex items-center justify-center"},xr={class:"text-sm font-medium text-gray-600 dark:text-gray-300"},yr={class:"flex-1"},fr={class:"flex items-center space-x-2"},hr={class:"text-lg font-medium text-gray-900 dark:text-white"},br={key:0,class:"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200"},kr={class:"text-sm text-gray-600 dark:text-gray-400"},wr={class:"text-xs text-gray-500 dark:text-gray-500"},_r={class:"flex items-center space-x-4"},$r={class:"text-right"},jr={class:"text-sm font-medium text-gray-900 dark:text-white"},Cr={class:"text-right"},Mr={class:"text-sm font-medium text-gray-900 dark:text-white"},Tr={class:"text-right"},Pr={class:"text-sm font-medium text-gray-900 dark:text-white"},Sr={class:"flex items-center space-x-2"},Dr=["onClick"],Vr=["onClick"],zr={class:"mt-4"},Ir={class:"flex items-center justify-between text-xs text-gray-500 dark:text-gray-400 mb-1"},Ar={class:"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2"},Br={key:0,class:"text-center py-8"},Er={class:"mt-6"},Ur={class:"mt-3"},Hr={class:"space-y-4"},Or=["value"],Rr={class:"flex justify-end space-x-3 mt-6"},Lr=["disabled"],Fr={__name:"ProjectTeam",props:{project:{type:Object,default:null},loading:{type:Boolean,default:!1}},emits:["refresh"],setup(x,{expose:P,emit:C}){const M=x,$=ne(),p=V(!1),v=V([]),m=V(!1),u=V({user_id:"",role:""}),g=B(()=>{var r;return((r=M.project)==null?void 0:r.team_members)||[]}),A=B(()=>g.value.reduce((r,i)=>r+(i.hours_worked||0),0)),R=B(()=>g.value.length===0?0:Math.round(A.value/g.value.length)),E=B(()=>g.value.filter(r=>(r.hours_worked||0)>0).length),I=r=>r?r.split(" ").map(i=>i.charAt(0).toUpperCase()).slice(0,2).join(""):"??",f=r=>{var b;return(((b=M.project)==null?void 0:b.tasks)||[]).filter(w=>w.assignee_id===r).length},N=r=>{var b;return(((b=M.project)==null?void 0:b.tasks)||[]).filter(w=>w.assignee_id===r&&w.status==="done").length},c=r=>{const i=f(r),b=N(r);return i===0?0:Math.round(b/i*100)},a=r=>{const i=c(r);return i>=80?"bg-green-600":i>=60?"bg-yellow-600":i>=40?"bg-orange-600":"bg-red-600"},S=r=>!r||r===0?"0.00":parseFloat(r).toFixed(2),z=async()=>{var r;try{const i=await fetch("/api/personnel/users",{headers:{"Content-Type":"application/json","X-CSRFToken":$.csrfToken}});if(i.ok){const b=await i.json(),w=g.value.map(q=>q.id);v.value=(r=b.data)!=null&&r.users?b.data.users.filter(q=>!w.includes(q.id)):[]}}catch(i){console.error("Errore nel caricamento utenti:",i),v.value=[]}},k=async()=>{m.value=!0;try{const r=await fetch(`/api/projects/${M.project.id}/team`,{method:"POST",headers:{"Content-Type":"application/json","X-CSRFToken":$.csrfToken},body:JSON.stringify(u.value)});if(r.ok)h("refresh"),L();else{const i=await r.json();alert(i.message||"Errore nell'aggiunta del membro")}}catch{alert("Errore nell'aggiunta del membro")}finally{m.value=!1}},j=r=>{console.log("Edit member:",r)},_=async r=>{if(confirm(`Rimuovere ${r.full_name} dal progetto?`))try{const i=await fetch(`/api/projects/${M.project.id}/team/${r.id}`,{method:"DELETE",headers:{"Content-Type":"application/json","X-CSRFToken":$.csrfToken}});if(i.ok)h("refresh");else{const b=await i.json();alert(b.message||"Errore nella rimozione del membro")}}catch{alert("Errore nella rimozione del membro")}},L=()=>{p.value=!1,u.value={user_id:"",role:""}},h=C;return se(()=>{z()}),Z(()=>p.value,r=>{r&&z()}),Z(()=>{var r;return(r=M.project)==null?void 0:r.team_members},()=>{p.value&&z()}),P({refresh:z}),(r,i)=>(t(),s("div",Ys,[e("div",Gs,[e("div",Js,[e("div",Qs,[i[6]||(i[6]=e("h3",{class:"text-lg font-medium text-gray-900 dark:text-white"}," Team del Progetto ",-1)),e("button",{onClick:i[0]||(i[0]=b=>p.value=!0),class:"inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"},i[5]||(i[5]=[e("svg",{class:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 6v6m0 0v6m0-6h6m-6 0H6"})],-1),X(" Aggiungi Membro ")]))])]),e("div",Zs,[e("div",er,[e("div",tr,[e("div",sr,o(g.value.length),1),i[7]||(i[7]=e("div",{class:"text-sm text-gray-500 dark:text-gray-400"},"Membri Totali",-1))]),e("div",rr,[e("div",or,o(A.value)+"h",1),i[8]||(i[8]=e("div",{class:"text-sm text-gray-500 dark:text-gray-400"},"Ore Totali",-1))]),e("div",ar,[e("div",nr,o(R.value)+"h",1),i[9]||(i[9]=e("div",{class:"text-sm text-gray-500 dark:text-gray-400"},"Media per Membro",-1))]),e("div",ir,[e("div",lr,o(E.value),1),i[10]||(i[10]=e("div",{class:"text-sm text-gray-500 dark:text-gray-400"},"Membri Attivi",-1))])])]),e("div",dr,[e("div",ur,[(t(!0),s(F,null,K(g.value,b=>{var w,q;return t(),s("div",{key:b.id,class:"border border-gray-200 dark:border-gray-700 rounded-lg p-4 hover:shadow-md transition-all duration-200"},[e("div",cr,[e("div",gr,[e("div",mr,[b.profile_image?(t(),s("img",{key:0,src:b.profile_image,alt:b.full_name,class:"w-12 h-12 rounded-full"},null,8,pr)):(t(),s("div",vr,[e("span",xr,o(I(b.full_name)),1)]))]),e("div",yr,[e("div",fr,[e("h4",hr,o(b.full_name),1),b.id===((w=x.project)==null?void 0:w.manager_id)?(t(),s("span",br," Project Manager ")):D("",!0)]),e("p",kr,o(b.role||"Team Member"),1),e("p",wr,o(b.email),1)])]),e("div",_r,[e("div",$r,[e("div",jr,o(S(b.hours_worked||0))+"h",1),i[11]||(i[11]=e("div",{class:"text-xs text-gray-500 dark:text-gray-400"},"ore lavorate",-1))]),e("div",Cr,[e("div",Mr,o(f(b.id)),1),i[12]||(i[12]=e("div",{class:"text-xs text-gray-500 dark:text-gray-400"},"task assegnati",-1))]),e("div",Tr,[e("div",Pr,o(N(b.id)),1),i[13]||(i[13]=e("div",{class:"text-xs text-gray-500 dark:text-gray-400"},"completati",-1))]),e("div",Sr,[e("button",{onClick:T=>j(b),class:"p-1 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300",title:"Modifica membro"},i[14]||(i[14]=[e("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"})],-1)]),8,Dr),b.id!==((q=x.project)==null?void 0:q.manager_id)?(t(),s("button",{key:0,onClick:T=>_(b),class:"p-1 text-gray-400 hover:text-red-600",title:"Rimuovi dal progetto"},i[15]||(i[15]=[e("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"})],-1)]),8,Vr)):D("",!0)])])]),e("div",zr,[e("div",Ir,[i[16]||(i[16]=e("span",null,"Produttività",-1)),e("span",null,o(c(b.id))+"%",1)]),e("div",Ar,[e("div",{class:H(["h-2 rounded-full transition-all duration-300",a(b.id)]),style:ee({width:c(b.id)+"%"})},null,6)])])])}),128)),g.value.length===0?(t(),s("div",Br,[i[18]||(i[18]=e("svg",{class:"mx-auto h-12 w-12 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"})],-1)),i[19]||(i[19]=e("h3",{class:"mt-2 text-sm font-medium text-gray-900 dark:text-white"},"Nessun membro del team",-1)),i[20]||(i[20]=e("p",{class:"mt-1 text-sm text-gray-500 dark:text-gray-400"},"Inizia aggiungendo membri al progetto.",-1)),e("div",Er,[e("button",{onClick:i[1]||(i[1]=b=>p.value=!0),class:"inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700"},i[17]||(i[17]=[e("svg",{class:"-ml-1 mr-2 h-5 w-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 6v6m0 0v6m0-6h6m-6 0H6"})],-1),X(" Aggiungi primo membro ")]))])])):D("",!0)])])]),p.value?(t(),s("div",{key:0,class:"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50",onClick:L},[e("div",{class:"relative top-20 mx-auto p-5 border w-[500px] shadow-lg rounded-md bg-white dark:bg-gray-800",onClick:i[4]||(i[4]=oe(()=>{},["stop"]))},[e("div",Ur,[i[25]||(i[25]=e("h3",{class:"text-lg font-medium text-gray-900 dark:text-white mb-4"}," Aggiungi Membro al Team ",-1)),e("form",{onSubmit:oe(k,["prevent"])},[e("div",Hr,[e("div",null,[i[22]||(i[22]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Utente",-1)),U(e("select",{"onUpdate:modelValue":i[2]||(i[2]=b=>u.value.user_id=b),required:"",class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"},[i[21]||(i[21]=e("option",{value:""},"Seleziona utente",-1)),(t(!0),s(F,null,K(v.value,b=>(t(),s("option",{key:b.id,value:b.id},o(b.full_name)+" ("+o(b.email)+") ",9,Or))),128))],512),[[J,u.value.user_id]])]),e("div",null,[i[24]||(i[24]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Ruolo",-1)),U(e("select",{"onUpdate:modelValue":i[3]||(i[3]=b=>u.value.role=b),required:"",class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"},i[23]||(i[23]=[ae('<option value="">Seleziona ruolo</option><option value="Team Member">Team Member</option><option value="Developer">Developer</option><option value="Designer">Designer</option><option value="QA Tester">QA Tester</option><option value="Business Analyst">Business Analyst</option><option value="Technical Lead">Technical Lead</option>',7)]),512),[[J,u.value.role]])])]),e("div",Rr,[e("button",{type:"button",onClick:L,class:"px-4 py-2 text-sm font-medium text-gray-700 bg-gray-200 hover:bg-gray-300 rounded-md"}," Annulla "),e("button",{type:"submit",disabled:m.value,class:"px-4 py-2 text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 rounded-md disabled:opacity-50"},o(m.value?"Aggiungendo...":"Aggiungi"),9,Lr)])],32)])])])):D("",!0)]))}};function Kr(x,P){return t(),s("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M6.75 3v2.25M17.25 3v2.25M3 18.75V7.5a2.25 2.25 0 0 1 2.25-2.25h13.5A2.25 2.25 0 0 1 21 7.5v11.25m-18 0A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75m-18 0v-7.5A2.25 2.25 0 0 1 5.25 9h13.5A2.25 2.25 0 0 1 21 11.25v7.5"})])}function pe(x,P){return t(),s("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M2.25 8.25h19.5M2.25 9h19.5m-16.5 5.25h6m-6 2.25h3m-3.75 3h15a2.25 2.25 0 0 0 2.25-2.25V6.75A2.25 2.25 0 0 0 19.5 4.5h-15a2.25 2.25 0 0 0-2.25 2.25v10.5A2.25 2.25 0 0 0 4.5 19.5Z"})])}function qr(x,P){return t(),s("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M12 4.5v15m7.5-7.5h-15"})])}const Nr={class:"fixed inset-0 z-50 overflow-y-auto"},Xr={class:"flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0"},Wr={class:"inline-block align-bottom bg-white dark:bg-gray-800 rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full"},Yr={class:"bg-white dark:bg-gray-800 px-4 pt-5 pb-4 sm:p-6 sm:pb-4"},Gr={class:"mb-4"},Jr={class:"text-lg font-medium text-gray-900 dark:text-white"},Qr={class:"space-y-4"},Zr={class:"bg-gray-50 dark:bg-gray-700 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse"},eo=["disabled"],to={key:0},so={key:1},ro={__name:"ExpenseModal",props:{projectId:{type:[String,Number],required:!0},expense:{type:Object,default:null}},emits:["close","saved"],setup(x,{emit:P}){const C=x,M=P,$=V(!1),p=he({description:"",amount:0,category:"",date:new Date().toISOString().split("T")[0],notes:""}),v=async()=>{$.value=!0;try{const m=C.expense?`/api/expenses/${C.expense.id}`:`/api/projects/${C.projectId}/expenses`,u=C.expense?"PUT":"POST";(await fetch(m,{method:u,headers:{"Content-Type":"application/json"},body:JSON.stringify(p)})).ok?M("saved"):console.error("Error saving expense")}catch(m){console.error("Error saving expense:",m)}finally{$.value=!1}};return se(()=>{C.expense&&Object.assign(p,{description:C.expense.description,amount:C.expense.amount,category:C.expense.category,date:C.expense.date.split("T")[0],notes:C.expense.notes||""})}),(m,u)=>(t(),s("div",Nr,[e("div",Xr,[e("div",{class:"fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity",onClick:u[0]||(u[0]=g=>m.$emit("close"))}),e("div",Wr,[e("form",{onSubmit:oe(v,["prevent"])},[e("div",Yr,[e("div",Gr,[e("h3",Jr,o(x.expense?"Modifica Spesa":"Aggiungi Spesa"),1)]),e("div",Qr,[e("div",null,[u[7]||(u[7]=e("label",{for:"description",class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Descrizione ",-1)),U(e("input",{"onUpdate:modelValue":u[1]||(u[1]=g=>p.description=g),type:"text",id:"description",required:"",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white",placeholder:"Descrizione della spesa"},null,512),[[W,p.description]])]),e("div",null,[u[8]||(u[8]=e("label",{for:"amount",class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Importo (€) ",-1)),U(e("input",{"onUpdate:modelValue":u[2]||(u[2]=g=>p.amount=g),type:"number",step:"0.01",id:"amount",required:"",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white",placeholder:"0.00"},null,512),[[W,p.amount,void 0,{number:!0}]])]),e("div",null,[u[10]||(u[10]=e("label",{for:"category",class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Categoria ",-1)),U(e("select",{"onUpdate:modelValue":u[3]||(u[3]=g=>p.category=g),id:"category",required:"",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white"},u[9]||(u[9]=[ae('<option value="">Seleziona categoria</option><option value="travel">Viaggio</option><option value="meals">Pasti</option><option value="materials">Materiali</option><option value="software">Software</option><option value="hardware">Hardware</option><option value="services">Servizi</option><option value="other">Altro</option>',8)]),512),[[J,p.category]])]),e("div",null,[u[11]||(u[11]=e("label",{for:"date",class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Data ",-1)),U(e("input",{"onUpdate:modelValue":u[4]||(u[4]=g=>p.date=g),type:"date",id:"date",required:"",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white"},null,512),[[W,p.date]])]),e("div",null,[u[12]||(u[12]=e("label",{for:"notes",class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Note (opzionale) ",-1)),U(e("textarea",{"onUpdate:modelValue":u[5]||(u[5]=g=>p.notes=g),id:"notes",rows:"3",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white",placeholder:"Note aggiuntive..."},null,512),[[W,p.notes]])])])]),e("div",Zr,[e("button",{type:"submit",disabled:$.value,class:"w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-primary-600 text-base font-medium text-white hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 sm:ml-3 sm:w-auto sm:text-sm disabled:opacity-50"},[$.value?(t(),s("span",to,"Salvando...")):(t(),s("span",so,o(x.expense?"Aggiorna":"Salva"),1))],8,eo),e("button",{type:"button",onClick:u[6]||(u[6]=g=>m.$emit("close")),class:"mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 dark:border-gray-600 shadow-sm px-4 py-2 bg-white dark:bg-gray-800 text-base font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm"}," Annulla ")])],32)])])]))}},oo={class:"project-expenses"},ao={class:"space-y-6"},no={class:"flex justify-between items-center"},io={key:0,class:"text-center py-8"},lo={key:1,class:"text-center py-12"},uo={key:2,class:"bg-white dark:bg-gray-800 shadow overflow-hidden sm:rounded-md"},co={class:"divide-y divide-gray-200 dark:divide-gray-700"},go={class:"flex items-center justify-between"},mo={class:"flex-1"},po={class:"flex items-center"},vo={class:"flex-shrink-0"},xo={class:"h-10 w-10 rounded-full bg-gray-300 dark:bg-gray-600 flex items-center justify-center"},yo={class:"ml-4 flex-1"},fo={class:"flex items-center justify-between"},ho={class:"text-sm font-medium text-gray-900 dark:text-white"},bo={class:"ml-2 flex-shrink-0"},ko={class:"text-sm font-medium text-gray-900 dark:text-white"},wo={class:"mt-1 flex items-center text-sm text-gray-500 dark:text-gray-400"},_o={class:"capitalize"},$o={key:0,class:"mx-2"},jo={key:1},Co={class:"mt-2 flex items-center space-x-4 text-xs"},Mo={key:0,class:"inline-flex items-center text-green-600 dark:text-green-400"},To={key:0,class:"flex items-center space-x-2"},Po=["onClick"],So=["onClick"],Do={key:3,class:"bg-gray-50 dark:bg-gray-700 rounded-lg p-4"},Vo={class:"flex justify-between items-center"},zo={class:"text-lg font-bold text-gray-900 dark:text-white"},Io={__name:"ProjectExpenses",props:{project:{type:Object,required:!0},loading:{type:Boolean,default:!1}},setup(x){const P=x;xe();const C=ne(),M=V(!1),$=V([]),p=V(!1),v=V(null),m=B(()=>C.hasPermission("manage_expenses")),u=B(()=>$.value.reduce((c,a)=>c+a.amount,0)),g=async()=>{var c;if((c=P.project)!=null&&c.id){M.value=!0;try{const a=await fetch(`/api/projects/${P.project.id}/expenses`);a.ok&&($.value=await a.json())}catch(a){console.error("Error loading expenses:",a)}finally{M.value=!1}}},A=c=>{v.value=c,p.value=!0},R=async c=>{if(confirm("Sei sicuro di voler eliminare questa spesa?"))try{(await fetch(`/api/expenses/${c}`,{method:"DELETE"})).ok&&($.value=$.value.filter(S=>S.id!==c))}catch(a){console.error("Error deleting expense:",a)}},E=()=>{p.value=!1,v.value=null},I=()=>{E(),g()},f=c=>new Intl.NumberFormat("it-IT",{minimumFractionDigits:2,maximumFractionDigits:2}).format(c),N=c=>new Date(c).toLocaleDateString("it-IT");return Z(()=>{var c;return(c=P.project)==null?void 0:c.id},(c,a)=>{c&&c!==a&&g()},{immediate:!0}),se(()=>{g()}),(c,a)=>{var S;return t(),s("div",oo,[e("div",ao,[e("div",no,[a[2]||(a[2]=e("h3",{class:"text-lg font-medium text-gray-900 dark:text-white"},"Spese Progetto",-1)),m.value?(t(),s("button",{key:0,onClick:a[0]||(a[0]=z=>p.value=!0),class:"inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"},[re(le(qr),{class:"w-4 h-4 mr-2"}),a[1]||(a[1]=X(" Aggiungi Spesa "))])):D("",!0)]),M.value?(t(),s("div",io,a[3]||(a[3]=[e("div",{class:"inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"},null,-1),e("p",{class:"mt-2 text-sm text-gray-500"},"Caricamento spese...",-1)]))):$.value.length===0?(t(),s("div",lo,[re(le(pe),{class:"mx-auto h-12 w-12 text-gray-400"}),a[4]||(a[4]=e("h3",{class:"mt-2 text-sm font-medium text-gray-900 dark:text-white"},"Nessuna spesa",-1)),a[5]||(a[5]=e("p",{class:"mt-1 text-sm text-gray-500"},"Non ci sono ancora spese registrate per questo progetto.",-1))])):(t(),s("div",uo,[e("ul",co,[(t(!0),s(F,null,K($.value,z=>(t(),s("li",{key:z.id,class:"px-6 py-4"},[e("div",go,[e("div",mo,[e("div",po,[e("div",vo,[e("div",xo,[re(le(pe),{class:"h-5 w-5 text-gray-600 dark:text-gray-300"})])]),e("div",yo,[e("div",fo,[e("p",ho,o(z.description),1),e("div",bo,[e("p",ko," €"+o(f(z.amount)),1)])]),e("div",wo,[re(le(Kr),{class:"flex-shrink-0 mr-1.5 h-4 w-4"}),X(" "+o(N(z.date))+" ",1),a[6]||(a[6]=e("span",{class:"mx-2"},"•",-1)),e("span",_o,o(c.getCategoryLabel(z.category)),1),z.user?(t(),s("span",$o,"•")):D("",!0),z.user?(t(),s("span",jo,o(z.user.name),1)):D("",!0)]),e("div",Co,[e("span",{class:H([c.getBillingTypeClass(z.billing_type),"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"])},o(c.getBillingTypeLabel(z.billing_type)),3),e("span",{class:H([c.getStatusClass(z.status),"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"])},o(c.getStatusLabel(z.status)),3),z.receipt_path?(t(),s("span",Mo,a[7]||(a[7]=[e("svg",{class:"w-3 h-3 mr-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})],-1),X(" Ricevuta ")]))):D("",!0)])])])]),m.value?(t(),s("div",To,[e("button",{onClick:k=>A(z),class:"text-primary-600 hover:text-primary-900 text-sm font-medium"}," Modifica ",8,Po),e("button",{onClick:k=>R(z.id),class:"text-red-600 hover:text-red-900 text-sm font-medium"}," Elimina ",8,So)])):D("",!0)])]))),128))])])),$.value.length>0?(t(),s("div",Do,[e("div",Vo,[a[8]||(a[8]=e("span",{class:"text-sm font-medium text-gray-900 dark:text-white"},"Totale Spese:",-1)),e("span",zo,"€"+o(f(u.value)),1)])])):D("",!0)]),p.value?(t(),de(ro,{key:0,"project-id":(S=x.project)==null?void 0:S.id,expense:v.value,onClose:E,onSaved:I},null,8,["project-id","expense"])):D("",!0)])}}},Ao={class:"project-kpi"},Bo={key:0,class:"animate-pulse space-y-4"},Eo={class:"grid grid-cols-1 md:grid-cols-4 gap-4"},Uo={key:1,class:"space-y-6"},Ho={class:"bg-white shadow rounded-lg p-6"},Oo={class:"flex items-center justify-between"},Ro=["disabled"],Lo={class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4"},Fo={class:"bg-white shadow rounded-lg p-6"},Ko={class:"flex items-center"},qo={class:"ml-5 w-0 flex-1"},No={class:"text-lg font-medium text-gray-900"},Xo={class:"text-xs text-gray-500"},Wo={class:"bg-white shadow rounded-lg p-6"},Yo={class:"flex items-center"},Go={class:"ml-5 w-0 flex-1"},Jo={class:"text-lg font-medium text-gray-900"},Qo={class:"bg-white shadow rounded-lg p-6"},Zo={class:"flex items-center"},ea={class:"ml-5 w-0 flex-1"},ta={class:"text-lg font-medium text-gray-900"},sa={class:"text-xs text-gray-500"},ra={class:"bg-white shadow rounded-lg p-6"},oa={class:"flex items-center"},aa={class:"ml-5 w-0 flex-1"},na={class:"text-lg font-medium text-gray-900"},ia={class:"grid grid-cols-1 lg:grid-cols-2 gap-6"},la={class:"bg-white shadow rounded-lg p-6"},da={class:"space-y-4"},ua={class:"flex justify-between text-sm"},ca={class:"font-medium"},ga={class:"w-full bg-gray-200 rounded-full h-3"},ma={class:"flex justify-between text-sm"},pa={class:"text-gray-600"},va={class:"font-medium"},xa={class:"bg-white shadow rounded-lg p-6"},ya={class:"space-y-4"},fa={class:"flex justify-between text-sm"},ha={class:"font-medium"},ba={class:"w-full bg-gray-200 rounded-full h-3"},ka={class:"flex justify-between text-sm"},wa={class:"text-gray-600"},_a={class:"font-medium"},$a={class:"bg-white shadow rounded-lg p-6"},ja={class:"grid grid-cols-1 md:grid-cols-3 gap-4"},Ca={class:"text-center p-4 border rounded-lg"},Ma={class:"text-xs text-gray-500"},Ta={class:"text-center p-4 border rounded-lg"},Pa={class:"text-xs text-gray-500"},Sa={class:"text-center p-4 border rounded-lg"},Da={class:"text-xs text-gray-500"},Va={key:2,class:"text-center py-8"},za={key:3,class:"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50"},Ia={class:"relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white"},Aa={class:"mt-3"},Ba={class:"mt-6 space-y-6"},Ea={class:"bg-gray-50 p-4 rounded-lg"},Ua={class:"font-medium text-gray-900"},Ha={class:"text-sm text-gray-600"},Oa={class:"space-y-6"},Ra={class:"flex items-center justify-between mb-4"},La={class:"font-medium text-gray-900"},Fa={class:"text-sm text-gray-600"},Ka={class:"flex items-center space-x-2"},qa={class:"text-xs text-gray-500"},Na=["onClick"],Xa={class:"grid grid-cols-1 md:grid-cols-3 gap-4"},Wa=["onUpdate:modelValue","onInput"],Ya=["onUpdate:modelValue","onInput"],Ga=["onUpdate:modelValue","onInput"],Ja={class:"mt-4"},Qa=["onUpdate:modelValue","onInput"],Za={class:"mt-4 flex justify-end"},en=["onClick","disabled"],tn={key:0,class:"animate-spin -ml-1 mr-2 h-4 w-4 text-white",fill:"none",viewBox:"0 0 24 24"},sn={key:1,class:"text-sm text-green-600"},rn={class:"mt-6 pt-4 border-t flex justify-between"},on={class:"flex space-x-3"},an=["disabled"],nn={__name:"ProjectKPI",props:{project:{type:Object,default:null},loading:{type:Boolean,default:!1}},emits:["refresh"],setup(x,{emit:P}){const C=x,M=P,$=V(!1),p=V(!1),v=V(null),m=V({}),u=V({totalHours:0,workDays:0,totalCosts:0,costVariance:0,potentialRevenue:0,actualRevenue:0,marginPercentage:0}),g=V({budget:80,time:85,margin:15}),A=B(()=>{var n;return!((n=C.project)!=null&&n.budget)||u.value.totalCosts===0?0:Math.round(u.value.totalCosts/C.project.budget*100)}),R=B(()=>{var n;return!((n=C.project)!=null&&n.estimated_hours)||u.value.totalHours===0?0:Math.round(u.value.totalHours/C.project.estimated_hours*100)}),E=B(()=>{const n=u.value.costVariance;return n>0?"text-red-600":n<0?"text-green-600":"text-gray-600"}),I=B(()=>{const n=u.value.marginPercentage;return n>=g.value.margin?"text-green-600":n>=g.value.margin*.7?"text-yellow-600":"text-red-600"}),f=B(()=>{const n=u.value.marginPercentage;return n>=g.value.margin?"Ottimo":n>=g.value.margin*.7?"Accettabile":"Critico"}),N=B(()=>{const n=A.value;return n>=g.value.budget?"text-red-600":n>=g.value.budget*.8?"text-yellow-600":"text-green-600"}),c=B(()=>{const n=R.value;return n>=g.value.time?"text-red-600":n>=g.value.time*.8?"text-yellow-600":"text-green-600"}),a=B(()=>{const n=u.value.marginPercentage;return n>=g.value.margin?"text-green-600":n>=g.value.margin*.7?"text-yellow-600":"text-red-600"}),S=n=>new Intl.NumberFormat("it-IT",{style:"currency",currency:"EUR"}).format(n||0),z=n=>!n||n===0?"0h":`${parseFloat(n).toFixed(2)}h`,k=n=>`${(n||0).toFixed(1)}%`,j=async()=>{var n;(n=C.project)!=null&&n.id&&_()},_=()=>{const n=C.project;n&&(u.value={totalHours:n.total_hours||0,workDays:Math.ceil((n.total_hours||0)/8),totalCosts:(n.total_hours||0)*50,costVariance:(n.total_hours||0)*50-(n.budget||0),potentialRevenue:n.budget||0,actualRevenue:n.invoiced_amount||0,marginPercentage:n.budget?(n.budget-(n.total_hours||0)*50)/n.budget*100:0})},L=async()=>{$.value=!0;try{await j(),M("refresh")}catch(n){console.error("Error refreshing KPIs:",n)}finally{$.value=!1}},h=B(()=>{var d;const n=((d=C.project)==null?void 0:d.project_type)||"service";return r(n)}),r=n=>{const d={service:[{name:"margin_percentage",display_name:"Margine Netto %",description:"Percentuale di margine netto sul fatturato",unit:"%",target_min:25,target_max:40,warning_threshold:15},{name:"utilization_rate",display_name:"Tasso di Utilizzo %",description:"Percentuale di utilizzo del team rispetto alla capacità teorica",unit:"%",target_min:75,target_max:85,warning_threshold:60},{name:"cost_per_hour",display_name:"Costo per Ora",description:"Costo medio per ora di lavoro, inclusi tutti i costi",unit:"€",target_min:30,target_max:50,warning_threshold:60},{name:"cost_revenue_ratio",display_name:"Rapporto C/R",description:"Rapporto tra costi sostenuti e ricavi generati",unit:"ratio",target_min:.6,target_max:.75,warning_threshold:.85}]};return d[n]||d.service},i=n=>({service:"🔧 Servizio",license:"📄 Licenza",consulting:"💼 Consulenza",product:"📦 Prodotto",rd:"🔬 R&D",internal:"🏢 Interno"})[n]||"Sconosciuto",b=()=>{h.value.forEach(d=>{m.value[d.name]||(m.value[d.name]={target_min:d.target_min,target_max:d.target_max,warning_threshold:d.warning_threshold,custom_description:"",isDirty:!1,isSaved:!1})}),p.value=!0},w=()=>{p.value=!1},q=n=>{m.value[n]&&(m.value[n].isDirty=!0,m.value[n].isSaved=!1)},T=n=>{const d=h.value.find(Y=>Y.name===n);d&&m.value[n]&&(m.value[n].target_min=d.target_min,m.value[n].target_max=d.target_max,m.value[n].warning_threshold=d.warning_threshold,m.value[n].custom_description="",m.value[n].isDirty=!0,m.value[n].isSaved=!1)},l=()=>{confirm("Sei sicuro di voler ripristinare tutti i KPI ai valori di default?")&&h.value.forEach(n=>{T(n.name)})},Q=async n=>{var d;if(m.value[n]){v.value=n;try{const Y=m.value[n];await new Promise(ie=>setTimeout(ie,1e3)),console.log("Saving KPI config:",{project_id:(d=C.project)==null?void 0:d.id,kpi_name:n,target_min:Y.target_min,target_max:Y.target_max,warning_threshold:Y.warning_threshold,custom_description:Y.custom_description}),m.value[n].isDirty=!1,m.value[n].isSaved=!0,setTimeout(()=>{m.value[n]&&(m.value[n].isSaved=!1)},3e3)}catch(Y){console.error("Error saving KPI config:",Y),alert("Errore nel salvataggio della configurazione KPI")}finally{v.value=null}}},te=async()=>{const n=h.value.filter(d=>{var Y;return(Y=m.value[d.name])==null?void 0:Y.isDirty});for(const d of n)await Q(d.name)},y=B(()=>h.value.some(n=>{var d;return(d=m.value[n.name])==null?void 0:d.isDirty}));return Z(()=>C.project,n=>{n&&j()},{immediate:!0}),se(()=>{C.project&&j()}),(n,d)=>{var Y,ie;return t(),s("div",Ao,[x.loading?(t(),s("div",Bo,[e("div",Eo,[(t(),s(F,null,K(4,O=>e("div",{key:O,class:"bg-gray-200 rounded-lg h-24"})),64))]),d[0]||(d[0]=e("div",{class:"bg-gray-200 rounded-lg h-64"},null,-1))])):x.project?(t(),s("div",Uo,[e("div",Ho,[e("div",Oo,[d[3]||(d[3]=e("div",null,[e("h3",{class:"text-lg font-medium text-gray-900"},"KPI Progetto"),e("p",{class:"text-sm text-gray-600"},"Dashboard metriche e performance del progetto")],-1)),e("button",{onClick:L,disabled:$.value,class:"inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50"},[(t(),s("svg",{class:H(["w-4 h-4 mr-2",{"animate-spin":$.value}]),fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},d[1]||(d[1]=[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"},null,-1)]),2)),d[2]||(d[2]=X(" Aggiorna "))],8,Ro)])]),e("div",Lo,[e("div",Fo,[e("div",Ko,[d[5]||(d[5]=e("div",{class:"flex-shrink-0"},[e("svg",{class:"h-8 w-8 text-blue-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"})])],-1)),e("div",qo,[e("dl",null,[d[4]||(d[4]=e("dt",{class:"text-sm font-medium text-gray-500 truncate"},"Ore Totali",-1)),e("dd",No,o(z(u.value.totalHours)),1),e("dd",Xo,o(u.value.workDays)+" giorni lavorati",1)])])])]),e("div",Wo,[e("div",Yo,[d[7]||(d[7]=e("div",{class:"flex-shrink-0"},[e("svg",{class:"h-8 w-8 text-red-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"})])],-1)),e("div",Go,[e("dl",null,[d[6]||(d[6]=e("dt",{class:"text-sm font-medium text-gray-500 truncate"},"Costi Totali",-1)),e("dd",Jo,o(S(u.value.totalCosts)),1),e("dd",{class:H(["text-xs",E.value])},o(S(u.value.costVariance))+" vs budget",3)])])])]),e("div",Qo,[e("div",Zo,[d[9]||(d[9]=e("div",{class:"flex-shrink-0"},[e("svg",{class:"h-8 w-8 text-green-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"})])],-1)),e("div",ea,[e("dl",null,[d[8]||(d[8]=e("dt",{class:"text-sm font-medium text-gray-500 truncate"},"Ricavi Potenziali",-1)),e("dd",ta,o(S(u.value.potentialRevenue)),1),e("dd",sa,o(S(u.value.actualRevenue))+" fatturati",1)])])])]),e("div",ra,[e("div",oa,[d[11]||(d[11]=e("div",{class:"flex-shrink-0"},[e("svg",{class:"h-8 w-8 text-purple-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"})])],-1)),e("div",aa,[e("dl",null,[d[10]||(d[10]=e("dt",{class:"text-sm font-medium text-gray-500 truncate"},"Margine",-1)),e("dd",na,o(k(u.value.marginPercentage)),1),e("dd",{class:H(["text-xs",I.value])},o(f.value),3)])])])])]),e("div",ia,[e("div",la,[d[13]||(d[13]=e("h4",{class:"text-lg font-medium text-gray-900 mb-4"},"Andamento Budget",-1)),e("div",da,[e("div",ua,[d[12]||(d[12]=e("span",{class:"text-gray-600"},"Budget Totale",-1)),e("span",ca,o(S(x.project.budget||0)),1)]),e("div",ga,[e("div",{class:"bg-blue-600 h-3 rounded-full transition-all duration-300",style:ee({width:A.value+"%"})},null,4)]),e("div",ma,[e("span",pa,"Utilizzato: "+o(S(u.value.totalCosts)),1),e("span",va,o(A.value)+"%",1)])])]),e("div",xa,[d[15]||(d[15]=e("h4",{class:"text-lg font-medium text-gray-900 mb-4"},"Andamento Tempo",-1)),e("div",ya,[e("div",fa,[d[14]||(d[14]=e("span",{class:"text-gray-600"},"Ore Stimate",-1)),e("span",ha,o(z(x.project.estimated_hours||0)),1)]),e("div",ba,[e("div",{class:"bg-green-600 h-3 rounded-full transition-all duration-300",style:ee({width:R.value+"%"})},null,4)]),e("div",ka,[e("span",wa,"Lavorate: "+o(z(u.value.totalHours)),1),e("span",_a,o(R.value)+"%",1)])])])]),e("div",$a,[e("div",{class:"flex items-center justify-between mb-4"},[d[17]||(d[17]=e("h4",{class:"text-lg font-medium text-gray-900"},"Soglie KPI",-1)),e("button",{onClick:b,class:"inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"},d[16]||(d[16]=[e("svg",{class:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"}),e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M15 12a3 3 0 11-6 0 3 3 0 016 0z"})],-1),X(" Configura KPI ")]))]),e("div",ja,[e("div",Ca,[e("div",{class:H(["text-2xl font-bold",N.value])},o(A.value)+"% ",3),d[18]||(d[18]=e("div",{class:"text-sm text-gray-600"},"Budget Usage",-1)),e("div",Ma,"Soglia: "+o(g.value.budget)+"%",1)]),e("div",Ta,[e("div",{class:H(["text-2xl font-bold",c.value])},o(R.value)+"% ",3),d[19]||(d[19]=e("div",{class:"text-sm text-gray-600"},"Time Usage",-1)),e("div",Pa,"Soglia: "+o(g.value.time)+"%",1)]),e("div",Sa,[e("div",{class:H(["text-2xl font-bold",a.value])},o(k(u.value.marginPercentage)),3),d[20]||(d[20]=e("div",{class:"text-sm text-gray-600"},"Margine",-1)),e("div",Da,"Soglia: "+o(g.value.margin)+"%",1)])])])])):(t(),s("div",Va,d[21]||(d[21]=[e("p",{class:"text-gray-500"},"Progetto non trovato",-1)]))),p.value?(t(),s("div",za,[e("div",Ia,[e("div",Aa,[e("div",{class:"flex items-center justify-between pb-4 border-b"},[d[23]||(d[23]=e("h3",{class:"text-lg font-medium text-gray-900"},"Configurazione KPI Progetto",-1)),e("button",{onClick:w,class:"text-gray-400 hover:text-gray-600"},d[22]||(d[22]=[e("svg",{class:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)]))]),e("div",Ba,[e("div",Ea,[e("h4",Ua,o((Y=x.project)==null?void 0:Y.name),1),e("p",Ha,"Tipo: "+o(i((ie=x.project)==null?void 0:ie.project_type)),1)]),e("div",Oa,[(t(!0),s(F,null,K(h.value,O=>{var ce,ge;return t(),s("div",{key:O.name,class:"border border-gray-200 rounded-lg p-4"},[e("div",Ra,[e("div",null,[e("h5",La,o(O.display_name),1),e("p",Fa,o(O.description),1)]),e("div",Ka,[e("span",qa,o(O.unit),1),e("button",{onClick:G=>T(O.name),class:"text-xs text-blue-600 hover:text-blue-800",title:"Reset ai valori di default"}," Reset ",8,Na)])]),e("div",Xa,[e("div",null,[d[24]||(d[24]=e("label",{class:"block text-sm font-medium text-gray-700 mb-1"},"Target Minimo",-1)),U(e("input",{type:"number",step:"0.1","onUpdate:modelValue":G=>m.value[O.name].target_min=G,onInput:G=>q(O.name),class:"w-full border border-gray-300 rounded-md px-3 py-2 text-sm"},null,40,Wa),[[W,m.value[O.name].target_min]])]),e("div",null,[d[25]||(d[25]=e("label",{class:"block text-sm font-medium text-gray-700 mb-1"},"Target Massimo",-1)),U(e("input",{type:"number",step:"0.1","onUpdate:modelValue":G=>m.value[O.name].target_max=G,onInput:G=>q(O.name),class:"w-full border border-gray-300 rounded-md px-3 py-2 text-sm"},null,40,Ya),[[W,m.value[O.name].target_max]])]),e("div",null,[d[26]||(d[26]=e("label",{class:"block text-sm font-medium text-gray-700 mb-1"},"Soglia Warning",-1)),U(e("input",{type:"number",step:"0.1","onUpdate:modelValue":G=>m.value[O.name].warning_threshold=G,onInput:G=>q(O.name),class:"w-full border border-gray-300 rounded-md px-3 py-2 text-sm"},null,40,Ga),[[W,m.value[O.name].warning_threshold]])])]),e("div",Ja,[d[27]||(d[27]=e("label",{class:"block text-sm font-medium text-gray-700 mb-1"},"Descrizione Personalizzata",-1)),U(e("textarea",{"onUpdate:modelValue":G=>m.value[O.name].custom_description=G,onInput:G=>q(O.name),rows:"2",class:"w-full border border-gray-300 rounded-md px-3 py-2 text-sm",placeholder:"Descrizione specifica per questo progetto..."},null,40,Qa),[[W,m.value[O.name].custom_description]])]),e("div",Za,[(ce=m.value[O.name])!=null&&ce.isDirty?(t(),s("button",{key:0,onClick:G=>Q(O.name),disabled:v.value===O.name,class:"inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 disabled:opacity-50"},[v.value===O.name?(t(),s("svg",tn,d[28]||(d[28]=[e("circle",{class:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor","stroke-width":"4"},null,-1),e("path",{class:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"},null,-1)]))):D("",!0),X(" "+o(v.value===O.name?"Salvataggio...":"Salva KPI"),1)],8,en)):(ge=m.value[O.name])!=null&&ge.isSaved?(t(),s("span",sn,"✓ Salvato")):D("",!0)])])}),128))])]),e("div",rn,[e("button",{onClick:l,class:"inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"}," Reset Tutti "),e("div",on,[e("button",{onClick:w,class:"inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"}," Chiudi "),e("button",{onClick:te,disabled:!y.value,class:"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 disabled:opacity-50"}," Salva Tutto ",8,an)])])])])])):D("",!0)])}}},ln={class:"space-y-6"},dn={class:"bg-white dark:bg-gray-800 shadow rounded-lg"},un={class:"px-6 py-4 border-b border-gray-200 dark:border-gray-700"},cn={class:"flex items-center justify-between"},gn={class:"flex items-center space-x-4"},mn={class:"flex items-center space-x-2"},pn={key:0,class:"p-6"},vn={class:"overflow-x-auto"},xn={class:"min-w-[1000px]"},yn={class:"flex mb-4"},fn={class:"flex-1 flex"},hn={class:"space-y-1"},bn={class:"w-80 flex-shrink-0 px-4 py-3"},kn={class:"flex items-center space-x-2"},wn={class:"flex-1 min-w-0"},_n={class:"text-sm font-medium text-gray-900 dark:text-white truncate"},$n={class:"flex items-center space-x-2 text-xs text-gray-500 dark:text-gray-400"},jn={key:0},Cn={class:"text-xs text-gray-500 dark:text-gray-400 mt-1"},Mn={class:"flex-1 relative h-12 flex items-center"},Tn=["title"],Pn={class:"truncate"},Sn={class:"ml-2"},Dn={key:1,class:"text-center py-12"},Vn={key:2,class:"flex justify-center py-12"},zn={__name:"ProjectGantt",props:{project:{type:Object,default:null},loading:{type:Boolean,default:!1}},setup(x,{expose:P}){const C=x,M=V("weeks"),$=V(new Date),p=V([]),v=V(0),m=B(()=>{var k;return((k=C.project)==null?void 0:k.tasks)||[]}),u=B(()=>m.value.filter(k=>k.start_date&&k.due_date).map(k=>{const j=A(k);return{...k,timeline:j}})),g=()=>{const k=new Date,j=new Date($.value),_=[],L=12;for(let h=0;h<L;h++){const r=new Date(j);M.value==="weeks"?r.setDate(j.getDate()+h*7):M.value==="months"&&r.setMonth(j.getMonth()+h),_.push(r)}if(p.value=_,_.length>1){const h=_[0],r=new Date(_[_.length-1]);M.value==="weeks"?r.setDate(r.getDate()+7):M.value==="months"&&r.setMonth(r.getMonth()+1);const i=r-h,b=k-h;v.value=Math.max(0,Math.min(100,b/i*100))}else v.value=0},A=k=>{if(!p.value.length)return null;const j=new Date(k.start_date),_=new Date(k.due_date),L=p.value[0],r=p.value[p.value.length-1]-L,i=j-L,b=_-j,w=Math.max(0,i/r*100),q=Math.min(100-w,b/r*100);return{leftPercent:w,widthPercent:Math.max(5,q)}},R=k=>M.value==="weeks"?`${k.getDate()}/${k.getMonth()+1}`:M.value==="months"?k.toLocaleDateString("it-IT",{month:"short",year:"2-digit"}):"",E=k=>{const j=new Date,_=new Date(k);if(M.value==="weeks"){const L=new Date(_),h=new Date(_);return h.setDate(h.getDate()+6),j>=L&&j<=h}else if(M.value==="months")return _.getMonth()===j.getMonth()&&_.getFullYear()===j.getFullYear();return!1},I=()=>{const k=new Date;if(M.value==="weeks"){const j=new Date(k);j.setDate(k.getDate()-k.getDay()),$.value=j}else{const j=new Date(k.getFullYear(),k.getMonth(),1);$.value=j}g()},f=k=>({todo:"bg-gray-400","in-progress":"bg-blue-500",review:"bg-yellow-500",done:"bg-green-500"})[k]||"bg-gray-400",N=k=>({todo:"bg-gray-500","in-progress":"bg-blue-600",review:"bg-yellow-600",done:"bg-green-600"})[k]||"bg-gray-500",c=k=>({low:"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200",medium:"bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200",high:"bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200",urgent:"bg-red-200 text-red-900 dark:bg-red-800 dark:text-red-100"})[k]||"bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200",a=k=>({low:"Bassa",medium:"Media",high:"Alta",urgent:"Urgente"})[k]||"Non specificata",S=k=>({todo:0,"in-progress":50,review:75,done:100})[k.status]||0,z=k=>k?new Date(k).toLocaleDateString("it-IT",{day:"2-digit",month:"2-digit"}):"";return Z(()=>C.project,()=>{g()},{immediate:!0}),se(()=>{I()}),P({refresh:g}),(k,j)=>(t(),s("div",ln,[e("div",dn,[e("div",un,[e("div",cn,[j[3]||(j[3]=e("h3",{class:"text-lg font-medium text-gray-900 dark:text-white"}," Diagramma di Gantt ",-1)),e("div",gn,[e("div",mn,[j[2]||(j[2]=e("span",{class:"text-sm text-gray-500 dark:text-gray-400"},"Vista:",-1)),U(e("select",{"onUpdate:modelValue":j[0]||(j[0]=_=>M.value=_),onChange:g,class:"text-sm border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md shadow-sm focus:border-primary-500 focus:ring-primary-500"},j[1]||(j[1]=[e("option",{value:"weeks"},"Settimane",-1),e("option",{value:"months"},"Mesi",-1)]),544),[[J,M.value]])]),e("button",{onClick:I,class:"px-3 py-1 text-sm bg-primary-100 text-primary-700 rounded-md hover:bg-primary-200"}," Oggi ")])])]),!x.loading&&u.value.length>0?(t(),s("div",pn,[e("div",vn,[e("div",xn,[e("div",yn,[j[4]||(j[4]=e("div",{class:"w-80 flex-shrink-0 px-4 py-2 text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider"}," Task ",-1)),e("div",fn,[(t(!0),s(F,null,K(p.value,(_,L)=>(t(),s("div",{key:L,class:H(["flex-1 text-xs text-center text-gray-500 dark:text-gray-400 py-2 border-l border-gray-200 dark:border-gray-600",{"bg-blue-50 dark:bg-blue-900":E(_)}])},o(R(_)),3))),128))])]),e("div",hn,[(t(!0),s(F,null,K(u.value,_=>(t(),s("div",{key:_.id,class:"flex items-center hover:bg-gray-50 dark:hover:bg-gray-700 rounded"},[e("div",bn,[e("div",kn,[e("div",{class:H(["w-3 h-3 rounded-full",f(_.status)])},null,2),e("div",wn,[e("p",_n,o(_.name),1),e("div",$n,[_.assignee?(t(),s("span",jn,o(_.assignee.full_name),1)):D("",!0),e("span",{class:H(["inline-flex items-center px-1.5 py-0.5 rounded-full text-xs font-medium",c(_.priority)])},o(a(_.priority)),3)]),e("div",Cn,o(z(_.start_date))+" - "+o(z(_.due_date)),1)])])]),e("div",Mn,[_.timeline?(t(),s("div",{key:0,class:H(["absolute h-6 rounded-md flex items-center justify-between px-2 text-xs text-white font-medium shadow-sm cursor-pointer",N(_.status)]),style:ee({left:_.timeline.leftPercent+"%",width:_.timeline.widthPercent+"%",minWidth:"60px"}),title:`${_.name} - ${S(_)}% completato`},[e("span",Pn,o(_.name.length>15?_.name.substring(0,15)+"...":_.name),1),e("span",Sn,o(S(_))+"%",1)],14,Tn)):D("",!0),_.timeline&&S(_)>0&&S(_)<100?(t(),s("div",{key:1,class:"absolute h-6 rounded-md bg-green-600 opacity-80",style:ee({left:_.timeline.leftPercent+"%",width:_.timeline.widthPercent*S(_)/100+"%",minWidth:"2px"})},null,4)):D("",!0),(t(!0),s(F,null,K(p.value,(L,h)=>(t(),s("div",{key:h,class:"absolute top-0 bottom-0 border-l border-gray-200 dark:border-gray-600",style:ee({left:h/p.value.length*100+"%"})},null,4))),128)),v.value>=0&&v.value<=100?(t(),s("div",{key:2,class:"absolute top-0 bottom-0 w-0.5 bg-red-500 z-10",style:ee({left:v.value+"%"})},null,4)):D("",!0)])]))),128))])])]),j[5]||(j[5]=ae('<div class="mt-6 flex items-center space-x-6 text-xs"><div class="flex items-center space-x-2"><div class="w-3 h-3 bg-gray-400 rounded"></div><span class="text-gray-600 dark:text-gray-400">Da fare</span></div><div class="flex items-center space-x-2"><div class="w-3 h-3 bg-blue-500 rounded"></div><span class="text-gray-600 dark:text-gray-400">In corso</span></div><div class="flex items-center space-x-2"><div class="w-3 h-3 bg-yellow-500 rounded"></div><span class="text-gray-600 dark:text-gray-400">In revisione</span></div><div class="flex items-center space-x-2"><div class="w-3 h-3 bg-green-500 rounded"></div><span class="text-gray-600 dark:text-gray-400">Completato</span></div><div class="flex items-center space-x-2"><div class="w-0.5 h-4 bg-red-500"></div><span class="text-gray-600 dark:text-gray-400">Oggi</span></div></div>',1))])):x.loading?D("",!0):(t(),s("div",Dn,j[6]||(j[6]=[e("svg",{class:"mx-auto h-12 w-12 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"})],-1),e("h3",{class:"mt-2 text-sm font-medium text-gray-900 dark:text-white"},"Nessun task pianificato",-1),e("p",{class:"mt-1 text-sm text-gray-500 dark:text-gray-400"},"I task con date di inizio e fine appariranno nel diagramma di Gantt.",-1)]))),x.loading?(t(),s("div",Vn,j[7]||(j[7]=[e("div",{class:"animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"},null,-1)]))):D("",!0)])]))}},In={class:"space-y-6"},An={class:"bg-white dark:bg-gray-800 shadow rounded-lg"},Bn={class:"px-6 py-4 border-b border-gray-200 dark:border-gray-700"},En={class:"flex items-center justify-between"},Un={class:"flex items-center space-x-4"},Hn={class:"flex items-center space-x-2"},On={class:"text-sm font-medium text-gray-900 dark:text-white min-w-[80px] text-center"},Rn={class:"flex items-center space-x-2"},Ln=["value"],Fn={key:0,class:"flex justify-center py-8"},Kn={key:1,class:"bg-red-50 border border-red-200 rounded-md p-4 m-6"},qn={class:"text-red-600"},Nn={key:2,class:"p-6"},Xn={class:"overflow-x-auto"},Wn={class:"min-w-full divide-y divide-gray-200 dark:divide-gray-700"},Yn={class:"bg-gray-50 dark:bg-gray-700"},Gn={class:"bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700"},Jn={class:"px-4 py-3 whitespace-nowrap sticky left-0 bg-white dark:bg-gray-800 border-r border-gray-200 dark:border-gray-700"},Qn={class:"text-sm font-medium text-gray-900 dark:text-white"},Zn={class:"text-xs text-gray-500 dark:text-gray-400"},ei=["onClick"],ti={key:0,class:"text-xs font-medium text-primary-600 dark:text-primary-400"},si={key:1,class:"text-gray-300 dark:text-gray-600"},ri={class:"px-3 py-3 text-center bg-gray-50 dark:bg-gray-700"},oi={class:"text-sm font-medium text-gray-900 dark:text-white"},ai={class:"bg-gray-100 dark:bg-gray-600 font-medium"},ni={class:"px-3 py-3 text-center text-sm font-semibold text-gray-900 dark:text-white bg-gray-100 dark:bg-gray-600"},ii={key:0,class:"text-center py-8"},li={class:"mt-3"},di={class:"text-lg font-medium text-gray-900 dark:text-white mb-4"},ui={class:"grid grid-cols-1 gap-4"},ci=["value"],gi={class:"flex justify-end space-x-3 mt-6"},mi=["disabled"],pi={__name:"ProjectTimesheet",props:{project:{type:Object,default:null},loading:{type:Boolean,default:!1}},setup(x,{expose:P}){const C=x,M=ne(),$=V(null),p=V(!1),v=V(""),m=V(!1),u=V(new Date().getFullYear()),g=V(new Date().getMonth()+1),A=V(""),R=V(!1),E=V(!1),I=V(null),f=V({task_id:"",date:"",hours:0,description:""}),N=B(()=>$.value?Array.from({length:$.value.days_in_month},(h,r)=>r+1):[]),c=async()=>{var h;if((h=C.project)!=null&&h.id){p.value=!0,v.value="";try{const r=new URLSearchParams({year:u.value.toString(),month:g.value.toString()});A.value&&r.append("member_id",A.value.toString());const i=await fetch(`/api/timesheets/project/${C.project.id}/monthly?${r}`,{headers:{"Content-Type":"application/json","X-CSRFToken":M.csrfToken}});if(!i.ok)throw new Error("Errore nel caricamento del timesheet");const b=await i.json();$.value=b.data}catch(r){v.value=r.message}finally{p.value=!1}}},a=async()=>{m.value=!0;try{const h={...f.value,project_id:C.project.id};if(!(await fetch("/api/timesheets/",{method:"POST",headers:{"Content-Type":"application/json","X-CSRFToken":M.csrfToken},body:JSON.stringify(h)})).ok)throw new Error("Errore nel salvataggio del timesheet");await c(),z()}catch(h){v.value=h.message}finally{m.value=!1}},S=(h,r)=>{const i=$.value.tasks.find(b=>b.id===h);i&&(I.value={taskId:h,day:r},f.value={task_id:h,date:`${u.value}-${String(g.value).padStart(2,"0")}-${String(r).padStart(2,"0")}`,hours:i.daily_hours[r]||0,description:""},i.daily_hours[r]>0?E.value=!0:R.value=!0)},z=()=>{R.value=!1,E.value=!1,I.value=null,f.value={task_id:"",date:"",hours:0,description:""}},k=()=>{g.value===1?(g.value=12,u.value--):g.value--,c()},j=()=>{g.value===12?(g.value=1,u.value++):g.value++,c()},_=h=>{const r=new Date;return r.getFullYear()===u.value&&r.getMonth()+1===g.value&&r.getDate()===h},L=h=>!h||h===0?"0":h%1===0?h.toString():h.toFixed(2);return Z(()=>{var h;return(h=C.project)==null?void 0:h.id},h=>{h&&c()}),Z(A,()=>{c()}),se(()=>{var h;(h=C.project)!=null&&h.id&&c()}),P({refresh:c}),(h,r)=>{var i,b;return t(),s("div",In,[e("div",An,[e("div",Bn,[e("div",En,[e("div",Un,[r[11]||(r[11]=e("h3",{class:"text-lg font-medium text-gray-900 dark:text-white"}," Timesheet Dettaglio ",-1)),e("div",Hn,[e("button",{onClick:k,class:"p-1 rounded-md text-gray-400 hover:text-gray-600 hover:bg-gray-100 dark:hover:bg-gray-700"},r[7]||(r[7]=[e("svg",{class:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M15 19l-7-7 7-7"})],-1)])),e("span",On,o(g.value)+"/"+o(u.value),1),e("button",{onClick:j,class:"p-1 rounded-md text-gray-400 hover:text-gray-600 hover:bg-gray-100 dark:hover:bg-gray-700"},r[8]||(r[8]=[e("svg",{class:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 5l7 7-7 7"})],-1)]))]),e("div",Rn,[r[10]||(r[10]=e("span",{class:"text-sm text-gray-500 dark:text-gray-400"},"Vista:",-1)),U(e("select",{"onUpdate:modelValue":r[0]||(r[0]=w=>A.value=w),onChange:c,class:"text-sm border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md shadow-sm focus:border-primary-500 focus:ring-primary-500"},[r[9]||(r[9]=e("option",{value:""},"Tutti i membri",-1)),(t(!0),s(F,null,K(((i=x.project)==null?void 0:i.team_members)||[],w=>(t(),s("option",{key:w.id,value:w.id},o(w.first_name)+" "+o(w.last_name),9,Ln))),128))],544),[[J,A.value]])])]),e("button",{onClick:r[1]||(r[1]=w=>R.value=!0),class:"inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"},r[12]||(r[12]=[e("svg",{class:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 6v6m0 0v6m0-6h6m-6 0H6"})],-1),X(" Aggiungi Ore ")]))])]),p.value?(t(),s("div",Fn,r[13]||(r[13]=[e("div",{class:"animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"},null,-1)]))):D("",!0),v.value?(t(),s("div",Kn,[e("p",qn,o(v.value),1)])):D("",!0),!p.value&&$.value?(t(),s("div",Nn,[e("div",Xn,[e("table",Wn,[e("thead",Yn,[e("tr",null,[r[14]||(r[14]=e("th",{class:"px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider sticky left-0 bg-gray-50 dark:bg-gray-700"}," Task ",-1)),(t(!0),s(F,null,K(N.value,w=>(t(),s("th",{key:w,class:H(["px-2 py-3 text-center text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider min-w-[40px]",{"bg-blue-50 dark:bg-blue-900":_(w)}])},o(w),3))),128)),r[15]||(r[15]=e("th",{class:"px-3 py-3 text-center text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider bg-gray-50 dark:bg-gray-700"}," Tot ",-1))])]),e("tbody",Gn,[(t(!0),s(F,null,K($.value.tasks,w=>(t(),s("tr",{key:w.id},[e("td",Jn,[e("div",Qn,o(w.name),1),e("div",Zn,o(w.workers.length?w.workers.join(", "):"Nessuno ha lavorato"),1)]),(t(!0),s(F,null,K(N.value,q=>(t(),s("td",{key:q,class:H(["px-2 py-3 text-center min-w-[40px] cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700",{"bg-blue-50 dark:bg-blue-900":_(q)}]),onClick:T=>S(w.id,q)},[w.daily_hours[q]>0?(t(),s("span",ti,o(L(w.daily_hours[q])),1)):(t(),s("span",si,"-"))],10,ei))),128)),e("td",ri,[e("span",oi,o(L(w.total_hours)),1)])]))),128)),e("tr",ai,[r[16]||(r[16]=e("td",{class:"px-4 py-3 text-sm font-semibold text-gray-900 dark:text-white sticky left-0 bg-gray-100 dark:bg-gray-600"}," TOTALE GIORNALIERO ",-1)),(t(!0),s(F,null,K(N.value,w=>(t(),s("td",{key:w,class:H(["px-2 py-3 text-center text-sm font-semibold text-gray-900 dark:text-white",{"bg-blue-100 dark:bg-blue-800":_(w)}])},o(L($.value.daily_totals[w]||0)),3))),128)),e("td",ni,o(L($.value.grand_total)),1)])])])]),$.value.tasks.length===0?(t(),s("div",ii,r[17]||(r[17]=[e("p",{class:"text-gray-500 dark:text-gray-400"},"Nessun task trovato per questo progetto",-1)]))):D("",!0)])):D("",!0)]),R.value||E.value?(t(),s("div",{key:0,class:"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50",onClick:z},[e("div",{class:"relative top-20 mx-auto p-5 border w-[400px] shadow-lg rounded-md bg-white dark:bg-gray-800",onClick:r[6]||(r[6]=oe(()=>{},["stop"]))},[e("div",li,[e("h3",di,o(E.value?"Modifica Ore":"Aggiungi Ore"),1),e("form",{onSubmit:oe(a,["prevent"])},[e("div",ui,[e("div",null,[r[19]||(r[19]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Task",-1)),U(e("select",{"onUpdate:modelValue":r[2]||(r[2]=w=>f.value.task_id=w),required:"",class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"},[r[18]||(r[18]=e("option",{value:""},"Seleziona task",-1)),(t(!0),s(F,null,K(((b=$.value)==null?void 0:b.tasks)||[],w=>(t(),s("option",{key:w.id,value:w.id},o(w.name),9,ci))),128))],512),[[J,f.value.task_id]])]),e("div",null,[r[20]||(r[20]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Data",-1)),U(e("input",{"onUpdate:modelValue":r[3]||(r[3]=w=>f.value.date=w),type:"date",required:"",class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"},null,512),[[W,f.value.date]])]),e("div",null,[r[21]||(r[21]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Ore",-1)),U(e("input",{"onUpdate:modelValue":r[4]||(r[4]=w=>f.value.hours=w),type:"number",step:"0.25",min:"0",max:"24",required:"",class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"},null,512),[[W,f.value.hours]])]),e("div",null,[r[22]||(r[22]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Descrizione",-1)),U(e("textarea",{"onUpdate:modelValue":r[5]||(r[5]=w=>f.value.description=w),rows:"3",class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"},null,512),[[W,f.value.description]])])]),e("div",gi,[e("button",{type:"button",onClick:z,class:"px-4 py-2 text-sm font-medium text-gray-700 bg-gray-200 hover:bg-gray-300 rounded-md"}," Annulla "),e("button",{type:"submit",disabled:m.value,class:"px-4 py-2 text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 rounded-md disabled:opacity-50"},o(m.value?"Salvataggio...":E.value?"Aggiorna":"Aggiungi"),9,mi)])],32)])])])):D("",!0)])}}},vi={class:"project-view"},xi={class:"tab-content"},yi={__name:"ProjectView",setup(x){const P=xe(),C=ne(),M=be(),$=we(),p=V(!0),v=V("overview"),m=B(()=>P.currentProject),u=B(()=>[{id:"overview",label:"Panoramica",icon:"chart-bar"},{id:"tasks",label:"Task",icon:"clipboard-list"},{id:"team",label:"Team",icon:"users"},{id:"gantt",label:"Gantt",icon:"calendar"},{id:"timesheet",label:"Timesheet",icon:"clock"},{id:"expenses",label:"Spese",icon:"credit-card"},{id:"kpi",label:"KPI & Analytics",icon:"trending-up"}].filter(f=>!!(["overview","tasks","gantt","team","timesheet"].includes(f.id)||f.id==="kpi"&&C.hasPermission("view_reports")||f.id==="expenses"&&C.hasPermission("manage_expenses")))),g=B(()=>({overview:me,tasks:Ws,team:Fr,expenses:Io,kpi:nn,gantt:zn,timesheet:pi})[v.value]||me),A=async()=>{p.value=!0;try{const I=M.params.id;await P.fetchProject(I)}catch(I){console.error("Error loading project:",I)}finally{p.value=!1}},R=()=>{$.push(`/projects/${M.params.id}/edit`)},E=async()=>{if(confirm("Sei sicuro di voler eliminare questo progetto?"))try{await P.deleteProject(M.params.id),$.push("/projects")}catch(I){console.error("Error deleting project:",I)}};return Z(()=>M.params.id,(I,f)=>{I&&I!==f&&A()}),Z(()=>M.hash,I=>{if(I){const f=I.replace("#","");u.value.find(N=>N.id===f)&&v.value!==f&&(v.value=f)}},{immediate:!0}),Z(v,I=>{const f=`#${I}`;M.hash!==f&&$.replace({...M,hash:f})}),se(()=>{if(M.hash){const I=M.hash.replace("#","");u.value.find(f=>f.id===I)&&(v.value=I)}A()}),(I,f)=>(t(),s("div",vi,[re(Ue,{project:m.value,loading:p.value,onEdit:R,onDelete:E},null,8,["project","loading"]),re(qe,{modelValue:v.value,"onUpdate:modelValue":f[0]||(f[0]=N=>v.value=N),tabs:u.value,class:"mb-6"},null,8,["modelValue","tabs"]),e("div",xi,[(t(),de(ke,null,[(t(),de(ve(g.value),{project:m.value,loading:p.value},null,8,["project","loading"]))],1024))])]))}},bi=ue(yi,[["__scopeId","data-v-3aa24582"]]);export{bi as default};
