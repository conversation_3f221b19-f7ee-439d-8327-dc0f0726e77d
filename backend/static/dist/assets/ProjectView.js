import{c as s,o as t,j as e,t as o,n as O,g as D,l as X,F,k as K,h as de,D as ve,f as B,z as ee,r as V,w as Z,A as se,v as U,H as J,I as ae,x as W,s as re,N as ye,a as oe,p as le,u as fe,O as he,m as be}from"./vendor.js";import{_ as ue,u as ne,a as we,b as xe}from"./app.js";const ke={class:"project-header bg-white shadow-sm rounded-lg p-6 mb-6"},_e={key:0,class:"animate-pulse"},$e={key:1,class:"flex flex-col sm:flex-row sm:items-center sm:justify-between"},je={class:"flex-1"},Ce={class:"flex items-center space-x-3 mb-2"},Me={class:"text-2xl font-bold text-gray-900"},Te={class:"flex flex-wrap items-center gap-4 text-sm text-gray-500"},Pe={key:0},Se={key:1},De={key:2},Ve={key:3},ze={class:"mt-4 sm:mt-0 flex space-x-3"},Ie={key:2,class:"text-center py-8"},Ae={__name:"ProjectHeader",props:{project:{type:Object,default:null},loading:{type:Boolean,default:!1}},emits:["edit","delete"],setup(x){const P=m=>({planning:"bg-yellow-100 text-yellow-800",active:"bg-green-100 text-green-800",on_hold:"bg-orange-100 text-orange-800",completed:"bg-blue-100 text-blue-800",cancelled:"bg-red-100 text-red-800"})[m]||"bg-gray-100 text-gray-800",C=m=>({planning:"Pianificazione",active:"Attivo",on_hold:"In Pausa",completed:"Completato",cancelled:"Annullato"})[m]||m,M=m=>m?new Date(m).toLocaleDateString("it-IT"):"",$=m=>m?new Intl.NumberFormat("it-IT",{style:"currency",currency:"EUR"}).format(m):"";return(m,v)=>(t(),s("div",ke,[x.loading?(t(),s("div",_e,v[2]||(v[2]=[e("div",{class:"h-8 bg-gray-200 rounded w-1/3 mb-2"},null,-1),e("div",{class:"h-4 bg-gray-200 rounded w-1/2"},null,-1)]))):x.project?(t(),s("div",$e,[e("div",je,[e("div",Ce,[e("h1",Me,o(x.project.name),1),e("span",{class:O(["inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium",P(x.project.status)])},o(C(x.project.status)),3)]),e("div",Te,[x.project.client?(t(),s("span",Pe,[v[3]||(v[3]=e("svg",{class:"w-4 h-4 inline mr-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-4m-5 0H3m2 0h3M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"})],-1)),X(" Cliente: "+o(x.project.client.name),1)])):D("",!0),x.project.start_date?(t(),s("span",Se,[v[4]||(v[4]=e("svg",{class:"w-4 h-4 inline mr-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"})],-1)),X(" Inizio: "+o(M(x.project.start_date)),1)])):D("",!0),x.project.end_date?(t(),s("span",De,[v[5]||(v[5]=e("svg",{class:"w-4 h-4 inline mr-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"})],-1)),X(" Fine: "+o(M(x.project.end_date)),1)])):D("",!0),x.project.budget?(t(),s("span",Ve,[v[6]||(v[6]=e("svg",{class:"w-4 h-4 inline mr-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"})],-1)),X(" Budget: "+o($(x.project.budget)),1)])):D("",!0)])]),e("div",ze,[e("button",{onClick:v[0]||(v[0]=p=>m.$emit("edit")),class:"inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"},v[7]||(v[7]=[e("svg",{class:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"})],-1),X(" Modifica ")])),e("button",{onClick:v[1]||(v[1]=p=>m.$emit("delete")),class:"inline-flex items-center px-3 py-2 border border-red-300 shadow-sm text-sm leading-4 font-medium rounded-md text-red-700 bg-white hover:bg-red-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"},v[8]||(v[8]=[e("svg",{class:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"})],-1),X(" Elimina ")]))])])):(t(),s("div",Ie,v[9]||(v[9]=[e("p",{class:"text-gray-500"},"Progetto non trovato",-1)])))]))}},Be=ue(Ae,[["__scopeId","data-v-24fa98b0"]]),Ee={class:"tab-navigation"},Ue={class:"border-b border-gray-200"},He={class:"-mb-px flex space-x-8","aria-label":"Tabs"},Oe=["onClick","aria-current"],Re={key:1,class:"ml-2 inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-600"},Le={__name:"TabNavigation",props:{modelValue:{type:String,required:!0},tabs:{type:Array,required:!0,validator:x=>x.every(P=>typeof P=="object"&&P.id&&P.label)}},emits:["update:modelValue"],setup(x,{emit:P}){const C=x,M=P,$=p=>C.modelValue===p,m=p=>{M("update:modelValue",p)},v=p=>{const u={"chart-bar":{template:`<svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
      </svg>`},"clipboard-list":{template:`<svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01" />
      </svg>`},users:{template:`<svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a4 4 0 11-8 0 4 4 0 018 0z" />
      </svg>`},folder:{template:`<svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-6l-2-2H5a2 2 0 00-2 2z" />
      </svg>`},"trending-up":{template:`<svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
      </svg>`},calendar:{template:`<svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
      </svg>`},clock:{template:`<svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
      </svg>`}};return u[p]||u["chart-bar"]};return(p,u)=>(t(),s("div",Ee,[e("div",Ue,[e("nav",He,[(t(!0),s(F,null,K(x.tabs,c=>(t(),s("button",{key:c.id,onClick:I=>m(c.id),class:O(["whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm flex items-center space-x-2",$(c.id)?"border-primary-500 text-primary-600":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"]),"aria-current":$(c.id)?"page":void 0},[c.icon?(t(),de(ve(v(c.icon)),{key:0,class:"w-4 h-4"})):D("",!0),e("span",null,o(c.label),1),c.count!==void 0?(t(),s("span",Re,o(c.count),1)):D("",!0)],10,Oe))),128))])])]))}},Fe=ue(Le,[["__scopeId","data-v-c205976e"]]),Ke={class:"project-overview"},qe={key:0,class:"animate-pulse space-y-4"},Ne={key:1,class:"space-y-6"},Xe={class:"bg-white shadow rounded-lg p-6"},We={key:0,class:"text-gray-600"},Ye={key:1,class:"text-gray-400 italic"},Ge={class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4"},Je={class:"bg-white shadow rounded-lg p-6"},Qe={class:"flex items-center"},Ze={class:"ml-5 w-0 flex-1"},et={class:"text-lg font-medium text-gray-900"},tt={class:"bg-white shadow rounded-lg p-6"},st={class:"flex items-center"},rt={class:"ml-5 w-0 flex-1"},ot={class:"text-lg font-medium text-gray-900"},at={class:"bg-white shadow rounded-lg p-6"},nt={class:"flex items-center"},it={class:"ml-5 w-0 flex-1"},lt={class:"text-lg font-medium text-gray-900"},dt={class:"bg-white shadow rounded-lg p-6"},ut={class:"flex items-center"},ct={class:"ml-5 w-0 flex-1"},gt={class:"text-lg font-medium text-gray-900"},mt={class:"bg-white shadow rounded-lg p-6"},pt={class:"w-full bg-gray-200 rounded-full h-2.5"},vt={class:"text-sm text-gray-500 mt-2"},xt={class:"bg-white shadow rounded-lg p-6"},yt={class:"space-y-4"},ft={class:"flex justify-between items-center"},ht={class:"text-sm font-medium"},bt={class:"flex justify-between items-center"},wt={class:"text-sm font-medium"},kt={class:"w-full bg-gray-200 rounded-full h-3"},_t={class:"flex justify-between items-center text-sm"},$t={class:"bg-white shadow rounded-lg p-6"},jt={class:"space-y-3"},Ct={class:"flex-shrink-0"},Mt=["src","alt"],Tt={key:1,class:"w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center"},Pt={class:"text-xs font-medium text-gray-600"},St={class:"flex-1"},Dt={class:"text-sm font-medium text-gray-900"},Vt={class:"text-xs text-gray-500"},zt={class:"text-right"},It={class:"text-xs text-gray-500"},At={key:0,class:"text-center py-4"},Bt={class:"bg-white shadow rounded-lg p-6"},Et={class:"space-y-3"},Ut={class:"flex-shrink-0"},Ht={class:"flex-1"},Ot={class:"text-sm text-gray-900"},Rt={class:"flex items-center space-x-2 mt-1"},Lt={class:"text-xs text-gray-500"},Ft={class:"text-xs text-gray-500"},Kt={key:0,class:"text-center py-4"},qt={key:2,class:"text-center py-8"},Nt={__name:"ProjectOverview",props:{project:{type:Object,default:null},loading:{type:Boolean,default:!1}},setup(x){const P=x,C=B(()=>{if(!P.project||!P.project.task_count)return 0;const g=P.project.completed_tasks||0,a=P.project.task_count||1;return Math.round(g/a*100)}),M=B(()=>{var g;return((g=P.project)==null?void 0:g.team_members)||[]}),$=B(()=>{var S,A,w;if((S=P.project)!=null&&S.expenses)return P.project.expenses;const g=((A=P.project)==null?void 0:A.total_hours)||0,a=(w=P.project)!=null&&w.client_daily_rate?P.project.client_daily_rate/8:50;return g*a}),m=B(()=>{var a;return(((a=P.project)==null?void 0:a.budget)||0)-$.value}),v=B(()=>{var a;const g=((a=P.project)==null?void 0:a.budget)||1;return Math.min(Math.round($.value/g*100),100)}),p=B(()=>{const g=v.value;return g>=90?"bg-red-600":g>=75?"bg-yellow-600":"bg-green-600"}),u=B(()=>{var a;const g=m.value;return g<0?"text-red-600":g<(((a=P.project)==null?void 0:a.budget)||0)*.1?"text-yellow-600":"text-green-600"}),c=B(()=>{var g;return(g=P.project)!=null&&g.tasks?[...P.project.tasks].sort((a,S)=>new Date(S.updated_at)-new Date(a.updated_at)).slice(0,5).map(a=>{var S;return{id:a.id,description:`Task "${a.name}" ${I(a.status)}`,created_at:a.updated_at,user_name:((S=a.assignee)==null?void 0:S.full_name)||"Non assegnato",type:R(a.status)}}):[]}),I=g=>({todo:"creato","in-progress":"in corso",review:"in revisione",done:"completato"})[g]||g,R=g=>({todo:"task_created","in-progress":"task_updated",review:"task_updated",done:"task_completed"})[g]||"task_updated",E=g=>g?new Intl.NumberFormat("it-IT",{style:"currency",currency:"EUR"}).format(g):"Non specificato",z=g=>g?new Date(g).toLocaleDateString("it-IT",{day:"numeric",month:"short",hour:"2-digit",minute:"2-digit"}):"",f=g=>g?g.split(" ").map(a=>a.charAt(0).toUpperCase()).slice(0,2).join(""):"??",N=g=>{const a={task_created:"bg-blue-600",task_completed:"bg-green-600",task_updated:"bg-yellow-600",comment_added:"bg-purple-600",file_uploaded:"bg-indigo-600",member_added:"bg-pink-600",default:"bg-gray-600"};return a[g]||a.default};return(g,a)=>(t(),s("div",Ke,[x.loading?(t(),s("div",qe,a[0]||(a[0]=[e("div",{class:"h-4 bg-gray-200 rounded w-3/4"},null,-1),e("div",{class:"h-4 bg-gray-200 rounded w-1/2"},null,-1),e("div",{class:"h-32 bg-gray-200 rounded"},null,-1)]))):x.project?(t(),s("div",Ne,[e("div",Xe,[a[1]||(a[1]=e("h3",{class:"text-lg font-medium text-gray-900 mb-4"},"Descrizione Progetto",-1)),x.project.description?(t(),s("p",We,o(x.project.description),1)):(t(),s("p",Ye,"Nessuna descrizione disponibile"))]),e("div",Ge,[e("div",Je,[e("div",Qe,[a[3]||(a[3]=e("div",{class:"flex-shrink-0"},[e("svg",{class:"h-8 w-8 text-blue-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01"})])],-1)),e("div",Ze,[e("dl",null,[a[2]||(a[2]=e("dt",{class:"text-sm font-medium text-gray-500 truncate"},"Task Totali",-1)),e("dd",et,o(x.project.task_count||0),1)])])])]),e("div",tt,[e("div",st,[a[5]||(a[5]=e("div",{class:"flex-shrink-0"},[e("svg",{class:"h-8 w-8 text-green-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"})])],-1)),e("div",rt,[e("dl",null,[a[4]||(a[4]=e("dt",{class:"text-sm font-medium text-gray-500 truncate"},"Task Completati",-1)),e("dd",ot,o(x.project.completed_tasks||0),1)])])])]),e("div",at,[e("div",nt,[a[7]||(a[7]=e("div",{class:"flex-shrink-0"},[e("svg",{class:"h-8 w-8 text-purple-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"})])],-1)),e("div",it,[e("dl",null,[a[6]||(a[6]=e("dt",{class:"text-sm font-medium text-gray-500 truncate"},"Membri Team",-1)),e("dd",lt,o(x.project.team_count||0),1)])])])]),e("div",dt,[e("div",ut,[a[9]||(a[9]=e("div",{class:"flex-shrink-0"},[e("svg",{class:"h-8 w-8 text-yellow-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"})])],-1)),e("div",ct,[e("dl",null,[a[8]||(a[8]=e("dt",{class:"text-sm font-medium text-gray-500 truncate"},"Budget",-1)),e("dd",gt,o(E(x.project.budget)),1)])])])])]),e("div",mt,[a[10]||(a[10]=e("h3",{class:"text-lg font-medium text-gray-900 mb-4"},"Progresso Progetto",-1)),e("div",pt,[e("div",{class:"bg-blue-600 h-2.5 rounded-full transition-all duration-300",style:ee({width:`${C.value}%`})},null,4)]),e("p",vt,o(C.value)+"% completato",1)]),e("div",xt,[a[15]||(a[15]=e("h3",{class:"text-lg font-medium text-gray-900 mb-4"},"Budget vs Spese",-1)),e("div",yt,[e("div",ft,[a[11]||(a[11]=e("span",{class:"text-sm text-gray-600"},"Budget Totale",-1)),e("span",ht,o(E(x.project.budget)),1)]),a[14]||(a[14]=e("div",{class:"w-full bg-gray-200 rounded-full h-3"},[e("div",{class:"bg-blue-600 h-3 rounded-full transition-all duration-300",style:{width:"100%"}})],-1)),e("div",bt,[a[12]||(a[12]=e("span",{class:"text-sm text-gray-600"},"Spese Sostenute",-1)),e("span",wt,o(E($.value)),1)]),e("div",kt,[e("div",{class:O(["h-3 rounded-full transition-all duration-300",p.value]),style:ee({width:v.value+"%"})},null,6)]),e("div",_t,[a[13]||(a[13]=e("span",{class:"text-gray-600"},"Rimanente",-1)),e("span",{class:O(["font-medium",u.value])},o(E(m.value)),3)])])]),e("div",$t,[a[17]||(a[17]=e("div",{class:"flex items-center justify-between mb-4"},[e("h3",{class:"text-lg font-medium text-gray-900"},"Team Members"),e("button",{class:"text-sm text-blue-600 hover:text-blue-800"},"Visualizza tutti")],-1)),e("div",jt,[(t(!0),s(F,null,K(M.value,S=>(t(),s("div",{key:S.id,class:"flex items-center space-x-3"},[e("div",Ct,[S.profile_image?(t(),s("img",{key:0,src:S.profile_image,alt:S.full_name,class:"w-8 h-8 rounded-full"},null,8,Mt)):(t(),s("div",Tt,[e("span",Pt,o(f(S.full_name)),1)]))]),e("div",St,[e("p",Dt,o(S.full_name),1),e("p",Vt,o(S.role||"Team Member"),1)]),e("div",zt,[e("p",It,o(S.hours_worked||0)+"h",1)])]))),128)),M.value.length===0?(t(),s("div",At,a[16]||(a[16]=[e("p",{class:"text-gray-500"},"Nessun membro del team assegnato",-1)]))):D("",!0)])]),e("div",Bt,[a[20]||(a[20]=e("div",{class:"flex items-center justify-between mb-4"},[e("h3",{class:"text-lg font-medium text-gray-900"},"Attività Recenti"),e("button",{class:"text-sm text-blue-600 hover:text-blue-800"},"Visualizza tutte")],-1)),e("div",Et,[(t(!0),s(F,null,K(c.value,S=>(t(),s("div",{key:S.id,class:"flex items-start space-x-3"},[e("div",Ut,[e("div",{class:O(["w-2 h-2 rounded-full mt-2",N(S.type)])},null,2)]),e("div",Ht,[e("p",Ot,o(S.description),1),e("div",Rt,[e("p",Lt,o(z(S.created_at)),1),a[18]||(a[18]=e("span",{class:"text-xs text-gray-400"},"•",-1)),e("p",Ft,o(S.user_name),1)])])]))),128)),c.value.length===0?(t(),s("div",Kt,a[19]||(a[19]=[e("p",{class:"text-gray-500"},"Nessuna attività recente",-1)]))):D("",!0)])])])):(t(),s("div",qt,a[21]||(a[21]=[e("p",{class:"text-gray-500"},"Progetto non trovato",-1)])))]))}},me=ue(Nt,[["__scopeId","data-v-16274846"]]),Xt={class:"space-y-6"},Wt={class:"bg-white dark:bg-gray-800 shadow rounded-lg"},Yt={class:"px-6 py-4 border-b border-gray-200 dark:border-gray-700"},Gt={class:"flex items-center justify-between"},Jt={class:"mt-4 grid grid-cols-1 md:grid-cols-4 gap-4"},Qt=["value"],Zt={class:"mt-4 flex items-center justify-between"},es={class:"flex items-center space-x-4"},ts={class:"text-sm text-gray-500 dark:text-gray-400"},ss={key:0,class:"flex justify-center py-8"},rs={key:1,class:"bg-red-50 border border-red-200 rounded-md p-4"},os={class:"text-red-600"},as={key:2,class:"bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden"},ns={class:"min-w-full divide-y divide-gray-200 dark:divide-gray-700"},is={class:"col-span-4"},ls={class:"text-sm font-medium text-gray-900 dark:text-white"},ds={key:0,class:"text-sm text-gray-500 dark:text-gray-400 truncate"},us={class:"col-span-2"},cs={key:0,class:"flex items-center"},gs={class:"h-8 w-8 rounded-full bg-gray-300 flex items-center justify-center text-sm font-medium text-gray-700"},ms={class:"ml-2"},ps={class:"text-sm font-medium text-gray-900 dark:text-white"},vs={key:1,class:"text-sm text-gray-500 dark:text-gray-400"},xs={class:"col-span-1"},ys={class:"col-span-1"},fs={class:"col-span-2"},hs={key:0,class:"text-sm text-gray-900 dark:text-white"},bs={key:1,class:"text-sm text-gray-500 dark:text-gray-400"},ws={class:"col-span-1"},ks={class:"text-sm text-gray-900 dark:text-white"},_s={key:0,class:"text-gray-500"},$s={class:"col-span-1"},js={class:"flex items-center space-x-2"},Cs=["onClick"],Ms={key:0,class:"px-6 py-12 text-center"},Ts={key:3,class:"grid grid-cols-1 md:grid-cols-4 gap-6"},Ps={class:"flex items-center justify-between mb-4"},Ss={class:"font-medium text-gray-900 dark:text-white"},Ds={class:"bg-gray-200 dark:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-full px-2 py-1 text-xs"},Vs={class:"space-y-3"},zs=["onClick"],Is={class:"font-medium text-sm text-gray-900 dark:text-white mb-1"},As={key:0,class:"text-xs text-gray-500 dark:text-gray-400 mb-2 line-clamp-2"},Bs={class:"flex items-center justify-between"},Es={key:0,class:"h-6 w-6 rounded-full bg-gray-300 flex items-center justify-center text-xs font-medium text-gray-700"},Us={class:"mt-3"},Hs={class:"text-lg font-medium text-gray-900 dark:text-white mb-4"},Os={class:"grid grid-cols-1 gap-4"},Rs={class:"grid grid-cols-2 gap-4"},Ls=["value"],Fs={class:"grid grid-cols-2 gap-4"},Ks={class:"flex justify-end space-x-3 mt-6"},qs=["disabled"],Ns={__name:"ProjectTasks",props:{project:{type:Object,default:null},loading:{type:Boolean,default:!1}},setup(x,{expose:P}){const C=x,M=ne(),{hasPermission:$}=we(),m=V([]),v=V(!1),p=V(""),u=V("list"),c=V(!1),I=V({status:"",priority:"",assignee_id:"",search:""}),R=V(!1),E=V(!1),z=V(null),f=V({name:"",description:"",status:"todo",priority:"medium",assignee_id:"",due_date:"",estimated_hours:null}),N=B(()=>$.value("manage_project_tasks")),g=[{value:"todo",label:"Da fare"},{value:"in-progress",label:"In corso"},{value:"review",label:"In revisione"},{value:"done",label:"Completato"}],a=async()=>{var T,l;if((T=C.project)!=null&&T.id){v.value=!0,p.value="";try{const Q=new URLSearchParams({project_id:C.project.id,...I.value}),te=await fetch(`/api/tasks?${Q}`,{headers:{"Content-Type":"application/json","X-CSRFToken":M.csrfToken}});if(!te.ok)throw new Error("Errore nel caricamento dei task");const y=await te.json();m.value=((l=y.data)==null?void 0:l.tasks)||y.tasks||[]}catch(Q){p.value=Q.message}finally{v.value=!1}}},S=async()=>{c.value=!0;try{const T=E.value?`/api/tasks/${z.value.id}`:"/api/tasks",l=E.value?"PUT":"POST",Q={...f.value,project_id:C.project.id};if(!(await fetch(T,{method:l,headers:{"Content-Type":"application/json","X-CSRFToken":M.csrfToken},body:JSON.stringify(Q)})).ok)throw new Error("Errore nel salvataggio del task");await a(),w()}catch(T){p.value=T.message}finally{c.value=!1}},A=T=>{z.value=T,f.value={name:T.name,description:T.description||"",status:T.status,priority:T.priority,assignee_id:T.assignee_id||"",due_date:T.due_date?T.due_date.split("T")[0]:"",estimated_hours:T.estimated_hours},E.value=!0},w=()=>{R.value=!1,E.value=!1,z.value=null,f.value={name:"",description:"",status:"todo",priority:"medium",assignee_id:"",due_date:"",estimated_hours:null}},j=T=>m.value.filter(l=>l.status===T),_=T=>({todo:"bg-gray-100 text-gray-800","in-progress":"bg-blue-100 text-blue-800",review:"bg-yellow-100 text-yellow-800",done:"bg-green-100 text-green-800"})[T]||"bg-gray-100 text-gray-800",L=T=>({todo:"Da fare","in-progress":"In corso",review:"In revisione",done:"Completato"})[T]||T,h=T=>({low:"bg-green-100 text-green-800",medium:"bg-yellow-100 text-yellow-800",high:"bg-orange-100 text-orange-800",urgent:"bg-red-100 text-red-800"})[T]||"bg-gray-100 text-gray-800",r=T=>({low:"Bassa",medium:"Media",high:"Alta",urgent:"Urgente"})[T]||T,i=(T,l)=>`${(T==null?void 0:T.charAt(0))||""}${(l==null?void 0:l.charAt(0))||""}`.toUpperCase(),b=T=>new Date(T).toLocaleDateString("it-IT");let k;const q=()=>{clearTimeout(k),k=setTimeout(()=>{a()},300)};return Z(()=>{var T;return(T=C.project)==null?void 0:T.id},T=>{T&&a()}),se(()=>{var T;(T=C.project)!=null&&T.id&&a()}),P({refresh:a}),(T,l)=>{var Q,te;return t(),s("div",Xt,[e("div",Wt,[e("div",Yt,[e("div",Gt,[l[16]||(l[16]=e("h3",{class:"text-lg font-medium text-gray-900 dark:text-white"},"Task del Progetto",-1)),N.value?(t(),s("button",{key:0,onClick:l[0]||(l[0]=y=>R.value=!0),class:"inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"},l[15]||(l[15]=[e("svg",{class:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 6v6m0 0v6m0-6h6m-6 0H6"})],-1),X(" Nuovo Task ")]))):D("",!0)]),e("div",Jt,[e("div",null,[l[18]||(l[18]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Stato",-1)),U(e("select",{"onUpdate:modelValue":l[1]||(l[1]=y=>I.value.status=y),onChange:a,class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"},l[17]||(l[17]=[ae('<option value="">Tutti gli stati</option><option value="todo">Da fare</option><option value="in-progress">In corso</option><option value="review">In revisione</option><option value="done">Completato</option>',5)]),544),[[J,I.value.status]])]),e("div",null,[l[20]||(l[20]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Priorità",-1)),U(e("select",{"onUpdate:modelValue":l[2]||(l[2]=y=>I.value.priority=y),onChange:a,class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"},l[19]||(l[19]=[ae('<option value="">Tutte le priorità</option><option value="low">Bassa</option><option value="medium">Media</option><option value="high">Alta</option><option value="urgent">Urgente</option>',5)]),544),[[J,I.value.priority]])]),e("div",null,[l[22]||(l[22]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Assegnatario",-1)),U(e("select",{"onUpdate:modelValue":l[3]||(l[3]=y=>I.value.assignee_id=y),onChange:a,class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"},[l[21]||(l[21]=e("option",{value:""},"Tutti",-1)),(t(!0),s(F,null,K(((Q=x.project)==null?void 0:Q.team_members)||[],y=>(t(),s("option",{key:y.id,value:y.id},o(y.first_name)+" "+o(y.last_name),9,Qt))),128))],544),[[J,I.value.assignee_id]])]),e("div",null,[l[23]||(l[23]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Ricerca",-1)),U(e("input",{"onUpdate:modelValue":l[4]||(l[4]=y=>I.value.search=y),onInput:q,type:"text",placeholder:"Cerca task...",class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"},null,544),[[W,I.value.search]])])]),e("div",Zt,[e("div",es,[l[24]||(l[24]=e("span",{class:"text-sm text-gray-500 dark:text-gray-400"},"Vista:",-1)),e("button",{onClick:l[5]||(l[5]=y=>u.value="list"),class:O([u.value==="list"?"bg-primary-100 text-primary-700":"text-gray-500 hover:text-gray-700","px-3 py-1 rounded-md text-sm font-medium"])}," Lista ",2),e("button",{onClick:l[6]||(l[6]=y=>u.value="kanban"),class:O([u.value==="kanban"?"bg-primary-100 text-primary-700":"text-gray-500 hover:text-gray-700","px-3 py-1 rounded-md text-sm font-medium"])}," Kanban ",2)]),e("div",ts,o(m.value.length)+" task trovati ",1)])])]),v.value?(t(),s("div",ss,l[25]||(l[25]=[e("div",{class:"animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"},null,-1)]))):D("",!0),p.value?(t(),s("div",rs,[e("p",os,o(p.value),1)])):D("",!0),!v.value&&u.value==="list"?(t(),s("div",as,[e("div",ns,[l[27]||(l[27]=ae('<div class="bg-gray-50 dark:bg-gray-700 px-6 py-3 grid grid-cols-12 gap-4 text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"><div class="col-span-4">Task</div><div class="col-span-2">Assegnatario</div><div class="col-span-1">Stato</div><div class="col-span-1">Priorità</div><div class="col-span-2">Scadenza</div><div class="col-span-1">Ore</div><div class="col-span-1">Azioni</div></div>',1)),(t(!0),s(F,null,K(m.value,y=>(t(),s("div",{key:y.id,class:"px-6 py-4 grid grid-cols-12 gap-4 items-center hover:bg-gray-50 dark:hover:bg-gray-700"},[e("div",is,[e("div",ls,o(y.name),1),y.description?(t(),s("div",ds,o(y.description),1)):D("",!0)]),e("div",us,[y.assignee?(t(),s("div",cs,[e("div",gs,o(i(y.assignee.first_name,y.assignee.last_name)),1),e("div",ms,[e("div",ps,o(y.assignee.first_name)+" "+o(y.assignee.last_name),1)])])):(t(),s("span",vs,"Non assegnato"))]),e("div",xs,[e("span",{class:O([_(y.status),"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"])},o(L(y.status)),3)]),e("div",ys,[e("span",{class:O([h(y.priority),"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"])},o(r(y.priority)),3)]),e("div",fs,[y.due_date?(t(),s("div",hs,o(b(y.due_date)),1)):(t(),s("span",bs,"-"))]),e("div",ws,[e("div",ks,[X(o(y.actual_hours||0)+"h ",1),y.estimated_hours?(t(),s("span",_s,"/ "+o(y.estimated_hours)+"h",1)):D("",!0)])]),e("div",$s,[e("div",js,[e("button",{onClick:n=>A(y),class:"text-primary-600 hover:text-primary-900 text-sm"}," Modifica ",8,Cs)])])]))),128)),m.value.length===0?(t(),s("div",Ms,l[26]||(l[26]=[e("p",{class:"text-gray-500 dark:text-gray-400"},"Nessun task trovato",-1)]))):D("",!0)])])):D("",!0),!v.value&&u.value==="kanban"?(t(),s("div",Ts,[(t(),s(F,null,K(g,y=>e("div",{key:y.value,class:"bg-gray-50 dark:bg-gray-700 rounded-lg p-4"},[e("div",Ps,[e("h4",Ss,o(y.label),1),e("span",Ds,o(j(y.value).length),1)]),e("div",Vs,[(t(!0),s(F,null,K(j(y.value),n=>(t(),s("div",{key:n.id,class:"bg-white dark:bg-gray-800 p-3 rounded-lg shadow-sm border border-gray-200 dark:border-gray-600 cursor-pointer hover:shadow-md transition-shadow",onClick:d=>A(n)},[e("div",Is,o(n.name),1),n.description?(t(),s("div",As,o(n.description),1)):D("",!0),e("div",Bs,[e("span",{class:O([h(n.priority),"inline-flex items-center px-2 py-0.5 rounded text-xs font-medium"])},o(r(n.priority)),3),n.assignee?(t(),s("div",Es,o(i(n.assignee.first_name,n.assignee.last_name)),1)):D("",!0)])],8,zs))),128))])])),64))])):D("",!0),R.value||E.value?(t(),s("div",{key:4,class:"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50",onClick:w},[e("div",{class:"relative top-20 mx-auto p-5 border w-[500px] shadow-lg rounded-md bg-white dark:bg-gray-800",onClick:l[14]||(l[14]=re(()=>{},["stop"]))},[e("div",Us,[e("h3",Hs,o(E.value?"Modifica Task":"Nuovo Task"),1),e("form",{onSubmit:re(S,["prevent"])},[e("div",Os,[e("div",null,[l[28]||(l[28]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Nome",-1)),U(e("input",{"onUpdate:modelValue":l[7]||(l[7]=y=>f.value.name=y),type:"text",required:"",class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"},null,512),[[W,f.value.name]])]),e("div",null,[l[29]||(l[29]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Descrizione",-1)),U(e("textarea",{"onUpdate:modelValue":l[8]||(l[8]=y=>f.value.description=y),rows:"3",class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"},null,512),[[W,f.value.description]])]),e("div",Rs,[e("div",null,[l[31]||(l[31]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Stato",-1)),U(e("select",{"onUpdate:modelValue":l[9]||(l[9]=y=>f.value.status=y),class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"},l[30]||(l[30]=[e("option",{value:"todo"},"Da fare",-1),e("option",{value:"in-progress"},"In corso",-1),e("option",{value:"review"},"In revisione",-1),e("option",{value:"done"},"Completato",-1)]),512),[[J,f.value.status]])]),e("div",null,[l[33]||(l[33]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Priorità",-1)),U(e("select",{"onUpdate:modelValue":l[10]||(l[10]=y=>f.value.priority=y),class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"},l[32]||(l[32]=[e("option",{value:"low"},"Bassa",-1),e("option",{value:"medium"},"Media",-1),e("option",{value:"high"},"Alta",-1),e("option",{value:"urgent"},"Urgente",-1)]),512),[[J,f.value.priority]])])]),e("div",null,[l[35]||(l[35]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Assegnatario",-1)),U(e("select",{"onUpdate:modelValue":l[11]||(l[11]=y=>f.value.assignee_id=y),class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"},[l[34]||(l[34]=e("option",{value:""},"Non assegnato",-1)),(t(!0),s(F,null,K(((te=x.project)==null?void 0:te.team_members)||[],y=>(t(),s("option",{key:y.id,value:y.id},o(y.first_name)+" "+o(y.last_name),9,Ls))),128))],512),[[J,f.value.assignee_id]])]),e("div",Fs,[e("div",null,[l[36]||(l[36]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Scadenza",-1)),U(e("input",{"onUpdate:modelValue":l[12]||(l[12]=y=>f.value.due_date=y),type:"date",class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"},null,512),[[W,f.value.due_date]])]),e("div",null,[l[37]||(l[37]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Ore stimate",-1)),U(e("input",{"onUpdate:modelValue":l[13]||(l[13]=y=>f.value.estimated_hours=y),type:"number",step:"0.5",min:"0",class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"},null,512),[[W,f.value.estimated_hours]])])])]),e("div",Ks,[e("button",{type:"button",onClick:w,class:"px-4 py-2 text-sm font-medium text-gray-700 bg-gray-200 hover:bg-gray-300 rounded-md"}," Annulla "),e("button",{type:"submit",disabled:c.value,class:"px-4 py-2 text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 rounded-md disabled:opacity-50"},o(c.value?"Salvataggio...":E.value?"Aggiorna":"Crea"),9,qs)])],32)])])])):D("",!0)])}}},Xs={class:"space-y-6"},Ws={class:"bg-white dark:bg-gray-800 shadow rounded-lg"},Ys={class:"px-6 py-4 border-b border-gray-200 dark:border-gray-700"},Gs={class:"flex items-center justify-between"},Js={class:"p-6 border-b border-gray-200 dark:border-gray-700"},Qs={class:"grid grid-cols-1 md:grid-cols-4 gap-4"},Zs={class:"text-center"},er={class:"text-2xl font-bold text-primary-600 dark:text-primary-400"},tr={class:"text-center"},sr={class:"text-2xl font-bold text-green-600"},rr={class:"text-center"},or={class:"text-2xl font-bold text-blue-600"},ar={class:"text-center"},nr={class:"text-2xl font-bold text-purple-600"},ir={class:"p-6"},lr={class:"space-y-4"},dr={class:"flex items-center justify-between"},ur={class:"flex items-center space-x-4"},cr={class:"flex-shrink-0"},gr=["src","alt"],mr={key:1,class:"w-12 h-12 bg-gray-300 dark:bg-gray-600 rounded-full flex items-center justify-center"},pr={class:"text-sm font-medium text-gray-600 dark:text-gray-300"},vr={class:"flex-1"},xr={class:"flex items-center space-x-2"},yr={class:"text-lg font-medium text-gray-900 dark:text-white"},fr={key:0,class:"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200"},hr={class:"text-sm text-gray-600 dark:text-gray-400"},br={class:"text-xs text-gray-500 dark:text-gray-500"},wr={class:"flex items-center space-x-4"},kr={class:"text-right"},_r={class:"text-sm font-medium text-gray-900 dark:text-white"},$r={class:"text-right"},jr={class:"text-sm font-medium text-gray-900 dark:text-white"},Cr={class:"text-right"},Mr={class:"text-sm font-medium text-gray-900 dark:text-white"},Tr={class:"flex items-center space-x-2"},Pr=["onClick"],Sr=["onClick"],Dr={class:"mt-4"},Vr={class:"flex items-center justify-between text-xs text-gray-500 dark:text-gray-400 mb-1"},zr={class:"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2"},Ir={key:0,class:"text-center py-8"},Ar={class:"mt-6"},Br={class:"mt-3"},Er={class:"space-y-4"},Ur=["value"],Hr={class:"flex justify-end space-x-3 mt-6"},Or=["disabled"],Rr={__name:"ProjectTeam",props:{project:{type:Object,default:null},loading:{type:Boolean,default:!1}},emits:["refresh"],setup(x,{expose:P,emit:C}){const M=x,$=ne(),m=V(!1),v=V([]),p=V(!1),u=V({user_id:"",role:""}),c=B(()=>{var r;return((r=M.project)==null?void 0:r.team_members)||[]}),I=B(()=>c.value.reduce((r,i)=>r+(i.hours_worked||0),0)),R=B(()=>c.value.length===0?0:Math.round(I.value/c.value.length)),E=B(()=>c.value.filter(r=>(r.hours_worked||0)>0).length),z=r=>r?r.split(" ").map(i=>i.charAt(0).toUpperCase()).slice(0,2).join(""):"??",f=r=>{var b;return(((b=M.project)==null?void 0:b.tasks)||[]).filter(k=>k.assignee_id===r).length},N=r=>{var b;return(((b=M.project)==null?void 0:b.tasks)||[]).filter(k=>k.assignee_id===r&&k.status==="done").length},g=r=>{const i=f(r),b=N(r);return i===0?0:Math.round(b/i*100)},a=r=>{const i=g(r);return i>=80?"bg-green-600":i>=60?"bg-yellow-600":i>=40?"bg-orange-600":"bg-red-600"},S=r=>!r||r===0?"0.00":parseFloat(r).toFixed(2),A=async()=>{var r;try{const i=await fetch("/api/personnel/users",{headers:{"Content-Type":"application/json","X-CSRFToken":$.csrfToken}});if(i.ok){const b=await i.json(),k=c.value.map(q=>q.id);v.value=(r=b.data)!=null&&r.users?b.data.users.filter(q=>!k.includes(q.id)):[]}}catch(i){console.error("Errore nel caricamento utenti:",i),v.value=[]}},w=async()=>{p.value=!0;try{const r=await fetch(`/api/projects/${M.project.id}/team`,{method:"POST",headers:{"Content-Type":"application/json","X-CSRFToken":$.csrfToken},body:JSON.stringify(u.value)});if(r.ok)h("refresh"),L();else{const i=await r.json();alert(i.message||"Errore nell'aggiunta del membro")}}catch{alert("Errore nell'aggiunta del membro")}finally{p.value=!1}},j=r=>{console.log("Edit member:",r)},_=async r=>{if(confirm(`Rimuovere ${r.full_name} dal progetto?`))try{const i=await fetch(`/api/projects/${M.project.id}/team/${r.id}`,{method:"DELETE",headers:{"Content-Type":"application/json","X-CSRFToken":$.csrfToken}});if(i.ok)h("refresh");else{const b=await i.json();alert(b.message||"Errore nella rimozione del membro")}}catch{alert("Errore nella rimozione del membro")}},L=()=>{m.value=!1,u.value={user_id:"",role:""}},h=C;return se(()=>{A()}),Z(()=>m.value,r=>{r&&A()}),Z(()=>{var r;return(r=M.project)==null?void 0:r.team_members},()=>{m.value&&A()}),P({refresh:A}),(r,i)=>(t(),s("div",Xs,[e("div",Ws,[e("div",Ys,[e("div",Gs,[i[6]||(i[6]=e("h3",{class:"text-lg font-medium text-gray-900 dark:text-white"}," Team del Progetto ",-1)),e("button",{onClick:i[0]||(i[0]=b=>m.value=!0),class:"inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"},i[5]||(i[5]=[e("svg",{class:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 6v6m0 0v6m0-6h6m-6 0H6"})],-1),X(" Aggiungi Membro ")]))])]),e("div",Js,[e("div",Qs,[e("div",Zs,[e("div",er,o(c.value.length),1),i[7]||(i[7]=e("div",{class:"text-sm text-gray-500 dark:text-gray-400"},"Membri Totali",-1))]),e("div",tr,[e("div",sr,o(I.value)+"h",1),i[8]||(i[8]=e("div",{class:"text-sm text-gray-500 dark:text-gray-400"},"Ore Totali",-1))]),e("div",rr,[e("div",or,o(R.value)+"h",1),i[9]||(i[9]=e("div",{class:"text-sm text-gray-500 dark:text-gray-400"},"Media per Membro",-1))]),e("div",ar,[e("div",nr,o(E.value),1),i[10]||(i[10]=e("div",{class:"text-sm text-gray-500 dark:text-gray-400"},"Membri Attivi",-1))])])]),e("div",ir,[e("div",lr,[(t(!0),s(F,null,K(c.value,b=>{var k,q;return t(),s("div",{key:b.id,class:"border border-gray-200 dark:border-gray-700 rounded-lg p-4 hover:shadow-md transition-all duration-200"},[e("div",dr,[e("div",ur,[e("div",cr,[b.profile_image?(t(),s("img",{key:0,src:b.profile_image,alt:b.full_name,class:"w-12 h-12 rounded-full"},null,8,gr)):(t(),s("div",mr,[e("span",pr,o(z(b.full_name)),1)]))]),e("div",vr,[e("div",xr,[e("h4",yr,o(b.full_name),1),b.id===((k=x.project)==null?void 0:k.manager_id)?(t(),s("span",fr," Project Manager ")):D("",!0)]),e("p",hr,o(b.role||"Team Member"),1),e("p",br,o(b.email),1)])]),e("div",wr,[e("div",kr,[e("div",_r,o(S(b.hours_worked||0))+"h",1),i[11]||(i[11]=e("div",{class:"text-xs text-gray-500 dark:text-gray-400"},"ore lavorate",-1))]),e("div",$r,[e("div",jr,o(f(b.id)),1),i[12]||(i[12]=e("div",{class:"text-xs text-gray-500 dark:text-gray-400"},"task assegnati",-1))]),e("div",Cr,[e("div",Mr,o(N(b.id)),1),i[13]||(i[13]=e("div",{class:"text-xs text-gray-500 dark:text-gray-400"},"completati",-1))]),e("div",Tr,[e("button",{onClick:T=>j(b),class:"p-1 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300",title:"Modifica membro"},i[14]||(i[14]=[e("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"})],-1)]),8,Pr),b.id!==((q=x.project)==null?void 0:q.manager_id)?(t(),s("button",{key:0,onClick:T=>_(b),class:"p-1 text-gray-400 hover:text-red-600",title:"Rimuovi dal progetto"},i[15]||(i[15]=[e("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"})],-1)]),8,Sr)):D("",!0)])])]),e("div",Dr,[e("div",Vr,[i[16]||(i[16]=e("span",null,"Produttività",-1)),e("span",null,o(g(b.id))+"%",1)]),e("div",zr,[e("div",{class:O(["h-2 rounded-full transition-all duration-300",a(b.id)]),style:ee({width:g(b.id)+"%"})},null,6)])])])}),128)),c.value.length===0?(t(),s("div",Ir,[i[18]||(i[18]=e("svg",{class:"mx-auto h-12 w-12 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"})],-1)),i[19]||(i[19]=e("h3",{class:"mt-2 text-sm font-medium text-gray-900 dark:text-white"},"Nessun membro del team",-1)),i[20]||(i[20]=e("p",{class:"mt-1 text-sm text-gray-500 dark:text-gray-400"},"Inizia aggiungendo membri al progetto.",-1)),e("div",Ar,[e("button",{onClick:i[1]||(i[1]=b=>m.value=!0),class:"inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700"},i[17]||(i[17]=[e("svg",{class:"-ml-1 mr-2 h-5 w-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 6v6m0 0v6m0-6h6m-6 0H6"})],-1),X(" Aggiungi primo membro ")]))])])):D("",!0)])])]),m.value?(t(),s("div",{key:0,class:"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50",onClick:L},[e("div",{class:"relative top-20 mx-auto p-5 border w-[500px] shadow-lg rounded-md bg-white dark:bg-gray-800",onClick:i[4]||(i[4]=re(()=>{},["stop"]))},[e("div",Br,[i[25]||(i[25]=e("h3",{class:"text-lg font-medium text-gray-900 dark:text-white mb-4"}," Aggiungi Membro al Team ",-1)),e("form",{onSubmit:re(w,["prevent"])},[e("div",Er,[e("div",null,[i[22]||(i[22]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Utente",-1)),U(e("select",{"onUpdate:modelValue":i[2]||(i[2]=b=>u.value.user_id=b),required:"",class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"},[i[21]||(i[21]=e("option",{value:""},"Seleziona utente",-1)),(t(!0),s(F,null,K(v.value,b=>(t(),s("option",{key:b.id,value:b.id},o(b.full_name)+" ("+o(b.email)+") ",9,Ur))),128))],512),[[J,u.value.user_id]])]),e("div",null,[i[24]||(i[24]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Ruolo",-1)),U(e("select",{"onUpdate:modelValue":i[3]||(i[3]=b=>u.value.role=b),required:"",class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"},i[23]||(i[23]=[ae('<option value="">Seleziona ruolo</option><option value="Team Member">Team Member</option><option value="Developer">Developer</option><option value="Designer">Designer</option><option value="QA Tester">QA Tester</option><option value="Business Analyst">Business Analyst</option><option value="Technical Lead">Technical Lead</option>',7)]),512),[[J,u.value.role]])])]),e("div",Hr,[e("button",{type:"button",onClick:L,class:"px-4 py-2 text-sm font-medium text-gray-700 bg-gray-200 hover:bg-gray-300 rounded-md"}," Annulla "),e("button",{type:"submit",disabled:p.value,class:"px-4 py-2 text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 rounded-md disabled:opacity-50"},o(p.value?"Aggiungendo...":"Aggiungi"),9,Or)])],32)])])])):D("",!0)]))}};function Lr(x,P){return t(),s("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M6.75 3v2.25M17.25 3v2.25M3 18.75V7.5a2.25 2.25 0 0 1 2.25-2.25h13.5A2.25 2.25 0 0 1 21 7.5v11.25m-18 0A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75m-18 0v-7.5A2.25 2.25 0 0 1 5.25 9h13.5A2.25 2.25 0 0 1 21 11.25v7.5"})])}function pe(x,P){return t(),s("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M2.25 8.25h19.5M2.25 9h19.5m-16.5 5.25h6m-6 2.25h3m-3.75 3h15a2.25 2.25 0 0 0 2.25-2.25V6.75A2.25 2.25 0 0 0 19.5 4.5h-15a2.25 2.25 0 0 0-2.25 2.25v10.5A2.25 2.25 0 0 0 4.5 19.5Z"})])}function Fr(x,P){return t(),s("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M12 4.5v15m7.5-7.5h-15"})])}const Kr={class:"fixed inset-0 z-50 overflow-y-auto"},qr={class:"flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0"},Nr={class:"inline-block align-bottom bg-white dark:bg-gray-800 rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full"},Xr={class:"bg-white dark:bg-gray-800 px-4 pt-5 pb-4 sm:p-6 sm:pb-4"},Wr={class:"mb-4"},Yr={class:"text-lg font-medium text-gray-900 dark:text-white"},Gr={class:"space-y-4"},Jr={class:"bg-gray-50 dark:bg-gray-700 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse"},Qr=["disabled"],Zr={key:0},eo={key:1},to={__name:"ExpenseModal",props:{projectId:{type:[String,Number],required:!0},expense:{type:Object,default:null}},emits:["close","saved"],setup(x,{emit:P}){const C=x,M=P,$=V(!1),m=ye({description:"",amount:0,category:"",date:new Date().toISOString().split("T")[0],notes:""}),v=async()=>{$.value=!0;try{const p=C.expense?`/api/expenses/${C.expense.id}`:`/api/projects/${C.projectId}/expenses`,u=C.expense?"PUT":"POST";(await fetch(p,{method:u,headers:{"Content-Type":"application/json"},body:JSON.stringify(m)})).ok?M("saved"):console.error("Error saving expense")}catch(p){console.error("Error saving expense:",p)}finally{$.value=!1}};return se(()=>{C.expense&&Object.assign(m,{description:C.expense.description,amount:C.expense.amount,category:C.expense.category,date:C.expense.date.split("T")[0],notes:C.expense.notes||""})}),(p,u)=>(t(),s("div",Kr,[e("div",qr,[e("div",{class:"fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity",onClick:u[0]||(u[0]=c=>p.$emit("close"))}),e("div",Nr,[e("form",{onSubmit:re(v,["prevent"])},[e("div",Xr,[e("div",Wr,[e("h3",Yr,o(x.expense?"Modifica Spesa":"Aggiungi Spesa"),1)]),e("div",Gr,[e("div",null,[u[7]||(u[7]=e("label",{for:"description",class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Descrizione ",-1)),U(e("input",{"onUpdate:modelValue":u[1]||(u[1]=c=>m.description=c),type:"text",id:"description",required:"",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white",placeholder:"Descrizione della spesa"},null,512),[[W,m.description]])]),e("div",null,[u[8]||(u[8]=e("label",{for:"amount",class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Importo (€) ",-1)),U(e("input",{"onUpdate:modelValue":u[2]||(u[2]=c=>m.amount=c),type:"number",step:"0.01",id:"amount",required:"",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white",placeholder:"0.00"},null,512),[[W,m.amount,void 0,{number:!0}]])]),e("div",null,[u[10]||(u[10]=e("label",{for:"category",class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Categoria ",-1)),U(e("select",{"onUpdate:modelValue":u[3]||(u[3]=c=>m.category=c),id:"category",required:"",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white"},u[9]||(u[9]=[ae('<option value="">Seleziona categoria</option><option value="travel">Viaggio</option><option value="meals">Pasti</option><option value="materials">Materiali</option><option value="software">Software</option><option value="hardware">Hardware</option><option value="services">Servizi</option><option value="other">Altro</option>',8)]),512),[[J,m.category]])]),e("div",null,[u[11]||(u[11]=e("label",{for:"date",class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Data ",-1)),U(e("input",{"onUpdate:modelValue":u[4]||(u[4]=c=>m.date=c),type:"date",id:"date",required:"",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white"},null,512),[[W,m.date]])]),e("div",null,[u[12]||(u[12]=e("label",{for:"notes",class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Note (opzionale) ",-1)),U(e("textarea",{"onUpdate:modelValue":u[5]||(u[5]=c=>m.notes=c),id:"notes",rows:"3",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white",placeholder:"Note aggiuntive..."},null,512),[[W,m.notes]])])])]),e("div",Jr,[e("button",{type:"submit",disabled:$.value,class:"w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-primary-600 text-base font-medium text-white hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 sm:ml-3 sm:w-auto sm:text-sm disabled:opacity-50"},[$.value?(t(),s("span",Zr,"Salvando...")):(t(),s("span",eo,o(x.expense?"Aggiorna":"Salva"),1))],8,Qr),e("button",{type:"button",onClick:u[6]||(u[6]=c=>p.$emit("close")),class:"mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 dark:border-gray-600 shadow-sm px-4 py-2 bg-white dark:bg-gray-800 text-base font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm"}," Annulla ")])],32)])])]))}},so={class:"project-expenses"},ro={class:"space-y-6"},oo={class:"flex justify-between items-center"},ao={key:0,class:"text-center py-8"},no={key:1,class:"text-center py-12"},io={key:2,class:"bg-white dark:bg-gray-800 shadow overflow-hidden sm:rounded-md"},lo={class:"divide-y divide-gray-200 dark:divide-gray-700"},uo={class:"flex items-center justify-between"},co={class:"flex-1"},go={class:"flex items-center"},mo={class:"flex-shrink-0"},po={class:"h-10 w-10 rounded-full bg-gray-300 dark:bg-gray-600 flex items-center justify-center"},vo={class:"ml-4 flex-1"},xo={class:"flex items-center justify-between"},yo={class:"text-sm font-medium text-gray-900 dark:text-white"},fo={class:"ml-2 flex-shrink-0"},ho={class:"text-sm font-medium text-gray-900 dark:text-white"},bo={class:"mt-1 flex items-center text-sm text-gray-500 dark:text-gray-400"},wo={class:"capitalize"},ko={key:0,class:"mx-2"},_o={key:1},$o={key:0,class:"flex items-center space-x-2"},jo=["onClick"],Co=["onClick"],Mo={key:3,class:"bg-gray-50 dark:bg-gray-700 rounded-lg p-4"},To={class:"flex justify-between items-center"},Po={class:"text-lg font-bold text-gray-900 dark:text-white"},So={__name:"ProjectExpenses",props:{project:{type:Object,required:!0},loading:{type:Boolean,default:!1}},setup(x){const P=x;xe();const C=ne(),M=V(!1),$=V([]),m=V(!1),v=V(null),p=B(()=>C.hasPermission("manage_expenses")),u=B(()=>$.value.reduce((g,a)=>g+a.amount,0)),c=async()=>{var g;if((g=P.project)!=null&&g.id){M.value=!0;try{const a=await fetch(`/api/projects/${P.project.id}/expenses`);a.ok&&($.value=await a.json())}catch(a){console.error("Error loading expenses:",a)}finally{M.value=!1}}},I=g=>{v.value=g,m.value=!0},R=async g=>{if(confirm("Sei sicuro di voler eliminare questa spesa?"))try{(await fetch(`/api/expenses/${g}`,{method:"DELETE"})).ok&&($.value=$.value.filter(S=>S.id!==g))}catch(a){console.error("Error deleting expense:",a)}},E=()=>{m.value=!1,v.value=null},z=()=>{E(),c()},f=g=>new Intl.NumberFormat("it-IT",{minimumFractionDigits:2,maximumFractionDigits:2}).format(g),N=g=>new Date(g).toLocaleDateString("it-IT");return Z(()=>{var g;return(g=P.project)==null?void 0:g.id},(g,a)=>{g&&g!==a&&c()},{immediate:!0}),se(()=>{c()}),(g,a)=>{var S;return t(),s("div",so,[e("div",ro,[e("div",oo,[a[2]||(a[2]=e("h3",{class:"text-lg font-medium text-gray-900 dark:text-white"},"Spese Progetto",-1)),p.value?(t(),s("button",{key:0,onClick:a[0]||(a[0]=A=>m.value=!0),class:"inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"},[oe(le(Fr),{class:"w-4 h-4 mr-2"}),a[1]||(a[1]=X(" Aggiungi Spesa "))])):D("",!0)]),M.value?(t(),s("div",ao,a[3]||(a[3]=[e("div",{class:"inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"},null,-1),e("p",{class:"mt-2 text-sm text-gray-500"},"Caricamento spese...",-1)]))):$.value.length===0?(t(),s("div",no,[oe(le(pe),{class:"mx-auto h-12 w-12 text-gray-400"}),a[4]||(a[4]=e("h3",{class:"mt-2 text-sm font-medium text-gray-900 dark:text-white"},"Nessuna spesa",-1)),a[5]||(a[5]=e("p",{class:"mt-1 text-sm text-gray-500"},"Non ci sono ancora spese registrate per questo progetto.",-1))])):(t(),s("div",io,[e("ul",lo,[(t(!0),s(F,null,K($.value,A=>(t(),s("li",{key:A.id,class:"px-6 py-4"},[e("div",uo,[e("div",co,[e("div",go,[e("div",mo,[e("div",po,[oe(le(pe),{class:"h-5 w-5 text-gray-600 dark:text-gray-300"})])]),e("div",vo,[e("div",xo,[e("p",yo,o(A.description),1),e("div",fo,[e("p",ho," €"+o(f(A.amount)),1)])]),e("div",bo,[oe(le(Lr),{class:"flex-shrink-0 mr-1.5 h-4 w-4"}),X(" "+o(N(A.date))+" ",1),a[6]||(a[6]=e("span",{class:"mx-2"},"•",-1)),e("span",wo,o(A.category),1),A.user?(t(),s("span",ko,"•")):D("",!0),A.user?(t(),s("span",_o,o(A.user.name),1)):D("",!0)])])])]),p.value?(t(),s("div",$o,[e("button",{onClick:w=>I(A),class:"text-primary-600 hover:text-primary-900 text-sm font-medium"}," Modifica ",8,jo),e("button",{onClick:w=>R(A.id),class:"text-red-600 hover:text-red-900 text-sm font-medium"}," Elimina ",8,Co)])):D("",!0)])]))),128))])])),$.value.length>0?(t(),s("div",Mo,[e("div",To,[a[7]||(a[7]=e("span",{class:"text-sm font-medium text-gray-900 dark:text-white"},"Totale Spese:",-1)),e("span",Po,"€"+o(f(u.value)),1)])])):D("",!0)]),m.value?(t(),de(to,{key:0,"project-id":(S=x.project)==null?void 0:S.id,expense:v.value,onClose:E,onSaved:z},null,8,["project-id","expense"])):D("",!0)])}}},Do={class:"project-kpi"},Vo={key:0,class:"animate-pulse space-y-4"},zo={class:"grid grid-cols-1 md:grid-cols-4 gap-4"},Io={key:1,class:"space-y-6"},Ao={class:"bg-white shadow rounded-lg p-6"},Bo={class:"flex items-center justify-between"},Eo=["disabled"],Uo={class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4"},Ho={class:"bg-white shadow rounded-lg p-6"},Oo={class:"flex items-center"},Ro={class:"ml-5 w-0 flex-1"},Lo={class:"text-lg font-medium text-gray-900"},Fo={class:"text-xs text-gray-500"},Ko={class:"bg-white shadow rounded-lg p-6"},qo={class:"flex items-center"},No={class:"ml-5 w-0 flex-1"},Xo={class:"text-lg font-medium text-gray-900"},Wo={class:"bg-white shadow rounded-lg p-6"},Yo={class:"flex items-center"},Go={class:"ml-5 w-0 flex-1"},Jo={class:"text-lg font-medium text-gray-900"},Qo={class:"text-xs text-gray-500"},Zo={class:"bg-white shadow rounded-lg p-6"},ea={class:"flex items-center"},ta={class:"ml-5 w-0 flex-1"},sa={class:"text-lg font-medium text-gray-900"},ra={class:"grid grid-cols-1 lg:grid-cols-2 gap-6"},oa={class:"bg-white shadow rounded-lg p-6"},aa={class:"space-y-4"},na={class:"flex justify-between text-sm"},ia={class:"font-medium"},la={class:"w-full bg-gray-200 rounded-full h-3"},da={class:"flex justify-between text-sm"},ua={class:"text-gray-600"},ca={class:"font-medium"},ga={class:"bg-white shadow rounded-lg p-6"},ma={class:"space-y-4"},pa={class:"flex justify-between text-sm"},va={class:"font-medium"},xa={class:"w-full bg-gray-200 rounded-full h-3"},ya={class:"flex justify-between text-sm"},fa={class:"text-gray-600"},ha={class:"font-medium"},ba={class:"bg-white shadow rounded-lg p-6"},wa={class:"grid grid-cols-1 md:grid-cols-3 gap-4"},ka={class:"text-center p-4 border rounded-lg"},_a={class:"text-xs text-gray-500"},$a={class:"text-center p-4 border rounded-lg"},ja={class:"text-xs text-gray-500"},Ca={class:"text-center p-4 border rounded-lg"},Ma={class:"text-xs text-gray-500"},Ta={key:2,class:"text-center py-8"},Pa={key:3,class:"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50"},Sa={class:"relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white"},Da={class:"mt-3"},Va={class:"mt-6 space-y-6"},za={class:"bg-gray-50 p-4 rounded-lg"},Ia={class:"font-medium text-gray-900"},Aa={class:"text-sm text-gray-600"},Ba={class:"space-y-6"},Ea={class:"flex items-center justify-between mb-4"},Ua={class:"font-medium text-gray-900"},Ha={class:"text-sm text-gray-600"},Oa={class:"flex items-center space-x-2"},Ra={class:"text-xs text-gray-500"},La=["onClick"],Fa={class:"grid grid-cols-1 md:grid-cols-3 gap-4"},Ka=["onUpdate:modelValue","onInput"],qa=["onUpdate:modelValue","onInput"],Na=["onUpdate:modelValue","onInput"],Xa={class:"mt-4"},Wa=["onUpdate:modelValue","onInput"],Ya={class:"mt-4 flex justify-end"},Ga=["onClick","disabled"],Ja={key:0,class:"animate-spin -ml-1 mr-2 h-4 w-4 text-white",fill:"none",viewBox:"0 0 24 24"},Qa={key:1,class:"text-sm text-green-600"},Za={class:"mt-6 pt-4 border-t flex justify-between"},en={class:"flex space-x-3"},tn=["disabled"],sn={__name:"ProjectKPI",props:{project:{type:Object,default:null},loading:{type:Boolean,default:!1}},emits:["refresh"],setup(x,{emit:P}){const C=x,M=P,$=V(!1),m=V(!1),v=V(null),p=V({}),u=V({totalHours:0,workDays:0,totalCosts:0,costVariance:0,potentialRevenue:0,actualRevenue:0,marginPercentage:0}),c=V({budget:80,time:85,margin:15}),I=B(()=>{var n;return!((n=C.project)!=null&&n.budget)||u.value.totalCosts===0?0:Math.round(u.value.totalCosts/C.project.budget*100)}),R=B(()=>{var n;return!((n=C.project)!=null&&n.estimated_hours)||u.value.totalHours===0?0:Math.round(u.value.totalHours/C.project.estimated_hours*100)}),E=B(()=>{const n=u.value.costVariance;return n>0?"text-red-600":n<0?"text-green-600":"text-gray-600"}),z=B(()=>{const n=u.value.marginPercentage;return n>=c.value.margin?"text-green-600":n>=c.value.margin*.7?"text-yellow-600":"text-red-600"}),f=B(()=>{const n=u.value.marginPercentage;return n>=c.value.margin?"Ottimo":n>=c.value.margin*.7?"Accettabile":"Critico"}),N=B(()=>{const n=I.value;return n>=c.value.budget?"text-red-600":n>=c.value.budget*.8?"text-yellow-600":"text-green-600"}),g=B(()=>{const n=R.value;return n>=c.value.time?"text-red-600":n>=c.value.time*.8?"text-yellow-600":"text-green-600"}),a=B(()=>{const n=u.value.marginPercentage;return n>=c.value.margin?"text-green-600":n>=c.value.margin*.7?"text-yellow-600":"text-red-600"}),S=n=>new Intl.NumberFormat("it-IT",{style:"currency",currency:"EUR"}).format(n||0),A=n=>!n||n===0?"0h":`${parseFloat(n).toFixed(2)}h`,w=n=>`${(n||0).toFixed(1)}%`,j=async()=>{var n;(n=C.project)!=null&&n.id&&_()},_=()=>{const n=C.project;n&&(u.value={totalHours:n.total_hours||0,workDays:Math.ceil((n.total_hours||0)/8),totalCosts:(n.total_hours||0)*50,costVariance:(n.total_hours||0)*50-(n.budget||0),potentialRevenue:n.budget||0,actualRevenue:n.invoiced_amount||0,marginPercentage:n.budget?(n.budget-(n.total_hours||0)*50)/n.budget*100:0})},L=async()=>{$.value=!0;try{await j(),M("refresh")}catch(n){console.error("Error refreshing KPIs:",n)}finally{$.value=!1}},h=B(()=>{var d;const n=((d=C.project)==null?void 0:d.project_type)||"service";return r(n)}),r=n=>{const d={service:[{name:"margin_percentage",display_name:"Margine Netto %",description:"Percentuale di margine netto sul fatturato",unit:"%",target_min:25,target_max:40,warning_threshold:15},{name:"utilization_rate",display_name:"Tasso di Utilizzo %",description:"Percentuale di utilizzo del team rispetto alla capacità teorica",unit:"%",target_min:75,target_max:85,warning_threshold:60},{name:"cost_per_hour",display_name:"Costo per Ora",description:"Costo medio per ora di lavoro, inclusi tutti i costi",unit:"€",target_min:30,target_max:50,warning_threshold:60},{name:"cost_revenue_ratio",display_name:"Rapporto C/R",description:"Rapporto tra costi sostenuti e ricavi generati",unit:"ratio",target_min:.6,target_max:.75,warning_threshold:.85}]};return d[n]||d.service},i=n=>({service:"🔧 Servizio",license:"📄 Licenza",consulting:"💼 Consulenza",product:"📦 Prodotto",rd:"🔬 R&D",internal:"🏢 Interno"})[n]||"Sconosciuto",b=()=>{h.value.forEach(d=>{p.value[d.name]||(p.value[d.name]={target_min:d.target_min,target_max:d.target_max,warning_threshold:d.warning_threshold,custom_description:"",isDirty:!1,isSaved:!1})}),m.value=!0},k=()=>{m.value=!1},q=n=>{p.value[n]&&(p.value[n].isDirty=!0,p.value[n].isSaved=!1)},T=n=>{const d=h.value.find(Y=>Y.name===n);d&&p.value[n]&&(p.value[n].target_min=d.target_min,p.value[n].target_max=d.target_max,p.value[n].warning_threshold=d.warning_threshold,p.value[n].custom_description="",p.value[n].isDirty=!0,p.value[n].isSaved=!1)},l=()=>{confirm("Sei sicuro di voler ripristinare tutti i KPI ai valori di default?")&&h.value.forEach(n=>{T(n.name)})},Q=async n=>{var d;if(p.value[n]){v.value=n;try{const Y=p.value[n];await new Promise(ie=>setTimeout(ie,1e3)),console.log("Saving KPI config:",{project_id:(d=C.project)==null?void 0:d.id,kpi_name:n,target_min:Y.target_min,target_max:Y.target_max,warning_threshold:Y.warning_threshold,custom_description:Y.custom_description}),p.value[n].isDirty=!1,p.value[n].isSaved=!0,setTimeout(()=>{p.value[n]&&(p.value[n].isSaved=!1)},3e3)}catch(Y){console.error("Error saving KPI config:",Y),alert("Errore nel salvataggio della configurazione KPI")}finally{v.value=null}}},te=async()=>{const n=h.value.filter(d=>{var Y;return(Y=p.value[d.name])==null?void 0:Y.isDirty});for(const d of n)await Q(d.name)},y=B(()=>h.value.some(n=>{var d;return(d=p.value[n.name])==null?void 0:d.isDirty}));return Z(()=>C.project,n=>{n&&j()},{immediate:!0}),se(()=>{C.project&&j()}),(n,d)=>{var Y,ie;return t(),s("div",Do,[x.loading?(t(),s("div",Vo,[e("div",zo,[(t(),s(F,null,K(4,H=>e("div",{key:H,class:"bg-gray-200 rounded-lg h-24"})),64))]),d[0]||(d[0]=e("div",{class:"bg-gray-200 rounded-lg h-64"},null,-1))])):x.project?(t(),s("div",Io,[e("div",Ao,[e("div",Bo,[d[3]||(d[3]=e("div",null,[e("h3",{class:"text-lg font-medium text-gray-900"},"KPI Progetto"),e("p",{class:"text-sm text-gray-600"},"Dashboard metriche e performance del progetto")],-1)),e("button",{onClick:L,disabled:$.value,class:"inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50"},[(t(),s("svg",{class:O(["w-4 h-4 mr-2",{"animate-spin":$.value}]),fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},d[1]||(d[1]=[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"},null,-1)]),2)),d[2]||(d[2]=X(" Aggiorna "))],8,Eo)])]),e("div",Uo,[e("div",Ho,[e("div",Oo,[d[5]||(d[5]=e("div",{class:"flex-shrink-0"},[e("svg",{class:"h-8 w-8 text-blue-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"})])],-1)),e("div",Ro,[e("dl",null,[d[4]||(d[4]=e("dt",{class:"text-sm font-medium text-gray-500 truncate"},"Ore Totali",-1)),e("dd",Lo,o(A(u.value.totalHours)),1),e("dd",Fo,o(u.value.workDays)+" giorni lavorati",1)])])])]),e("div",Ko,[e("div",qo,[d[7]||(d[7]=e("div",{class:"flex-shrink-0"},[e("svg",{class:"h-8 w-8 text-red-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"})])],-1)),e("div",No,[e("dl",null,[d[6]||(d[6]=e("dt",{class:"text-sm font-medium text-gray-500 truncate"},"Costi Totali",-1)),e("dd",Xo,o(S(u.value.totalCosts)),1),e("dd",{class:O(["text-xs",E.value])},o(S(u.value.costVariance))+" vs budget",3)])])])]),e("div",Wo,[e("div",Yo,[d[9]||(d[9]=e("div",{class:"flex-shrink-0"},[e("svg",{class:"h-8 w-8 text-green-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"})])],-1)),e("div",Go,[e("dl",null,[d[8]||(d[8]=e("dt",{class:"text-sm font-medium text-gray-500 truncate"},"Ricavi Potenziali",-1)),e("dd",Jo,o(S(u.value.potentialRevenue)),1),e("dd",Qo,o(S(u.value.actualRevenue))+" fatturati",1)])])])]),e("div",Zo,[e("div",ea,[d[11]||(d[11]=e("div",{class:"flex-shrink-0"},[e("svg",{class:"h-8 w-8 text-purple-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"})])],-1)),e("div",ta,[e("dl",null,[d[10]||(d[10]=e("dt",{class:"text-sm font-medium text-gray-500 truncate"},"Margine",-1)),e("dd",sa,o(w(u.value.marginPercentage)),1),e("dd",{class:O(["text-xs",z.value])},o(f.value),3)])])])])]),e("div",ra,[e("div",oa,[d[13]||(d[13]=e("h4",{class:"text-lg font-medium text-gray-900 mb-4"},"Andamento Budget",-1)),e("div",aa,[e("div",na,[d[12]||(d[12]=e("span",{class:"text-gray-600"},"Budget Totale",-1)),e("span",ia,o(S(x.project.budget||0)),1)]),e("div",la,[e("div",{class:"bg-blue-600 h-3 rounded-full transition-all duration-300",style:ee({width:I.value+"%"})},null,4)]),e("div",da,[e("span",ua,"Utilizzato: "+o(S(u.value.totalCosts)),1),e("span",ca,o(I.value)+"%",1)])])]),e("div",ga,[d[15]||(d[15]=e("h4",{class:"text-lg font-medium text-gray-900 mb-4"},"Andamento Tempo",-1)),e("div",ma,[e("div",pa,[d[14]||(d[14]=e("span",{class:"text-gray-600"},"Ore Stimate",-1)),e("span",va,o(A(x.project.estimated_hours||0)),1)]),e("div",xa,[e("div",{class:"bg-green-600 h-3 rounded-full transition-all duration-300",style:ee({width:R.value+"%"})},null,4)]),e("div",ya,[e("span",fa,"Lavorate: "+o(A(u.value.totalHours)),1),e("span",ha,o(R.value)+"%",1)])])])]),e("div",ba,[e("div",{class:"flex items-center justify-between mb-4"},[d[17]||(d[17]=e("h4",{class:"text-lg font-medium text-gray-900"},"Soglie KPI",-1)),e("button",{onClick:b,class:"inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"},d[16]||(d[16]=[e("svg",{class:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"}),e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M15 12a3 3 0 11-6 0 3 3 0 016 0z"})],-1),X(" Configura KPI ")]))]),e("div",wa,[e("div",ka,[e("div",{class:O(["text-2xl font-bold",N.value])},o(I.value)+"% ",3),d[18]||(d[18]=e("div",{class:"text-sm text-gray-600"},"Budget Usage",-1)),e("div",_a,"Soglia: "+o(c.value.budget)+"%",1)]),e("div",$a,[e("div",{class:O(["text-2xl font-bold",g.value])},o(R.value)+"% ",3),d[19]||(d[19]=e("div",{class:"text-sm text-gray-600"},"Time Usage",-1)),e("div",ja,"Soglia: "+o(c.value.time)+"%",1)]),e("div",Ca,[e("div",{class:O(["text-2xl font-bold",a.value])},o(w(u.value.marginPercentage)),3),d[20]||(d[20]=e("div",{class:"text-sm text-gray-600"},"Margine",-1)),e("div",Ma,"Soglia: "+o(c.value.margin)+"%",1)])])])])):(t(),s("div",Ta,d[21]||(d[21]=[e("p",{class:"text-gray-500"},"Progetto non trovato",-1)]))),m.value?(t(),s("div",Pa,[e("div",Sa,[e("div",Da,[e("div",{class:"flex items-center justify-between pb-4 border-b"},[d[23]||(d[23]=e("h3",{class:"text-lg font-medium text-gray-900"},"Configurazione KPI Progetto",-1)),e("button",{onClick:k,class:"text-gray-400 hover:text-gray-600"},d[22]||(d[22]=[e("svg",{class:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)]))]),e("div",Va,[e("div",za,[e("h4",Ia,o((Y=x.project)==null?void 0:Y.name),1),e("p",Aa,"Tipo: "+o(i((ie=x.project)==null?void 0:ie.project_type)),1)]),e("div",Ba,[(t(!0),s(F,null,K(h.value,H=>{var ce,ge;return t(),s("div",{key:H.name,class:"border border-gray-200 rounded-lg p-4"},[e("div",Ea,[e("div",null,[e("h5",Ua,o(H.display_name),1),e("p",Ha,o(H.description),1)]),e("div",Oa,[e("span",Ra,o(H.unit),1),e("button",{onClick:G=>T(H.name),class:"text-xs text-blue-600 hover:text-blue-800",title:"Reset ai valori di default"}," Reset ",8,La)])]),e("div",Fa,[e("div",null,[d[24]||(d[24]=e("label",{class:"block text-sm font-medium text-gray-700 mb-1"},"Target Minimo",-1)),U(e("input",{type:"number",step:"0.1","onUpdate:modelValue":G=>p.value[H.name].target_min=G,onInput:G=>q(H.name),class:"w-full border border-gray-300 rounded-md px-3 py-2 text-sm"},null,40,Ka),[[W,p.value[H.name].target_min]])]),e("div",null,[d[25]||(d[25]=e("label",{class:"block text-sm font-medium text-gray-700 mb-1"},"Target Massimo",-1)),U(e("input",{type:"number",step:"0.1","onUpdate:modelValue":G=>p.value[H.name].target_max=G,onInput:G=>q(H.name),class:"w-full border border-gray-300 rounded-md px-3 py-2 text-sm"},null,40,qa),[[W,p.value[H.name].target_max]])]),e("div",null,[d[26]||(d[26]=e("label",{class:"block text-sm font-medium text-gray-700 mb-1"},"Soglia Warning",-1)),U(e("input",{type:"number",step:"0.1","onUpdate:modelValue":G=>p.value[H.name].warning_threshold=G,onInput:G=>q(H.name),class:"w-full border border-gray-300 rounded-md px-3 py-2 text-sm"},null,40,Na),[[W,p.value[H.name].warning_threshold]])])]),e("div",Xa,[d[27]||(d[27]=e("label",{class:"block text-sm font-medium text-gray-700 mb-1"},"Descrizione Personalizzata",-1)),U(e("textarea",{"onUpdate:modelValue":G=>p.value[H.name].custom_description=G,onInput:G=>q(H.name),rows:"2",class:"w-full border border-gray-300 rounded-md px-3 py-2 text-sm",placeholder:"Descrizione specifica per questo progetto..."},null,40,Wa),[[W,p.value[H.name].custom_description]])]),e("div",Ya,[(ce=p.value[H.name])!=null&&ce.isDirty?(t(),s("button",{key:0,onClick:G=>Q(H.name),disabled:v.value===H.name,class:"inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 disabled:opacity-50"},[v.value===H.name?(t(),s("svg",Ja,d[28]||(d[28]=[e("circle",{class:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor","stroke-width":"4"},null,-1),e("path",{class:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"},null,-1)]))):D("",!0),X(" "+o(v.value===H.name?"Salvataggio...":"Salva KPI"),1)],8,Ga)):(ge=p.value[H.name])!=null&&ge.isSaved?(t(),s("span",Qa,"✓ Salvato")):D("",!0)])])}),128))])]),e("div",Za,[e("button",{onClick:l,class:"inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"}," Reset Tutti "),e("div",en,[e("button",{onClick:k,class:"inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"}," Chiudi "),e("button",{onClick:te,disabled:!y.value,class:"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 disabled:opacity-50"}," Salva Tutto ",8,tn)])])])])])):D("",!0)])}}},rn={class:"space-y-6"},on={class:"bg-white dark:bg-gray-800 shadow rounded-lg"},an={class:"px-6 py-4 border-b border-gray-200 dark:border-gray-700"},nn={class:"flex items-center justify-between"},ln={class:"flex items-center space-x-4"},dn={class:"flex items-center space-x-2"},un={key:0,class:"p-6"},cn={class:"overflow-x-auto"},gn={class:"min-w-[1000px]"},mn={class:"flex mb-4"},pn={class:"flex-1 flex"},vn={class:"space-y-1"},xn={class:"w-80 flex-shrink-0 px-4 py-3"},yn={class:"flex items-center space-x-2"},fn={class:"flex-1 min-w-0"},hn={class:"text-sm font-medium text-gray-900 dark:text-white truncate"},bn={class:"flex items-center space-x-2 text-xs text-gray-500 dark:text-gray-400"},wn={key:0},kn={class:"text-xs text-gray-500 dark:text-gray-400 mt-1"},_n={class:"flex-1 relative h-12 flex items-center"},$n=["title"],jn={class:"truncate"},Cn={class:"ml-2"},Mn={key:1,class:"text-center py-12"},Tn={key:2,class:"flex justify-center py-12"},Pn={__name:"ProjectGantt",props:{project:{type:Object,default:null},loading:{type:Boolean,default:!1}},setup(x,{expose:P}){const C=x,M=V("weeks"),$=V(new Date),m=V([]),v=V(0),p=B(()=>{var w;return((w=C.project)==null?void 0:w.tasks)||[]}),u=B(()=>p.value.filter(w=>w.start_date&&w.due_date).map(w=>{const j=I(w);return{...w,timeline:j}})),c=()=>{const w=new Date,j=new Date($.value),_=[],L=12;for(let h=0;h<L;h++){const r=new Date(j);M.value==="weeks"?r.setDate(j.getDate()+h*7):M.value==="months"&&r.setMonth(j.getMonth()+h),_.push(r)}if(m.value=_,_.length>1){const h=_[0],r=new Date(_[_.length-1]);M.value==="weeks"?r.setDate(r.getDate()+7):M.value==="months"&&r.setMonth(r.getMonth()+1);const i=r-h,b=w-h;v.value=Math.max(0,Math.min(100,b/i*100))}else v.value=0},I=w=>{if(!m.value.length)return null;const j=new Date(w.start_date),_=new Date(w.due_date),L=m.value[0],r=m.value[m.value.length-1]-L,i=j-L,b=_-j,k=Math.max(0,i/r*100),q=Math.min(100-k,b/r*100);return{leftPercent:k,widthPercent:Math.max(5,q)}},R=w=>M.value==="weeks"?`${w.getDate()}/${w.getMonth()+1}`:M.value==="months"?w.toLocaleDateString("it-IT",{month:"short",year:"2-digit"}):"",E=w=>{const j=new Date,_=new Date(w);if(M.value==="weeks"){const L=new Date(_),h=new Date(_);return h.setDate(h.getDate()+6),j>=L&&j<=h}else if(M.value==="months")return _.getMonth()===j.getMonth()&&_.getFullYear()===j.getFullYear();return!1},z=()=>{const w=new Date;if(M.value==="weeks"){const j=new Date(w);j.setDate(w.getDate()-w.getDay()),$.value=j}else{const j=new Date(w.getFullYear(),w.getMonth(),1);$.value=j}c()},f=w=>({todo:"bg-gray-400","in-progress":"bg-blue-500",review:"bg-yellow-500",done:"bg-green-500"})[w]||"bg-gray-400",N=w=>({todo:"bg-gray-500","in-progress":"bg-blue-600",review:"bg-yellow-600",done:"bg-green-600"})[w]||"bg-gray-500",g=w=>({low:"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200",medium:"bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200",high:"bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200",urgent:"bg-red-200 text-red-900 dark:bg-red-800 dark:text-red-100"})[w]||"bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200",a=w=>({low:"Bassa",medium:"Media",high:"Alta",urgent:"Urgente"})[w]||"Non specificata",S=w=>({todo:0,"in-progress":50,review:75,done:100})[w.status]||0,A=w=>w?new Date(w).toLocaleDateString("it-IT",{day:"2-digit",month:"2-digit"}):"";return Z(()=>C.project,()=>{c()},{immediate:!0}),se(()=>{z()}),P({refresh:c}),(w,j)=>(t(),s("div",rn,[e("div",on,[e("div",an,[e("div",nn,[j[3]||(j[3]=e("h3",{class:"text-lg font-medium text-gray-900 dark:text-white"}," Diagramma di Gantt ",-1)),e("div",ln,[e("div",dn,[j[2]||(j[2]=e("span",{class:"text-sm text-gray-500 dark:text-gray-400"},"Vista:",-1)),U(e("select",{"onUpdate:modelValue":j[0]||(j[0]=_=>M.value=_),onChange:c,class:"text-sm border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md shadow-sm focus:border-primary-500 focus:ring-primary-500"},j[1]||(j[1]=[e("option",{value:"weeks"},"Settimane",-1),e("option",{value:"months"},"Mesi",-1)]),544),[[J,M.value]])]),e("button",{onClick:z,class:"px-3 py-1 text-sm bg-primary-100 text-primary-700 rounded-md hover:bg-primary-200"}," Oggi ")])])]),!x.loading&&u.value.length>0?(t(),s("div",un,[e("div",cn,[e("div",gn,[e("div",mn,[j[4]||(j[4]=e("div",{class:"w-80 flex-shrink-0 px-4 py-2 text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider"}," Task ",-1)),e("div",pn,[(t(!0),s(F,null,K(m.value,(_,L)=>(t(),s("div",{key:L,class:O(["flex-1 text-xs text-center text-gray-500 dark:text-gray-400 py-2 border-l border-gray-200 dark:border-gray-600",{"bg-blue-50 dark:bg-blue-900":E(_)}])},o(R(_)),3))),128))])]),e("div",vn,[(t(!0),s(F,null,K(u.value,_=>(t(),s("div",{key:_.id,class:"flex items-center hover:bg-gray-50 dark:hover:bg-gray-700 rounded"},[e("div",xn,[e("div",yn,[e("div",{class:O(["w-3 h-3 rounded-full",f(_.status)])},null,2),e("div",fn,[e("p",hn,o(_.name),1),e("div",bn,[_.assignee?(t(),s("span",wn,o(_.assignee.full_name),1)):D("",!0),e("span",{class:O(["inline-flex items-center px-1.5 py-0.5 rounded-full text-xs font-medium",g(_.priority)])},o(a(_.priority)),3)]),e("div",kn,o(A(_.start_date))+" - "+o(A(_.due_date)),1)])])]),e("div",_n,[_.timeline?(t(),s("div",{key:0,class:O(["absolute h-6 rounded-md flex items-center justify-between px-2 text-xs text-white font-medium shadow-sm cursor-pointer",N(_.status)]),style:ee({left:_.timeline.leftPercent+"%",width:_.timeline.widthPercent+"%",minWidth:"60px"}),title:`${_.name} - ${S(_)}% completato`},[e("span",jn,o(_.name.length>15?_.name.substring(0,15)+"...":_.name),1),e("span",Cn,o(S(_))+"%",1)],14,$n)):D("",!0),_.timeline&&S(_)>0&&S(_)<100?(t(),s("div",{key:1,class:"absolute h-6 rounded-md bg-green-600 opacity-80",style:ee({left:_.timeline.leftPercent+"%",width:_.timeline.widthPercent*S(_)/100+"%",minWidth:"2px"})},null,4)):D("",!0),(t(!0),s(F,null,K(m.value,(L,h)=>(t(),s("div",{key:h,class:"absolute top-0 bottom-0 border-l border-gray-200 dark:border-gray-600",style:ee({left:h/m.value.length*100+"%"})},null,4))),128)),v.value>=0&&v.value<=100?(t(),s("div",{key:2,class:"absolute top-0 bottom-0 w-0.5 bg-red-500 z-10",style:ee({left:v.value+"%"})},null,4)):D("",!0)])]))),128))])])]),j[5]||(j[5]=ae('<div class="mt-6 flex items-center space-x-6 text-xs"><div class="flex items-center space-x-2"><div class="w-3 h-3 bg-gray-400 rounded"></div><span class="text-gray-600 dark:text-gray-400">Da fare</span></div><div class="flex items-center space-x-2"><div class="w-3 h-3 bg-blue-500 rounded"></div><span class="text-gray-600 dark:text-gray-400">In corso</span></div><div class="flex items-center space-x-2"><div class="w-3 h-3 bg-yellow-500 rounded"></div><span class="text-gray-600 dark:text-gray-400">In revisione</span></div><div class="flex items-center space-x-2"><div class="w-3 h-3 bg-green-500 rounded"></div><span class="text-gray-600 dark:text-gray-400">Completato</span></div><div class="flex items-center space-x-2"><div class="w-0.5 h-4 bg-red-500"></div><span class="text-gray-600 dark:text-gray-400">Oggi</span></div></div>',1))])):x.loading?D("",!0):(t(),s("div",Mn,j[6]||(j[6]=[e("svg",{class:"mx-auto h-12 w-12 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"})],-1),e("h3",{class:"mt-2 text-sm font-medium text-gray-900 dark:text-white"},"Nessun task pianificato",-1),e("p",{class:"mt-1 text-sm text-gray-500 dark:text-gray-400"},"I task con date di inizio e fine appariranno nel diagramma di Gantt.",-1)]))),x.loading?(t(),s("div",Tn,j[7]||(j[7]=[e("div",{class:"animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"},null,-1)]))):D("",!0)])]))}},Sn={class:"space-y-6"},Dn={class:"bg-white dark:bg-gray-800 shadow rounded-lg"},Vn={class:"px-6 py-4 border-b border-gray-200 dark:border-gray-700"},zn={class:"flex items-center justify-between"},In={class:"flex items-center space-x-4"},An={class:"flex items-center space-x-2"},Bn={class:"text-sm font-medium text-gray-900 dark:text-white min-w-[80px] text-center"},En={class:"flex items-center space-x-2"},Un=["value"],Hn={key:0,class:"flex justify-center py-8"},On={key:1,class:"bg-red-50 border border-red-200 rounded-md p-4 m-6"},Rn={class:"text-red-600"},Ln={key:2,class:"p-6"},Fn={class:"overflow-x-auto"},Kn={class:"min-w-full divide-y divide-gray-200 dark:divide-gray-700"},qn={class:"bg-gray-50 dark:bg-gray-700"},Nn={class:"bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700"},Xn={class:"px-4 py-3 whitespace-nowrap sticky left-0 bg-white dark:bg-gray-800 border-r border-gray-200 dark:border-gray-700"},Wn={class:"text-sm font-medium text-gray-900 dark:text-white"},Yn={class:"text-xs text-gray-500 dark:text-gray-400"},Gn=["onClick"],Jn={key:0,class:"text-xs font-medium text-primary-600 dark:text-primary-400"},Qn={key:1,class:"text-gray-300 dark:text-gray-600"},Zn={class:"px-3 py-3 text-center bg-gray-50 dark:bg-gray-700"},ei={class:"text-sm font-medium text-gray-900 dark:text-white"},ti={class:"bg-gray-100 dark:bg-gray-600 font-medium"},si={class:"px-3 py-3 text-center text-sm font-semibold text-gray-900 dark:text-white bg-gray-100 dark:bg-gray-600"},ri={key:0,class:"text-center py-8"},oi={class:"mt-3"},ai={class:"text-lg font-medium text-gray-900 dark:text-white mb-4"},ni={class:"grid grid-cols-1 gap-4"},ii=["value"],li={class:"flex justify-end space-x-3 mt-6"},di=["disabled"],ui={__name:"ProjectTimesheet",props:{project:{type:Object,default:null},loading:{type:Boolean,default:!1}},setup(x,{expose:P}){const C=x,M=ne(),$=V(null),m=V(!1),v=V(""),p=V(!1),u=V(new Date().getFullYear()),c=V(new Date().getMonth()+1),I=V(""),R=V(!1),E=V(!1),z=V(null),f=V({task_id:"",date:"",hours:0,description:""}),N=B(()=>$.value?Array.from({length:$.value.days_in_month},(h,r)=>r+1):[]),g=async()=>{var h;if((h=C.project)!=null&&h.id){m.value=!0,v.value="";try{const r=new URLSearchParams({year:u.value.toString(),month:c.value.toString()});I.value&&r.append("member_id",I.value.toString());const i=await fetch(`/api/timesheets/project/${C.project.id}/monthly?${r}`,{headers:{"Content-Type":"application/json","X-CSRFToken":M.csrfToken}});if(!i.ok)throw new Error("Errore nel caricamento del timesheet");const b=await i.json();$.value=b.data}catch(r){v.value=r.message}finally{m.value=!1}}},a=async()=>{p.value=!0;try{const h={...f.value,project_id:C.project.id};if(!(await fetch("/api/timesheets/",{method:"POST",headers:{"Content-Type":"application/json","X-CSRFToken":M.csrfToken},body:JSON.stringify(h)})).ok)throw new Error("Errore nel salvataggio del timesheet");await g(),A()}catch(h){v.value=h.message}finally{p.value=!1}},S=(h,r)=>{const i=$.value.tasks.find(b=>b.id===h);i&&(z.value={taskId:h,day:r},f.value={task_id:h,date:`${u.value}-${String(c.value).padStart(2,"0")}-${String(r).padStart(2,"0")}`,hours:i.daily_hours[r]||0,description:""},i.daily_hours[r]>0?E.value=!0:R.value=!0)},A=()=>{R.value=!1,E.value=!1,z.value=null,f.value={task_id:"",date:"",hours:0,description:""}},w=()=>{c.value===1?(c.value=12,u.value--):c.value--,g()},j=()=>{c.value===12?(c.value=1,u.value++):c.value++,g()},_=h=>{const r=new Date;return r.getFullYear()===u.value&&r.getMonth()+1===c.value&&r.getDate()===h},L=h=>!h||h===0?"0":h%1===0?h.toString():h.toFixed(2);return Z(()=>{var h;return(h=C.project)==null?void 0:h.id},h=>{h&&g()}),Z(I,()=>{g()}),se(()=>{var h;(h=C.project)!=null&&h.id&&g()}),P({refresh:g}),(h,r)=>{var i,b;return t(),s("div",Sn,[e("div",Dn,[e("div",Vn,[e("div",zn,[e("div",In,[r[11]||(r[11]=e("h3",{class:"text-lg font-medium text-gray-900 dark:text-white"}," Timesheet Dettaglio ",-1)),e("div",An,[e("button",{onClick:w,class:"p-1 rounded-md text-gray-400 hover:text-gray-600 hover:bg-gray-100 dark:hover:bg-gray-700"},r[7]||(r[7]=[e("svg",{class:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M15 19l-7-7 7-7"})],-1)])),e("span",Bn,o(c.value)+"/"+o(u.value),1),e("button",{onClick:j,class:"p-1 rounded-md text-gray-400 hover:text-gray-600 hover:bg-gray-100 dark:hover:bg-gray-700"},r[8]||(r[8]=[e("svg",{class:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 5l7 7-7 7"})],-1)]))]),e("div",En,[r[10]||(r[10]=e("span",{class:"text-sm text-gray-500 dark:text-gray-400"},"Vista:",-1)),U(e("select",{"onUpdate:modelValue":r[0]||(r[0]=k=>I.value=k),onChange:g,class:"text-sm border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md shadow-sm focus:border-primary-500 focus:ring-primary-500"},[r[9]||(r[9]=e("option",{value:""},"Tutti i membri",-1)),(t(!0),s(F,null,K(((i=x.project)==null?void 0:i.team_members)||[],k=>(t(),s("option",{key:k.id,value:k.id},o(k.first_name)+" "+o(k.last_name),9,Un))),128))],544),[[J,I.value]])])]),e("button",{onClick:r[1]||(r[1]=k=>R.value=!0),class:"inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"},r[12]||(r[12]=[e("svg",{class:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 6v6m0 0v6m0-6h6m-6 0H6"})],-1),X(" Aggiungi Ore ")]))])]),m.value?(t(),s("div",Hn,r[13]||(r[13]=[e("div",{class:"animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"},null,-1)]))):D("",!0),v.value?(t(),s("div",On,[e("p",Rn,o(v.value),1)])):D("",!0),!m.value&&$.value?(t(),s("div",Ln,[e("div",Fn,[e("table",Kn,[e("thead",qn,[e("tr",null,[r[14]||(r[14]=e("th",{class:"px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider sticky left-0 bg-gray-50 dark:bg-gray-700"}," Task ",-1)),(t(!0),s(F,null,K(N.value,k=>(t(),s("th",{key:k,class:O(["px-2 py-3 text-center text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider min-w-[40px]",{"bg-blue-50 dark:bg-blue-900":_(k)}])},o(k),3))),128)),r[15]||(r[15]=e("th",{class:"px-3 py-3 text-center text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider bg-gray-50 dark:bg-gray-700"}," Tot ",-1))])]),e("tbody",Nn,[(t(!0),s(F,null,K($.value.tasks,k=>(t(),s("tr",{key:k.id},[e("td",Xn,[e("div",Wn,o(k.name),1),e("div",Yn,o(k.workers.length?k.workers.join(", "):"Nessuno ha lavorato"),1)]),(t(!0),s(F,null,K(N.value,q=>(t(),s("td",{key:q,class:O(["px-2 py-3 text-center min-w-[40px] cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700",{"bg-blue-50 dark:bg-blue-900":_(q)}]),onClick:T=>S(k.id,q)},[k.daily_hours[q]>0?(t(),s("span",Jn,o(L(k.daily_hours[q])),1)):(t(),s("span",Qn,"-"))],10,Gn))),128)),e("td",Zn,[e("span",ei,o(L(k.total_hours)),1)])]))),128)),e("tr",ti,[r[16]||(r[16]=e("td",{class:"px-4 py-3 text-sm font-semibold text-gray-900 dark:text-white sticky left-0 bg-gray-100 dark:bg-gray-600"}," TOTALE GIORNALIERO ",-1)),(t(!0),s(F,null,K(N.value,k=>(t(),s("td",{key:k,class:O(["px-2 py-3 text-center text-sm font-semibold text-gray-900 dark:text-white",{"bg-blue-100 dark:bg-blue-800":_(k)}])},o(L($.value.daily_totals[k]||0)),3))),128)),e("td",si,o(L($.value.grand_total)),1)])])])]),$.value.tasks.length===0?(t(),s("div",ri,r[17]||(r[17]=[e("p",{class:"text-gray-500 dark:text-gray-400"},"Nessun task trovato per questo progetto",-1)]))):D("",!0)])):D("",!0)]),R.value||E.value?(t(),s("div",{key:0,class:"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50",onClick:A},[e("div",{class:"relative top-20 mx-auto p-5 border w-[400px] shadow-lg rounded-md bg-white dark:bg-gray-800",onClick:r[6]||(r[6]=re(()=>{},["stop"]))},[e("div",oi,[e("h3",ai,o(E.value?"Modifica Ore":"Aggiungi Ore"),1),e("form",{onSubmit:re(a,["prevent"])},[e("div",ni,[e("div",null,[r[19]||(r[19]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Task",-1)),U(e("select",{"onUpdate:modelValue":r[2]||(r[2]=k=>f.value.task_id=k),required:"",class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"},[r[18]||(r[18]=e("option",{value:""},"Seleziona task",-1)),(t(!0),s(F,null,K(((b=$.value)==null?void 0:b.tasks)||[],k=>(t(),s("option",{key:k.id,value:k.id},o(k.name),9,ii))),128))],512),[[J,f.value.task_id]])]),e("div",null,[r[20]||(r[20]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Data",-1)),U(e("input",{"onUpdate:modelValue":r[3]||(r[3]=k=>f.value.date=k),type:"date",required:"",class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"},null,512),[[W,f.value.date]])]),e("div",null,[r[21]||(r[21]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Ore",-1)),U(e("input",{"onUpdate:modelValue":r[4]||(r[4]=k=>f.value.hours=k),type:"number",step:"0.25",min:"0",max:"24",required:"",class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"},null,512),[[W,f.value.hours]])]),e("div",null,[r[22]||(r[22]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Descrizione",-1)),U(e("textarea",{"onUpdate:modelValue":r[5]||(r[5]=k=>f.value.description=k),rows:"3",class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"},null,512),[[W,f.value.description]])])]),e("div",li,[e("button",{type:"button",onClick:A,class:"px-4 py-2 text-sm font-medium text-gray-700 bg-gray-200 hover:bg-gray-300 rounded-md"}," Annulla "),e("button",{type:"submit",disabled:p.value,class:"px-4 py-2 text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 rounded-md disabled:opacity-50"},o(p.value?"Salvataggio...":E.value?"Aggiorna":"Aggiungi"),9,di)])],32)])])])):D("",!0)])}}},ci={class:"project-view"},gi={class:"tab-content"},mi={__name:"ProjectView",setup(x){const P=xe(),C=ne(),M=fe(),$=be(),m=V(!0),v=V("overview"),p=B(()=>P.currentProject),u=B(()=>[{id:"overview",label:"Panoramica",icon:"chart-bar"},{id:"tasks",label:"Task",icon:"clipboard-list"},{id:"team",label:"Team",icon:"users"},{id:"gantt",label:"Gantt",icon:"calendar"},{id:"timesheet",label:"Timesheet",icon:"clock"},{id:"expenses",label:"Spese",icon:"credit-card"},{id:"kpi",label:"KPI & Analytics",icon:"trending-up"}].filter(f=>!!(["overview","tasks","gantt","team","timesheet"].includes(f.id)||f.id==="kpi"&&C.hasPermission("view_reports")||f.id==="expenses"&&C.hasPermission("manage_expenses")))),c=B(()=>({overview:me,tasks:Ns,team:Rr,expenses:So,kpi:sn,gantt:Pn,timesheet:ui})[v.value]||me),I=async()=>{m.value=!0;try{const z=M.params.id;await P.fetchProject(z)}catch(z){console.error("Error loading project:",z)}finally{m.value=!1}},R=()=>{$.push(`/projects/${M.params.id}/edit`)},E=async()=>{if(confirm("Sei sicuro di voler eliminare questo progetto?"))try{await P.deleteProject(M.params.id),$.push("/projects")}catch(z){console.error("Error deleting project:",z)}};return Z(()=>M.params.id,(z,f)=>{z&&z!==f&&I()}),Z(()=>M.hash,z=>{if(z){const f=z.replace("#","");u.value.find(N=>N.id===f)&&v.value!==f&&(v.value=f)}},{immediate:!0}),Z(v,z=>{const f=`#${z}`;M.hash!==f&&$.replace({...M,hash:f})}),se(()=>{if(M.hash){const z=M.hash.replace("#","");u.value.find(f=>f.id===z)&&(v.value=z)}I()}),(z,f)=>(t(),s("div",ci,[oe(Be,{project:p.value,loading:m.value,onEdit:R,onDelete:E},null,8,["project","loading"]),oe(Fe,{modelValue:v.value,"onUpdate:modelValue":f[0]||(f[0]=N=>v.value=N),tabs:u.value,class:"mb-6"},null,8,["modelValue","tabs"]),e("div",gi,[(t(),de(he,null,[(t(),de(ve(c.value),{project:p.value,loading:m.value},null,8,["project","loading"]))],1024))])]))}},xi=ue(mi,[["__scopeId","data-v-3aa24582"]]);export{xi as default};
