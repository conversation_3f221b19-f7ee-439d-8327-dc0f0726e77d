import{c as r,j as t,a as o,i as s,b as n,o as i,m as l}from"./vendor.js";const d={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6"},p={class:"mt-4"},_={__name:"SkillsMatrix",setup(m){return(x,e)=>{const a=n("router-link");return i(),r("div",null,[e[2]||(e[2]=t("h1",{class:"text-2xl font-bold text-gray-900 dark:text-white mb-6"},"🎯 Matrice Competenze",-1)),t("div",d,[e[1]||(e[1]=t("p",{class:"text-gray-600 dark:text-gray-400"},"Matrice competenze in fase di migrazione...",-1)),t("div",p,[o(a,{to:"/app/personnel",class:"text-primary-600 dark:text-primary-400 hover:underline"},{default:s(()=>e[0]||(e[0]=[l(" ← Torna al Team ")])),_:1,__:[0]})])])])}}};export{_ as default};
