const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/ProjectCreate.js","assets/vendor.js","assets/ProjectView.js","assets/ProjectView.css","assets/ProjectEdit.js","assets/PersonnelDirectory.js","assets/PersonnelOrgChart.js","assets/SkillsMatrix.js","assets/DepartmentList.js","assets/DepartmentCreate.js","assets/DepartmentView.js","assets/DepartmentEdit.js","assets/PersonnelAdmin.js","assets/PersonnelProfile.js","assets/Admin.js","assets/KPITemplates.js","assets/Profile.js","assets/Settings.js"])))=>i.map(i=>d[i]);
import{r as M,w as oe,c as a,a as P,b as W,o as s,d as De,e as ce,f as _,g as b,n as L,h as O,i as D,t as d,u as ue,j as e,F,k as K,l as T,m as se,p as I,q as ze,s as ge,v as Q,x as te,y as ie,z as ve,A as X,T as Ve,B as Le,C as Be,D as Se,E as me,G as qe,H as ne,I as Re,J as He,K as Ue,L as Ne,M as Fe}from"./vendor.js";(function(){const o=document.createElement("link").relList;if(o&&o.supports&&o.supports("modulepreload"))return;for(const r of document.querySelectorAll('link[rel="modulepreload"]'))t(r);new MutationObserver(r=>{for(const c of r)if(c.type==="childList")for(const u of c.addedNodes)u.tagName==="LINK"&&u.rel==="modulepreload"&&t(u)}).observe(document,{childList:!0,subtree:!0});function l(r){const c={};return r.integrity&&(c.integrity=r.integrity),r.referrerPolicy&&(c.referrerPolicy=r.referrerPolicy),r.crossOrigin==="use-credentials"?c.credentials="include":r.crossOrigin==="anonymous"?c.credentials="omit":c.credentials="same-origin",c}function t(r){if(r.ep)return;r.ep=!0;const c=l(r);fetch(r.href,c)}})();const Y=M(!1);let we=!1;const Ee=n=>{n?(document.documentElement.classList.add("dark"),localStorage.setItem("darkMode","true")):(document.documentElement.classList.remove("dark"),localStorage.setItem("darkMode","false"))},Oe=()=>{we||(oe(Y,n=>{Ee(n)}),we=!0)};function he(){return Oe(),{isDarkMode:Y,toggleDarkMode:()=>{Y.value=!Y.value},setDarkMode:t=>{Y.value=t},initializeDarkMode:()=>{const t=localStorage.getItem("darkMode"),r=document.documentElement.classList.contains("dark");if(t==="true")Y.value=!0;else if(t==="false")Y.value=!1;else{const x=window.matchMedia("(prefers-color-scheme: dark)").matches;Y.value=r||x}Ee(Y.value);const c=window.matchMedia("(prefers-color-scheme: dark)"),u=x=>{const h=localStorage.getItem("darkMode");(!h||h==="null")&&(Y.value=x.matches)};c.addEventListener("change",u)}}}const Ke={id:"app"},Ge={__name:"App",setup(n){const{initializeDarkMode:o}=he();return o(),(l,t)=>{const r=W("router-view");return s(),a("div",Ke,[P(r)])}}},We="modulepreload",Qe=function(n){return"/"+n},$e={},G=function(o,l,t){let r=Promise.resolve();if(l&&l.length>0){document.getElementsByTagName("link");const u=document.querySelector("meta[property=csp-nonce]"),x=(u==null?void 0:u.nonce)||(u==null?void 0:u.getAttribute("nonce"));r=Promise.allSettled(l.map(h=>{if(h=Qe(h),h in $e)return;$e[h]=!0;const m=h.endsWith(".css"),i=m?'[rel="stylesheet"]':"";if(document.querySelector(`link[href="${h}"]${i}`))return;const g=document.createElement("link");if(g.rel=m?"stylesheet":We,m||(g.as="script"),g.crossOrigin="",g.href=h,x&&g.setAttribute("nonce",x),document.head.appendChild(g),m)return new Promise((y,w)=>{g.addEventListener("load",y),g.addEventListener("error",()=>w(new Error(`Unable to preload CSS for ${h}`)))})}))}function c(u){const x=new Event("vite:preloadError",{cancelable:!0});if(x.payload=u,window.dispatchEvent(x),!x.defaultPrevented)throw u}return r.then(u=>{for(const x of u||[])x.status==="rejected"&&c(x.reason);return o().catch(c)})},J=De.create({baseURL:"",timeout:1e4,withCredentials:!0,headers:{"Content-Type":"application/json"}});J.interceptors.request.use(n=>{var l,t;const o=(l=document.querySelector('meta[name="csrf-token"]'))==null?void 0:l.getAttribute("content");return o&&["post","put","patch","delete"].includes((t=n.method)==null?void 0:t.toLowerCase())&&(n.headers["X-CSRFToken"]=o),n},n=>Promise.reject(n));J.interceptors.response.use(n=>n,n=>{var o;return((o=n.response)==null?void 0:o.status)===401&&(localStorage.removeItem("user"),console.warn("Sessione scaduta, autenticazione richiesta")),Promise.reject(n)});const re=ce("auth",()=>{const n=localStorage.getItem("user"),o=M(n?JSON.parse(n):null),l=M(!1),t=M(null),r=M(!1),c=_(()=>!!o.value&&r.value),u={admin:["admin","manage_users","assign_roles","view_all_projects","create_project","edit_project","delete_project","assign_to_project","manage_project_tasks","manage_project_resources","approve_timesheets","view_personnel_data","edit_personnel_data","view_contracts","manage_contracts","view_crm","manage_clients","manage_proposals","view_reports","view_dashboard","submit_timesheet","view_own_timesheets","view_funding","manage_funding","view_products","manage_products","view_performance","manage_performance","view_communications","manage_communications","view_startup","manage_startup"],manager:["view_dashboard","view_all_projects","edit_project","assign_to_project","manage_project_tasks","manage_project_resources","approve_timesheets","view_personnel_data","view_crm","view_reports","submit_timesheet","view_own_timesheets","manage_clients","manage_proposals","view_funding","manage_funding","view_products","manage_products","view_performance","manage_performance","view_communications","manage_communications","view_startup","manage_startup"],employee:["view_dashboard","view_own_timesheets","submit_timesheet"],sales:["view_dashboard","view_crm","manage_clients","manage_proposals","submit_timesheet","view_own_timesheets","view_reports","view_funding","view_products","manage_products"],human_resources:["view_dashboard","manage_users","view_personnel_data","edit_personnel_data","view_contracts","manage_contracts","submit_timesheet","view_own_timesheets","view_reports","view_funding","manage_funding","view_performance","manage_performance","view_communications","manage_communications","view_startup","manage_startup"]},x=S=>!o.value||!o.value.role?!1:o.value.role==="admin"?!0:(u[o.value.role]||[]).includes(S),h=()=>{var S,V;console.log("Current user:",o.value),console.log("User role:",(S=o.value)==null?void 0:S.role),console.log("Has admin permission:",x("admin")),console.log("Available permissions for role:",u[(V=o.value)==null?void 0:V.role])};async function m(S){var V,E;l.value=!0,t.value=null;try{const C=await J.post("/api/auth/login",S);return C.data.success?(o.value=C.data.data.user,localStorage.setItem("user",JSON.stringify(o.value)),r.value=!0,{success:!0}):(t.value=C.data.message||"Errore durante il login",{success:!1,error:t.value})}catch(C){return t.value=((E=(V=C.response)==null?void 0:V.data)==null?void 0:E.message)||"Errore di connessione",{success:!1,error:t.value}}finally{l.value=!1}}async function i(S){var V,E;l.value=!0,t.value=null;try{const C=await J.post("/api/auth/register",S);return C.data.success?{success:!0,message:C.data.message}:(t.value=C.data.message||"Errore durante la registrazione",{success:!1,error:t.value})}catch(C){return t.value=((E=(V=C.response)==null?void 0:V.data)==null?void 0:E.message)||"Errore di connessione",{success:!1,error:t.value}}finally{l.value=!1}}async function g(){try{await J.post("/api/auth/logout")}catch(S){console.warn("Errore durante il logout:",S)}finally{o.value=null,r.value=!1,localStorage.removeItem("user")}}async function y(){if(r.value)return c.value;try{const S=await J.get("/api/auth/me");return S.data.success?(o.value=S.data.data.user,localStorage.setItem("user",JSON.stringify(o.value)),r.value=!0,!0):(await g(),!1)}catch{return await g(),!1}}async function w(){return o.value?await y():(r.value=!0,!1)}return{user:o,loading:l,error:t,sessionChecked:r,isAuthenticated:c,hasPermission:x,debugPermissions:h,login:m,register:i,logout:g,checkAuth:y,initializeAuth:w}}),ae=ce("tenant",()=>{const n=M(null),o=M(!1),l=M(null),t=_(()=>{var i;return((i=n.value)==null?void 0:i.company)||{}}),r=_(()=>{var i;return((i=n.value)==null?void 0:i.contact)||{}}),c=_(()=>{var i;return((i=n.value)==null?void 0:i.pages)||{}}),u=_(()=>{var i;return((i=n.value)==null?void 0:i.navigation)||{}}),x=_(()=>{var i;return((i=n.value)==null?void 0:i.footer)||{}});async function h(){try{if(o.value=!0,window.TENANT_CONFIG){n.value=window.TENANT_CONFIG;return}const i=await fetch("/api/config/tenant");n.value=await i.json()}catch(i){l.value="Errore nel caricamento della configurazione",console.error("Errore caricamento tenant config:",i)}finally{o.value=!1}}function m(i,g={}){if(!i||typeof i!="string")return i;let y=i;const w={"company.name":t.value.name||"DatVinci","company.tagline":t.value.tagline||"","company.description":t.value.description||"","company.mission":t.value.mission||"","company.vision":t.value.vision||"","company.founded":t.value.founded||"","company.team_size":t.value.team_size||"","contact.email":r.value.email||"","contact.phone":r.value.phone||"","contact.address":r.value.address||"",current_year:new Date().getFullYear().toString(),...g};for(const[S,V]of Object.entries(w)){const E=new RegExp(`\\{${S}\\}`,"g");y=y.replace(E,V||"")}return y}return{config:n,loading:o,error:l,company:t,contact:r,pages:c,navigation:u,footer:x,loadConfig:h,interpolateText:m}});function Ae(){const n=re(),o=_(()=>w=>n.hasPermission(w)),l=_(()=>{var w;return((w=n.user)==null?void 0:w.role)||null}),t=_(()=>l.value==="admin"),r=_(()=>l.value==="manager"),c=_(()=>l.value==="employee"),u=_(()=>l.value==="sales"),x=_(()=>l.value==="human_resources"),h=_(()=>o.value("create_project")||o.value("edit_project")||o.value("delete_project")),m=_(()=>o.value("manage_users")||o.value("assign_roles")),i=_(()=>o.value("view_all_projects")),g=_(()=>o.value("view_personnel_data")||o.value("edit_personnel_data")),y=_(()=>o.value("approve_timesheets"));return{hasPermission:o,userRole:l,isAdmin:t,isManager:r,isEmployee:c,isSales:u,isHR:x,canManageProjects:h,canManageUsers:m,canViewAllProjects:i,canManagePersonnel:g,canApproveTimesheets:y}}const Je={key:0,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z"},Ye={key:1,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"},Xe={key:2,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.25 2.25 0 11-4.5 0 2.25 2.25 0 014.5 0z"},Ze={key:3,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"},et={key:4,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"},tt={key:5,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"},st={key:6,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"},rt={key:7,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M15 12a3 3 0 11-6 0 3 3 0 016 0z"},at={key:8,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.25 2.25 0 11-4.5 0 2.25 2.25 0 014.5 0z"},ot={key:9,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 20H5a2 2 0 01-2-2V6a2 2 0 012-2h10a2 2 0 012 2v1m2 13a2 2 0 01-2-2V7m2 13a2 2 0 002-2V9a2 2 0 00-2-2h-2m-4-3H9M7 16h6M7 8h6v4H7V8z"},nt={key:10,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z"},it={key:11,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 17v-2m3 2v-4m3 4v-6m2 10H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"},lt={key:12,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 6v6m0 0v6m0-6h6m-6 0H6"},pe={__name:"SidebarIcon",props:{icon:{type:String,required:!0},className:{type:String,default:"h-5 w-5"}},setup(n){return(o,l)=>(s(),a("svg",{class:L(n.className),fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[n.icon==="dashboard"?(s(),a("path",Je)):n.icon==="projects"?(s(),a("path",Ye)):n.icon==="users"?(s(),a("path",Xe)):n.icon==="clients"?(s(),a("path",Ze)):n.icon==="products"?(s(),a("path",et)):n.icon==="reports"?(s(),a("path",tt)):n.icon==="settings"?(s(),a("path",st)):b("",!0),n.icon==="settings"?(s(),a("path",rt)):n.icon==="user-management"?(s(),a("path",at)):n.icon==="communications"?(s(),a("path",ot)):n.icon==="funding"?(s(),a("path",nt)):n.icon==="reporting"?(s(),a("path",it)):(s(),a("path",lt))],2))}},dt={key:0,class:"truncate"},ct={key:0,class:"truncate"},ee={__name:"SidebarNavItem",props:{item:{type:Object,required:!0},isCollapsed:{type:Boolean,default:!1}},emits:["click"],setup(n){const o=_(()=>["text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 hover:text-primary-600 dark:hover:text-primary-400"]);return(l,t)=>{const r=W("router-link");return s(),a("div",null,[n.item.path!=="#"?(s(),O(r,{key:0,to:n.item.path,class:L(["group flex items-center px-2 py-2 text-sm font-medium rounded-md transition-colors duration-150",[o.value,{"justify-center":n.isCollapsed}]]),"active-class":"text-primary-600 bg-primary-50 border-r-2 border-primary-600",onClick:t[0]||(t[0]=c=>l.$emit("click"))},{default:D(()=>[P(pe,{icon:n.item.icon,class:L(["flex-shrink-0 h-6 w-6",{"mr-0":n.isCollapsed,"mr-3":!n.isCollapsed}])},null,8,["icon","class"]),n.isCollapsed?b("",!0):(s(),a("span",dt,d(n.item.name),1))]),_:1},8,["to","class"])):(s(),a("div",{key:1,class:L(["group flex items-center px-2 py-2 text-sm font-medium rounded-md transition-colors duration-150 cursor-not-allowed opacity-75",["text-gray-400 hover:text-gray-500",{"justify-center":n.isCollapsed}]])},[P(pe,{icon:n.item.icon,class:L(["flex-shrink-0 h-6 w-6",{"mr-0":n.isCollapsed,"mr-3":!n.isCollapsed}])},null,8,["icon","class"]),n.isCollapsed?b("",!0):(s(),a("span",ct,d(n.item.name),1))],2))])}}},ut={key:0,class:"flex-1 text-left truncate"},mt={key:0,class:"ml-6 space-y-1 mt-1"},Ce={__name:"SidebarNavItemCollapsible",props:{item:{type:Object,required:!0},isCollapsed:{type:Boolean,default:!1}},emits:["click"],setup(n){const o=n,l=ue(),t=re(),r=M(!1),c=_(()=>["text-gray-700 hover:bg-gray-50 hover:text-primary-600",{"text-primary-600 bg-primary-50":u.value}]),u=_(()=>o.item.children?o.item.children.some(i=>i.path!=="#"&&l.path.startsWith(i.path)):!1),x=_(()=>o.item.children?o.item.children.filter(i=>{var g;return i.admin?((g=t.user)==null?void 0:g.role)==="admin":!0}):[]);u.value&&(r.value=!0);function h(){o.isCollapsed||(r.value=!r.value)}function m(i){if(i.path==="#")return!1}return(i,g)=>{const y=W("router-link");return s(),a("div",null,[e("button",{onClick:h,class:L(["group w-full flex items-center px-2 py-2 text-sm font-medium rounded-md transition-colors duration-150",[c.value,{"justify-center":n.isCollapsed}]])},[P(pe,{icon:n.item.icon,class:L(["flex-shrink-0 h-6 w-6",{"mr-0":n.isCollapsed,"mr-3":!n.isCollapsed}])},null,8,["icon","class"]),n.isCollapsed?b("",!0):(s(),a("span",ut,d(n.item.name),1)),n.isCollapsed?b("",!0):(s(),a("svg",{key:1,class:L([{"rotate-90":r.value},"ml-2 h-4 w-4 transition-transform duration-150"]),fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},g[0]||(g[0]=[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 5l7 7-7 7"},null,-1)]),2))],2),r.value&&!n.isCollapsed?(s(),a("div",mt,[(s(!0),a(F,null,K(x.value,w=>(s(),O(y,{key:w.name,to:w.path,class:L(["group flex items-center px-2 py-2 text-sm font-medium rounded-md transition-colors duration-150",w.path==="#"?"text-gray-400 hover:text-gray-500 cursor-not-allowed opacity-75":"text-gray-600 hover:bg-gray-50 hover:text-primary-600"]),"active-class":"text-primary-600 bg-primary-50",onClick:S=>m(w)},{default:D(()=>[T(d(w.name),1)]),_:2},1032,["to","class","onClick"]))),128))])):b("",!0)])}}},pt={class:"mt-5 flex-grow flex flex-col overflow-hidden"},gt={class:"flex-1 px-2 space-y-1"},je={__name:"SidebarNavigation",props:{isCollapsed:{type:Boolean,default:!1}},emits:["item-click"],setup(n){const{hasPermission:o}=Ae(),l=_(()=>o.value("view_dashboard")),t=_(()=>o.value("view_personnel_data")),r=_(()=>o.value("view_all_projects")),c=_(()=>o.value("view_crm")),u=_(()=>o.value("view_products")),x=_(()=>o.value("view_performance")),h=_(()=>o.value("view_communications")),m=_(()=>o.value("view_funding")),i=_(()=>o.value("view_reports")),g=_(()=>o.value("admin_access"));return(y,w)=>(s(),a("div",pt,[e("nav",gt,[l.value?(s(),O(ee,{key:0,item:{name:"Dashboard",path:"/app/dashboard",icon:"dashboard"},"is-collapsed":n.isCollapsed,onClick:w[0]||(w[0]=S=>y.$emit("item-click"))},null,8,["is-collapsed"])):b("",!0),t.value?(s(),O(Ce,{key:1,item:{name:"Personale",icon:"users",children:[{name:"👥 Team",path:"/app/personnel"},{name:"📖 Directory",path:"/app/personnel/directory"},{name:"🏢 Organigramma",path:"/app/personnel/orgchart"},{name:"🎯 Competenze",path:"/app/personnel/skills"},{name:"🏢 Dipartimenti",path:"/app/personnel/departments",admin:!0},{name:"⚙️ Amministrazione",path:"/app/personnel/admin",admin:!0}]},"is-collapsed":n.isCollapsed,onClick:w[1]||(w[1]=S=>y.$emit("item-click"))},null,8,["is-collapsed"])):b("",!0),r.value?(s(),O(ee,{key:2,item:{name:"Progetti",path:"/app/projects",icon:"projects"},"is-collapsed":n.isCollapsed,onClick:w[2]||(w[2]=S=>y.$emit("item-click"))},null,8,["is-collapsed"])):b("",!0),c.value?(s(),O(ee,{key:3,item:{name:"CRM",path:"#",icon:"clients"},"is-collapsed":n.isCollapsed,onClick:w[3]||(w[3]=S=>y.$emit("item-click"))},null,8,["is-collapsed"])):b("",!0),u.value?(s(),O(ee,{key:4,item:{name:"Prodotti",path:"#",icon:"products"},"is-collapsed":n.isCollapsed,onClick:w[4]||(w[4]=S=>y.$emit("item-click"))},null,8,["is-collapsed"])):b("",!0),x.value?(s(),O(ee,{key:5,item:{name:"Performance",path:"#",icon:"reports"},"is-collapsed":n.isCollapsed,onClick:w[5]||(w[5]=S=>y.$emit("item-click"))},null,8,["is-collapsed"])):b("",!0),h.value?(s(),O(ee,{key:6,item:{name:"Comunicazione",path:"#",icon:"communications"},"is-collapsed":n.isCollapsed,onClick:w[6]||(w[6]=S=>y.$emit("item-click"))},null,8,["is-collapsed"])):b("",!0),m.value?(s(),O(ee,{key:7,item:{name:"Finanziamenti",path:"#",icon:"funding"},"is-collapsed":n.isCollapsed,onClick:w[7]||(w[7]=S=>y.$emit("item-click"))},null,8,["is-collapsed"])):b("",!0),i.value?(s(),O(ee,{key:8,item:{name:"Rendicontazione",path:"#",icon:"reporting"},"is-collapsed":n.isCollapsed,onClick:w[8]||(w[8]=S=>y.$emit("item-click"))},null,8,["is-collapsed"])):b("",!0),g.value?(s(),O(Ce,{key:9,item:{name:"Amministrazione",icon:"settings",children:[{name:"👥 Gestione Utenti",path:"/app/admin/users"},{name:"📊 Template KPI",path:"/app/admin/kpi-templates"}]},"is-collapsed":n.isCollapsed,onClick:w[9]||(w[9]=S=>y.$emit("item-click"))},null,8,["is-collapsed"])):b("",!0)])]))}},vt={class:"flex-shrink-0 border-t border-gray-200 p-4"},ht={class:"flex-shrink-0"},ft={class:"h-8 w-8 rounded-full bg-primary-100 flex items-center justify-center"},xt={class:"text-sm font-medium text-primary-700"},yt={key:0,class:"ml-3 flex-1 min-w-0"},_t={class:"text-sm font-medium text-gray-900 truncate"},bt={class:"text-xs text-gray-500 truncate"},kt={class:"py-1"},wt={key:0,class:"mt-3 text-xs text-gray-400 text-center"},Me={__name:"SidebarFooter",props:{isCollapsed:{type:Boolean,default:!1}},setup(n){const o=se(),l=re(),t=M(!1),r=_(()=>l.user&&(l.user.name||l.user.username)||"Utente"),c=_(()=>l.user?r.value.charAt(0).toUpperCase():"U"),u=_(()=>l.user?{admin:"Amministratore",manager:"Manager",employee:"Dipendente",client:"Cliente"}[l.user.role]||l.user.role:""),x=_(()=>"1.0.0");async function h(){t.value=!1,await l.logout(),o.push("/auth/login")}return(m,i)=>{const g=W("router-link");return s(),a("div",vt,[e("div",{class:L(["flex items-center",{"justify-center":n.isCollapsed}])},[e("div",ht,[e("div",ft,[e("span",xt,d(c.value),1)])]),n.isCollapsed?b("",!0):(s(),a("div",yt,[e("p",_t,d(r.value),1),e("p",bt,d(u.value),1)])),e("div",{class:L(["relative",{"ml-3":!n.isCollapsed}])},[e("button",{onClick:i[0]||(i[0]=y=>t.value=!t.value),class:"p-1 rounded-full text-gray-400 hover:text-gray-600 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-primary-500"},i[4]||(i[4]=[e("svg",{class:"h-4 w-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M5 12h.01M12 12h.01M19 12h.01M6 12a1 1 0 11-2 0 1 1 0 012 0zM13 12a1 1 0 11-2 0 1 1 0 012 0zM20 12a1 1 0 11-2 0 1 1 0 012 0z"})],-1)])),t.value?(s(),a("div",{key:0,onClick:i[3]||(i[3]=y=>t.value=!1),class:"origin-bottom-left fixed bottom-16 left-4 w-48 rounded-md shadow-xl bg-white dark:bg-gray-800 ring-1 ring-black ring-opacity-5 border border-gray-200 dark:border-gray-600",style:{"z-index":"99999"}},[e("div",kt,[P(g,{to:"/app/profile",class:"block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700",onClick:i[1]||(i[1]=y=>t.value=!1)},{default:D(()=>i[5]||(i[5]=[T(" Il tuo profilo ")])),_:1,__:[5]}),P(g,{to:"/app/settings",class:"block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700",onClick:i[2]||(i[2]=y=>t.value=!1)},{default:D(()=>i[6]||(i[6]=[T(" Impostazioni ")])),_:1,__:[6]}),i[7]||(i[7]=e("hr",{class:"my-1 border-gray-200 dark:border-gray-600"},null,-1)),e("button",{onClick:h,class:"block w-full text-left px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700"}," Esci ")])])):b("",!0)],2)],2),x.value&&!n.isCollapsed?(s(),a("div",wt," v"+d(x.value),1)):b("",!0)])}}},$t={class:"flex"},Ct={class:"hidden lg:flex lg:flex-shrink-0 lg:fixed lg:inset-y-0 z-10"},jt={class:"flex flex-col flex-grow pt-5 pb-4 overflow-y-auto bg-white dark:bg-gray-800 border-r border-gray-200 dark:border-gray-700 shadow-sm"},Mt={class:"flex items-center flex-shrink-0 px-4"},Pt={class:"w-10 h-10 bg-primary-600 rounded flex items-center justify-center mr-3"},zt={class:"text-white font-bold text-lg"},St={class:"text-xl font-semibold text-gray-900 dark:text-white"},Et={class:"w-8 h-8 bg-primary-600 rounded flex items-center justify-center"},At={class:"text-white font-bold text-sm"},Tt={class:"h-5 w-5",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},It=["d"],Dt={class:"flex flex-col h-full pt-5 pb-4 overflow-y-auto bg-white dark:bg-gray-800 border-r border-gray-200 dark:border-gray-700 shadow-sm"},Vt={class:"flex items-center justify-between px-4 mb-4"},Lt={class:"w-8 h-8 bg-primary-600 rounded flex items-center justify-center mr-3"},Bt={class:"text-white font-bold text-sm"},qt={class:"text-xl font-semibold text-gray-900 dark:text-white"},Rt={__name:"AppSidebar",props:{isMobileOpen:{type:Boolean,default:!1}},emits:["close","toggle-collapsed"],setup(n,{emit:o}){const l=o,t=ae(),r=M(!1),c=_(()=>t.config||{}),u=_(()=>{var i;return((i=c.value.company)==null?void 0:i.name)||"DatPortal"}),x=_(()=>u.value.split(" ").map(g=>g[0]).join("").toUpperCase().slice(0,2));function h(){r.value=!r.value,l("toggle-collapsed",r.value)}function m(){r.value&&(r.value=!1)}return(i,g)=>{const y=W("router-link");return s(),a("div",$t,[e("div",Ct,[e("div",{class:L(["flex flex-col transition-all duration-300",[r.value?"w-20":"w-64"]])},[e("div",jt,[e("div",Mt,[e("div",{class:L(["flex items-center",{"justify-center":r.value}])},[P(y,{to:"/app/dashboard",class:L(["flex items-center",{hidden:r.value}])},{default:D(()=>[e("div",Pt,[e("span",zt,d(x.value),1)]),e("h3",St,d(u.value),1)]),_:1},8,["class"]),P(y,{to:"/app/dashboard",class:L(["flex items-center justify-center",{hidden:!r.value}])},{default:D(()=>[e("div",Et,[e("span",At,d(x.value),1)])]),_:1},8,["class"])],2),e("button",{onClick:h,class:"ml-auto text-gray-600 dark:text-gray-400 focus:outline-none hover:bg-gray-100 dark:hover:bg-gray-700 p-1 rounded"},[(s(),a("svg",Tt,[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:r.value?"M13 5l7 7-7 7M5 5l7 7-7 7":"M11 19l-7-7 7-7m8 14l-7-7 7-7"},null,8,It)]))])]),P(je,{"is-collapsed":r.value,onItemClick:m},null,8,["is-collapsed"]),P(Me,{"is-collapsed":r.value},null,8,["is-collapsed"])])],2)]),e("div",{class:L(["fixed inset-y-0 left-0 z-30 w-64 bg-primary-700 transform transition-transform duration-300 ease-in-out lg:hidden",n.isMobileOpen?"translate-x-0":"-translate-x-full"])},[e("div",Dt,[e("div",Vt,[P(y,{to:"/app/dashboard",class:"flex items-center"},{default:D(()=>[e("div",Lt,[e("span",Bt,d(x.value),1)]),e("h3",qt,d(u.value),1)]),_:1}),e("button",{onClick:g[0]||(g[0]=w=>i.$emit("close")),class:"p-2 rounded-md text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-700"},g[2]||(g[2]=[e("svg",{class:"h-6 w-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)]))]),P(je,{"is-collapsed":!1,onItemClick:g[1]||(g[1]=w=>i.$emit("close"))}),P(Me,{"is-collapsed":!1})])],2)])}}},Ht={class:"flex","aria-label":"Breadcrumb"},Ut={class:"flex items-center space-x-2 text-sm text-gray-500"},Nt={key:0,class:"mr-2"},Ft={class:"flex items-center"},Ot={key:0,class:"h-3 w-3 mr-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},Kt=["d"],Gt={key:2,class:"font-medium text-gray-900"},Wt={__name:"HeaderBreadcrumbs",props:{breadcrumbs:{type:Array,required:!0}},setup(n){return(o,l)=>{const t=W("router-link");return s(),a("nav",Ht,[e("ol",Ut,[(s(!0),a(F,null,K(n.breadcrumbs,(r,c)=>(s(),a("li",{key:c,class:"flex items-center"},[c>0?(s(),a("div",Nt,l[0]||(l[0]=[e("svg",{class:"h-3 w-3 text-gray-400",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z","clip-rule":"evenodd"})],-1)]))):b("",!0),r.to&&c<n.breadcrumbs.length-1?(s(),O(t,{key:1,to:r.to,class:"hover:text-gray-700 transition-colors duration-150"},{default:D(()=>[e("span",Ft,[r.icon?(s(),a("svg",Ot,[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:r.icon},null,8,Kt)])):b("",!0),T(" "+d(r.label),1)])]),_:2},1032,["to"])):(s(),a("span",Gt,d(r.label),1))]))),128))])])}}},Qt={class:"flex items-center space-x-2"},Jt={key:0,class:"h-3 w-3",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},Yt={key:1,class:"h-3 w-3",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},Xt={__name:"HeaderQuickActions",emits:["quick-create-project"],setup(n){const o=ue(),{isDarkMode:l,toggleDarkMode:t}=he(),r=_(()=>{var c;return((c=o.name)==null?void 0:c.includes("projects"))||o.path.includes("/projects")});return(c,u)=>(s(),a("div",Qt,[r.value?(s(),a("button",{key:0,onClick:u[0]||(u[0]=x=>c.$emit("quick-create-project")),class:"inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded-md text-primary-700 bg-primary-100 hover:bg-primary-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"},u[2]||(u[2]=[e("svg",{class:"h-3 w-3 mr-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 6v6m0 0v6m0-6h6m-6 0H6"})],-1),T(" Nuovo Progetto ")]))):b("",!0),e("button",{onClick:u[1]||(u[1]=(...x)=>I(t)&&I(t)(...x)),class:"inline-flex items-center px-3 py-1.5 border border-gray-300 text-xs font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 dark:bg-gray-800 dark:text-gray-300 dark:border-gray-600 dark:hover:bg-gray-700",title:"Cambia tema"},[I(l)?(s(),a("svg",Yt,u[4]||(u[4]=[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z"},null,-1)]))):(s(),a("svg",Jt,u[3]||(u[3]=[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z"},null,-1)])))])]))}},Zt={class:"relative"},es={class:"relative"},ts={key:0,class:"absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-4 w-4 flex items-center justify-center"},ss={class:"py-1"},rs={key:0,class:"px-4 py-8 text-center text-gray-500 text-sm"},as={key:1,class:"max-h-64 overflow-y-auto"},os=["onClick"],ns={class:"flex items-start"},is={class:"flex-shrink-0"},ls={class:"h-4 w-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},ds=["d"],cs={class:"ml-3 flex-1"},us={class:"text-sm font-medium text-gray-900"},ms={class:"text-xs text-gray-500 mt-1"},ps={class:"text-xs text-gray-400 mt-1"},gs={key:0,class:"flex-shrink-0"},vs={key:2,class:"px-4 py-2 border-t border-gray-100"},hs={__name:"HeaderNotifications",setup(n){const o=M(!1),l=M([{id:1,type:"task",title:"Nuovo task assegnato",message:'Ti è stato assegnato un nuovo task nel progetto "Website Redesign"',created_at:new Date().toISOString(),read:!1},{id:2,type:"project",title:"Progetto completato",message:'Il progetto "Mobile App" è stato completato con successo',created_at:new Date(Date.now()-36e5).toISOString(),read:!0}]),t=_(()=>l.value.filter(m=>!m.read).length);function r(m){const i={task:"h-6 w-6 rounded-full bg-blue-100 text-blue-600 flex items-center justify-center",project:"h-6 w-6 rounded-full bg-green-100 text-green-600 flex items-center justify-center",user:"h-6 w-6 rounded-full bg-purple-100 text-purple-600 flex items-center justify-center",system:"h-6 w-6 rounded-full bg-gray-100 text-gray-600 flex items-center justify-center"};return i[m]||i.system}function c(m){const i={task:"M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2",project:"M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10",user:"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z",system:"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"};return i[m]||i.system}function u(m){const i=new Date(m),y=new Date-i;return y<6e4?"Adesso":y<36e5?`${Math.floor(y/6e4)}m fa`:y<864e5?`${Math.floor(y/36e5)}h fa`:i.toLocaleDateString("it-IT")}function x(m){m.read||(m.read=!0),o.value=!1}function h(){l.value.forEach(m=>m.read=!0)}return(m,i)=>(s(),a("div",Zt,[e("button",{onClick:i[0]||(i[0]=g=>o.value=!o.value),class:"p-2 rounded-full text-gray-400 hover:text-gray-600 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"},[i[3]||(i[3]=e("span",{class:"sr-only"},"Visualizza notifiche",-1)),e("div",es,[i[2]||(i[2]=e("svg",{class:"h-5 w-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M15 17h5l-5 5v-5zM10 21a2 2 0 01-2-2V7a7 7 0 1114 0v12a2 2 0 01-2 2H10z"})],-1)),t.value>0?(s(),a("span",ts,d(t.value>9?"9+":t.value),1)):b("",!0)])]),o.value?(s(),a("div",{key:0,onClick:i[1]||(i[1]=g=>o.value=!1),class:"origin-top-right absolute right-0 mt-2 w-80 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5 z-50"},[e("div",ss,[i[5]||(i[5]=e("div",{class:"px-4 py-2 border-b border-gray-100"},[e("h3",{class:"text-sm font-medium text-gray-900"},"Notifiche")],-1)),l.value.length===0?(s(),a("div",rs," Nessuna notifica ")):(s(),a("div",as,[(s(!0),a(F,null,K(l.value,g=>(s(),a("div",{key:g.id,class:"px-4 py-3 hover:bg-gray-50 cursor-pointer border-b border-gray-50 last:border-b-0",onClick:y=>x(g)},[e("div",ns,[e("div",is,[e("div",{class:L(r(g.type))},[(s(),a("svg",ls,[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:c(g.type)},null,8,ds)]))],2)]),e("div",cs,[e("p",us,d(g.title),1),e("p",ms,d(g.message),1),e("p",ps,d(u(g.created_at)),1)]),g.read?b("",!0):(s(),a("div",gs,i[4]||(i[4]=[e("div",{class:"h-2 w-2 bg-primary-500 rounded-full"},null,-1)])))])],8,os))),128))])),l.value.length>0?(s(),a("div",vs,[e("button",{onClick:h,class:"text-xs text-primary-600 hover:text-primary-800"}," Segna tutte come lette ")])):b("",!0)])])):b("",!0)]))}},fs={class:"relative"},xs={class:"flex items-start justify-center min-h-screen pt-16 px-4 pb-20 text-center sm:block sm:p-0"},ys={class:"inline-block align-bottom bg-white rounded-lg px-4 pt-5 pb-4 text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full sm:p-6"},_s={class:"flex items-center"},bs={class:"flex-1"},ks={key:0,class:"mt-4 max-h-64 overflow-y-auto"},ws={class:"space-y-1"},$s=["onClick"],Cs={class:"flex-shrink-0"},js={class:"h-4 w-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},Ms=["d"],Ps={class:"ml-3 flex-1 min-w-0"},zs={class:"text-sm font-medium text-gray-900 truncate"},Ss={class:"text-xs text-gray-500 truncate"},Es={class:"ml-2 text-xs text-gray-400"},As={key:1,class:"mt-4 text-center py-4"},Ts={key:2,class:"mt-4 text-center py-4"},Is={__name:"HeaderSearch",setup(n){const o=se(),l=M(!1),t=M(""),r=M([]),c=M(-1),u=M(!1),x=M(null),h=[{id:1,type:"project",title:"Website Redesign",description:"Progetto di redesign del sito web aziendale",path:"/app/projects/1"},{id:2,type:"person",title:"Mario Rossi",description:"Senior Developer",path:"/app/personnel/directory/1"},{id:3,type:"document",title:"Specifiche Tecniche",description:"Documento delle specifiche del progetto mobile",path:"/app/documents/1"},{id:4,type:"task",title:"Implementazione API",description:"Task per l'implementazione delle API REST",path:"/app/projects/1/tasks/5"}];oe(l,async E=>{var C;E?(await ze(),(C=x.value)==null||C.focus()):(t.value="",r.value=[],c.value=-1)});function m(){if(!t.value.trim()){r.value=[];return}u.value=!0,setTimeout(()=>{r.value=h.filter(E=>E.title.toLowerCase().includes(t.value.toLowerCase())||E.description.toLowerCase().includes(t.value.toLowerCase())),c.value=-1,u.value=!1},200)}function i(E){if(r.value.length===0)return;const C=c.value+E;C>=0&&C<r.value.length&&(c.value=C)}function g(){c.value>=0&&r.value[c.value]&&y(r.value[c.value])}function y(E){l.value=!1,o.push(E.path)}function w(E){const C={project:"h-6 w-6 rounded bg-blue-100 text-blue-600 flex items-center justify-center",person:"h-6 w-6 rounded bg-green-100 text-green-600 flex items-center justify-center",document:"h-6 w-6 rounded bg-yellow-100 text-yellow-600 flex items-center justify-center",task:"h-6 w-6 rounded bg-purple-100 text-purple-600 flex items-center justify-center"};return C[E]||C.document}function S(E){const C={project:"M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10",person:"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z",document:"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z",task:"M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"};return C[E]||C.document}function V(E){return{project:"Progetto",person:"Persona",document:"Documento",task:"Task"}[E]||"Elemento"}return(E,C)=>(s(),a("div",fs,[e("button",{onClick:C[0]||(C[0]=U=>l.value=!l.value),class:"p-2 rounded-full text-gray-400 hover:text-gray-600 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"},C[7]||(C[7]=[e("span",{class:"sr-only"},"Cerca",-1),e("svg",{class:"h-5 w-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"})],-1)])),l.value?(s(),a("div",{key:0,class:"fixed inset-0 z-50 overflow-y-auto",onClick:C[6]||(C[6]=ge(U=>l.value=!1,["self"]))},[e("div",xs,[C[11]||(C[11]=e("div",{class:"fixed inset-0 bg-black bg-opacity-25 transition-opacity"},null,-1)),e("div",ys,[e("div",null,[e("div",_s,[e("div",bs,[Q(e("input",{ref_key:"searchInput",ref:x,"onUpdate:modelValue":C[1]||(C[1]=U=>t.value=U),onInput:m,onKeydown:[C[2]||(C[2]=ie(U=>l.value=!1,["escape"])),ie(g,["enter"]),C[3]||(C[3]=ie(U=>i(-1),["up"])),C[4]||(C[4]=ie(U=>i(1),["down"]))],type:"text",placeholder:"Cerca progetti, persone, documenti...",class:"block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 sm:text-sm"},null,544),[[te,t.value]])]),e("button",{onClick:C[5]||(C[5]=U=>l.value=!1),class:"ml-3 p-2 text-gray-400 hover:text-gray-600"},C[8]||(C[8]=[e("svg",{class:"h-5 w-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)]))]),r.value.length>0?(s(),a("div",ks,[e("div",ws,[(s(!0),a(F,null,K(r.value,(U,z)=>(s(),a("div",{key:U.id,onClick:k=>y(U),class:L(["flex items-center px-3 py-2 rounded-md cursor-pointer",z===c.value?"bg-primary-50":"hover:bg-gray-50"])},[e("div",Cs,[e("div",{class:L(w(U.type))},[(s(),a("svg",js,[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:S(U.type)},null,8,Ms)]))],2)]),e("div",Ps,[e("p",zs,d(U.title),1),e("p",Ss,d(U.description),1)]),e("div",Es,d(V(U.type)),1)],10,$s))),128))])])):t.value&&!u.value?(s(),a("div",As,C[9]||(C[9]=[e("p",{class:"text-sm text-gray-500"},"Nessun risultato trovato",-1)]))):t.value?b("",!0):(s(),a("div",Ts,C[10]||(C[10]=[e("p",{class:"text-xs text-gray-400"},"Inizia a digitare per cercare...",-1)])))])])])])):b("",!0)]))}},Ds={class:"relative"},Vs={class:"h-8 w-8 rounded-full bg-primary-100 flex items-center justify-center"},Ls={class:"text-sm font-medium text-primary-700"},Bs={class:"py-1"},qs={class:"px-4 py-2 border-b border-gray-100 dark:border-gray-700"},Rs={class:"text-sm font-medium text-gray-900 dark:text-white"},Hs={class:"text-xs text-gray-500 dark:text-gray-400"},Us={__name:"HeaderUserMenu",setup(n){const o=se(),l=re(),t=M(!1),{isDarkMode:r,toggleDarkMode:c}=he(),u=_(()=>l.user&&(l.user.name||l.user.username)||"Utente"),x=_(()=>{var i;return((i=l.user)==null?void 0:i.email)||""}),h=_(()=>l.user?u.value.charAt(0).toUpperCase():"U");async function m(){t.value=!1,await l.logout(),o.push("/auth/login")}return(i,g)=>{const y=W("router-link");return s(),a("div",Ds,[e("button",{onClick:g[0]||(g[0]=w=>t.value=!t.value),class:"flex items-center text-sm rounded-full focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"},[g[5]||(g[5]=e("span",{class:"sr-only"},"Apri menu utente",-1)),e("div",Vs,[e("span",Ls,d(h.value),1)])]),t.value?(s(),a("div",{key:0,onClick:g[4]||(g[4]=w=>t.value=!1),class:"origin-top-right absolute right-0 mt-2 w-48 rounded-md shadow-lg bg-white dark:bg-gray-800 ring-1 ring-black ring-opacity-5 z-50"},[e("div",Bs,[e("div",qs,[e("p",Rs,d(u.value),1),e("p",Hs,d(x.value),1)]),P(y,{to:"/app/profile",class:"block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700",onClick:g[1]||(g[1]=w=>t.value=!1)},{default:D(()=>g[6]||(g[6]=[T(" Il tuo profilo ")])),_:1,__:[6]}),P(y,{to:"/app/settings",class:"block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700",onClick:g[2]||(g[2]=w=>t.value=!1)},{default:D(()=>g[7]||(g[7]=[T(" Impostazioni ")])),_:1,__:[7]}),g[8]||(g[8]=e("div",{class:"border-t border-gray-100 dark:border-gray-700 my-1"},null,-1)),e("button",{onClick:g[3]||(g[3]=(...w)=>I(c)&&I(c)(...w)),class:"flex items-center justify-between w-full px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700"},[e("span",null,d(I(r)?"Modalità chiara":"Modalità scura"),1),e("i",{class:L([I(r)?"fas fa-sun":"fas fa-moon","text-xs"])},null,2)]),e("button",{onClick:m,class:"block w-full text-left px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700"}," Esci ")])])):b("",!0)])}}},Ns={class:"bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700"},Fs={class:"flex justify-between items-center px-4 py-4 sm:px-6 lg:px-8"},Os={class:"flex items-center space-x-4"},Ks={class:"flex flex-col"},Gs={class:"text-lg font-semibold text-gray-900 dark:text-white"},Ws={class:"flex items-center space-x-4"},Qs={class:"hidden md:flex items-center space-x-2"},Js={__name:"AppHeader",props:{pageTitle:{type:String,required:!0},breadcrumbs:{type:Array,default:()=>[]}},emits:["toggle-mobile-sidebar","quick-create-project"],setup(n){return(o,l)=>(s(),a("header",Ns,[e("div",Fs,[e("div",Os,[e("button",{onClick:l[0]||(l[0]=t=>o.$emit("toggle-mobile-sidebar")),class:"lg:hidden p-2 rounded-md text-gray-400 hover:text-gray-600 hover:bg-gray-100 dark:text-gray-500 dark:hover:text-gray-300 dark:hover:bg-gray-700"},l[2]||(l[2]=[e("svg",{class:"h-6 w-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 6h16M4 12h16M4 18h16"})],-1)])),e("div",Ks,[e("h2",Gs,d(n.pageTitle),1),n.breadcrumbs.length>0?(s(),O(Wt,{key:0,breadcrumbs:n.breadcrumbs},null,8,["breadcrumbs"])):b("",!0)])]),e("div",Ws,[e("div",Qs,[P(Xt,{onQuickCreateProject:l[1]||(l[1]=t=>o.$emit("quick-create-project"))})]),P(hs),P(Is),P(Us)])])]))}},Ys={__name:"LoadingSpinner",props:{size:{type:String,default:"md",validator:n=>["sm","md","lg","xl"].includes(n)},message:{type:String,default:""},centered:{type:Boolean,default:!0}},setup(n){const o=n,l=_(()=>{const u={sm:"20px",md:"32px",lg:"48px",xl:"64px"};return`width: ${u[o.size]}; height: ${u[o.size]};`}),t=_(()=>["flex",o.centered?"items-center justify-center":"","space-y-2"]),r=_(()=>["flex items-center justify-center"]),c=_(()=>["text-sm text-gray-600 text-center"]);return(u,x)=>(s(),a("div",{class:L(t.value)},[e("div",{class:L(r.value)},[e("div",{class:"animate-spin rounded-full border-2 border-gray-300 border-t-primary-600",style:ve(l.value)},null,4)],2),n.message?(s(),a("p",{key:0,class:L(c.value)},d(n.message),3)):b("",!0)],2))}},Te=(n,o)=>{const l=n.__vccOpts||n;for(const[t,r]of o)l[t]=r;return l},Xs={class:"fixed bottom-0 right-0 z-50 p-6 space-y-4"},Zs={class:"p-4"},er={class:"flex items-start"},tr={class:"flex-shrink-0"},sr={class:"h-5 w-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},rr=["d"],ar={class:"ml-3 w-0 flex-1 pt-0.5"},or={class:"text-sm font-medium text-gray-900"},nr={class:"mt-1 text-sm text-gray-500"},ir={class:"ml-4 flex-shrink-0 flex"},lr=["onClick"],dr={__name:"NotificationManager",setup(n){const o=M([]);function l(x){const h=Date.now(),m={id:h,type:x.type||"info",title:x.title,message:x.message,duration:x.duration||5e3};o.value.push(m),m.duration>0&&setTimeout(()=>{t(h)},m.duration)}function t(x){const h=o.value.findIndex(m=>m.id===x);h>-1&&o.value.splice(h,1)}function r(x){const h={success:"border-l-4 border-green-400",error:"border-l-4 border-red-400",warning:"border-l-4 border-yellow-400",info:"border-l-4 border-blue-400"};return h[x]||h.info}function c(x){const h={success:"h-8 w-8 rounded-full bg-green-100 text-green-600 flex items-center justify-center",error:"h-8 w-8 rounded-full bg-red-100 text-red-600 flex items-center justify-center",warning:"h-8 w-8 rounded-full bg-yellow-100 text-yellow-600 flex items-center justify-center",info:"h-8 w-8 rounded-full bg-blue-100 text-blue-600 flex items-center justify-center"};return h[x]||h.info}function u(x){const h={success:"M5 13l4 4L19 7",error:"M6 18L18 6M6 6l12 12",warning:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-2.694-.833-3.464 0L3.34 16.5c-.77.833.192 2.5 1.732 2.5z",info:"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"};return h[x]||h.info}return window.showNotification=l,X(()=>{}),(x,h)=>(s(),a("div",Xs,[P(Ve,{name:"notification",tag:"div",class:"space-y-4"},{default:D(()=>[(s(!0),a(F,null,K(o.value,m=>(s(),a("div",{key:m.id,class:L([r(m.type),"max-w-sm w-full bg-white shadow-lg rounded-lg pointer-events-auto ring-1 ring-black ring-opacity-5 overflow-hidden"])},[e("div",Zs,[e("div",er,[e("div",tr,[e("div",{class:L(c(m.type))},[(s(),a("svg",sr,[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:u(m.type)},null,8,rr)]))],2)]),e("div",ar,[e("p",or,d(m.title),1),e("p",nr,d(m.message),1)]),e("div",ir,[e("button",{onClick:i=>t(m.id),class:"bg-white rounded-md inline-flex text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"},h[0]||(h[0]=[e("span",{class:"sr-only"},"Chiudi",-1),e("svg",{class:"h-5 w-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)]),8,lr)])])])],2))),128))]),_:1})]))}},cr=Te(dr,[["__scopeId","data-v-220f0827"]]),ur={class:"h-screen flex bg-gray-50 dark:bg-gray-900"},mr={class:"flex-1 overflow-y-auto bg-gray-50 dark:bg-gray-900"},pr={class:"py-6"},gr={class:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8"},vr={key:0,class:"mb-6"},hr={key:1,class:"flex items-center justify-center h-64"},fr={__name:"AppLayout",setup(n){const o=ue(),l=se(),t=ae(),r=M(!1),c=M(!1),u=M(!1);_(()=>t.config||{});const x=_(()=>t.config!==null),h=_(()=>{var E;return(E=o.meta)!=null&&E.title?o.meta.title:{dashboard:"Dashboard",projects:"Progetti","projects-list":"Elenco Progetti","projects-view":"Dettaglio Progetto","projects-create":"Nuovo Progetto",personnel:"Personale","personnel-directory":"Rubrica Aziendale","personnel-orgchart":"Organigramma","personnel-skills":"Competenze"}[o.name]||"DatPortal"}),m=_(()=>{var V;return(V=o.meta)!=null&&V.breadcrumbs?o.meta.breadcrumbs.map(E=>({label:E.label,to:E.to,icon:E.icon})):[]}),i=_(()=>{var V;return((V=o.meta)==null?void 0:V.hasActions)||!1});function g(){r.value=!r.value}function y(){r.value=!1}function w(V){c.value=V}function S(){l.push("/app/projects/create")}return oe(o,()=>{u.value=!0,setTimeout(()=>{u.value=!1},300)}),oe(o,()=>{y()}),X(()=>{x.value||t.loadConfig()}),(V,E)=>{const C=W("router-view");return s(),a("div",ur,[r.value?(s(),a("div",{key:0,onClick:y,class:"fixed inset-0 z-20 bg-black bg-opacity-50 lg:hidden"})):b("",!0),P(Rt,{"is-mobile-open":r.value,onClose:y,onToggleCollapsed:w},null,8,["is-mobile-open"]),e("div",{class:L(["flex flex-col flex-1 overflow-hidden transition-all duration-300",[c.value?"lg:ml-20":"lg:ml-64"]])},[P(Js,{"page-title":h.value,breadcrumbs:m.value,onToggleMobileSidebar:g,onQuickCreateProject:S},null,8,["page-title","breadcrumbs"]),e("main",mr,[e("div",pr,[e("div",gr,[i.value?(s(),a("div",vr,[Le(V.$slots,"page-actions")])):b("",!0),u.value?(s(),a("div",hr,[P(Ys)])):(s(),O(C,{key:2}))])])])],2),P(cr)])}}},xr={class:"min-h-screen bg-gray-50"},yr={class:"bg-white shadow-sm border-b"},_r={class:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8"},br={class:"flex justify-between h-16"},kr={class:"flex items-center"},wr={class:"w-8 h-8 bg-primary-600 rounded flex items-center justify-center"},$r={class:"text-white font-bold text-sm"},Cr={class:"text-xl font-semibold text-gray-900"},jr={class:"hidden md:flex items-center space-x-8"},Mr={class:"flex items-center space-x-4 ml-8 pl-8 border-l border-gray-200"},Pr={class:"md:hidden flex items-center"},zr={key:0,class:"md:hidden"},Sr={class:"px-2 pt-2 pb-3 space-y-1 sm:px-3 bg-white border-t"},Er={class:"bg-gray-800 text-white"},Ar={class:"max-w-7xl mx-auto py-12 px-4 sm:px-6 lg:px-8"},Tr={class:"grid grid-cols-1 md:grid-cols-4 gap-8"},Ir={class:"col-span-1 md:col-span-2"},Dr={class:"flex items-center space-x-3 mb-4"},Vr={class:"w-8 h-8 bg-primary-600 rounded flex items-center justify-center"},Lr={class:"text-white font-bold text-sm"},Br={class:"text-xl font-semibold"},qr={class:"text-gray-300 max-w-md"},Rr={class:"space-y-2"},Hr={class:"space-y-2 text-gray-300"},Ur={key:0},Nr={key:1},Fr={key:2},Or={class:"mt-8 pt-8 border-t border-gray-700 text-center text-gray-400"},Pe={__name:"PublicLayout",setup(n){const o=ae(),l=M(!1),t=_(()=>o.config||{}),r=_(()=>{var h;return((h=t.value.company)==null?void 0:h.name)||"DatVinci"}),c=_(()=>r.value.split(" ").map(m=>m[0]).join("").toUpperCase().slice(0,2)),u=_(()=>o.config!==null),x=new Date().getFullYear();return X(()=>{u.value||o.loadConfig()}),(h,m)=>{var y,w,S,V,E,C;const i=W("router-link"),g=W("router-view");return s(),a("div",xr,[e("nav",yr,[e("div",_r,[e("div",br,[e("div",kr,[P(i,{to:"/",class:"flex items-center space-x-3"},{default:D(()=>[e("div",wr,[e("span",$r,d(c.value),1)]),e("span",Cr,d(r.value),1)]),_:1})]),e("div",jr,[P(i,{to:"/",class:"text-gray-700 hover:text-primary-600 px-3 py-2 text-sm font-medium"},{default:D(()=>m[1]||(m[1]=[T(" Home ")])),_:1,__:[1]}),P(i,{to:"/about",class:"text-gray-700 hover:text-primary-600 px-3 py-2 text-sm font-medium"},{default:D(()=>m[2]||(m[2]=[T(" Chi Siamo ")])),_:1,__:[2]}),P(i,{to:"/services",class:"text-gray-700 hover:text-primary-600 px-3 py-2 text-sm font-medium"},{default:D(()=>m[3]||(m[3]=[T(" Servizi ")])),_:1,__:[3]}),P(i,{to:"/contact",class:"text-gray-700 hover:text-primary-600 px-3 py-2 text-sm font-medium"},{default:D(()=>m[4]||(m[4]=[T(" Contatti ")])),_:1,__:[4]}),e("div",Mr,[P(i,{to:"/auth/login",class:"text-gray-700 hover:text-primary-600 px-3 py-2 text-sm font-medium"},{default:D(()=>m[5]||(m[5]=[T(" Accedi ")])),_:1,__:[5]}),P(i,{to:"/auth/register",class:"bg-primary-600 text-white hover:bg-primary-700 px-4 py-2 rounded-md text-sm font-medium"},{default:D(()=>m[6]||(m[6]=[T(" Registrati ")])),_:1,__:[6]})])]),e("div",Pr,[e("button",{onClick:m[0]||(m[0]=U=>l.value=!l.value),class:"text-gray-400 hover:text-gray-500"},m[7]||(m[7]=[e("svg",{class:"h-6 w-6",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 6h16M4 12h16M4 18h16"})],-1)]))])])]),l.value?(s(),a("div",zr,[e("div",Sr,[P(i,{to:"/",class:"block px-3 py-2 text-base font-medium text-gray-700 hover:text-primary-600"},{default:D(()=>m[8]||(m[8]=[T(" Home ")])),_:1,__:[8]}),P(i,{to:"/about",class:"block px-3 py-2 text-base font-medium text-gray-700 hover:text-primary-600"},{default:D(()=>m[9]||(m[9]=[T(" Chi Siamo ")])),_:1,__:[9]}),P(i,{to:"/services",class:"block px-3 py-2 text-base font-medium text-gray-700 hover:text-primary-600"},{default:D(()=>m[10]||(m[10]=[T(" Servizi ")])),_:1,__:[10]}),P(i,{to:"/contact",class:"block px-3 py-2 text-base font-medium text-gray-700 hover:text-primary-600"},{default:D(()=>m[11]||(m[11]=[T(" Contatti ")])),_:1,__:[11]}),m[14]||(m[14]=e("hr",{class:"my-2"},null,-1)),P(i,{to:"/auth/login",class:"block px-3 py-2 text-base font-medium text-gray-700 hover:text-primary-600"},{default:D(()=>m[12]||(m[12]=[T(" Accedi ")])),_:1,__:[12]}),P(i,{to:"/auth/register",class:"block px-3 py-2 text-base font-medium bg-primary-600 text-white rounded-md"},{default:D(()=>m[13]||(m[13]=[T(" Registrati ")])),_:1,__:[13]})])])):b("",!0)]),e("main",null,[P(g)]),e("footer",Er,[e("div",Ar,[e("div",Tr,[e("div",Ir,[e("div",Dr,[e("div",Vr,[e("span",Lr,d(c.value),1)]),e("span",Br,d(r.value),1)]),e("p",qr,d(I(o).interpolateText((y=t.value.footer)==null?void 0:y.description)||((w=t.value.company)==null?void 0:w.description)||"Innovazione e tecnologia per il futuro digitale della tua azienda."),1)]),e("div",null,[m[19]||(m[19]=e("h3",{class:"text-sm font-semibold uppercase tracking-wider mb-4"},"Link Rapidi",-1)),e("ul",Rr,[e("li",null,[P(i,{to:"/",class:"text-gray-300 hover:text-white"},{default:D(()=>m[15]||(m[15]=[T("Home")])),_:1,__:[15]})]),e("li",null,[P(i,{to:"/about",class:"text-gray-300 hover:text-white"},{default:D(()=>m[16]||(m[16]=[T("Chi Siamo")])),_:1,__:[16]})]),e("li",null,[P(i,{to:"/services",class:"text-gray-300 hover:text-white"},{default:D(()=>m[17]||(m[17]=[T("Servizi")])),_:1,__:[17]})]),e("li",null,[P(i,{to:"/contact",class:"text-gray-300 hover:text-white"},{default:D(()=>m[18]||(m[18]=[T("Contatti")])),_:1,__:[18]})])])]),e("div",null,[m[20]||(m[20]=e("h3",{class:"text-sm font-semibold uppercase tracking-wider mb-4"},"Contatti",-1)),e("ul",Hr,[(S=t.value.contact)!=null&&S.email?(s(),a("li",Ur,d(t.value.contact.email),1)):b("",!0),(V=t.value.contact)!=null&&V.phone?(s(),a("li",Nr,d(t.value.contact.phone),1)):b("",!0),(E=t.value.contact)!=null&&E.address?(s(),a("li",Fr,d(t.value.contact.address),1)):b("",!0)])])]),e("div",Or,[e("p",null,d(I(o).interpolateText((C=t.value.footer)==null?void 0:C.copyright)||`© ${I(x)} ${r.value}. Tutti i diritti riservati.`),1)])])])])}}},Kr={class:"bg-white"},Gr={class:"relative overflow-hidden"},Wr={class:"max-w-7xl mx-auto"},Qr={class:"relative z-10 pb-8 bg-white sm:pb-16 md:pb-20 lg:max-w-2xl lg:w-full lg:pb-28 xl:pb-32"},Jr={class:"mt-10 mx-auto max-w-7xl px-4 sm:mt-12 sm:px-6 md:mt-16 lg:mt-20 lg:px-8 xl:mt-28"},Yr={class:"sm:text-center lg:text-left"},Xr={class:"text-4xl tracking-tight font-extrabold text-gray-900 sm:text-5xl md:text-6xl"},Zr={class:"block xl:inline"},ea={class:"mt-3 text-base text-gray-500 sm:mt-5 sm:text-lg sm:max-w-xl sm:mx-auto md:mt-5 md:text-xl lg:mx-0"},ta={class:"mt-5 sm:mt-8 sm:flex sm:justify-center lg:justify-start"},sa={class:"rounded-md shadow"},ra={class:"mt-3 sm:mt-0 sm:ml-3"},aa={class:"py-12 bg-white"},oa={class:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8"},na={class:"lg:text-center"},ia={class:"text-base text-primary-600 font-semibold tracking-wide uppercase"},la={class:"mt-2 text-3xl leading-8 font-extrabold tracking-tight text-gray-900 sm:text-4xl"},da={key:0,class:"mt-10"},ca={class:"space-y-10 md:space-y-0 md:grid md:grid-cols-2 md:gap-x-8 md:gap-y-10"},ua={class:"absolute flex items-center justify-center h-12 w-12 rounded-md bg-primary-500 text-white"},ma={class:"h-6 w-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},pa={key:0,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"},ga={key:1,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.25 2.25 0 11-4.5 0 2.25 2.25 0 014.5 0z"},va={key:2,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"},ha={key:3,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M13 10V3L4 14h7v7l9-11h-7z"},fa={class:"ml-16 text-lg leading-6 font-medium text-gray-900"},xa={class:"mt-2 ml-16 text-base text-gray-500"},ya={__name:"Home",setup(n){const o=ae(),l=_(()=>o.config||{}),t=_(()=>{var c;return((c=l.value.pages)==null?void 0:c.home)||{}}),r=_(()=>l.value.company||{});return X(()=>{o.config||o.loadConfig()}),(c,u)=>{var h,m,i,g;const x=W("router-link");return s(),a("div",Kr,[e("div",Gr,[e("div",Wr,[e("div",Qr,[e("main",Jr,[e("div",Yr,[e("h1",Xr,[e("span",Zr,d(((h=t.value.hero)==null?void 0:h.title)||"Innovazione per il futuro"),1)]),e("p",ea,d(((m=t.value.hero)==null?void 0:m.subtitle)||I(o).interpolateText(r.value.description)||"Supportiamo le aziende nel loro percorso di crescita attraverso soluzioni tecnologiche all'avanguardia"),1),e("div",ta,[e("div",sa,[P(x,{to:"/services",class:"w-full flex items-center justify-center px-8 py-3 border border-transparent text-base font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 md:py-4 md:text-lg md:px-10"},{default:D(()=>{var y;return[T(d(((y=t.value.hero)==null?void 0:y.cta_primary)||"Scopri i nostri servizi"),1)]}),_:1})]),e("div",ra,[P(x,{to:"/contact",class:"w-full flex items-center justify-center px-8 py-3 border border-transparent text-base font-medium rounded-md text-primary-700 bg-primary-100 hover:bg-primary-200 md:py-4 md:text-lg md:px-10"},{default:D(()=>{var y;return[T(d(((y=t.value.hero)==null?void 0:y.cta_secondary)||"Contattaci"),1)]}),_:1})])])])])])]),u[0]||(u[0]=e("div",{class:"lg:absolute lg:inset-y-0 lg:right-0 lg:w-1/2"},[e("div",{class:"h-56 w-full bg-gradient-to-r from-primary-400 to-primary-600 sm:h-72 md:h-96 lg:w-full lg:h-full flex items-center justify-center"},[e("svg",{class:"h-24 w-24 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"})])])],-1))]),e("div",aa,[e("div",oa,[e("div",na,[e("h2",ia,d(((i=t.value.services_section)==null?void 0:i.title)||"I nostri servizi"),1),e("p",la,d(((g=t.value.services_section)==null?void 0:g.subtitle)||"Soluzioni innovative per ogni esigenza aziendale"),1)]),r.value.platform_features?(s(),a("div",da,[e("div",ca,[(s(!0),a(F,null,K(r.value.platform_features,y=>(s(),a("div",{key:y.title,class:"relative"},[e("div",ua,[(s(),a("svg",ma,[y.icon==="briefcase"?(s(),a("path",pa)):y.icon==="users"?(s(),a("path",ga)):y.icon==="chart"?(s(),a("path",va)):(s(),a("path",ha))]))]),e("p",fa,d(y.title),1),e("p",xa,d(y.description),1)]))),128))])])):b("",!0)])])])}}},_a={class:"py-16 bg-white"},ba={class:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8"},ka={class:"text-center"},wa={class:"text-4xl font-extrabold text-gray-900 sm:text-5xl"},$a={class:"mt-4 text-xl text-gray-600"},Ca={key:0,class:"mt-16"},ja={class:"max-w-3xl mx-auto"},Ma={class:"text-3xl font-bold text-gray-900 text-center mb-8"},Pa={class:"text-lg text-gray-700 leading-relaxed"},za={class:"mt-16 grid grid-cols-1 md:grid-cols-2 gap-12"},Sa={key:0,class:"bg-gray-50 p-8 rounded-lg"},Ea={class:"text-2xl font-bold text-gray-900 mb-4"},Aa={class:"text-gray-700"},Ta={key:1,class:"bg-gray-50 p-8 rounded-lg"},Ia={class:"text-2xl font-bold text-gray-900 mb-4"},Da={class:"text-gray-700"},Va={key:1,class:"mt-16"},La={class:"text-center mb-12"},Ba={class:"text-3xl font-bold text-gray-900"},qa={class:"mt-4 text-xl text-gray-600"},Ra={class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8"},Ha={class:"text-lg font-semibold text-gray-900"},Ua={key:2,class:"mt-16"},Na={class:"text-center"},Fa={class:"text-3xl font-bold text-gray-900"},Oa={class:"mt-4 text-xl text-gray-600"},Ka={class:"mt-8 inline-flex items-center px-6 py-3 bg-primary-50 rounded-lg"},Ga={class:"text-primary-900 font-medium"},Wa={__name:"About",setup(n){const o=ae(),l=_(()=>o.config||{}),t=_(()=>{var c;return((c=l.value.pages)==null?void 0:c.about)||{}}),r=_(()=>l.value.company||{});return X(()=>{o.config||o.loadConfig()}),(c,u)=>{var x,h;return s(),a("div",_a,[e("div",ba,[e("div",ka,[e("h1",wa,d(((x=t.value.hero)==null?void 0:x.title)||"Chi Siamo"),1),e("p",$a,d(((h=t.value.hero)==null?void 0:h.subtitle)||"La nostra storia e i nostri valori"),1)]),t.value.story_section?(s(),a("div",Ca,[e("div",ja,[e("h2",Ma,d(t.value.story_section.title),1),e("p",Pa,d(I(o).interpolateText(t.value.story_section.content)),1)])])):b("",!0),e("div",za,[t.value.mission_section?(s(),a("div",Sa,[e("h3",Ea,d(t.value.mission_section.title),1),e("p",Aa,d(I(o).interpolateText(t.value.mission_section.content)),1)])):b("",!0),t.value.vision_section?(s(),a("div",Ta,[e("h3",Ia,d(t.value.vision_section.title),1),e("p",Da,d(I(o).interpolateText(t.value.vision_section.content)),1)])):b("",!0)]),t.value.expertise_section&&r.value.expertise?(s(),a("div",Va,[e("div",La,[e("h2",Ba,d(t.value.expertise_section.title),1),e("p",qa,d(t.value.expertise_section.subtitle),1)]),e("div",Ra,[(s(!0),a(F,null,K(r.value.expertise,m=>(s(),a("div",{key:m,class:"bg-white p-6 rounded-lg shadow-sm border border-gray-200 text-center"},[u[0]||(u[0]=e("div",{class:"w-12 h-12 bg-primary-100 rounded-lg flex items-center justify-center mx-auto mb-4"},[e("svg",{class:"w-6 h-6 text-primary-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M13 10V3L4 14h7v7l9-11h-7z"})])],-1)),e("h3",Ha,d(m),1)]))),128))])])):b("",!0),t.value.team_section?(s(),a("div",Ua,[e("div",Na,[e("h2",Fa,d(t.value.team_section.title),1),e("p",Oa,d(t.value.team_section.subtitle),1),e("div",Ka,[u[1]||(u[1]=e("svg",{class:"w-5 h-5 text-primary-600 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"})],-1)),e("span",Ga,d(r.value.team_size),1)])])])):b("",!0)])])}}},Qa={class:"py-16 bg-white"},Ja={class:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8"},Ya={class:"text-center"},Xa={class:"text-4xl font-extrabold text-gray-900 sm:text-5xl"},Za={class:"mt-4 text-xl text-gray-600"},eo={key:0,class:"mt-8 text-center"},to={class:"text-lg text-gray-700 max-w-3xl mx-auto"},so={class:"mt-16 grid grid-cols-1 lg:grid-cols-2 gap-16"},ro={key:0},ao={class:"text-2xl font-bold text-gray-900 mb-8"},oo={class:"block text-sm font-medium text-gray-700 mb-2"},no={class:"block text-sm font-medium text-gray-700 mb-2"},io={class:"block text-sm font-medium text-gray-700 mb-2"},lo=["disabled"],co={key:1},uo={class:"text-2xl font-bold text-gray-900 mb-8"},mo={class:"space-y-6"},po={key:0,class:"flex items-start"},go={class:"font-medium text-gray-900"},vo={class:"text-gray-600"},ho={key:1,class:"flex items-start"},fo={class:"font-medium text-gray-900"},xo={class:"text-gray-600"},yo={key:2,class:"flex items-start"},_o={class:"font-medium text-gray-900"},bo={class:"text-gray-600"},ko={key:3,class:"flex items-start"},wo={class:"font-medium text-gray-900"},$o={class:"text-gray-600"},Co={__name:"Contact",setup(n){const o=ae(),l=_(()=>o.config||{}),t=_(()=>{var m;return((m=l.value.pages)==null?void 0:m.contact)||{}}),r=_(()=>l.value.contact||{}),c=M({name:"",email:"",message:""}),u=M(!1),x=M({text:"",type:""}),h=async()=>{var m,i;if(!c.value.name||!c.value.email||!c.value.message){x.value={text:((m=t.value.form)==null?void 0:m.error_message)||"Tutti i campi sono obbligatori",type:"error"};return}u.value=!0,x.value={text:"",type:""};try{await new Promise(g=>setTimeout(g,1e3)),x.value={text:((i=t.value.form)==null?void 0:i.success_message)||"Messaggio inviato con successo!",type:"success"},c.value={name:"",email:"",message:""}}catch{x.value={text:"Errore durante l'invio. Riprova più tardi.",type:"error"}}finally{u.value=!1}};return X(()=>{o.config||o.loadConfig()}),(m,i)=>{var g,y;return s(),a("div",Qa,[e("div",Ja,[e("div",Ya,[e("h1",Xa,d(((g=t.value.hero)==null?void 0:g.title)||"Contattaci"),1),e("p",Za,d(((y=t.value.hero)==null?void 0:y.subtitle)||"Siamo qui per aiutarti"),1)]),t.value.intro?(s(),a("div",eo,[e("p",to,d(t.value.intro.content),1)])):b("",!0),e("div",so,[t.value.form?(s(),a("div",ro,[e("h2",ao,d(t.value.form.title),1),e("form",{onSubmit:ge(h,["prevent"]),class:"space-y-6"},[e("div",null,[e("label",oo,d(t.value.form.name_label),1),Q(e("input",{"onUpdate:modelValue":i[0]||(i[0]=w=>c.value.name=w),type:"text",required:"",class:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"},null,512),[[te,c.value.name]])]),e("div",null,[e("label",no,d(t.value.form.email_label),1),Q(e("input",{"onUpdate:modelValue":i[1]||(i[1]=w=>c.value.email=w),type:"email",required:"",class:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"},null,512),[[te,c.value.email]])]),e("div",null,[e("label",io,d(t.value.form.message_label),1),Q(e("textarea",{"onUpdate:modelValue":i[2]||(i[2]=w=>c.value.message=w),rows:"6",required:"",class:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"},null,512),[[te,c.value.message]])]),e("button",{type:"submit",disabled:u.value,class:"w-full bg-primary-600 text-white py-3 px-6 rounded-lg font-medium hover:bg-primary-700 disabled:opacity-50"},d(u.value?"Invio in corso...":t.value.form.submit_button),9,lo),x.value.text?(s(),a("div",{key:0,class:L([x.value.type==="success"?"text-green-600":"text-red-600","text-sm mt-2"])},d(x.value.text),3)):b("",!0)],32)])):b("",!0),t.value.info?(s(),a("div",co,[e("h2",uo,d(t.value.info.title),1),e("div",mo,[r.value.address?(s(),a("div",po,[i[3]||(i[3]=e("svg",{class:"w-6 h-6 text-primary-600 mt-1 mr-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"}),e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M15 11a3 3 0 11-6 0 3 3 0 016 0z"})],-1)),e("div",null,[e("h3",go,d(t.value.info.address_label),1),e("p",vo,d(r.value.address),1)])])):b("",!0),r.value.phone?(s(),a("div",ho,[i[4]||(i[4]=e("svg",{class:"w-6 h-6 text-primary-600 mt-1 mr-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"})],-1)),e("div",null,[e("h3",fo,d(t.value.info.phone_label),1),e("p",xo,d(r.value.phone),1)])])):b("",!0),r.value.email?(s(),a("div",yo,[i[5]||(i[5]=e("svg",{class:"w-6 h-6 text-primary-600 mt-1 mr-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"})],-1)),e("div",null,[e("h3",_o,d(t.value.info.email_label),1),e("p",bo,d(r.value.email),1)])])):b("",!0),r.value.hours?(s(),a("div",ko,[i[6]||(i[6]=e("svg",{class:"w-6 h-6 text-primary-600 mt-1 mr-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"})],-1)),e("div",null,[e("h3",wo,d(t.value.info.hours_label),1),e("p",$o,d(r.value.hours),1)])])):b("",!0)])])):b("",!0)])])])}}},jo={class:"py-16 bg-white"},Mo={class:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8"},Po={class:"text-center"},zo={class:"text-4xl font-extrabold text-gray-900 sm:text-5xl"},So={class:"mt-4 text-xl text-gray-600"},Eo={key:0,class:"mt-8 text-center"},Ao={class:"text-lg text-gray-700 max-w-3xl mx-auto"},To={key:1,class:"mt-16"},Io={class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8"},Do={class:"text-xl font-bold text-gray-900 text-center mb-4"},Vo={class:"text-gray-600 text-center"},Lo={key:2,class:"mt-20"},Bo={class:"bg-primary-50 rounded-2xl p-12 text-center"},qo={class:"text-3xl font-bold text-gray-900 mb-4"},Ro={class:"text-xl text-gray-600 mb-8"},Ho={__name:"Services",setup(n){const o=ae(),l=_(()=>o.config||{}),t=_(()=>{var u;return((u=l.value.pages)==null?void 0:u.services)||{}}),r=_(()=>l.value.company||{}),c=u=>({"Sviluppo Software":"Soluzioni software personalizzate per ottimizzare i processi aziendali e migliorare l'efficienza operativa.","Intelligenza Artificiale":"Implementazione di sistemi AI avanzati per automatizzare processi e analizzare dati complessi.","Consulenza IT":"Consulenza strategica per la trasformazione digitale e l'ottimizzazione dell'infrastruttura tecnologica.","Gestione Progetti Innovativi":"Coordinamento e gestione di progetti tecnologici complessi con metodologie agili.","Supporto su Bandi e Finanziamenti":"Assistenza nella ricerca e gestione di bandi pubblici e finanziamenti per l'innovazione."})[u]||"Servizio professionale di alta qualità per supportare la crescita della tua azienda.";return X(()=>{o.config||o.loadConfig()}),(u,x)=>{var m,i;const h=W("router-link");return s(),a("div",jo,[e("div",Mo,[e("div",Po,[e("h1",zo,d(((m=t.value.hero)==null?void 0:m.title)||"I nostri servizi"),1),e("p",So,d(((i=t.value.hero)==null?void 0:i.subtitle)||"Soluzioni complete per la tua azienda"),1)]),t.value.intro?(s(),a("div",Eo,[e("p",Ao,d(t.value.intro.content),1)])):b("",!0),r.value.expertise?(s(),a("div",To,[e("div",Io,[(s(!0),a(F,null,K(r.value.expertise,g=>(s(),a("div",{key:g,class:"bg-white p-8 rounded-lg shadow-lg border border-gray-200 hover:shadow-xl transition-shadow"},[x[0]||(x[0]=e("div",{class:"w-16 h-16 bg-primary-100 rounded-lg flex items-center justify-center mx-auto mb-6"},[e("svg",{class:"w-8 h-8 text-primary-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M13 10V3L4 14h7v7l9-11h-7z"})])],-1)),e("h3",Do,d(g),1),e("p",Vo,d(c(g)),1)]))),128))])])):b("",!0),t.value.cta?(s(),a("div",Lo,[e("div",Bo,[e("h2",qo,d(t.value.cta.title),1),e("p",Ro,d(t.value.cta.subtitle),1),P(h,{to:"/contact",class:"inline-flex items-center px-8 py-4 bg-primary-600 text-white font-medium rounded-lg hover:bg-primary-700 transition-colors"},{default:D(()=>[T(d(t.value.cta.button)+" ",1),x[1]||(x[1]=e("svg",{class:"ml-2 w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M17 8l4 4m0 0l-4 4m4-4H3"})],-1))]),_:1,__:[1]})])])):b("",!0)])])}}},Uo={class:"min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8"},No={class:"max-w-md w-full space-y-8"},Fo={class:"mt-2 text-center text-sm text-gray-600"},Oo={key:0,class:"rounded-md bg-red-50 p-4"},Ko={class:"text-sm text-red-700"},Go={class:"rounded-md shadow-sm -space-y-px"},Wo={class:"flex items-center justify-between"},Qo={class:"flex items-center"},Jo=["disabled"],Yo={key:0,class:"absolute left-0 inset-y-0 flex items-center pl-3"},Xo={__name:"Login",setup(n){const o=se(),l=re(),t=M({username:"",password:"",remember:!1}),r=_(()=>l.loading),c=_(()=>l.error);async function u(){(await l.login({username:t.value.username,password:t.value.password,remember:t.value.remember})).success&&o.push("/app/dashboard")}return(x,h)=>{const m=W("router-link");return s(),a("div",Uo,[e("div",No,[e("div",null,[h[5]||(h[5]=e("div",{class:"mx-auto h-12 w-12 flex items-center justify-center rounded-full bg-primary-100"},[e("svg",{class:"h-6 w-6 text-primary-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"})])],-1)),h[6]||(h[6]=e("h2",{class:"mt-6 text-center text-3xl font-extrabold text-gray-900"}," Accedi al tuo account ",-1)),e("p",Fo,[h[4]||(h[4]=T(" Oppure ")),P(m,{to:"/auth/register",class:"font-medium text-primary-600 hover:text-primary-500"},{default:D(()=>h[3]||(h[3]=[T(" registrati per un nuovo account ")])),_:1,__:[3]})])]),e("form",{onSubmit:ge(u,["prevent"]),class:"mt-8 space-y-6"},[c.value?(s(),a("div",Oo,[e("div",Ko,d(c.value),1)])):b("",!0),e("div",Go,[e("div",null,[h[7]||(h[7]=e("label",{for:"username",class:"sr-only"},"Username",-1)),Q(e("input",{id:"username","onUpdate:modelValue":h[0]||(h[0]=i=>t.value.username=i),name:"username",type:"text",autocomplete:"username",required:"",class:"appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-t-md focus:outline-none focus:ring-primary-500 focus:border-primary-500 focus:z-10 sm:text-sm",placeholder:"Username"},null,512),[[te,t.value.username]])]),e("div",null,[h[8]||(h[8]=e("label",{for:"password",class:"sr-only"},"Password",-1)),Q(e("input",{id:"password","onUpdate:modelValue":h[1]||(h[1]=i=>t.value.password=i),name:"password",type:"password",autocomplete:"current-password",required:"",class:"appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-b-md focus:outline-none focus:ring-primary-500 focus:border-primary-500 focus:z-10 sm:text-sm",placeholder:"Password"},null,512),[[te,t.value.password]])])]),e("div",Wo,[e("div",Qo,[Q(e("input",{id:"remember-me","onUpdate:modelValue":h[2]||(h[2]=i=>t.value.remember=i),name:"remember-me",type:"checkbox",class:"h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"},null,512),[[Be,t.value.remember]]),h[9]||(h[9]=e("label",{for:"remember-me",class:"ml-2 block text-sm text-gray-900"}," Ricordami ",-1))]),h[10]||(h[10]=e("div",{class:"text-sm"},[e("a",{href:"#",class:"font-medium text-primary-600 hover:text-primary-500"}," Password dimenticata? ")],-1))]),e("div",null,[e("button",{type:"submit",disabled:r.value,class:"group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50 disabled:cursor-not-allowed"},[r.value?(s(),a("span",Yo,h[11]||(h[11]=[e("svg",{class:"h-5 w-5 text-primary-500 animate-spin",fill:"none",viewBox:"0 0 24 24"},[e("circle",{class:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor","stroke-width":"4"}),e("path",{class:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})],-1)]))):b("",!0),T(" "+d(r.value?"Accesso in corso...":"Accedi"),1)],8,Jo)])],32)])])}}},Zo={},en={class:"min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8"};function tn(n,o){return s(),a("div",en,o[0]||(o[0]=[e("div",{class:"max-w-md w-full space-y-8"},[e("div",null,[e("h2",{class:"mt-6 text-center text-3xl font-extrabold text-gray-900"}," Registra un nuovo account ")]),e("div",{class:"text-center text-gray-600"}," Registrazione in arrivo... ")],-1)]))}const sn=Te(Zo,[["render",tn]]),rn={class:"bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg"},an={class:"p-5"},on={class:"flex items-center"},nn={class:"ml-5 w-0 flex-1"},ln={class:"text-sm font-medium text-gray-500 dark:text-gray-400 truncate"},dn={class:"text-lg font-medium text-gray-900 dark:text-white"},cn={key:0,class:"text-xs text-gray-500 dark:text-gray-400"},un={key:0,class:"bg-gray-50 dark:bg-gray-700 px-5 py-3"},mn={class:"text-sm"},le={__name:"StatsCard",props:{title:{type:String,required:!0},value:{type:[Number,String],required:!0},subtitle:{type:String,default:null},icon:{type:String,required:!0},color:{type:String,default:"primary"},link:{type:String,default:null}},setup(n){const o=t=>t==="project"?{template:`<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
      </svg>`}:t==="users"?{template:`<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
      </svg>`}:t==="clock"?{template:`<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
      </svg>`}:t==="team"?{template:`<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
      </svg>`}:{template:`<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
    </svg>`},l=t=>{const r={primary:"bg-primary-500",secondary:"bg-secondary-500",red:"bg-red-500",yellow:"bg-yellow-500",blue:"bg-blue-500",green:"bg-green-500"};return r[t]||r.primary};return(t,r)=>{const c=W("router-link");return s(),a("div",rn,[e("div",an,[e("div",on,[e("div",{class:L(["flex-shrink-0 rounded-md p-3",l(n.color)])},[(s(),O(Se(o(n.icon)),{class:"h-6 w-6 text-white"}))],2),e("div",nn,[e("dl",null,[e("dt",ln,d(n.title),1),e("dd",null,[e("div",dn,d(n.value),1),n.subtitle?(s(),a("div",cn,d(n.subtitle),1)):b("",!0)])])])])]),n.link?(s(),a("div",un,[e("div",mn,[P(c,{to:n.link,class:"font-medium text-primary-600 dark:text-primary-400 hover:text-primary-500 dark:hover:text-primary-300"},{default:D(()=>r[0]||(r[0]=[T(" Vedi tutti ")])),_:1,__:[0]},8,["to"])])])):b("",!0)])}}},pn={class:"py-6"},gn={class:"flex flex-col md:flex-row md:items-center md:justify-between mb-8"},vn={class:"mt-4 md:mt-0 flex space-x-3"},hn={class:"relative"},fn=["disabled"],xn={class:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 mb-8"},yn={class:"grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8"},_n={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6"},bn={class:"relative h-64"},kn={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6"},wn={class:"relative h-64"},$n={class:"grid grid-cols-1 lg:grid-cols-3 gap-6"},Cn={class:"bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden"},jn={class:"p-6"},Mn={key:0,class:"text-center py-8 text-gray-500"},Pn={key:1,class:"space-y-4"},zn={class:"flex justify-between items-start"},Sn={class:"flex-1"},En={class:"text-sm font-medium text-gray-900 dark:text-white"},An={class:"text-xs text-gray-500 dark:text-gray-400"},Tn={class:"mt-2 flex justify-between items-center"},In={class:"text-xs text-gray-500 dark:text-gray-400"},Dn={class:"bg-gray-50 dark:bg-gray-700 px-6 py-3"},Vn={class:"text-sm"},Ln={class:"bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden"},Bn={class:"p-6"},qn={key:0,class:"text-center py-8 text-gray-500"},Rn={key:1,class:"space-y-4"},Hn={class:"flex-shrink-0"},Un={class:"flex-1 min-w-0"},Nn={class:"text-sm font-medium text-gray-900 dark:text-white"},Fn={class:"text-xs text-gray-500 dark:text-gray-400"},On={class:"text-xs text-gray-400 dark:text-gray-500"},Kn={class:"bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden"},Gn={class:"p-6"},Wn={key:0,class:"text-center py-8 text-gray-500"},Qn={key:1,class:"space-y-4"},Jn={class:"flex justify-between items-start"},Yn={class:"flex-1"},Xn={class:"text-sm font-medium text-gray-900 dark:text-white"},Zn={class:"text-xs text-gray-500 dark:text-gray-400"},ei={class:"text-right"},ti={class:"text-sm font-bold text-gray-900 dark:text-white"},si={class:"text-xs text-gray-500"},ri={class:"mt-2"},ai={class:"w-full bg-gray-200 rounded-full h-2"},oi={class:"text-xs text-gray-500 mt-1"},ni={__name:"Dashboard",setup(n){me.register(...qe),se(),re();const o=M(!1),l=M("7"),t=M({}),r=M([]),c=M([]),u=M([]),x=M(null),h=M(null);let m=null,i=null;const g=async()=>{try{const v=await fetch("/api/dashboard/stats");if(!v.ok)throw new Error("Failed to fetch stats");const p=await v.json();t.value=p.data}catch(v){console.error("Error fetching dashboard stats:",v),t.value={}}},y=async()=>{try{const v=await fetch(`/api/dashboard/upcoming-tasks?days=${l.value}&limit=5`);if(!v.ok)throw new Error("Failed to fetch upcoming tasks");const p=await v.json();r.value=p.data.tasks}catch(v){console.error("Error fetching upcoming tasks:",v),r.value=[]}},w=async()=>{try{const v=await fetch("/api/dashboard/recent-activities?limit=5");if(!v.ok)throw new Error("Failed to fetch recent activities");const p=await v.json();c.value=p.data.activities}catch(v){console.error("Error fetching recent activities:",v),c.value=[]}},S=async()=>{try{const v=await fetch("/api/dashboard/kpis?limit=3");if(!v.ok)throw new Error("Failed to fetch KPIs");const p=await v.json();u.value=p.data.kpis}catch(v){console.error("Error fetching KPIs:",v),u.value=[]}},V=async()=>{try{const v=await fetch("/api/dashboard/charts/project-status");if(!v.ok)throw new Error("Failed to fetch project chart data");const p=await v.json();C(p.data.chart)}catch(v){console.error("Error fetching project chart:",v)}},E=async()=>{try{const v=await fetch("/api/dashboard/charts/task-status");if(!v.ok)throw new Error("Failed to fetch task chart data");const p=await v.json();U(p.data.chart)}catch(v){console.error("Error fetching task chart:",v)}},C=v=>{if(!x.value)return;const p=x.value.getContext("2d");m&&m.destroy(),m=new me(p,{type:"doughnut",data:{labels:v.labels,datasets:[{data:v.data,backgroundColor:["#3B82F6","#10B981","#F59E0B","#EF4444","#8B5CF6"],borderWidth:2,borderColor:"#ffffff"}]},options:{responsive:!0,maintainAspectRatio:!1,plugins:{legend:{position:"bottom",labels:{padding:20,usePointStyle:!0}}}}})},U=v=>{if(!h.value)return;const p=h.value.getContext("2d");i&&i.destroy(),i=new me(p,{type:"bar",data:{labels:v.labels,datasets:[{label:"Tasks",data:v.data,backgroundColor:["#60A5FA","#34D399","#FBBF24","#F87171"],borderColor:["#3B82F6","#10B981","#F59E0B","#EF4444"],borderWidth:1}]},options:{responsive:!0,maintainAspectRatio:!1,plugins:{legend:{display:!1}},scales:{y:{beginAtZero:!0,ticks:{stepSize:1}}}}})},z=async()=>{o.value=!0;try{await Promise.all([g(),y(),w(),S(),V(),E()])}finally{o.value=!1}},k=v=>new Date(v).toLocaleDateString("it-IT"),$=v=>{const p=new Date(v),j=Math.floor((new Date-p)/(1e3*60));return j<60?`${j} minuti fa`:j<1440?`${Math.floor(j/60)} ore fa`:`${Math.floor(j/1440)} giorni fa`},f=v=>{const p={high:"bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200",medium:"bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200",low:"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200"};return p[v]||p.medium},A=v=>{const p={todo:"bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300","in-progress":"bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200",review:"bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200",done:"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200"};return p[v]||p.todo},q=v=>{const p={task:{template:`<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
      </svg>`},timesheet:{template:`<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
      </svg>`},event:{template:`<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
      </svg>`}};return p[v]||p.task},B=v=>{const p={task:"bg-blue-100 text-blue-600",timesheet:"bg-green-100 text-green-600",event:"bg-purple-100 text-purple-600"};return p[v]||p.task},R=v=>v>=90?"bg-green-500":v>=70?"bg-yellow-500":"bg-red-500";return X(async()=>{await z(),await ze(),x.value&&h.value&&(await V(),await E())}),(v,p)=>{var j,Z,fe,xe,ye,_e,be,ke;const N=W("router-link");return s(),a("div",pn,[e("div",gn,[p[4]||(p[4]=e("div",null,[e("h1",{class:"text-2xl font-bold text-gray-900 dark:text-white"},"Dashboard"),e("p",{class:"mt-1 text-sm text-gray-600 dark:text-gray-400"}," Benvenuto! Ecco una panoramica delle attività della tua azienda. ")],-1)),e("div",vn,[e("div",hn,[Q(e("select",{"onUpdate:modelValue":p[0]||(p[0]=H=>l.value=H),onChange:z,class:"bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-md py-2 pl-3 pr-10 text-sm font-medium text-gray-700 dark:text-gray-300 focus:outline-none focus:ring-primary-500 focus:border-primary-500"},p[1]||(p[1]=[e("option",{value:"7"},"Ultimi 7 giorni",-1),e("option",{value:"30"},"Ultimo mese",-1),e("option",{value:"90"},"Ultimi 3 mesi",-1)]),544),[[ne,l.value]])]),e("button",{onClick:z,disabled:o.value,type:"button",class:"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50"},[(s(),a("svg",{xmlns:"http://www.w3.org/2000/svg",class:L(["h-4 w-4 mr-2",{"animate-spin":o.value}]),viewBox:"0 0 20 20",fill:"currentColor"},p[2]||(p[2]=[e("path",{"fill-rule":"evenodd",d:"M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z","clip-rule":"evenodd"},null,-1)]),2)),p[3]||(p[3]=T(" Aggiorna "))],8,fn)])]),e("div",xn,[P(le,{title:"Progetti Attivi",value:((j=t.value.projects)==null?void 0:j.active)||0,subtitle:`di ${((Z=t.value.projects)==null?void 0:Z.total)||0} totali`,icon:"project",color:"primary",link:"/app/projects?status=active"},null,8,["value","subtitle"]),P(le,{title:"Clienti",value:((fe=t.value.team)==null?void 0:fe.clients)||0,icon:"users",color:"secondary",link:"/app/crm/clients"},null,8,["value"]),P(le,{title:"Task Pendenti",value:((xe=t.value.tasks)==null?void 0:xe.pending)||0,subtitle:`${((ye=t.value.tasks)==null?void 0:ye.overdue)||0} in ritardo`,icon:"clock",color:((_e=t.value.tasks)==null?void 0:_e.overdue)>0?"red":"yellow",link:"/app/tasks?status=pending"},null,8,["value","subtitle","color"]),P(le,{title:"Team Members",value:((be=t.value.team)==null?void 0:be.users)||0,subtitle:`${((ke=t.value.team)==null?void 0:ke.departments)||0} dipartimenti`,icon:"team",color:"blue",link:"/app/personnel"},null,8,["value","subtitle"])]),e("div",yn,[e("div",_n,[p[5]||(p[5]=e("div",{class:"flex items-center justify-between mb-4"},[e("h2",{class:"text-lg font-medium text-gray-900 dark:text-white"},"Stato Progetti")],-1)),e("div",bn,[e("canvas",{ref_key:"projectChart",ref:x},null,512)])]),e("div",kn,[p[6]||(p[6]=e("div",{class:"flex items-center justify-between mb-4"},[e("h2",{class:"text-lg font-medium text-gray-900 dark:text-white"},"Stato Attività")],-1)),e("div",wn,[e("canvas",{ref_key:"taskChart",ref:h},null,512)])])]),e("div",$n,[e("div",Cn,[e("div",jn,[p[7]||(p[7]=e("h2",{class:"text-lg font-medium text-gray-900 dark:text-white mb-4"},"Attività in Scadenza",-1)),r.value.length===0?(s(),a("div",Mn," Nessuna attività in scadenza ")):(s(),a("div",Pn,[(s(!0),a(F,null,K(r.value,H=>(s(),a("div",{key:H.id,class:"border-b border-gray-200 dark:border-gray-700 pb-4 last:border-b-0 last:pb-0"},[e("div",zn,[e("div",Sn,[e("h3",En,d(H.name),1),e("p",An,d(H.project_name),1)]),e("span",{class:L(["inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium",f(H.priority)])},d(H.priority),3)]),e("div",Tn,[e("span",In," Scadenza: "+d(k(H.due_date)),1),e("span",{class:L(["inline-flex items-center px-2 py-1 rounded-full text-xs font-medium",A(H.status)])},d(H.status),3)])]))),128))]))]),e("div",Dn,[e("div",Vn,[P(N,{to:"/app/tasks",class:"font-medium text-primary-600 dark:text-primary-400 hover:text-primary-500"},{default:D(()=>p[8]||(p[8]=[T(" Vedi tutte le attività ")])),_:1,__:[8]})])])]),e("div",Ln,[e("div",Bn,[p[9]||(p[9]=e("h2",{class:"text-lg font-medium text-gray-900 dark:text-white mb-4"},"Attività Recenti",-1)),c.value.length===0?(s(),a("div",qn," Nessuna attività recente ")):(s(),a("div",Rn,[(s(!0),a(F,null,K(c.value,H=>(s(),a("div",{key:`${H.type}-${H.id}`,class:"flex items-start space-x-3"},[e("div",Hn,[e("div",{class:L(["w-8 h-8 rounded-full flex items-center justify-center",B(H.type)])},[(s(),O(Se(q(H.type)),{class:"w-4 h-4"}))],2)]),e("div",Un,[e("p",Nn,d(H.title),1),e("p",Fn,d(H.description),1),e("p",On,d($(H.timestamp)),1)])]))),128))]))])]),e("div",Kn,[e("div",Gn,[p[10]||(p[10]=e("h2",{class:"text-lg font-medium text-gray-900 dark:text-white mb-4"},"KPIs Principali",-1)),u.value.length===0?(s(),a("div",Wn," Nessun KPI configurato ")):(s(),a("div",Qn,[(s(!0),a(F,null,K(u.value,H=>(s(),a("div",{key:H.id,class:"border-b border-gray-200 dark:border-gray-700 pb-4 last:border-b-0 last:pb-0"},[e("div",Jn,[e("div",Yn,[e("h3",Xn,d(H.name),1),e("p",Zn,d(H.description),1)]),e("div",ei,[e("p",ti,d(H.current_value)+d(H.unit),1),e("p",si," Target: "+d(H.target_value)+d(H.unit),1)])]),e("div",ri,[e("div",ai,[e("div",{class:L(["h-2 rounded-full",R(H.performance_percentage)]),style:ve({width:Math.min(H.performance_percentage,100)+"%"})},null,6)]),e("p",oi,d(Math.round(H.performance_percentage))+"% del target",1)])]))),128))]))])])])])}}},ii=ce("projects",()=>{const n=M([]),o=M(null),l=M(!1),t=M(null),r=M(new Map),c=M({page:1,perPage:20,total:0,totalPages:0}),u=M({search:"",status:"",client:"",type:""}),x=_(()=>{let f=n.value;if(u.value.search){const A=u.value.search.toLowerCase();f=f.filter(q=>{var B,R,v;return q.name.toLowerCase().includes(A)||((B=q.description)==null?void 0:B.toLowerCase().includes(A))||((v=(R=q.client)==null?void 0:R.name)==null?void 0:v.toLowerCase().includes(A))})}return u.value.status&&(f=f.filter(A=>A.status===u.value.status)),u.value.client&&(f=f.filter(A=>A.client_id===u.value.client)),u.value.type&&(f=f.filter(A=>A.project_type===u.value.type)),f}),h=_(()=>{const f={};return n.value.forEach(A=>{f[A.status]||(f[A.status]=[]),f[A.status].push(A)}),f}),m=async(f={})=>{var A,q;l.value=!0,t.value=null;try{const B=new URLSearchParams({page:f.page||c.value.page,per_page:f.perPage||c.value.perPage,search:f.search||u.value.search,status:f.status||u.value.status,client:f.client||u.value.client,type:f.type||u.value.type}),R=await J.get(`/api/projects?${B}`);R.data.success&&(n.value=R.data.data.projects,c.value=R.data.data.pagination)}catch(B){t.value=((q=(A=B.response)==null?void 0:A.data)==null?void 0:q.message)||"Errore nel caricamento progetti",console.error("Error fetching projects:",B)}finally{l.value=!1}},i=async(f,A=!1)=>{var q,B;if(!A&&r.value.has(f)){const R=r.value.get(f);return o.value=R,R}l.value=!0,t.value=null;try{const R=await J.get(`/api/projects/${f}`);if(R.data.success){const v=R.data.data.project;return o.value=v,r.value.set(f,v),v}}catch(R){throw t.value=((B=(q=R.response)==null?void 0:q.data)==null?void 0:B.message)||"Errore nel caricamento progetto",console.error("Error fetching project:",R),R}finally{l.value=!1}};return{projects:n,currentProject:o,loading:l,error:t,pagination:c,filters:u,filteredProjects:x,projectsByStatus:h,fetchProjects:m,fetchProject:i,createProject:async f=>{var A,q;l.value=!0,t.value=null;try{const B=await J.post("/api/projects",f);if(B.data.success){const R=B.data.data.project;return n.value.unshift(R),R}}catch(B){throw t.value=((q=(A=B.response)==null?void 0:A.data)==null?void 0:q.message)||"Errore nella creazione progetto",console.error("Error creating project:",B),B}finally{l.value=!1}},updateProject:async(f,A)=>{var q,B,R;l.value=!0,t.value=null;try{const v=await J.put(`/api/projects/${f}`,A);if(v.data.success){const p=v.data.data.project,N=n.value.findIndex(j=>j.id===f);return N!==-1&&(n.value[N]=p),((q=o.value)==null?void 0:q.id)===f&&(o.value=p),r.value.set(f,p),p}}catch(v){throw t.value=((R=(B=v.response)==null?void 0:B.data)==null?void 0:R.message)||"Errore nell'aggiornamento progetto",console.error("Error updating project:",v),v}finally{l.value=!1}},deleteProject:async f=>{var A,q,B;l.value=!0,t.value=null;try{(await J.delete(`/api/projects/${f}`)).data.success&&(n.value=n.value.filter(v=>v.id!==f),((A=o.value)==null?void 0:A.id)===f&&(o.value=null),r.value.delete(f))}catch(R){throw t.value=((B=(q=R.response)==null?void 0:q.data)==null?void 0:B.message)||"Errore nell'eliminazione progetto",console.error("Error deleting project:",R),R}finally{l.value=!1}},setFilters:f=>{u.value={...u.value,...f}},clearFilters:()=>{u.value={search:"",status:"",client:"",type:""}},setCurrentProject:f=>{o.value=f},clearCurrentProject:()=>{o.value=null},clearCache:()=>{r.value.clear()},refreshProject:async f=>await i(f,!0),getCachedProject:f=>r.value.get(f),$reset:()=>{n.value=[],o.value=null,l.value=!1,t.value=null,r.value.clear(),c.value={page:1,perPage:20,total:0,totalPages:0},u.value={search:"",status:"",client:"",type:""}}}}),li={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6 mb-6"},di={class:"grid grid-cols-1 md:grid-cols-4 gap-4"},ci=["value"],ui={class:"bg-white dark:bg-gray-800 shadow rounded-lg"},mi={class:"px-6 py-4 border-b border-gray-200 dark:border-gray-700"},pi={class:"text-lg font-medium text-gray-900 dark:text-white"},gi={key:0,class:"p-6 text-center"},vi={key:1,class:"p-6 text-center"},hi={key:2,class:"divide-y divide-gray-200 dark:divide-gray-700"},fi=["onClick"],xi={class:"flex items-center justify-between"},yi={class:"flex-1"},_i={class:"flex items-center"},bi={class:"text-lg font-medium text-gray-900 dark:text-white"},ki={class:"mt-1 text-sm text-gray-600 dark:text-gray-400"},wi={class:"mt-2 flex items-center text-sm text-gray-500 dark:text-gray-400"},$i={key:0},Ci={key:1,class:"mx-2"},ji={key:2},Mi={key:3,class:"mx-2"},Pi={key:4},zi={class:"ml-4 flex items-center space-x-2"},Si={class:"text-right"},Ei={class:"text-sm font-medium text-gray-900 dark:text-white"},Ai={class:"w-20 bg-gray-200 dark:bg-gray-600 rounded-full h-2 mt-1"},Ti={__name:"Projects",setup(n){const o=se(),l=ii(),t=M(!0),r=M(""),c=M({status:"",client:""}),u=_(()=>l.projects),x=M([]),h=_(()=>{let k=u.value;if(c.value.status&&(k=k.filter($=>$.status===c.value.status)),c.value.client&&(k=k.filter($=>$.client_id==c.value.client)),r.value){const $=r.value.toLowerCase();k=k.filter(f=>f.name.toLowerCase().includes($)||f.description&&f.description.toLowerCase().includes($)||f.client&&f.client.name&&f.client.name.toLowerCase().includes($))}return k}),m=async()=>{t.value=!0;try{await l.fetchProjects(),x.value=[]}catch(k){console.error("Error loading projects:",k)}finally{t.value=!1}},i=()=>{},g=()=>{},y=()=>{c.value={status:"",client:""},r.value=""},w=()=>{o.push("/app/projects/create")},S=k=>{o.push(`/app/projects/${k}`)},V=k=>({planning:"bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400",active:"bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400",completed:"bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400","on-hold":"bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400"})[k]||"bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400",E=k=>({planning:"Pianificazione",active:"Attivo",completed:"Completato","on-hold":"In Pausa"})[k]||k,C=k=>new Date(k).toLocaleDateString("it-IT"),U=k=>new Intl.NumberFormat("it-IT",{style:"currency",currency:"EUR"}).format(k),z=k=>({planning:10,active:50,completed:100,"on-hold":25})[k.status]||0;return X(()=>{m()}),(k,$)=>(s(),a("div",null,[e("div",{class:"mb-6"},[e("div",{class:"flex flex-col sm:flex-row sm:items-center sm:justify-between"},[$[4]||($[4]=e("div",null,[e("h1",{class:"text-2xl font-bold text-gray-900 dark:text-white"},"Progetti"),e("p",{class:"mt-1 text-sm text-gray-500 dark:text-gray-400"}," Gestisci e monitora tutti i progetti aziendali ")],-1)),e("div",{class:"mt-4 sm:mt-0"},[e("button",{onClick:w,class:"inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"},$[3]||($[3]=[e("svg",{class:"h-4 w-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 4v16m8-8H4"})],-1),T(" Nuovo Progetto ")]))])])]),e("div",li,[e("div",di,[e("div",null,[$[6]||($[6]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"},"Stato",-1)),Q(e("select",{"onUpdate:modelValue":$[0]||($[0]=f=>c.value.status=f),onChange:g,class:"w-full border border-gray-300 dark:border-gray-600 rounded-md px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary-500"},$[5]||($[5]=[Re('<option value="">Tutti gli stati</option><option value="planning">Pianificazione</option><option value="active">Attivo</option><option value="completed">Completato</option><option value="on-hold">In Pausa</option>',5)]),544),[[ne,c.value.status]])]),e("div",null,[$[8]||($[8]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"},"Cliente",-1)),Q(e("select",{"onUpdate:modelValue":$[1]||($[1]=f=>c.value.client=f),onChange:g,class:"w-full border border-gray-300 dark:border-gray-600 rounded-md px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary-500"},[$[7]||($[7]=e("option",{value:""},"Tutti i clienti",-1)),(s(!0),a(F,null,K(x.value,f=>(s(),a("option",{key:f.id,value:f.id},d(f.name),9,ci))),128))],544),[[ne,c.value.client]])]),e("div",null,[$[9]||($[9]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"},"Ricerca",-1)),Q(e("input",{"onUpdate:modelValue":$[2]||($[2]=f=>r.value=f),onInput:i,type:"text",placeholder:"Cerca progetti...",class:"w-full border border-gray-300 dark:border-gray-600 rounded-md px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary-500"},null,544),[[te,r.value]])]),e("div",{class:"flex items-end"},[e("button",{onClick:y,class:"w-full px-4 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600"}," Reset Filtri ")])])]),e("div",ui,[e("div",mi,[e("h3",pi," Progetti ("+d(h.value.length)+") ",1)]),t.value?(s(),a("div",gi,$[10]||($[10]=[e("div",{class:"animate-spin rounded-full h-8 w-8 border-b-2 border-primary-500 mx-auto"},null,-1),e("p",{class:"mt-2 text-gray-600 dark:text-gray-400"},"Caricamento progetti...",-1)]))):h.value.length===0?(s(),a("div",vi,$[11]||($[11]=[e("svg",{class:"mx-auto h-12 w-12 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})],-1),e("h3",{class:"mt-2 text-sm font-medium text-gray-900 dark:text-white"},"Nessun progetto",-1),e("p",{class:"mt-1 text-sm text-gray-500 dark:text-gray-400"},"Inizia creando il tuo primo progetto.",-1)]))):(s(),a("div",hi,[(s(!0),a(F,null,K(h.value,f=>(s(),a("div",{key:f.id,class:"p-6 hover:bg-gray-50 dark:hover:bg-gray-700 cursor-pointer",onClick:A=>S(f.id)},[e("div",xi,[e("div",yi,[e("div",_i,[e("h4",bi,d(f.name),1),e("span",{class:L([V(f.status),"ml-3 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"])},d(E(f.status)),3)]),e("p",ki,d(f.description),1),e("div",wi,[f.client?(s(),a("span",$i,"Cliente: "+d(f.client.name),1)):b("",!0),f.client?(s(),a("span",Ci,"•")):b("",!0),f.end_date?(s(),a("span",ji,"Scadenza: "+d(C(f.end_date)),1)):b("",!0),f.end_date&&f.budget?(s(),a("span",Mi,"•")):b("",!0),f.budget?(s(),a("span",Pi,"Budget: "+d(U(f.budget)),1)):b("",!0)])]),e("div",zi,[e("div",Si,[e("div",Ei,d(z(f))+"% ",1),e("div",Ai,[e("div",{class:"bg-primary-600 h-2 rounded-full",style:ve({width:z(f)+"%"})},null,4)])]),$[12]||($[12]=e("svg",{class:"h-5 w-5 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 5l7 7-7 7"})],-1))])])],8,fi))),128))]))])]))}},Ii=ce("personnel",()=>{const n=M([]),o=M([]),l=M([]),t=M(null),r=M(!1),c=M(null),u=M({search:"",department:null,skill:null,role:null,location:null,sort:"name"}),x=M({page:1,per_page:20,total:0,pages:0}),h=_(()=>{let z=n.value;if(u.value.search){const k=u.value.search.toLowerCase();z=z.filter($=>{var f,A,q;return((f=$.full_name)==null?void 0:f.toLowerCase().includes(k))||((A=$.email)==null?void 0:A.toLowerCase().includes(k))||((q=$.position)==null?void 0:q.toLowerCase().includes(k))})}return u.value.department&&(z=z.filter(k=>k.department_id===u.value.department)),u.value.skill&&(z=z.filter(k=>{var $;return($=k.skills)==null?void 0:$.some(f=>f.id===u.value.skill)})),u.value.role&&(z=z.filter(k=>k.role===u.value.role)),z}),m=_(()=>{const z=(k=null)=>o.value.filter($=>$.parent_id===k).map($=>({...$,children:z($.id)}));return z()}),i=async(z={})=>{r.value=!0,c.value=null;try{const k=new URLSearchParams({page:z.page||x.value.page,per_page:z.per_page||x.value.per_page,...z}),$=await fetch(`/api/personnel/users?${k}`,{credentials:"include"});if(!$.ok)throw new Error(`HTTP ${$.status}: ${$.statusText}`);const f=await $.json();if(f.success){n.value=f.data.users||[];const A=f.data.pagination||{};x.value={page:A.page||1,per_page:A.per_page||20,total:A.total||0,pages:A.pages||0}}else throw new Error(f.message||"Errore nel caricamento utenti")}catch(k){c.value=k.message,console.error("Errore fetchUsers:",k)}finally{r.value=!1}},g=async z=>{r.value=!0,c.value=null;try{const k=await fetch(`/api/personnel/users/${z}`,{credentials:"include"});if(!k.ok)throw new Error(`HTTP ${k.status}: ${k.statusText}`);const $=await k.json();if($.success)return t.value=$.data.user,$.data.user;throw new Error($.message||"Errore nel caricamento utente")}catch(k){throw c.value=k.message,console.error("Errore fetchUser:",k),k}finally{r.value=!1}},y=async(z,k)=>{var $;r.value=!0,c.value=null;try{const f=await fetch(`/api/personnel/users/${z}`,{method:"PUT",headers:{"Content-Type":"application/json"},credentials:"include",body:JSON.stringify(k)});if(!f.ok)throw new Error(`HTTP ${f.status}: ${f.statusText}`);const A=await f.json();if(A.success){const q=n.value.findIndex(B=>B.id===z);return q!==-1&&(n.value[q]={...n.value[q],...A.data.user}),(($=t.value)==null?void 0:$.id)===z&&(t.value={...t.value,...A.data.user}),A.data.user}else throw new Error(A.message||"Errore nell'aggiornamento utente")}catch(f){throw c.value=f.message,console.error("Errore updateUser:",f),f}finally{r.value=!1}},w=async()=>{r.value=!0,c.value=null;try{const z=await fetch("/api/personnel/departments",{credentials:"include"});if(!z.ok)throw new Error(`HTTP ${z.status}: ${z.statusText}`);const k=await z.json();if(k.success)o.value=k.data.departments||[];else throw new Error(k.message||"Errore nel caricamento dipartimenti")}catch(z){c.value=z.message,console.error("Errore fetchDepartments:",z)}finally{r.value=!1}},S=async()=>{r.value=!0,c.value=null;try{const z=await fetch("/api/personnel/skills",{credentials:"include"});if(!z.ok)throw new Error(`HTTP ${z.status}: ${z.statusText}`);const k=await z.json();if(k.success)l.value=k.data.skills||[];else throw new Error(k.message||"Errore nel caricamento competenze")}catch(z){c.value=z.message,console.error("Errore fetchSkills:",z)}finally{r.value=!1}},V=(z,k)=>{u.value[z]=k},E=()=>{u.value={search:"",department:null,skill:null,role:null,location:null,sort:"name"}};return{users:n,departments:o,skills:l,currentUser:t,loading:r,error:c,filters:u,pagination:x,filteredUsers:h,departmentTree:m,fetchUsers:i,fetchUser:g,updateUser:y,fetchDepartments:w,fetchSkills:S,setFilter:V,clearFilters:E,setPagination:(z,k=null)=>{x.value.page=z,k&&(x.value.per_page=k)},$reset:()=>{n.value=[],o.value=[],l.value=[],t.value=null,r.value=!1,c.value=null,E(),x.value={page:1,per_page:20,total:0,pages:0}}}}),Di={class:"mb-6"},Vi={class:"flex flex-col sm:flex-row sm:items-center sm:justify-between"},Li={class:"mt-4 sm:mt-0 flex flex-wrap gap-3"},Bi={class:"bg-white dark:bg-gray-800 shadow rounded-lg mb-6"},qi={class:"p-6"},Ri={class:"grid grid-cols-1 md:grid-cols-3 gap-4"},Hi=["value"],Ui=["value"],Ni={key:0,class:"mt-4 flex justify-end"},Fi={class:"bg-white dark:bg-gray-800 shadow rounded-lg"},Oi={class:"px-6 py-4 border-b border-gray-200 dark:border-gray-700"},Ki={class:"flex items-center justify-between"},Gi={class:"text-lg font-medium text-gray-900 dark:text-white"},Wi={key:0,class:"text-sm text-gray-500 dark:text-gray-400"},Qi={key:0,class:"inline-flex items-center px-2 py-1 rounded-full text-xs bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200 mr-2"},Ji={key:1,class:"inline-flex items-center px-2 py-1 rounded-full text-xs bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200 mr-2"},Yi={key:2,class:"inline-flex items-center px-2 py-1 rounded-full text-xs bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200"},Xi={key:0,class:"p-6"},Zi={key:1,class:"p-6"},el={class:"bg-red-50 dark:bg-red-900 border border-red-200 dark:border-red-700 rounded-md p-4"},tl={class:"flex"},sl={class:"ml-3"},rl={class:"mt-2 text-sm text-red-700 dark:text-red-300"},al={key:2,class:"overflow-hidden"},ol={class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 p-6"},nl={class:"flex items-center mb-4"},il={class:"flex-shrink-0"},ll=["src","alt"],dl={key:1,class:"h-12 w-12 rounded-full bg-primary-500 dark:bg-primary-600 flex items-center justify-center text-white text-lg font-medium"},cl={class:"ml-4 flex-1"},ul={class:"text-lg font-medium text-gray-900 dark:text-white"},ml={class:"text-sm text-gray-500 dark:text-gray-400"},pl={class:"space-y-2 mb-4"},gl={class:"flex items-center text-sm"},vl={class:"text-gray-900 dark:text-white"},hl={class:"flex items-center text-sm"},fl={key:0,class:"flex items-center text-sm"},xl={class:"text-gray-900 dark:text-white"},yl={key:0,class:"mb-4"},_l={class:"flex flex-wrap gap-1"},bl={key:0,class:"inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-gray-100 text-gray-600 dark:bg-gray-600 dark:text-gray-300"},kl={class:"flex space-x-2"},wl=["href"],$l=["href"],Cl={key:3,class:"text-center py-12"},jl={class:"text-gray-500 dark:text-gray-400"},Ml={key:4,class:"px-6 py-4 border-t border-gray-200 dark:border-gray-700"},Pl={class:"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4"},zl={class:"text-sm text-gray-700 dark:text-gray-300"},Sl={class:"font-medium"},El={class:"font-medium"},Al={class:"font-medium"},Tl={class:"flex items-center space-x-1"},Il={key:1,class:"px-3 py-2 border border-gray-200 dark:border-gray-700 rounded-md text-sm font-medium text-gray-400 dark:text-gray-600 bg-gray-50 dark:bg-gray-900 cursor-not-allowed"},Dl=["onClick"],Vl={key:1,class:"px-2 py-2 text-sm text-gray-500 dark:text-gray-400"},Ll={key:3,class:"px-3 py-2 border border-gray-200 dark:border-gray-700 rounded-md text-sm font-medium text-gray-400 dark:text-gray-600 bg-gray-50 dark:bg-gray-900 cursor-not-allowed"},Bl={__name:"Personnel",setup(n){const o=ue();se();const l=Ii(),{hasPermission:t}=Ae(),r=M(""),c=M(null),{users:u,departments:x,skills:h,loading:m,error:i,filters:g,pagination:y}=l,w=_(()=>t.value("admin_access")),S=_(()=>r.value||g.department||g.skill||g.role),V=_(()=>g.department?x.find(v=>v.id===g.department):null),E=_(()=>g.skill?h.find(v=>v.id===g.skill):null),C=v=>{var j,Z;const p=((j=v.first_name)==null?void 0:j[0])||"",N=((Z=v.last_name)==null?void 0:Z[0])||"";return(p+N).toUpperCase()},U=v=>({admin:"Admin",manager:"Manager",employee:"Dipendente",human_resources:"HR",project_manager:"PM"})[v]||v,z=v=>({admin:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200",manager:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200",human_resources:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200",project_manager:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200"})[v]||"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200",k=()=>{const v=[],p=y.page,N=y.pages;N>0&&v.push(1);for(let j=Math.max(2,p-1);j<=Math.min(N-1,p+1);j++)v.includes(j)||v.push(j);return N>1&&!v.includes(N)&&(v[v.length-1]<N-1&&v.push("..."),v.push(N)),v},$=()=>{clearTimeout(c.value),c.value=setTimeout(()=>{f()},500)},f=async()=>{l.setFilter("search",r.value),l.setPagination(1),await B()},A=async()=>{r.value="",l.clearFilters(),l.setPagination(1),await B()},q=async v=>{l.setPagination(v),await B()},B=async()=>{const v={page:y.page,per_page:y.per_page};r.value&&(v.search=r.value),g.department&&(v.department_id=g.department),g.skill&&(v.skills=g.skill),g.role&&(v.role=g.role),await l.fetchUsers(v)},R=async()=>{await Promise.all([l.fetchDepartments(),l.fetchSkills()]);const v=o.query;v.search&&(r.value=v.search),v.department&&l.setFilter("department",parseInt(v.department)),v.skill&&l.setFilter("skill",parseInt(v.skill)),v.page&&l.setPagination(parseInt(v.page)),await B()};return oe(()=>o.query,v=>{v.search!==r.value&&(r.value=v.search||"")},{deep:!0}),X(()=>{R()}),(v,p)=>{const N=W("router-link");return s(),a("div",null,[e("div",Di,[e("div",Vi,[p[9]||(p[9]=e("div",null,[e("h1",{class:"text-2xl font-bold text-gray-900 dark:text-white"},"👥 Personale"),e("p",{class:"mt-1 text-sm text-gray-500 dark:text-gray-400"}," Gestisci il team e le competenze aziendali ")],-1)),e("div",Li,[P(N,{to:"/app/personnel/directory",class:"inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700"},{default:D(()=>p[5]||(p[5]=[T(" 📖 Directory ")])),_:1,__:[5]}),P(N,{to:"/app/personnel/orgchart",class:"inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700"},{default:D(()=>p[6]||(p[6]=[T(" 🏢 Organigramma ")])),_:1,__:[6]}),P(N,{to:"/app/personnel/skills",class:"inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700"},{default:D(()=>p[7]||(p[7]=[T(" 🎯 Competenze ")])),_:1,__:[7]}),w.value?(s(),O(N,{key:0,to:"/app/personnel/admin",class:"inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700"},{default:D(()=>p[8]||(p[8]=[T(" ⚙️ Amministrazione ")])),_:1,__:[8]})):b("",!0)])])]),e("div",Bi,[p[15]||(p[15]=e("div",{class:"px-6 py-4 border-b border-gray-200 dark:border-gray-700"},[e("h3",{class:"text-lg font-medium text-gray-900 dark:text-white"},"Filtri")],-1)),e("div",qi,[e("div",Ri,[e("div",null,[p[11]||(p[11]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"}," Dipartimento ",-1)),Q(e("select",{"onUpdate:modelValue":p[0]||(p[0]=j=>I(g).department=j),onChange:f,class:"w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white"},[p[10]||(p[10]=e("option",{value:""},"Tutti i dipartimenti",-1)),(s(!0),a(F,null,K(I(x),j=>(s(),a("option",{key:j.id,value:j.id},d(j.name),9,Hi))),128))],544),[[ne,I(g).department]])]),e("div",null,[p[13]||(p[13]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"}," Competenza ",-1)),Q(e("select",{"onUpdate:modelValue":p[1]||(p[1]=j=>I(g).skill=j),onChange:f,class:"w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white"},[p[12]||(p[12]=e("option",{value:""},"Tutte le competenze",-1)),(s(!0),a(F,null,K(I(h),j=>(s(),a("option",{key:j.id,value:j.id},d(j.name)+" ("+d(j.category)+") ",9,Ui))),128))],544),[[ne,I(g).skill]])]),e("div",null,[p[14]||(p[14]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"}," Ricerca ",-1)),Q(e("input",{"onUpdate:modelValue":p[2]||(p[2]=j=>r.value=j),onInput:$,type:"text",placeholder:"Nome, email, posizione...",class:"w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white"},null,544),[[te,r.value]])])]),S.value?(s(),a("div",Ni,[e("button",{onClick:A,class:"inline-flex items-center px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700"}," 🗑️ Pulisci filtri ")])):b("",!0)])]),e("div",Fi,[e("div",Oi,[e("div",Ki,[e("h3",Gi," Team ("+d(I(y).total)+" dipendenti"+d(S.value?" - filtrati":"")+") ",1),S.value?(s(),a("div",Wi,[r.value?(s(),a("span",Qi,' 🔍 "'+d(r.value)+'" ',1)):b("",!0),V.value?(s(),a("span",Ji," 🏢 "+d(V.value.name),1)):b("",!0),E.value?(s(),a("span",Yi," 🎯 "+d(E.value.name),1)):b("",!0)])):b("",!0)])]),I(m)?(s(),a("div",Xi,p[16]||(p[16]=[e("div",{class:"flex items-center justify-center"},[e("div",{class:"animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"}),e("span",{class:"ml-2 text-gray-600 dark:text-gray-400"},"Caricamento...")],-1)]))):I(i)?(s(),a("div",Zi,[e("div",el,[e("div",tl,[p[18]||(p[18]=e("div",{class:"flex-shrink-0"},[e("svg",{class:"h-5 w-5 text-red-400",viewBox:"0 0 20 20",fill:"currentColor"},[e("path",{"fill-rule":"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z","clip-rule":"evenodd"})])],-1)),e("div",sl,[p[17]||(p[17]=e("h3",{class:"text-sm font-medium text-red-800 dark:text-red-200"}," Errore nel caricamento ",-1)),e("div",rl,d(I(i)),1)])])])])):I(u).length>0?(s(),a("div",al,[e("div",ol,[(s(!0),a(F,null,K(I(u),j=>(s(),a("div",{key:j.id,class:"bg-gray-50 dark:bg-gray-700 rounded-lg p-6 hover:shadow-md transition-shadow duration-200"},[e("div",nl,[e("div",il,[j.profile_image?(s(),a("img",{key:0,src:j.profile_image,alt:j.full_name,class:"h-12 w-12 rounded-full"},null,8,ll)):(s(),a("div",dl,d(C(j)),1))]),e("div",cl,[e("h4",ul,d(j.full_name),1),e("p",ml,d(j.position||"Posizione non specificata"),1)])]),e("div",pl,[e("div",gl,[p[19]||(p[19]=e("span",{class:"text-gray-500 dark:text-gray-400 w-20"},"Email:",-1)),e("span",vl,d(j.email),1)]),e("div",hl,[p[20]||(p[20]=e("span",{class:"text-gray-500 dark:text-gray-400 w-20"},"Ruolo:",-1)),e("span",{class:L(z(j.role))},d(U(j.role)),3)]),j.department?(s(),a("div",fl,[p[21]||(p[21]=e("span",{class:"text-gray-500 dark:text-gray-400 w-20"},"Dipart.:",-1)),e("span",xl,d(j.department.name),1)])):b("",!0)]),j.skills&&j.skills.length>0?(s(),a("div",yl,[p[22]||(p[22]=e("p",{class:"text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"},"Competenze:",-1)),e("div",_l,[(s(!0),a(F,null,K(j.skills.slice(0,3),Z=>(s(),a("span",{key:Z.id,class:"inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-primary-100 text-primary-800 dark:bg-primary-900 dark:text-primary-200"},d(Z.name),1))),128)),j.skills.length>3?(s(),a("span",bl," +"+d(j.skills.length-3),1)):b("",!0)])])):b("",!0),e("div",kl,[P(N,{to:`/app/personnel/${j.id}`,class:"flex-1 text-center px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700"},{default:D(()=>p[23]||(p[23]=[T(" 👤 Profilo ")])),_:2,__:[23]},1032,["to"]),j.phone?(s(),a("a",{key:0,href:`tel:${j.phone}`,class:"px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700"}," 📞 ",8,wl)):b("",!0),e("a",{href:`mailto:${j.email}`,class:"px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700"}," ✉️ ",8,$l)])]))),128))])])):(s(),a("div",Cl,[p[24]||(p[24]=e("div",{class:"text-gray-400 dark:text-gray-500 text-6xl mb-4"},"👥",-1)),p[25]||(p[25]=e("h3",{class:"text-lg font-medium text-gray-900 dark:text-white mb-2"},"Nessun dipendente trovato",-1)),e("p",jl,d(S.value?"Prova a modificare i filtri di ricerca":"Non ci sono dipendenti da visualizzare"),1)])),I(y).pages>1?(s(),a("div",Ml,[e("div",Pl,[e("div",zl,[p[26]||(p[26]=T(" Mostrando ")),e("span",Sl,d((I(y).page-1)*I(y).per_page+1),1),p[27]||(p[27]=T(" - ")),e("span",El,d(Math.min(I(y).page*I(y).per_page,I(y).total)),1),p[28]||(p[28]=T(" di ")),e("span",Al,d(I(y).total),1),p[29]||(p[29]=T(" dipendenti "))]),e("nav",Tl,[I(y).page>1?(s(),a("button",{key:0,onClick:p[3]||(p[3]=j=>q(I(y).page-1)),class:"px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"}," ← Precedente ")):(s(),a("span",Il," ← Precedente ")),(s(!0),a(F,null,K(k(),j=>(s(),a(F,{key:j},[j!=="..."?(s(),a("button",{key:0,onClick:Z=>q(j),class:L(["px-3 py-2 border rounded-md text-sm font-medium transition-colors",j===I(y).page?"border-primary-500 dark:border-primary-400 text-white bg-primary-600 dark:bg-primary-500":"border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700"])},d(j),11,Dl)):(s(),a("span",Vl,"…"))],64))),128)),I(y).page<I(y).pages?(s(),a("button",{key:2,onClick:p[4]||(p[4]=j=>q(I(y).page+1)),class:"px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"}," Successiva → ")):(s(),a("span",Ll," Successiva → "))])])])):b("",!0)])])}}},ql=[{path:"/",component:Pe,children:[{path:"",name:"home",component:ya},{path:"about",name:"about",component:Wa},{path:"contact",name:"contact",component:Co},{path:"services",name:"services",component:Ho}]},{path:"/auth",component:Pe,children:[{path:"login",name:"login",component:Xo},{path:"register",name:"register",component:sn}]},{path:"/app",component:fr,meta:{requiresAuth:!0},children:[{path:"",redirect:"/app/dashboard"},{path:"dashboard",name:"dashboard",component:ni,meta:{requiresAuth:!0,requiredPermission:"view_dashboard"}},{path:"projects",name:"projects",component:Ti,meta:{requiresAuth:!0,requiredPermission:"view_all_projects"}},{path:"projects/create",name:"projects-create",component:()=>G(()=>import("./ProjectCreate.js"),__vite__mapDeps([0,1])),meta:{requiresAuth:!0,requiredPermission:"edit_project"}},{path:"projects/:id",name:"project-view",component:()=>G(()=>import("./ProjectView.js"),__vite__mapDeps([2,1,3])),meta:{requiresAuth:!0,requiredPermission:"view_all_projects"}},{path:"projects/:id/edit",name:"project-edit",component:()=>G(()=>import("./ProjectEdit.js"),__vite__mapDeps([4,1])),meta:{requiresAuth:!0,requiredPermission:"edit_project"}},{path:"personnel",name:"personnel",component:Bl,meta:{requiresAuth:!0,requiredPermission:"view_personnel_data"}},{path:"personnel/directory",name:"personnel-directory",component:()=>G(()=>import("./PersonnelDirectory.js"),__vite__mapDeps([5,1])),meta:{requiresAuth:!0,requiredPermission:"view_personnel_data"}},{path:"personnel/orgchart",name:"personnel-orgchart",component:()=>G(()=>import("./PersonnelOrgChart.js"),__vite__mapDeps([6,1])),meta:{requiresAuth:!0,requiredPermission:"view_personnel_data"}},{path:"personnel/skills",name:"personnel-skills",component:()=>G(()=>import("./SkillsMatrix.js"),__vite__mapDeps([7,1])),meta:{requiresAuth:!0,requiredPermission:"view_personnel_data"}},{path:"personnel/departments",name:"personnel-departments",component:()=>G(()=>import("./DepartmentList.js"),__vite__mapDeps([8,1])),meta:{requiresAuth:!0,requiredPermission:"manage_users"}},{path:"personnel/departments/create",name:"department-create",component:()=>G(()=>import("./DepartmentCreate.js"),__vite__mapDeps([9,1])),meta:{requiresAuth:!0,requiredPermission:"manage_users"}},{path:"personnel/departments/:id",name:"department-view",component:()=>G(()=>import("./DepartmentView.js"),__vite__mapDeps([10,1])),meta:{requiresAuth:!0,requiredPermission:"view_personnel_data"}},{path:"personnel/departments/:id/edit",name:"department-edit",component:()=>G(()=>import("./DepartmentEdit.js"),__vite__mapDeps([11,1])),meta:{requiresAuth:!0,requiredPermission:"manage_users"}},{path:"personnel/admin",name:"personnel-admin",component:()=>G(()=>import("./PersonnelAdmin.js"),__vite__mapDeps([12,1])),meta:{requiresAuth:!0,requiredPermission:"admin_access"}},{path:"personnel/:id",name:"personnel-profile",component:()=>G(()=>import("./PersonnelProfile.js"),__vite__mapDeps([13,1])),meta:{requiresAuth:!0,requiredPermission:"view_personnel_data"}},{path:"admin",redirect:"/app/admin/users"},{path:"admin/users",name:"admin-users",component:()=>G(()=>import("./Admin.js"),__vite__mapDeps([14,1])),meta:{requiresAuth:!0,requiredPermission:"admin_access"}},{path:"admin/kpi-templates",name:"admin-kpi-templates",component:()=>G(()=>import("./KPITemplates.js"),__vite__mapDeps([15,1])),meta:{requiresAuth:!0,requiredPermission:"admin_access"}},{path:"profile",name:"profile",component:()=>G(()=>import("./Profile.js"),__vite__mapDeps([16,1])),meta:{requiresAuth:!0}},{path:"settings",name:"settings",component:()=>G(()=>import("./Settings.js"),__vite__mapDeps([17,1])),meta:{requiresAuth:!0}}]}],Ie=He({history:Ue(),routes:ql});Ie.beforeEach(async(n,o,l)=>{const t=re();if(n.meta.requiresAuth){if(t.sessionChecked||await t.initializeAuth(),!t.isAuthenticated){l("/auth/login");return}if(n.meta.requiredPermission&&!t.hasPermission(n.meta.requiredPermission)){console.warn(`Accesso negato a ${n.path}: permesso '${n.meta.requiredPermission}' richiesto`),l("/app/dashboard");return}}l()});const de=Ne(Ge),Rl=Fe();de.use(Rl);de.use(Ie);const Hl=re();Hl.initializeAuth().then(()=>{console.log("Auth initialized successfully"),de.mount("#app")}).catch(n=>{console.error("Auth initialization failed:",n),de.mount("#app")});export{Te as _,Ae as a,ii as b,J as c,he as d,re as u};
