const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/ProjectCreate.js","assets/vendor.js","assets/ProjectView.js","assets/ProjectView.css","assets/ProjectEdit.js","assets/Admin.js","assets/KPITemplates.js","assets/Profile.js","assets/Settings.js"])))=>i.map(i=>d[i]);
import{r as C,w as ne,c as i,a as $,b as R,o,d as Ve,e as ue,f,g as b,n as A,h as q,i as P,t as c,u as me,j as e,F as U,k as F,l as S,m as te,p as N,q as Se,s as pe,v as K,x as ee,y as re,z as ve,A as J,T as De,B as Be,C as Te,D as Ae,E as le,G as Le,H as de,I as He,J as qe,K as Re,L as Ne,M as Ue}from"./vendor.js";(function(){const s=document.createElement("link").relList;if(s&&s.supports&&s.supports("modulepreload"))return;for(const r of document.querySelectorAll('link[rel="modulepreload"]'))t(r);new MutationObserver(r=>{for(const m of r)if(m.type==="childList")for(const u of m.addedNodes)u.tagName==="LINK"&&u.rel==="modulepreload"&&t(u)}).observe(document,{childList:!0,subtree:!0});function l(r){const m={};return r.integrity&&(m.integrity=r.integrity),r.referrerPolicy&&(m.referrerPolicy=r.referrerPolicy),r.crossOrigin==="use-credentials"?m.credentials="include":r.crossOrigin==="anonymous"?m.credentials="omit":m.credentials="same-origin",m}function t(r){if(r.ep)return;r.ep=!0;const m=l(r);fetch(r.href,m)}})();const Q=C(!1);let $e=!1;const Ee=n=>{n?(document.documentElement.classList.add("dark"),localStorage.setItem("darkMode","true")):(document.documentElement.classList.remove("dark"),localStorage.setItem("darkMode","false"))},Fe=()=>{$e||(ne(Q,n=>{Ee(n)}),$e=!0)};function ge(){return Fe(),{isDarkMode:Q,toggleDarkMode:()=>{Q.value=!Q.value},setDarkMode:t=>{Q.value=t},initializeDarkMode:()=>{const t=localStorage.getItem("darkMode"),r=document.documentElement.classList.contains("dark");if(t==="true")Q.value=!0;else if(t==="false")Q.value=!1;else{const p=window.matchMedia("(prefers-color-scheme: dark)").matches;Q.value=r||p}Ee(Q.value);const m=window.matchMedia("(prefers-color-scheme: dark)"),u=p=>{const v=localStorage.getItem("darkMode");(!v||v==="null")&&(Q.value=p.matches)};m.addEventListener("change",u)}}}const Oe={id:"app"},Ke={__name:"App",setup(n){const{initializeDarkMode:s}=ge();return s(),(l,t)=>{const r=R("router-view");return o(),i("div",Oe,[$(r)])}}},Ge="modulepreload",Qe=function(n){return"/"+n},Ce={},Z=function(s,l,t){let r=Promise.resolve();if(l&&l.length>0){document.getElementsByTagName("link");const u=document.querySelector("meta[property=csp-nonce]"),p=(u==null?void 0:u.nonce)||(u==null?void 0:u.getAttribute("nonce"));r=Promise.allSettled(l.map(v=>{if(v=Qe(v),v in Ce)return;Ce[v]=!0;const d=v.endsWith(".css"),a=d?'[rel="stylesheet"]':"";if(document.querySelector(`link[href="${v}"]${a}`))return;const g=document.createElement("link");if(g.rel=d?"stylesheet":Ge,d||(g.as="script"),g.crossOrigin="",g.href=v,p&&g.setAttribute("nonce",p),document.head.appendChild(g),d)return new Promise((x,k)=>{g.addEventListener("load",x),g.addEventListener("error",()=>k(new Error(`Unable to preload CSS for ${v}`)))})}))}function m(u){const p=new Event("vite:preloadError",{cancelable:!0});if(p.payload=u,window.dispatchEvent(p),!p.defaultPrevented)throw u}return r.then(u=>{for(const p of u||[])p.status==="rejected"&&m(p.reason);return s().catch(m)})},O=Ve.create({baseURL:"",timeout:1e4,withCredentials:!0,headers:{"Content-Type":"application/json"}});O.interceptors.request.use(n=>{var l,t;const s=(l=document.querySelector('meta[name="csrf-token"]'))==null?void 0:l.getAttribute("content");return s&&["post","put","patch","delete"].includes((t=n.method)==null?void 0:t.toLowerCase())&&(n.headers["X-CSRFToken"]=s),n},n=>Promise.reject(n));O.interceptors.response.use(n=>n,n=>{var s;return((s=n.response)==null?void 0:s.status)===401&&(localStorage.removeItem("user"),console.warn("Sessione scaduta, autenticazione richiesta")),Promise.reject(n)});const X=ue("auth",()=>{const n=localStorage.getItem("user"),s=C(n?JSON.parse(n):null),l=C(!1),t=C(null),r=C(!1),m=f(()=>!!s.value&&r.value),u={admin:["admin","manage_users","assign_roles","view_all_projects","create_project","edit_project","delete_project","assign_to_project","manage_project_tasks","manage_project_resources","approve_timesheets","view_personnel_data","edit_personnel_data","view_contracts","manage_contracts","view_crm","manage_clients","manage_proposals","view_reports","view_dashboard","submit_timesheet","view_own_timesheets","view_funding","manage_funding","view_products","manage_products","view_performance","manage_performance","view_communications","manage_communications","view_startup","manage_startup"],manager:["view_dashboard","view_all_projects","edit_project","assign_to_project","manage_project_tasks","manage_project_resources","approve_timesheets","view_personnel_data","view_crm","view_reports","submit_timesheet","view_own_timesheets","manage_clients","manage_proposals","view_funding","manage_funding","view_products","manage_products","view_performance","manage_performance","view_communications","manage_communications","view_startup","manage_startup"],employee:["view_dashboard","view_own_timesheets","submit_timesheet"],sales:["view_dashboard","view_crm","manage_clients","manage_proposals","submit_timesheet","view_own_timesheets","view_reports","view_funding","view_products","manage_products"],human_resources:["view_dashboard","manage_users","view_personnel_data","edit_personnel_data","view_contracts","manage_contracts","submit_timesheet","view_own_timesheets","view_reports","view_funding","manage_funding","view_performance","manage_performance","view_communications","manage_communications","view_startup","manage_startup"]},p=z=>!s.value||!s.value.role?!1:s.value.role==="admin"?!0:(u[s.value.role]||[]).includes(z),v=()=>{var z,T;console.log("Current user:",s.value),console.log("User role:",(z=s.value)==null?void 0:z.role),console.log("Has admin permission:",p("admin")),console.log("Available permissions for role:",u[(T=s.value)==null?void 0:T.role])};async function d(z){var T,j;l.value=!0,t.value=null;try{const _=await O.post("/api/auth/login",z);return _.data.success?(s.value=_.data.data.user,localStorage.setItem("user",JSON.stringify(s.value)),r.value=!0,{success:!0}):(t.value=_.data.message||"Errore durante il login",{success:!1,error:t.value})}catch(_){return t.value=((j=(T=_.response)==null?void 0:T.data)==null?void 0:j.message)||"Errore di connessione",{success:!1,error:t.value}}finally{l.value=!1}}async function a(z){var T,j;l.value=!0,t.value=null;try{const _=await O.post("/api/auth/register",z);return _.data.success?{success:!0,message:_.data.message}:(t.value=_.data.message||"Errore durante la registrazione",{success:!1,error:t.value})}catch(_){return t.value=((j=(T=_.response)==null?void 0:T.data)==null?void 0:j.message)||"Errore di connessione",{success:!1,error:t.value}}finally{l.value=!1}}async function g(){try{await O.post("/api/auth/logout")}catch(z){console.warn("Errore durante il logout:",z)}finally{s.value=null,r.value=!1,localStorage.removeItem("user")}}async function x(){if(r.value)return m.value;try{const z=await O.get("/api/auth/me");return z.data.success?(s.value=z.data.data.user,localStorage.setItem("user",JSON.stringify(s.value)),r.value=!0,!0):(await g(),!1)}catch{return await g(),!1}}async function k(){return s.value?await x():(r.value=!0,!1)}return{user:s,loading:l,error:t,sessionChecked:r,isAuthenticated:m,hasPermission:p,debugPermissions:v,login:d,register:a,logout:g,checkAuth:x,initializeAuth:k}}),se=ue("tenant",()=>{const n=C(null),s=C(!1),l=C(null),t=f(()=>{var a;return((a=n.value)==null?void 0:a.company)||{}}),r=f(()=>{var a;return((a=n.value)==null?void 0:a.contact)||{}}),m=f(()=>{var a;return((a=n.value)==null?void 0:a.pages)||{}}),u=f(()=>{var a;return((a=n.value)==null?void 0:a.navigation)||{}}),p=f(()=>{var a;return((a=n.value)==null?void 0:a.footer)||{}});async function v(){try{if(s.value=!0,window.TENANT_CONFIG){n.value=window.TENANT_CONFIG;return}const a=await fetch("/api/config/tenant");n.value=await a.json()}catch(a){l.value="Errore nel caricamento della configurazione",console.error("Errore caricamento tenant config:",a)}finally{s.value=!1}}function d(a,g={}){if(!a||typeof a!="string")return a;let x=a;const k={"company.name":t.value.name||"DatVinci","company.tagline":t.value.tagline||"","company.description":t.value.description||"","company.mission":t.value.mission||"","company.vision":t.value.vision||"","company.founded":t.value.founded||"","company.team_size":t.value.team_size||"","contact.email":r.value.email||"","contact.phone":r.value.phone||"","contact.address":r.value.address||"",current_year:new Date().getFullYear().toString(),...g};for(const[z,T]of Object.entries(k)){const j=new RegExp(`\\{${z}\\}`,"g");x=x.replace(j,T||"")}return x}return{config:n,loading:s,error:l,company:t,contact:r,pages:m,navigation:u,footer:p,loadConfig:v,interpolateText:d}});function We(){const n=X(),s=f(()=>k=>n.hasPermission(k)),l=f(()=>{var k;return((k=n.user)==null?void 0:k.role)||null}),t=f(()=>l.value==="admin"),r=f(()=>l.value==="manager"),m=f(()=>l.value==="employee"),u=f(()=>l.value==="sales"),p=f(()=>l.value==="human_resources"),v=f(()=>s.value("create_project")||s.value("edit_project")||s.value("delete_project")),d=f(()=>s.value("manage_users")||s.value("assign_roles")),a=f(()=>s.value("view_all_projects")),g=f(()=>s.value("view_personnel_data")||s.value("edit_personnel_data")),x=f(()=>s.value("approve_timesheets"));return{hasPermission:s,userRole:l,isAdmin:t,isManager:r,isEmployee:m,isSales:u,isHR:p,canManageProjects:v,canManageUsers:d,canViewAllProjects:a,canManagePersonnel:g,canApproveTimesheets:x}}const Je={key:0,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z"},Ye={key:1,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"},Xe={key:2,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.25 2.25 0 11-4.5 0 2.25 2.25 0 014.5 0z"},Ze={key:3,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"},et={key:4,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"},tt={key:5,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"},st={key:6,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"},ot={key:7,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M15 12a3 3 0 11-6 0 3 3 0 016 0z"},rt={key:8,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.25 2.25 0 11-4.5 0 2.25 2.25 0 014.5 0z"},at={key:9,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 20H5a2 2 0 01-2-2V6a2 2 0 012-2h10a2 2 0 012 2v1m2 13a2 2 0 01-2-2V7m2 13a2 2 0 002-2V9a2 2 0 00-2-2h-2m-4-3H9M7 16h6M7 8h6v4H7V8z"},nt={key:10,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z"},it={key:11,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 17v-2m3 2v-4m3 4v-6m2 10H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"},lt={key:12,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 6v6m0 0v6m0-6h6m-6 0H6"},ce={__name:"SidebarIcon",props:{icon:{type:String,required:!0},className:{type:String,default:"h-5 w-5"}},setup(n){return(s,l)=>(o(),i("svg",{class:A(n.className),fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[n.icon==="dashboard"?(o(),i("path",Je)):n.icon==="projects"?(o(),i("path",Ye)):n.icon==="users"?(o(),i("path",Xe)):n.icon==="clients"?(o(),i("path",Ze)):n.icon==="products"?(o(),i("path",et)):n.icon==="reports"?(o(),i("path",tt)):n.icon==="settings"?(o(),i("path",st)):b("",!0),n.icon==="settings"?(o(),i("path",ot)):n.icon==="user-management"?(o(),i("path",rt)):n.icon==="communications"?(o(),i("path",at)):n.icon==="funding"?(o(),i("path",nt)):n.icon==="reporting"?(o(),i("path",it)):(o(),i("path",lt))],2))}},dt={key:0,class:"truncate"},ct={key:0,class:"truncate"},Y={__name:"SidebarNavItem",props:{item:{type:Object,required:!0},isCollapsed:{type:Boolean,default:!1}},emits:["click"],setup(n){const s=f(()=>["text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 hover:text-primary-600 dark:hover:text-primary-400"]);return(l,t)=>{const r=R("router-link");return o(),i("div",null,[n.item.path!=="#"?(o(),q(r,{key:0,to:n.item.path,class:A(["group flex items-center px-2 py-2 text-sm font-medium rounded-md transition-colors duration-150",[s.value,{"justify-center":n.isCollapsed}]]),"active-class":"text-primary-600 bg-primary-50 border-r-2 border-primary-600",onClick:t[0]||(t[0]=m=>l.$emit("click"))},{default:P(()=>[$(ce,{icon:n.item.icon,class:A(["flex-shrink-0 h-6 w-6",{"mr-0":n.isCollapsed,"mr-3":!n.isCollapsed}])},null,8,["icon","class"]),n.isCollapsed?b("",!0):(o(),i("span",dt,c(n.item.name),1))]),_:1},8,["to","class"])):(o(),i("div",{key:1,class:A(["group flex items-center px-2 py-2 text-sm font-medium rounded-md transition-colors duration-150 cursor-not-allowed opacity-75",["text-gray-400 hover:text-gray-500",{"justify-center":n.isCollapsed}]])},[$(ce,{icon:n.item.icon,class:A(["flex-shrink-0 h-6 w-6",{"mr-0":n.isCollapsed,"mr-3":!n.isCollapsed}])},null,8,["icon","class"]),n.isCollapsed?b("",!0):(o(),i("span",ct,c(n.item.name),1))],2))])}}},ut={key:0,class:"flex-1 text-left truncate"},mt={key:0,class:"ml-6 space-y-1 mt-1"},je={__name:"SidebarNavItemCollapsible",props:{item:{type:Object,required:!0},isCollapsed:{type:Boolean,default:!1}},emits:["click"],setup(n){const s=n,l=me(),t=X(),r=C(!1),m=f(()=>["text-gray-700 hover:bg-gray-50 hover:text-primary-600",{"text-primary-600 bg-primary-50":u.value}]),u=f(()=>s.item.children?s.item.children.some(a=>a.path!=="#"&&l.path.startsWith(a.path)):!1),p=f(()=>s.item.children?s.item.children.filter(a=>{var g;return a.admin?((g=t.user)==null?void 0:g.role)==="admin":!0}):[]);u.value&&(r.value=!0);function v(){s.isCollapsed||(r.value=!r.value)}function d(a){if(a.path==="#")return!1}return(a,g)=>{const x=R("router-link");return o(),i("div",null,[e("button",{onClick:v,class:A(["group w-full flex items-center px-2 py-2 text-sm font-medium rounded-md transition-colors duration-150",[m.value,{"justify-center":n.isCollapsed}]])},[$(ce,{icon:n.item.icon,class:A(["flex-shrink-0 h-6 w-6",{"mr-0":n.isCollapsed,"mr-3":!n.isCollapsed}])},null,8,["icon","class"]),n.isCollapsed?b("",!0):(o(),i("span",ut,c(n.item.name),1)),n.isCollapsed?b("",!0):(o(),i("svg",{key:1,class:A([{"rotate-90":r.value},"ml-2 h-4 w-4 transition-transform duration-150"]),fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},g[0]||(g[0]=[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 5l7 7-7 7"},null,-1)]),2))],2),r.value&&!n.isCollapsed?(o(),i("div",mt,[(o(!0),i(U,null,F(p.value,k=>(o(),q(x,{key:k.name,to:k.path,class:A(["group flex items-center px-2 py-2 text-sm font-medium rounded-md transition-colors duration-150",k.path==="#"?"text-gray-400 hover:text-gray-500 cursor-not-allowed opacity-75":"text-gray-600 hover:bg-gray-50 hover:text-primary-600"]),"active-class":"text-primary-600 bg-primary-50",onClick:z=>d(k)},{default:P(()=>[S(c(k.name),1)]),_:2},1032,["to","class","onClick"]))),128))])):b("",!0)])}}},pt={class:"mt-5 flex-grow flex flex-col overflow-hidden"},vt={class:"flex-1 px-2 space-y-1"},Me={__name:"SidebarNavigation",props:{isCollapsed:{type:Boolean,default:!1}},emits:["item-click"],setup(n){const{hasPermission:s}=We(),l=f(()=>s.value("view_dashboard")),t=f(()=>s.value("view_personnel_data")),r=f(()=>s.value("view_all_projects")),m=f(()=>s.value("view_crm")),u=f(()=>s.value("view_products")),p=f(()=>s.value("view_performance")),v=f(()=>s.value("view_communications")),d=f(()=>s.value("view_funding")),a=f(()=>s.value("view_reports")),g=f(()=>s.value("admin_access"));return(x,k)=>(o(),i("div",pt,[e("nav",vt,[l.value?(o(),q(Y,{key:0,item:{name:"Dashboard",path:"/app/dashboard",icon:"dashboard"},"is-collapsed":n.isCollapsed,onClick:k[0]||(k[0]=z=>x.$emit("item-click"))},null,8,["is-collapsed"])):b("",!0),t.value?(o(),q(je,{key:1,item:{name:"Personale",icon:"users",children:[{name:"👥 Team",path:"/app/personnel"},{name:"📖 Directory",path:"/app/personnel/directory"},{name:"🏢 Organigramma",path:"/app/personnel/orgchart"},{name:"🎯 Competenze",path:"/app/personnel/skills"},{name:"🏢 Dipartimenti",path:"#",admin:!0},{name:"⚙️ Amministrazione",path:"#",admin:!0}]},"is-collapsed":n.isCollapsed,onClick:k[1]||(k[1]=z=>x.$emit("item-click"))},null,8,["is-collapsed"])):b("",!0),r.value?(o(),q(Y,{key:2,item:{name:"Progetti",path:"/app/projects",icon:"projects"},"is-collapsed":n.isCollapsed,onClick:k[2]||(k[2]=z=>x.$emit("item-click"))},null,8,["is-collapsed"])):b("",!0),m.value?(o(),q(Y,{key:3,item:{name:"CRM",path:"#",icon:"clients"},"is-collapsed":n.isCollapsed,onClick:k[3]||(k[3]=z=>x.$emit("item-click"))},null,8,["is-collapsed"])):b("",!0),u.value?(o(),q(Y,{key:4,item:{name:"Prodotti",path:"#",icon:"products"},"is-collapsed":n.isCollapsed,onClick:k[4]||(k[4]=z=>x.$emit("item-click"))},null,8,["is-collapsed"])):b("",!0),p.value?(o(),q(Y,{key:5,item:{name:"Performance",path:"#",icon:"reports"},"is-collapsed":n.isCollapsed,onClick:k[5]||(k[5]=z=>x.$emit("item-click"))},null,8,["is-collapsed"])):b("",!0),v.value?(o(),q(Y,{key:6,item:{name:"Comunicazione",path:"#",icon:"communications"},"is-collapsed":n.isCollapsed,onClick:k[6]||(k[6]=z=>x.$emit("item-click"))},null,8,["is-collapsed"])):b("",!0),d.value?(o(),q(Y,{key:7,item:{name:"Finanziamenti",path:"#",icon:"funding"},"is-collapsed":n.isCollapsed,onClick:k[7]||(k[7]=z=>x.$emit("item-click"))},null,8,["is-collapsed"])):b("",!0),a.value?(o(),q(Y,{key:8,item:{name:"Rendicontazione",path:"#",icon:"reporting"},"is-collapsed":n.isCollapsed,onClick:k[8]||(k[8]=z=>x.$emit("item-click"))},null,8,["is-collapsed"])):b("",!0),g.value?(o(),q(je,{key:9,item:{name:"Amministrazione",icon:"settings",children:[{name:"👥 Gestione Utenti",path:"/app/admin/users"},{name:"📊 Template KPI",path:"/app/admin/kpi-templates"}]},"is-collapsed":n.isCollapsed,onClick:k[9]||(k[9]=z=>x.$emit("item-click"))},null,8,["is-collapsed"])):b("",!0)])]))}},gt={class:"flex-shrink-0 border-t border-gray-200 p-4"},ht={class:"flex-shrink-0"},ft={class:"h-8 w-8 rounded-full bg-primary-100 flex items-center justify-center"},xt={class:"text-sm font-medium text-primary-700"},yt={key:0,class:"ml-3 flex-1 min-w-0"},_t={class:"text-sm font-medium text-gray-900 truncate"},bt={class:"text-xs text-gray-500 truncate"},kt={class:"py-1"},wt={key:0,class:"mt-3 text-xs text-gray-400 text-center"},ze={__name:"SidebarFooter",props:{isCollapsed:{type:Boolean,default:!1}},setup(n){const s=te(),l=X(),t=C(!1),r=f(()=>l.user&&(l.user.name||l.user.username)||"Utente"),m=f(()=>l.user?r.value.charAt(0).toUpperCase():"U"),u=f(()=>l.user?{admin:"Amministratore",manager:"Manager",employee:"Dipendente",client:"Cliente"}[l.user.role]||l.user.role:""),p=f(()=>"1.0.0");async function v(){t.value=!1,await l.logout(),s.push("/auth/login")}return(d,a)=>{const g=R("router-link");return o(),i("div",gt,[e("div",{class:A(["flex items-center",{"justify-center":n.isCollapsed}])},[e("div",ht,[e("div",ft,[e("span",xt,c(m.value),1)])]),n.isCollapsed?b("",!0):(o(),i("div",yt,[e("p",_t,c(r.value),1),e("p",bt,c(u.value),1)])),e("div",{class:A(["relative",{"ml-3":!n.isCollapsed}])},[e("button",{onClick:a[0]||(a[0]=x=>t.value=!t.value),class:"p-1 rounded-full text-gray-400 hover:text-gray-600 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-primary-500"},a[4]||(a[4]=[e("svg",{class:"h-4 w-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M5 12h.01M12 12h.01M19 12h.01M6 12a1 1 0 11-2 0 1 1 0 012 0zM13 12a1 1 0 11-2 0 1 1 0 012 0zM20 12a1 1 0 11-2 0 1 1 0 012 0z"})],-1)])),t.value?(o(),i("div",{key:0,onClick:a[3]||(a[3]=x=>t.value=!1),class:"origin-bottom-left absolute bottom-full left-0 mb-2 w-48 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5 z-50"},[e("div",kt,[$(g,{to:"/app/profile",class:"block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100",onClick:a[1]||(a[1]=x=>t.value=!1)},{default:P(()=>a[5]||(a[5]=[S(" Il tuo profilo ")])),_:1,__:[5]}),$(g,{to:"/app/settings",class:"block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100",onClick:a[2]||(a[2]=x=>t.value=!1)},{default:P(()=>a[6]||(a[6]=[S(" Impostazioni ")])),_:1,__:[6]}),a[7]||(a[7]=e("hr",{class:"my-1"},null,-1)),e("button",{onClick:v,class:"block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"}," Esci ")])])):b("",!0)],2)],2),p.value&&!n.isCollapsed?(o(),i("div",wt," v"+c(p.value),1)):b("",!0)])}}},$t={class:"flex"},Ct={class:"hidden lg:flex lg:flex-shrink-0 lg:fixed lg:inset-y-0 z-10"},jt={class:"flex flex-col flex-grow pt-5 pb-4 overflow-y-auto bg-white dark:bg-gray-800 border-r border-gray-200 dark:border-gray-700 shadow-sm"},Mt={class:"flex items-center flex-shrink-0 px-4"},zt={class:"w-10 h-10 bg-primary-600 rounded flex items-center justify-center mr-3"},Pt={class:"text-white font-bold text-lg"},St={class:"text-xl font-semibold text-gray-900 dark:text-white"},At={class:"w-8 h-8 bg-primary-600 rounded flex items-center justify-center"},Et={class:"text-white font-bold text-sm"},It={class:"h-5 w-5",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},Vt=["d"],Dt={class:"flex flex-col h-full pt-5 pb-4 overflow-y-auto bg-white dark:bg-gray-800 border-r border-gray-200 dark:border-gray-700 shadow-sm"},Bt={class:"flex items-center justify-between px-4 mb-4"},Tt={class:"w-8 h-8 bg-primary-600 rounded flex items-center justify-center mr-3"},Lt={class:"text-white font-bold text-sm"},Ht={class:"text-xl font-semibold text-gray-900 dark:text-white"},qt={__name:"AppSidebar",props:{isMobileOpen:{type:Boolean,default:!1}},emits:["close","toggle-collapsed"],setup(n,{emit:s}){const l=s,t=se(),r=C(!1),m=f(()=>t.config||{}),u=f(()=>{var a;return((a=m.value.company)==null?void 0:a.name)||"DatPortal"}),p=f(()=>u.value.split(" ").map(g=>g[0]).join("").toUpperCase().slice(0,2));function v(){r.value=!r.value,l("toggle-collapsed",r.value)}function d(){r.value&&(r.value=!1)}return(a,g)=>{const x=R("router-link");return o(),i("div",$t,[e("div",Ct,[e("div",{class:A(["flex flex-col transition-all duration-300",[r.value?"w-20":"w-64"]])},[e("div",jt,[e("div",Mt,[e("div",{class:A(["flex items-center",{"justify-center":r.value}])},[$(x,{to:"/app/dashboard",class:A(["flex items-center",{hidden:r.value}])},{default:P(()=>[e("div",zt,[e("span",Pt,c(p.value),1)]),e("h3",St,c(u.value),1)]),_:1},8,["class"]),$(x,{to:"/app/dashboard",class:A(["flex items-center justify-center",{hidden:!r.value}])},{default:P(()=>[e("div",At,[e("span",Et,c(p.value),1)])]),_:1},8,["class"])],2),e("button",{onClick:v,class:"ml-auto text-gray-600 dark:text-gray-400 focus:outline-none hover:bg-gray-100 dark:hover:bg-gray-700 p-1 rounded"},[(o(),i("svg",It,[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:r.value?"M13 5l7 7-7 7M5 5l7 7-7 7":"M11 19l-7-7 7-7m8 14l-7-7 7-7"},null,8,Vt)]))])]),$(Me,{"is-collapsed":r.value,onItemClick:d},null,8,["is-collapsed"]),$(ze,{"is-collapsed":r.value},null,8,["is-collapsed"])])],2)]),e("div",{class:A(["fixed inset-y-0 left-0 z-30 w-64 bg-primary-700 transform transition-transform duration-300 ease-in-out lg:hidden",n.isMobileOpen?"translate-x-0":"-translate-x-full"])},[e("div",Dt,[e("div",Bt,[$(x,{to:"/app/dashboard",class:"flex items-center"},{default:P(()=>[e("div",Tt,[e("span",Lt,c(p.value),1)]),e("h3",Ht,c(u.value),1)]),_:1}),e("button",{onClick:g[0]||(g[0]=k=>a.$emit("close")),class:"p-2 rounded-md text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-700"},g[2]||(g[2]=[e("svg",{class:"h-6 w-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)]))]),$(Me,{"is-collapsed":!1,onItemClick:g[1]||(g[1]=k=>a.$emit("close"))}),$(ze,{"is-collapsed":!1})])],2)])}}},Rt={class:"flex","aria-label":"Breadcrumb"},Nt={class:"flex items-center space-x-2 text-sm text-gray-500"},Ut={key:0,class:"mr-2"},Ft={class:"flex items-center"},Ot={key:0,class:"h-3 w-3 mr-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},Kt=["d"],Gt={key:2,class:"font-medium text-gray-900"},Qt={__name:"HeaderBreadcrumbs",props:{breadcrumbs:{type:Array,required:!0}},setup(n){return(s,l)=>{const t=R("router-link");return o(),i("nav",Rt,[e("ol",Nt,[(o(!0),i(U,null,F(n.breadcrumbs,(r,m)=>(o(),i("li",{key:m,class:"flex items-center"},[m>0?(o(),i("div",Ut,l[0]||(l[0]=[e("svg",{class:"h-3 w-3 text-gray-400",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z","clip-rule":"evenodd"})],-1)]))):b("",!0),r.to&&m<n.breadcrumbs.length-1?(o(),q(t,{key:1,to:r.to,class:"hover:text-gray-700 transition-colors duration-150"},{default:P(()=>[e("span",Ft,[r.icon?(o(),i("svg",Ot,[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:r.icon},null,8,Kt)])):b("",!0),S(" "+c(r.label),1)])]),_:2},1032,["to"])):(o(),i("span",Gt,c(r.label),1))]))),128))])])}}},Wt={class:"flex items-center space-x-2"},Jt={key:0,class:"h-3 w-3",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},Yt={key:1,class:"h-3 w-3",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},Xt={__name:"HeaderQuickActions",emits:["quick-create-project","quick-add-task"],setup(n){const s=me(),{isDarkMode:l,toggleDarkMode:t}=ge(),r=f(()=>{var u;return((u=s.name)==null?void 0:u.includes("projects"))||s.path.includes("/projects")}),m=f(()=>{var u,p;return((u=s.name)==null?void 0:u.includes("tasks"))||((p=s.name)==null?void 0:p.includes("projects"))||s.path.includes("/tasks")||s.path.includes("/projects")});return(u,p)=>(o(),i("div",Wt,[r.value?(o(),i("button",{key:0,onClick:p[0]||(p[0]=v=>u.$emit("quick-create-project")),class:"inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded-md text-primary-700 bg-primary-100 hover:bg-primary-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"},p[3]||(p[3]=[e("svg",{class:"h-3 w-3 mr-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 6v6m0 0v6m0-6h6m-6 0H6"})],-1),S(" Nuovo Progetto ")]))):b("",!0),m.value?(o(),i("button",{key:1,onClick:p[1]||(p[1]=v=>u.$emit("quick-add-task")),class:"inline-flex items-center px-3 py-1.5 border border-gray-300 text-xs font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"},p[4]||(p[4]=[e("svg",{class:"h-3 w-3 mr-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"})],-1),S(" Nuovo Task ")]))):b("",!0),e("button",{onClick:p[2]||(p[2]=(...v)=>N(t)&&N(t)(...v)),class:"inline-flex items-center px-3 py-1.5 border border-gray-300 text-xs font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 dark:bg-gray-800 dark:text-gray-300 dark:border-gray-600 dark:hover:bg-gray-700",title:"Cambia tema"},[N(l)?(o(),i("svg",Yt,p[6]||(p[6]=[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z"},null,-1)]))):(o(),i("svg",Jt,p[5]||(p[5]=[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z"},null,-1)])))])]))}},Zt={class:"relative"},es={class:"relative"},ts={key:0,class:"absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-4 w-4 flex items-center justify-center"},ss={class:"py-1"},os={key:0,class:"px-4 py-8 text-center text-gray-500 text-sm"},rs={key:1,class:"max-h-64 overflow-y-auto"},as=["onClick"],ns={class:"flex items-start"},is={class:"flex-shrink-0"},ls={class:"h-4 w-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},ds=["d"],cs={class:"ml-3 flex-1"},us={class:"text-sm font-medium text-gray-900"},ms={class:"text-xs text-gray-500 mt-1"},ps={class:"text-xs text-gray-400 mt-1"},vs={key:0,class:"flex-shrink-0"},gs={key:2,class:"px-4 py-2 border-t border-gray-100"},hs={__name:"HeaderNotifications",setup(n){const s=C(!1),l=C([{id:1,type:"task",title:"Nuovo task assegnato",message:'Ti è stato assegnato un nuovo task nel progetto "Website Redesign"',created_at:new Date().toISOString(),read:!1},{id:2,type:"project",title:"Progetto completato",message:'Il progetto "Mobile App" è stato completato con successo',created_at:new Date(Date.now()-36e5).toISOString(),read:!0}]),t=f(()=>l.value.filter(d=>!d.read).length);function r(d){const a={task:"h-6 w-6 rounded-full bg-blue-100 text-blue-600 flex items-center justify-center",project:"h-6 w-6 rounded-full bg-green-100 text-green-600 flex items-center justify-center",user:"h-6 w-6 rounded-full bg-purple-100 text-purple-600 flex items-center justify-center",system:"h-6 w-6 rounded-full bg-gray-100 text-gray-600 flex items-center justify-center"};return a[d]||a.system}function m(d){const a={task:"M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2",project:"M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10",user:"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z",system:"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"};return a[d]||a.system}function u(d){const a=new Date(d),x=new Date-a;return x<6e4?"Adesso":x<36e5?`${Math.floor(x/6e4)}m fa`:x<864e5?`${Math.floor(x/36e5)}h fa`:a.toLocaleDateString("it-IT")}function p(d){d.read||(d.read=!0),s.value=!1}function v(){l.value.forEach(d=>d.read=!0)}return(d,a)=>(o(),i("div",Zt,[e("button",{onClick:a[0]||(a[0]=g=>s.value=!s.value),class:"p-2 rounded-full text-gray-400 hover:text-gray-600 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"},[a[3]||(a[3]=e("span",{class:"sr-only"},"Visualizza notifiche",-1)),e("div",es,[a[2]||(a[2]=e("svg",{class:"h-5 w-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M15 17h5l-5 5v-5zM10 21a2 2 0 01-2-2V7a7 7 0 1114 0v12a2 2 0 01-2 2H10z"})],-1)),t.value>0?(o(),i("span",ts,c(t.value>9?"9+":t.value),1)):b("",!0)])]),s.value?(o(),i("div",{key:0,onClick:a[1]||(a[1]=g=>s.value=!1),class:"origin-top-right absolute right-0 mt-2 w-80 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5 z-50"},[e("div",ss,[a[5]||(a[5]=e("div",{class:"px-4 py-2 border-b border-gray-100"},[e("h3",{class:"text-sm font-medium text-gray-900"},"Notifiche")],-1)),l.value.length===0?(o(),i("div",os," Nessuna notifica ")):(o(),i("div",rs,[(o(!0),i(U,null,F(l.value,g=>(o(),i("div",{key:g.id,class:"px-4 py-3 hover:bg-gray-50 cursor-pointer border-b border-gray-50 last:border-b-0",onClick:x=>p(g)},[e("div",ns,[e("div",is,[e("div",{class:A(r(g.type))},[(o(),i("svg",ls,[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:m(g.type)},null,8,ds)]))],2)]),e("div",cs,[e("p",us,c(g.title),1),e("p",ms,c(g.message),1),e("p",ps,c(u(g.created_at)),1)]),g.read?b("",!0):(o(),i("div",vs,a[4]||(a[4]=[e("div",{class:"h-2 w-2 bg-primary-500 rounded-full"},null,-1)])))])],8,as))),128))])),l.value.length>0?(o(),i("div",gs,[e("button",{onClick:v,class:"text-xs text-primary-600 hover:text-primary-800"}," Segna tutte come lette ")])):b("",!0)])])):b("",!0)]))}},fs={class:"relative"},xs={class:"flex items-start justify-center min-h-screen pt-16 px-4 pb-20 text-center sm:block sm:p-0"},ys={class:"inline-block align-bottom bg-white rounded-lg px-4 pt-5 pb-4 text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full sm:p-6"},_s={class:"flex items-center"},bs={class:"flex-1"},ks={key:0,class:"mt-4 max-h-64 overflow-y-auto"},ws={class:"space-y-1"},$s=["onClick"],Cs={class:"flex-shrink-0"},js={class:"h-4 w-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},Ms=["d"],zs={class:"ml-3 flex-1 min-w-0"},Ps={class:"text-sm font-medium text-gray-900 truncate"},Ss={class:"text-xs text-gray-500 truncate"},As={class:"ml-2 text-xs text-gray-400"},Es={key:1,class:"mt-4 text-center py-4"},Is={key:2,class:"mt-4 text-center py-4"},Vs={__name:"HeaderSearch",setup(n){const s=te(),l=C(!1),t=C(""),r=C([]),m=C(-1),u=C(!1),p=C(null),v=[{id:1,type:"project",title:"Website Redesign",description:"Progetto di redesign del sito web aziendale",path:"/app/projects/1"},{id:2,type:"person",title:"Mario Rossi",description:"Senior Developer",path:"/app/personnel/directory/1"},{id:3,type:"document",title:"Specifiche Tecniche",description:"Documento delle specifiche del progetto mobile",path:"/app/documents/1"},{id:4,type:"task",title:"Implementazione API",description:"Task per l'implementazione delle API REST",path:"/app/projects/1/tasks/5"}];ne(l,async j=>{var _;j?(await Se(),(_=p.value)==null||_.focus()):(t.value="",r.value=[],m.value=-1)});function d(){if(!t.value.trim()){r.value=[];return}u.value=!0,setTimeout(()=>{r.value=v.filter(j=>j.title.toLowerCase().includes(t.value.toLowerCase())||j.description.toLowerCase().includes(t.value.toLowerCase())),m.value=-1,u.value=!1},200)}function a(j){if(r.value.length===0)return;const _=m.value+j;_>=0&&_<r.value.length&&(m.value=_)}function g(){m.value>=0&&r.value[m.value]&&x(r.value[m.value])}function x(j){l.value=!1,s.push(j.path)}function k(j){const _={project:"h-6 w-6 rounded bg-blue-100 text-blue-600 flex items-center justify-center",person:"h-6 w-6 rounded bg-green-100 text-green-600 flex items-center justify-center",document:"h-6 w-6 rounded bg-yellow-100 text-yellow-600 flex items-center justify-center",task:"h-6 w-6 rounded bg-purple-100 text-purple-600 flex items-center justify-center"};return _[j]||_.document}function z(j){const _={project:"M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10",person:"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z",document:"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z",task:"M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"};return _[j]||_.document}function T(j){return{project:"Progetto",person:"Persona",document:"Documento",task:"Task"}[j]||"Elemento"}return(j,_)=>(o(),i("div",fs,[e("button",{onClick:_[0]||(_[0]=L=>l.value=!l.value),class:"p-2 rounded-full text-gray-400 hover:text-gray-600 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"},_[7]||(_[7]=[e("span",{class:"sr-only"},"Cerca",-1),e("svg",{class:"h-5 w-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"})],-1)])),l.value?(o(),i("div",{key:0,class:"fixed inset-0 z-50 overflow-y-auto",onClick:_[6]||(_[6]=pe(L=>l.value=!1,["self"]))},[e("div",xs,[_[11]||(_[11]=e("div",{class:"fixed inset-0 bg-black bg-opacity-25 transition-opacity"},null,-1)),e("div",ys,[e("div",null,[e("div",_s,[e("div",bs,[K(e("input",{ref_key:"searchInput",ref:p,"onUpdate:modelValue":_[1]||(_[1]=L=>t.value=L),onInput:d,onKeydown:[_[2]||(_[2]=re(L=>l.value=!1,["escape"])),re(g,["enter"]),_[3]||(_[3]=re(L=>a(-1),["up"])),_[4]||(_[4]=re(L=>a(1),["down"]))],type:"text",placeholder:"Cerca progetti, persone, documenti...",class:"block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 sm:text-sm"},null,544),[[ee,t.value]])]),e("button",{onClick:_[5]||(_[5]=L=>l.value=!1),class:"ml-3 p-2 text-gray-400 hover:text-gray-600"},_[8]||(_[8]=[e("svg",{class:"h-5 w-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)]))]),r.value.length>0?(o(),i("div",ks,[e("div",ws,[(o(!0),i(U,null,F(r.value,(L,W)=>(o(),i("div",{key:L.id,onClick:D=>x(L),class:A(["flex items-center px-3 py-2 rounded-md cursor-pointer",W===m.value?"bg-primary-50":"hover:bg-gray-50"])},[e("div",Cs,[e("div",{class:A(k(L.type))},[(o(),i("svg",js,[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:z(L.type)},null,8,Ms)]))],2)]),e("div",zs,[e("p",Ps,c(L.title),1),e("p",Ss,c(L.description),1)]),e("div",As,c(T(L.type)),1)],10,$s))),128))])])):t.value&&!u.value?(o(),i("div",Es,_[9]||(_[9]=[e("p",{class:"text-sm text-gray-500"},"Nessun risultato trovato",-1)]))):t.value?b("",!0):(o(),i("div",Is,_[10]||(_[10]=[e("p",{class:"text-xs text-gray-400"},"Inizia a digitare per cercare...",-1)])))])])])])):b("",!0)]))}},Ds={class:"relative"},Bs={class:"h-8 w-8 rounded-full bg-primary-100 flex items-center justify-center"},Ts={class:"text-sm font-medium text-primary-700"},Ls={class:"py-1"},Hs={class:"px-4 py-2 border-b border-gray-100 dark:border-gray-700"},qs={class:"text-sm font-medium text-gray-900 dark:text-white"},Rs={class:"text-xs text-gray-500 dark:text-gray-400"},Ns={__name:"HeaderUserMenu",setup(n){const s=te(),l=X(),t=C(!1),{isDarkMode:r,toggleDarkMode:m}=ge(),u=f(()=>l.user&&(l.user.name||l.user.username)||"Utente"),p=f(()=>{var a;return((a=l.user)==null?void 0:a.email)||""}),v=f(()=>l.user?u.value.charAt(0).toUpperCase():"U");async function d(){t.value=!1,await l.logout(),s.push("/auth/login")}return(a,g)=>{const x=R("router-link");return o(),i("div",Ds,[e("button",{onClick:g[0]||(g[0]=k=>t.value=!t.value),class:"flex items-center text-sm rounded-full focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"},[g[5]||(g[5]=e("span",{class:"sr-only"},"Apri menu utente",-1)),e("div",Bs,[e("span",Ts,c(v.value),1)])]),t.value?(o(),i("div",{key:0,onClick:g[4]||(g[4]=k=>t.value=!1),class:"origin-top-right absolute right-0 mt-2 w-48 rounded-md shadow-lg bg-white dark:bg-gray-800 ring-1 ring-black ring-opacity-5 z-50"},[e("div",Ls,[e("div",Hs,[e("p",qs,c(u.value),1),e("p",Rs,c(p.value),1)]),$(x,{to:"/app/profile",class:"block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700",onClick:g[1]||(g[1]=k=>t.value=!1)},{default:P(()=>g[6]||(g[6]=[S(" Il tuo profilo ")])),_:1,__:[6]}),$(x,{to:"/app/settings",class:"block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700",onClick:g[2]||(g[2]=k=>t.value=!1)},{default:P(()=>g[7]||(g[7]=[S(" Impostazioni ")])),_:1,__:[7]}),g[8]||(g[8]=e("div",{class:"border-t border-gray-100 dark:border-gray-700 my-1"},null,-1)),e("button",{onClick:g[3]||(g[3]=(...k)=>N(m)&&N(m)(...k)),class:"flex items-center justify-between w-full px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700"},[e("span",null,c(N(r)?"Modalità chiara":"Modalità scura"),1),e("i",{class:A([N(r)?"fas fa-sun":"fas fa-moon","text-xs"])},null,2)]),e("button",{onClick:d,class:"block w-full text-left px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700"}," Esci ")])])):b("",!0)])}}},Us={class:"bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700"},Fs={class:"flex justify-between items-center px-4 py-4 sm:px-6 lg:px-8"},Os={class:"flex items-center space-x-4"},Ks={class:"flex flex-col"},Gs={class:"text-lg font-semibold text-gray-900 dark:text-white"},Qs={class:"flex items-center space-x-4"},Ws={class:"hidden md:flex items-center space-x-2"},Js={__name:"AppHeader",props:{pageTitle:{type:String,required:!0},breadcrumbs:{type:Array,default:()=>[]}},emits:["toggle-mobile-sidebar","quick-create-project","quick-add-task"],setup(n){return(s,l)=>(o(),i("header",Us,[e("div",Fs,[e("div",Os,[e("button",{onClick:l[0]||(l[0]=t=>s.$emit("toggle-mobile-sidebar")),class:"lg:hidden p-2 rounded-md text-gray-400 hover:text-gray-600 hover:bg-gray-100 dark:text-gray-500 dark:hover:text-gray-300 dark:hover:bg-gray-700"},l[3]||(l[3]=[e("svg",{class:"h-6 w-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 6h16M4 12h16M4 18h16"})],-1)])),e("div",Ks,[e("h2",Gs,c(n.pageTitle),1),n.breadcrumbs.length>0?(o(),q(Qt,{key:0,breadcrumbs:n.breadcrumbs},null,8,["breadcrumbs"])):b("",!0)])]),e("div",Qs,[e("div",Ws,[$(Xt,{onQuickCreateProject:l[1]||(l[1]=t=>s.$emit("quick-create-project")),onQuickAddTask:l[2]||(l[2]=t=>s.$emit("quick-add-task"))})]),$(hs),$(Vs),$(Ns)])])]))}},Ys={__name:"LoadingSpinner",props:{size:{type:String,default:"md",validator:n=>["sm","md","lg","xl"].includes(n)},message:{type:String,default:""},centered:{type:Boolean,default:!0}},setup(n){const s=n,l=f(()=>{const u={sm:"20px",md:"32px",lg:"48px",xl:"64px"};return`width: ${u[s.size]}; height: ${u[s.size]};`}),t=f(()=>["flex",s.centered?"items-center justify-center":"","space-y-2"]),r=f(()=>["flex items-center justify-center"]),m=f(()=>["text-sm text-gray-600 text-center"]);return(u,p)=>(o(),i("div",{class:A(t.value)},[e("div",{class:A(r.value)},[e("div",{class:"animate-spin rounded-full border-2 border-gray-300 border-t-primary-600",style:ve(l.value)},null,4)],2),n.message?(o(),i("p",{key:0,class:A(m.value)},c(n.message),3)):b("",!0)],2))}},he=(n,s)=>{const l=n.__vccOpts||n;for(const[t,r]of s)l[t]=r;return l},Xs={class:"fixed bottom-0 right-0 z-50 p-6 space-y-4"},Zs={class:"p-4"},eo={class:"flex items-start"},to={class:"flex-shrink-0"},so={class:"h-5 w-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},oo=["d"],ro={class:"ml-3 w-0 flex-1 pt-0.5"},ao={class:"text-sm font-medium text-gray-900"},no={class:"mt-1 text-sm text-gray-500"},io={class:"ml-4 flex-shrink-0 flex"},lo=["onClick"],co={__name:"NotificationManager",setup(n){const s=C([]);function l(p){const v=Date.now(),d={id:v,type:p.type||"info",title:p.title,message:p.message,duration:p.duration||5e3};s.value.push(d),d.duration>0&&setTimeout(()=>{t(v)},d.duration)}function t(p){const v=s.value.findIndex(d=>d.id===p);v>-1&&s.value.splice(v,1)}function r(p){const v={success:"border-l-4 border-green-400",error:"border-l-4 border-red-400",warning:"border-l-4 border-yellow-400",info:"border-l-4 border-blue-400"};return v[p]||v.info}function m(p){const v={success:"h-8 w-8 rounded-full bg-green-100 text-green-600 flex items-center justify-center",error:"h-8 w-8 rounded-full bg-red-100 text-red-600 flex items-center justify-center",warning:"h-8 w-8 rounded-full bg-yellow-100 text-yellow-600 flex items-center justify-center",info:"h-8 w-8 rounded-full bg-blue-100 text-blue-600 flex items-center justify-center"};return v[p]||v.info}function u(p){const v={success:"M5 13l4 4L19 7",error:"M6 18L18 6M6 6l12 12",warning:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-2.694-.833-3.464 0L3.34 16.5c-.77.833.192 2.5 1.732 2.5z",info:"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"};return v[p]||v.info}return window.showNotification=l,J(()=>{}),(p,v)=>(o(),i("div",Xs,[$(De,{name:"notification",tag:"div",class:"space-y-4"},{default:P(()=>[(o(!0),i(U,null,F(s.value,d=>(o(),i("div",{key:d.id,class:A([r(d.type),"max-w-sm w-full bg-white shadow-lg rounded-lg pointer-events-auto ring-1 ring-black ring-opacity-5 overflow-hidden"])},[e("div",Zs,[e("div",eo,[e("div",to,[e("div",{class:A(m(d.type))},[(o(),i("svg",so,[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:u(d.type)},null,8,oo)]))],2)]),e("div",ro,[e("p",ao,c(d.title),1),e("p",no,c(d.message),1)]),e("div",io,[e("button",{onClick:a=>t(d.id),class:"bg-white rounded-md inline-flex text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"},v[0]||(v[0]=[e("span",{class:"sr-only"},"Chiudi",-1),e("svg",{class:"h-5 w-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)]),8,lo)])])])],2))),128))]),_:1})]))}},uo=he(co,[["__scopeId","data-v-220f0827"]]),mo={class:"h-screen flex bg-gray-50 dark:bg-gray-900"},po={class:"flex-1 overflow-y-auto bg-gray-50 dark:bg-gray-900"},vo={class:"py-6"},go={class:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8"},ho={key:0,class:"mb-6"},fo={key:1,class:"flex items-center justify-center h-64"},xo={__name:"AppLayout",setup(n){const s=me(),l=te(),t=se(),r=C(!1),m=C(!1),u=C(!1);f(()=>t.config||{});const p=f(()=>t.config!==null),v=f(()=>{var _;return(_=s.meta)!=null&&_.title?s.meta.title:{dashboard:"Dashboard",projects:"Progetti","projects-list":"Elenco Progetti","projects-view":"Dettaglio Progetto","projects-create":"Nuovo Progetto",personnel:"Personale","personnel-directory":"Rubrica Aziendale","personnel-orgchart":"Organigramma","personnel-skills":"Competenze"}[s.name]||"DatPortal"}),d=f(()=>{var j;return(j=s.meta)!=null&&j.breadcrumbs?s.meta.breadcrumbs.map(_=>({label:_.label,to:_.to,icon:_.icon})):[]}),a=f(()=>{var j;return((j=s.meta)==null?void 0:j.hasActions)||!1});function g(){r.value=!r.value}function x(){r.value=!1}function k(j){m.value=j}function z(){l.push("/app/projects/create")}function T(){s.name==="project-view"&&s.params.id?l.push(`/app/projects/${s.params.id}?tab=tasks&action=create`):l.push("/app/projects?action=create-task")}return ne(s,()=>{u.value=!0,setTimeout(()=>{u.value=!1},300)}),ne(s,()=>{x()}),J(()=>{p.value||t.loadConfig()}),(j,_)=>{const L=R("router-view");return o(),i("div",mo,[r.value?(o(),i("div",{key:0,onClick:x,class:"fixed inset-0 z-20 bg-black bg-opacity-50 lg:hidden"})):b("",!0),$(qt,{"is-mobile-open":r.value,onClose:x,onToggleCollapsed:k},null,8,["is-mobile-open"]),e("div",{class:A(["flex flex-col flex-1 overflow-hidden transition-all duration-300",[m.value?"lg:ml-20":"lg:ml-64"]])},[$(Js,{"page-title":v.value,breadcrumbs:d.value,onToggleMobileSidebar:g,onQuickCreateProject:z,onQuickAddTask:T},null,8,["page-title","breadcrumbs"]),e("main",po,[e("div",vo,[e("div",go,[a.value?(o(),i("div",ho,[Be(j.$slots,"page-actions")])):b("",!0),u.value?(o(),i("div",fo,[$(Ys)])):(o(),q(L,{key:2}))])])])],2),$(uo)])}}},yo={class:"min-h-screen bg-gray-50"},_o={class:"bg-white shadow-sm border-b"},bo={class:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8"},ko={class:"flex justify-between h-16"},wo={class:"flex items-center"},$o={class:"w-8 h-8 bg-primary-600 rounded flex items-center justify-center"},Co={class:"text-white font-bold text-sm"},jo={class:"text-xl font-semibold text-gray-900"},Mo={class:"hidden md:flex items-center space-x-8"},zo={class:"flex items-center space-x-4 ml-8 pl-8 border-l border-gray-200"},Po={class:"md:hidden flex items-center"},So={key:0,class:"md:hidden"},Ao={class:"px-2 pt-2 pb-3 space-y-1 sm:px-3 bg-white border-t"},Eo={class:"bg-gray-800 text-white"},Io={class:"max-w-7xl mx-auto py-12 px-4 sm:px-6 lg:px-8"},Vo={class:"grid grid-cols-1 md:grid-cols-4 gap-8"},Do={class:"col-span-1 md:col-span-2"},Bo={class:"flex items-center space-x-3 mb-4"},To={class:"w-8 h-8 bg-primary-600 rounded flex items-center justify-center"},Lo={class:"text-white font-bold text-sm"},Ho={class:"text-xl font-semibold"},qo={class:"text-gray-300 max-w-md"},Ro={class:"space-y-2"},No={class:"space-y-2 text-gray-300"},Uo={key:0},Fo={key:1},Oo={key:2},Ko={class:"mt-8 pt-8 border-t border-gray-700 text-center text-gray-400"},Pe={__name:"PublicLayout",setup(n){const s=se(),l=C(!1),t=f(()=>s.config||{}),r=f(()=>{var v;return((v=t.value.company)==null?void 0:v.name)||"DatVinci"}),m=f(()=>r.value.split(" ").map(d=>d[0]).join("").toUpperCase().slice(0,2)),u=f(()=>s.config!==null),p=new Date().getFullYear();return J(()=>{u.value||s.loadConfig()}),(v,d)=>{var x,k,z,T,j,_;const a=R("router-link"),g=R("router-view");return o(),i("div",yo,[e("nav",_o,[e("div",bo,[e("div",ko,[e("div",wo,[$(a,{to:"/",class:"flex items-center space-x-3"},{default:P(()=>[e("div",$o,[e("span",Co,c(m.value),1)]),e("span",jo,c(r.value),1)]),_:1})]),e("div",Mo,[$(a,{to:"/",class:"text-gray-700 hover:text-primary-600 px-3 py-2 text-sm font-medium"},{default:P(()=>d[1]||(d[1]=[S(" Home ")])),_:1,__:[1]}),$(a,{to:"/about",class:"text-gray-700 hover:text-primary-600 px-3 py-2 text-sm font-medium"},{default:P(()=>d[2]||(d[2]=[S(" Chi Siamo ")])),_:1,__:[2]}),$(a,{to:"/services",class:"text-gray-700 hover:text-primary-600 px-3 py-2 text-sm font-medium"},{default:P(()=>d[3]||(d[3]=[S(" Servizi ")])),_:1,__:[3]}),$(a,{to:"/contact",class:"text-gray-700 hover:text-primary-600 px-3 py-2 text-sm font-medium"},{default:P(()=>d[4]||(d[4]=[S(" Contatti ")])),_:1,__:[4]}),e("div",zo,[$(a,{to:"/auth/login",class:"text-gray-700 hover:text-primary-600 px-3 py-2 text-sm font-medium"},{default:P(()=>d[5]||(d[5]=[S(" Accedi ")])),_:1,__:[5]}),$(a,{to:"/auth/register",class:"bg-primary-600 text-white hover:bg-primary-700 px-4 py-2 rounded-md text-sm font-medium"},{default:P(()=>d[6]||(d[6]=[S(" Registrati ")])),_:1,__:[6]})])]),e("div",Po,[e("button",{onClick:d[0]||(d[0]=L=>l.value=!l.value),class:"text-gray-400 hover:text-gray-500"},d[7]||(d[7]=[e("svg",{class:"h-6 w-6",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 6h16M4 12h16M4 18h16"})],-1)]))])])]),l.value?(o(),i("div",So,[e("div",Ao,[$(a,{to:"/",class:"block px-3 py-2 text-base font-medium text-gray-700 hover:text-primary-600"},{default:P(()=>d[8]||(d[8]=[S(" Home ")])),_:1,__:[8]}),$(a,{to:"/about",class:"block px-3 py-2 text-base font-medium text-gray-700 hover:text-primary-600"},{default:P(()=>d[9]||(d[9]=[S(" Chi Siamo ")])),_:1,__:[9]}),$(a,{to:"/services",class:"block px-3 py-2 text-base font-medium text-gray-700 hover:text-primary-600"},{default:P(()=>d[10]||(d[10]=[S(" Servizi ")])),_:1,__:[10]}),$(a,{to:"/contact",class:"block px-3 py-2 text-base font-medium text-gray-700 hover:text-primary-600"},{default:P(()=>d[11]||(d[11]=[S(" Contatti ")])),_:1,__:[11]}),d[14]||(d[14]=e("hr",{class:"my-2"},null,-1)),$(a,{to:"/auth/login",class:"block px-3 py-2 text-base font-medium text-gray-700 hover:text-primary-600"},{default:P(()=>d[12]||(d[12]=[S(" Accedi ")])),_:1,__:[12]}),$(a,{to:"/auth/register",class:"block px-3 py-2 text-base font-medium bg-primary-600 text-white rounded-md"},{default:P(()=>d[13]||(d[13]=[S(" Registrati ")])),_:1,__:[13]})])])):b("",!0)]),e("main",null,[$(g)]),e("footer",Eo,[e("div",Io,[e("div",Vo,[e("div",Do,[e("div",Bo,[e("div",To,[e("span",Lo,c(m.value),1)]),e("span",Ho,c(r.value),1)]),e("p",qo,c(N(s).interpolateText((x=t.value.footer)==null?void 0:x.description)||((k=t.value.company)==null?void 0:k.description)||"Innovazione e tecnologia per il futuro digitale della tua azienda."),1)]),e("div",null,[d[19]||(d[19]=e("h3",{class:"text-sm font-semibold uppercase tracking-wider mb-4"},"Link Rapidi",-1)),e("ul",Ro,[e("li",null,[$(a,{to:"/",class:"text-gray-300 hover:text-white"},{default:P(()=>d[15]||(d[15]=[S("Home")])),_:1,__:[15]})]),e("li",null,[$(a,{to:"/about",class:"text-gray-300 hover:text-white"},{default:P(()=>d[16]||(d[16]=[S("Chi Siamo")])),_:1,__:[16]})]),e("li",null,[$(a,{to:"/services",class:"text-gray-300 hover:text-white"},{default:P(()=>d[17]||(d[17]=[S("Servizi")])),_:1,__:[17]})]),e("li",null,[$(a,{to:"/contact",class:"text-gray-300 hover:text-white"},{default:P(()=>d[18]||(d[18]=[S("Contatti")])),_:1,__:[18]})])])]),e("div",null,[d[20]||(d[20]=e("h3",{class:"text-sm font-semibold uppercase tracking-wider mb-4"},"Contatti",-1)),e("ul",No,[(z=t.value.contact)!=null&&z.email?(o(),i("li",Uo,c(t.value.contact.email),1)):b("",!0),(T=t.value.contact)!=null&&T.phone?(o(),i("li",Fo,c(t.value.contact.phone),1)):b("",!0),(j=t.value.contact)!=null&&j.address?(o(),i("li",Oo,c(t.value.contact.address),1)):b("",!0)])])]),e("div",Ko,[e("p",null,c(N(s).interpolateText((_=t.value.footer)==null?void 0:_.copyright)||`© ${N(p)} ${r.value}. Tutti i diritti riservati.`),1)])])])])}}},Go={class:"bg-white"},Qo={class:"relative overflow-hidden"},Wo={class:"max-w-7xl mx-auto"},Jo={class:"relative z-10 pb-8 bg-white sm:pb-16 md:pb-20 lg:max-w-2xl lg:w-full lg:pb-28 xl:pb-32"},Yo={class:"mt-10 mx-auto max-w-7xl px-4 sm:mt-12 sm:px-6 md:mt-16 lg:mt-20 lg:px-8 xl:mt-28"},Xo={class:"sm:text-center lg:text-left"},Zo={class:"text-4xl tracking-tight font-extrabold text-gray-900 sm:text-5xl md:text-6xl"},er={class:"block xl:inline"},tr={class:"mt-3 text-base text-gray-500 sm:mt-5 sm:text-lg sm:max-w-xl sm:mx-auto md:mt-5 md:text-xl lg:mx-0"},sr={class:"mt-5 sm:mt-8 sm:flex sm:justify-center lg:justify-start"},or={class:"rounded-md shadow"},rr={class:"mt-3 sm:mt-0 sm:ml-3"},ar={class:"py-12 bg-white"},nr={class:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8"},ir={class:"lg:text-center"},lr={class:"text-base text-primary-600 font-semibold tracking-wide uppercase"},dr={class:"mt-2 text-3xl leading-8 font-extrabold tracking-tight text-gray-900 sm:text-4xl"},cr={key:0,class:"mt-10"},ur={class:"space-y-10 md:space-y-0 md:grid md:grid-cols-2 md:gap-x-8 md:gap-y-10"},mr={class:"absolute flex items-center justify-center h-12 w-12 rounded-md bg-primary-500 text-white"},pr={class:"h-6 w-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},vr={key:0,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"},gr={key:1,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.25 2.25 0 11-4.5 0 2.25 2.25 0 014.5 0z"},hr={key:2,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"},fr={key:3,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M13 10V3L4 14h7v7l9-11h-7z"},xr={class:"ml-16 text-lg leading-6 font-medium text-gray-900"},yr={class:"mt-2 ml-16 text-base text-gray-500"},_r={__name:"Home",setup(n){const s=se(),l=f(()=>s.config||{}),t=f(()=>{var m;return((m=l.value.pages)==null?void 0:m.home)||{}}),r=f(()=>l.value.company||{});return J(()=>{s.config||s.loadConfig()}),(m,u)=>{var v,d,a,g;const p=R("router-link");return o(),i("div",Go,[e("div",Qo,[e("div",Wo,[e("div",Jo,[e("main",Yo,[e("div",Xo,[e("h1",Zo,[e("span",er,c(((v=t.value.hero)==null?void 0:v.title)||"Innovazione per il futuro"),1)]),e("p",tr,c(((d=t.value.hero)==null?void 0:d.subtitle)||N(s).interpolateText(r.value.description)||"Supportiamo le aziende nel loro percorso di crescita attraverso soluzioni tecnologiche all'avanguardia"),1),e("div",sr,[e("div",or,[$(p,{to:"/services",class:"w-full flex items-center justify-center px-8 py-3 border border-transparent text-base font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 md:py-4 md:text-lg md:px-10"},{default:P(()=>{var x;return[S(c(((x=t.value.hero)==null?void 0:x.cta_primary)||"Scopri i nostri servizi"),1)]}),_:1})]),e("div",rr,[$(p,{to:"/contact",class:"w-full flex items-center justify-center px-8 py-3 border border-transparent text-base font-medium rounded-md text-primary-700 bg-primary-100 hover:bg-primary-200 md:py-4 md:text-lg md:px-10"},{default:P(()=>{var x;return[S(c(((x=t.value.hero)==null?void 0:x.cta_secondary)||"Contattaci"),1)]}),_:1})])])])])])]),u[0]||(u[0]=e("div",{class:"lg:absolute lg:inset-y-0 lg:right-0 lg:w-1/2"},[e("div",{class:"h-56 w-full bg-gradient-to-r from-primary-400 to-primary-600 sm:h-72 md:h-96 lg:w-full lg:h-full flex items-center justify-center"},[e("svg",{class:"h-24 w-24 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"})])])],-1))]),e("div",ar,[e("div",nr,[e("div",ir,[e("h2",lr,c(((a=t.value.services_section)==null?void 0:a.title)||"I nostri servizi"),1),e("p",dr,c(((g=t.value.services_section)==null?void 0:g.subtitle)||"Soluzioni innovative per ogni esigenza aziendale"),1)]),r.value.platform_features?(o(),i("div",cr,[e("div",ur,[(o(!0),i(U,null,F(r.value.platform_features,x=>(o(),i("div",{key:x.title,class:"relative"},[e("div",mr,[(o(),i("svg",pr,[x.icon==="briefcase"?(o(),i("path",vr)):x.icon==="users"?(o(),i("path",gr)):x.icon==="chart"?(o(),i("path",hr)):(o(),i("path",fr))]))]),e("p",xr,c(x.title),1),e("p",yr,c(x.description),1)]))),128))])])):b("",!0)])])])}}},br={class:"py-16 bg-white"},kr={class:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8"},wr={class:"text-center"},$r={class:"text-4xl font-extrabold text-gray-900 sm:text-5xl"},Cr={class:"mt-4 text-xl text-gray-600"},jr={key:0,class:"mt-16"},Mr={class:"max-w-3xl mx-auto"},zr={class:"text-3xl font-bold text-gray-900 text-center mb-8"},Pr={class:"text-lg text-gray-700 leading-relaxed"},Sr={class:"mt-16 grid grid-cols-1 md:grid-cols-2 gap-12"},Ar={key:0,class:"bg-gray-50 p-8 rounded-lg"},Er={class:"text-2xl font-bold text-gray-900 mb-4"},Ir={class:"text-gray-700"},Vr={key:1,class:"bg-gray-50 p-8 rounded-lg"},Dr={class:"text-2xl font-bold text-gray-900 mb-4"},Br={class:"text-gray-700"},Tr={key:1,class:"mt-16"},Lr={class:"text-center mb-12"},Hr={class:"text-3xl font-bold text-gray-900"},qr={class:"mt-4 text-xl text-gray-600"},Rr={class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8"},Nr={class:"text-lg font-semibold text-gray-900"},Ur={key:2,class:"mt-16"},Fr={class:"text-center"},Or={class:"text-3xl font-bold text-gray-900"},Kr={class:"mt-4 text-xl text-gray-600"},Gr={class:"mt-8 inline-flex items-center px-6 py-3 bg-primary-50 rounded-lg"},Qr={class:"text-primary-900 font-medium"},Wr={__name:"About",setup(n){const s=se(),l=f(()=>s.config||{}),t=f(()=>{var m;return((m=l.value.pages)==null?void 0:m.about)||{}}),r=f(()=>l.value.company||{});return J(()=>{s.config||s.loadConfig()}),(m,u)=>{var p,v;return o(),i("div",br,[e("div",kr,[e("div",wr,[e("h1",$r,c(((p=t.value.hero)==null?void 0:p.title)||"Chi Siamo"),1),e("p",Cr,c(((v=t.value.hero)==null?void 0:v.subtitle)||"La nostra storia e i nostri valori"),1)]),t.value.story_section?(o(),i("div",jr,[e("div",Mr,[e("h2",zr,c(t.value.story_section.title),1),e("p",Pr,c(N(s).interpolateText(t.value.story_section.content)),1)])])):b("",!0),e("div",Sr,[t.value.mission_section?(o(),i("div",Ar,[e("h3",Er,c(t.value.mission_section.title),1),e("p",Ir,c(N(s).interpolateText(t.value.mission_section.content)),1)])):b("",!0),t.value.vision_section?(o(),i("div",Vr,[e("h3",Dr,c(t.value.vision_section.title),1),e("p",Br,c(N(s).interpolateText(t.value.vision_section.content)),1)])):b("",!0)]),t.value.expertise_section&&r.value.expertise?(o(),i("div",Tr,[e("div",Lr,[e("h2",Hr,c(t.value.expertise_section.title),1),e("p",qr,c(t.value.expertise_section.subtitle),1)]),e("div",Rr,[(o(!0),i(U,null,F(r.value.expertise,d=>(o(),i("div",{key:d,class:"bg-white p-6 rounded-lg shadow-sm border border-gray-200 text-center"},[u[0]||(u[0]=e("div",{class:"w-12 h-12 bg-primary-100 rounded-lg flex items-center justify-center mx-auto mb-4"},[e("svg",{class:"w-6 h-6 text-primary-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M13 10V3L4 14h7v7l9-11h-7z"})])],-1)),e("h3",Nr,c(d),1)]))),128))])])):b("",!0),t.value.team_section?(o(),i("div",Ur,[e("div",Fr,[e("h2",Or,c(t.value.team_section.title),1),e("p",Kr,c(t.value.team_section.subtitle),1),e("div",Gr,[u[1]||(u[1]=e("svg",{class:"w-5 h-5 text-primary-600 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"})],-1)),e("span",Qr,c(r.value.team_size),1)])])])):b("",!0)])])}}},Jr={class:"py-16 bg-white"},Yr={class:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8"},Xr={class:"text-center"},Zr={class:"text-4xl font-extrabold text-gray-900 sm:text-5xl"},ea={class:"mt-4 text-xl text-gray-600"},ta={key:0,class:"mt-8 text-center"},sa={class:"text-lg text-gray-700 max-w-3xl mx-auto"},oa={class:"mt-16 grid grid-cols-1 lg:grid-cols-2 gap-16"},ra={key:0},aa={class:"text-2xl font-bold text-gray-900 mb-8"},na={class:"block text-sm font-medium text-gray-700 mb-2"},ia={class:"block text-sm font-medium text-gray-700 mb-2"},la={class:"block text-sm font-medium text-gray-700 mb-2"},da=["disabled"],ca={key:1},ua={class:"text-2xl font-bold text-gray-900 mb-8"},ma={class:"space-y-6"},pa={key:0,class:"flex items-start"},va={class:"font-medium text-gray-900"},ga={class:"text-gray-600"},ha={key:1,class:"flex items-start"},fa={class:"font-medium text-gray-900"},xa={class:"text-gray-600"},ya={key:2,class:"flex items-start"},_a={class:"font-medium text-gray-900"},ba={class:"text-gray-600"},ka={key:3,class:"flex items-start"},wa={class:"font-medium text-gray-900"},$a={class:"text-gray-600"},Ca={__name:"Contact",setup(n){const s=se(),l=f(()=>s.config||{}),t=f(()=>{var d;return((d=l.value.pages)==null?void 0:d.contact)||{}}),r=f(()=>l.value.contact||{}),m=C({name:"",email:"",message:""}),u=C(!1),p=C({text:"",type:""}),v=async()=>{var d,a;if(!m.value.name||!m.value.email||!m.value.message){p.value={text:((d=t.value.form)==null?void 0:d.error_message)||"Tutti i campi sono obbligatori",type:"error"};return}u.value=!0,p.value={text:"",type:""};try{await new Promise(g=>setTimeout(g,1e3)),p.value={text:((a=t.value.form)==null?void 0:a.success_message)||"Messaggio inviato con successo!",type:"success"},m.value={name:"",email:"",message:""}}catch{p.value={text:"Errore durante l'invio. Riprova più tardi.",type:"error"}}finally{u.value=!1}};return J(()=>{s.config||s.loadConfig()}),(d,a)=>{var g,x;return o(),i("div",Jr,[e("div",Yr,[e("div",Xr,[e("h1",Zr,c(((g=t.value.hero)==null?void 0:g.title)||"Contattaci"),1),e("p",ea,c(((x=t.value.hero)==null?void 0:x.subtitle)||"Siamo qui per aiutarti"),1)]),t.value.intro?(o(),i("div",ta,[e("p",sa,c(t.value.intro.content),1)])):b("",!0),e("div",oa,[t.value.form?(o(),i("div",ra,[e("h2",aa,c(t.value.form.title),1),e("form",{onSubmit:pe(v,["prevent"]),class:"space-y-6"},[e("div",null,[e("label",na,c(t.value.form.name_label),1),K(e("input",{"onUpdate:modelValue":a[0]||(a[0]=k=>m.value.name=k),type:"text",required:"",class:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"},null,512),[[ee,m.value.name]])]),e("div",null,[e("label",ia,c(t.value.form.email_label),1),K(e("input",{"onUpdate:modelValue":a[1]||(a[1]=k=>m.value.email=k),type:"email",required:"",class:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"},null,512),[[ee,m.value.email]])]),e("div",null,[e("label",la,c(t.value.form.message_label),1),K(e("textarea",{"onUpdate:modelValue":a[2]||(a[2]=k=>m.value.message=k),rows:"6",required:"",class:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"},null,512),[[ee,m.value.message]])]),e("button",{type:"submit",disabled:u.value,class:"w-full bg-primary-600 text-white py-3 px-6 rounded-lg font-medium hover:bg-primary-700 disabled:opacity-50"},c(u.value?"Invio in corso...":t.value.form.submit_button),9,da),p.value.text?(o(),i("div",{key:0,class:A([p.value.type==="success"?"text-green-600":"text-red-600","text-sm mt-2"])},c(p.value.text),3)):b("",!0)],32)])):b("",!0),t.value.info?(o(),i("div",ca,[e("h2",ua,c(t.value.info.title),1),e("div",ma,[r.value.address?(o(),i("div",pa,[a[3]||(a[3]=e("svg",{class:"w-6 h-6 text-primary-600 mt-1 mr-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"}),e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M15 11a3 3 0 11-6 0 3 3 0 016 0z"})],-1)),e("div",null,[e("h3",va,c(t.value.info.address_label),1),e("p",ga,c(r.value.address),1)])])):b("",!0),r.value.phone?(o(),i("div",ha,[a[4]||(a[4]=e("svg",{class:"w-6 h-6 text-primary-600 mt-1 mr-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"})],-1)),e("div",null,[e("h3",fa,c(t.value.info.phone_label),1),e("p",xa,c(r.value.phone),1)])])):b("",!0),r.value.email?(o(),i("div",ya,[a[5]||(a[5]=e("svg",{class:"w-6 h-6 text-primary-600 mt-1 mr-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"})],-1)),e("div",null,[e("h3",_a,c(t.value.info.email_label),1),e("p",ba,c(r.value.email),1)])])):b("",!0),r.value.hours?(o(),i("div",ka,[a[6]||(a[6]=e("svg",{class:"w-6 h-6 text-primary-600 mt-1 mr-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"})],-1)),e("div",null,[e("h3",wa,c(t.value.info.hours_label),1),e("p",$a,c(r.value.hours),1)])])):b("",!0)])])):b("",!0)])])])}}},ja={class:"py-16 bg-white"},Ma={class:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8"},za={class:"text-center"},Pa={class:"text-4xl font-extrabold text-gray-900 sm:text-5xl"},Sa={class:"mt-4 text-xl text-gray-600"},Aa={key:0,class:"mt-8 text-center"},Ea={class:"text-lg text-gray-700 max-w-3xl mx-auto"},Ia={key:1,class:"mt-16"},Va={class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8"},Da={class:"text-xl font-bold text-gray-900 text-center mb-4"},Ba={class:"text-gray-600 text-center"},Ta={key:2,class:"mt-20"},La={class:"bg-primary-50 rounded-2xl p-12 text-center"},Ha={class:"text-3xl font-bold text-gray-900 mb-4"},qa={class:"text-xl text-gray-600 mb-8"},Ra={__name:"Services",setup(n){const s=se(),l=f(()=>s.config||{}),t=f(()=>{var u;return((u=l.value.pages)==null?void 0:u.services)||{}}),r=f(()=>l.value.company||{}),m=u=>({"Sviluppo Software":"Soluzioni software personalizzate per ottimizzare i processi aziendali e migliorare l'efficienza operativa.","Intelligenza Artificiale":"Implementazione di sistemi AI avanzati per automatizzare processi e analizzare dati complessi.","Consulenza IT":"Consulenza strategica per la trasformazione digitale e l'ottimizzazione dell'infrastruttura tecnologica.","Gestione Progetti Innovativi":"Coordinamento e gestione di progetti tecnologici complessi con metodologie agili.","Supporto su Bandi e Finanziamenti":"Assistenza nella ricerca e gestione di bandi pubblici e finanziamenti per l'innovazione."})[u]||"Servizio professionale di alta qualità per supportare la crescita della tua azienda.";return J(()=>{s.config||s.loadConfig()}),(u,p)=>{var d,a;const v=R("router-link");return o(),i("div",ja,[e("div",Ma,[e("div",za,[e("h1",Pa,c(((d=t.value.hero)==null?void 0:d.title)||"I nostri servizi"),1),e("p",Sa,c(((a=t.value.hero)==null?void 0:a.subtitle)||"Soluzioni complete per la tua azienda"),1)]),t.value.intro?(o(),i("div",Aa,[e("p",Ea,c(t.value.intro.content),1)])):b("",!0),r.value.expertise?(o(),i("div",Ia,[e("div",Va,[(o(!0),i(U,null,F(r.value.expertise,g=>(o(),i("div",{key:g,class:"bg-white p-8 rounded-lg shadow-lg border border-gray-200 hover:shadow-xl transition-shadow"},[p[0]||(p[0]=e("div",{class:"w-16 h-16 bg-primary-100 rounded-lg flex items-center justify-center mx-auto mb-6"},[e("svg",{class:"w-8 h-8 text-primary-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M13 10V3L4 14h7v7l9-11h-7z"})])],-1)),e("h3",Da,c(g),1),e("p",Ba,c(m(g)),1)]))),128))])])):b("",!0),t.value.cta?(o(),i("div",Ta,[e("div",La,[e("h2",Ha,c(t.value.cta.title),1),e("p",qa,c(t.value.cta.subtitle),1),$(v,{to:"/contact",class:"inline-flex items-center px-8 py-4 bg-primary-600 text-white font-medium rounded-lg hover:bg-primary-700 transition-colors"},{default:P(()=>[S(c(t.value.cta.button)+" ",1),p[1]||(p[1]=e("svg",{class:"ml-2 w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M17 8l4 4m0 0l-4 4m4-4H3"})],-1))]),_:1,__:[1]})])])):b("",!0)])])}}},Na={class:"min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8"},Ua={class:"max-w-md w-full space-y-8"},Fa={class:"mt-2 text-center text-sm text-gray-600"},Oa={key:0,class:"rounded-md bg-red-50 p-4"},Ka={class:"text-sm text-red-700"},Ga={class:"rounded-md shadow-sm -space-y-px"},Qa={class:"flex items-center justify-between"},Wa={class:"flex items-center"},Ja=["disabled"],Ya={key:0,class:"absolute left-0 inset-y-0 flex items-center pl-3"},Xa={__name:"Login",setup(n){const s=te(),l=X(),t=C({username:"",password:"",remember:!1}),r=f(()=>l.loading),m=f(()=>l.error);async function u(){(await l.login({username:t.value.username,password:t.value.password,remember:t.value.remember})).success&&s.push("/app/dashboard")}return(p,v)=>{const d=R("router-link");return o(),i("div",Na,[e("div",Ua,[e("div",null,[v[5]||(v[5]=e("div",{class:"mx-auto h-12 w-12 flex items-center justify-center rounded-full bg-primary-100"},[e("svg",{class:"h-6 w-6 text-primary-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"})])],-1)),v[6]||(v[6]=e("h2",{class:"mt-6 text-center text-3xl font-extrabold text-gray-900"}," Accedi al tuo account ",-1)),e("p",Fa,[v[4]||(v[4]=S(" Oppure ")),$(d,{to:"/auth/register",class:"font-medium text-primary-600 hover:text-primary-500"},{default:P(()=>v[3]||(v[3]=[S(" registrati per un nuovo account ")])),_:1,__:[3]})])]),e("form",{onSubmit:pe(u,["prevent"]),class:"mt-8 space-y-6"},[m.value?(o(),i("div",Oa,[e("div",Ka,c(m.value),1)])):b("",!0),e("div",Ga,[e("div",null,[v[7]||(v[7]=e("label",{for:"username",class:"sr-only"},"Username",-1)),K(e("input",{id:"username","onUpdate:modelValue":v[0]||(v[0]=a=>t.value.username=a),name:"username",type:"text",autocomplete:"username",required:"",class:"appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-t-md focus:outline-none focus:ring-primary-500 focus:border-primary-500 focus:z-10 sm:text-sm",placeholder:"Username"},null,512),[[ee,t.value.username]])]),e("div",null,[v[8]||(v[8]=e("label",{for:"password",class:"sr-only"},"Password",-1)),K(e("input",{id:"password","onUpdate:modelValue":v[1]||(v[1]=a=>t.value.password=a),name:"password",type:"password",autocomplete:"current-password",required:"",class:"appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-b-md focus:outline-none focus:ring-primary-500 focus:border-primary-500 focus:z-10 sm:text-sm",placeholder:"Password"},null,512),[[ee,t.value.password]])])]),e("div",Qa,[e("div",Wa,[K(e("input",{id:"remember-me","onUpdate:modelValue":v[2]||(v[2]=a=>t.value.remember=a),name:"remember-me",type:"checkbox",class:"h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"},null,512),[[Te,t.value.remember]]),v[9]||(v[9]=e("label",{for:"remember-me",class:"ml-2 block text-sm text-gray-900"}," Ricordami ",-1))]),v[10]||(v[10]=e("div",{class:"text-sm"},[e("a",{href:"#",class:"font-medium text-primary-600 hover:text-primary-500"}," Password dimenticata? ")],-1))]),e("div",null,[e("button",{type:"submit",disabled:r.value,class:"group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50 disabled:cursor-not-allowed"},[r.value?(o(),i("span",Ya,v[11]||(v[11]=[e("svg",{class:"h-5 w-5 text-primary-500 animate-spin",fill:"none",viewBox:"0 0 24 24"},[e("circle",{class:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor","stroke-width":"4"}),e("path",{class:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})],-1)]))):b("",!0),S(" "+c(r.value?"Accesso in corso...":"Accedi"),1)],8,Ja)])],32)])])}}},Za={},en={class:"min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8"};function tn(n,s){return o(),i("div",en,s[0]||(s[0]=[e("div",{class:"max-w-md w-full space-y-8"},[e("div",null,[e("h2",{class:"mt-6 text-center text-3xl font-extrabold text-gray-900"}," Registra un nuovo account ")]),e("div",{class:"text-center text-gray-600"}," Registrazione in arrivo... ")],-1)]))}const sn=he(Za,[["render",tn]]),on={class:"bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg"},rn={class:"p-5"},an={class:"flex items-center"},nn={class:"ml-5 w-0 flex-1"},ln={class:"text-sm font-medium text-gray-500 dark:text-gray-400 truncate"},dn={class:"text-lg font-medium text-gray-900 dark:text-white"},cn={key:0,class:"text-xs text-gray-500 dark:text-gray-400"},un={key:0,class:"bg-gray-50 dark:bg-gray-700 px-5 py-3"},mn={class:"text-sm"},ae={__name:"StatsCard",props:{title:{type:String,required:!0},value:{type:[Number,String],required:!0},subtitle:{type:String,default:null},icon:{type:String,required:!0},color:{type:String,default:"primary"},link:{type:String,default:null}},setup(n){const s=t=>t==="project"?{template:`<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
      </svg>`}:t==="users"?{template:`<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
      </svg>`}:t==="clock"?{template:`<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
      </svg>`}:t==="team"?{template:`<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
      </svg>`}:{template:`<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
    </svg>`},l=t=>{const r={primary:"bg-primary-500",secondary:"bg-secondary-500",red:"bg-red-500",yellow:"bg-yellow-500",blue:"bg-blue-500",green:"bg-green-500"};return r[t]||r.primary};return(t,r)=>{const m=R("router-link");return o(),i("div",on,[e("div",rn,[e("div",an,[e("div",{class:A(["flex-shrink-0 rounded-md p-3",l(n.color)])},[(o(),q(Ae(s(n.icon)),{class:"h-6 w-6 text-white"}))],2),e("div",nn,[e("dl",null,[e("dt",ln,c(n.title),1),e("dd",null,[e("div",dn,c(n.value),1),n.subtitle?(o(),i("div",cn,c(n.subtitle),1)):b("",!0)])])])])]),n.link?(o(),i("div",un,[e("div",mn,[$(m,{to:n.link,class:"font-medium text-primary-600 dark:text-primary-400 hover:text-primary-500 dark:hover:text-primary-300"},{default:P(()=>r[0]||(r[0]=[S(" Vedi tutti ")])),_:1,__:[0]},8,["to"])])])):b("",!0)])}}},pn={class:"py-6"},vn={class:"flex flex-col md:flex-row md:items-center md:justify-between mb-8"},gn={class:"mt-4 md:mt-0 flex space-x-3"},hn={class:"relative"},fn=["disabled"],xn={class:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 mb-8"},yn={class:"grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8"},_n={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6"},bn={class:"relative h-64"},kn={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6"},wn={class:"relative h-64"},$n={class:"grid grid-cols-1 lg:grid-cols-3 gap-6"},Cn={class:"bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden"},jn={class:"p-6"},Mn={key:0,class:"text-center py-8 text-gray-500"},zn={key:1,class:"space-y-4"},Pn={class:"flex justify-between items-start"},Sn={class:"flex-1"},An={class:"text-sm font-medium text-gray-900 dark:text-white"},En={class:"text-xs text-gray-500 dark:text-gray-400"},In={class:"mt-2 flex justify-between items-center"},Vn={class:"text-xs text-gray-500 dark:text-gray-400"},Dn={class:"bg-gray-50 dark:bg-gray-700 px-6 py-3"},Bn={class:"text-sm"},Tn={class:"bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden"},Ln={class:"p-6"},Hn={key:0,class:"text-center py-8 text-gray-500"},qn={key:1,class:"space-y-4"},Rn={class:"flex-shrink-0"},Nn={class:"flex-1 min-w-0"},Un={class:"text-sm font-medium text-gray-900 dark:text-white"},Fn={class:"text-xs text-gray-500 dark:text-gray-400"},On={class:"text-xs text-gray-400 dark:text-gray-500"},Kn={class:"bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden"},Gn={class:"p-6"},Qn={key:0,class:"text-center py-8 text-gray-500"},Wn={key:1,class:"space-y-4"},Jn={class:"flex justify-between items-start"},Yn={class:"flex-1"},Xn={class:"text-sm font-medium text-gray-900 dark:text-white"},Zn={class:"text-xs text-gray-500 dark:text-gray-400"},ei={class:"text-right"},ti={class:"text-sm font-bold text-gray-900 dark:text-white"},si={class:"text-xs text-gray-500"},oi={class:"mt-2"},ri={class:"w-full bg-gray-200 rounded-full h-2"},ai={class:"text-xs text-gray-500 mt-1"},ni={__name:"Dashboard",setup(n){le.register(...Le),te(),X();const s=C(!1),l=C("7"),t=C({}),r=C([]),m=C([]),u=C([]),p=C(null),v=C(null);let d=null,a=null;const g=async()=>{try{const y=await fetch("/api/dashboard/stats");if(!y.ok)throw new Error("Failed to fetch stats");const w=await y.json();t.value=w.data}catch(y){console.error("Error fetching dashboard stats:",y),t.value={}}},x=async()=>{try{const y=await fetch(`/api/dashboard/upcoming-tasks?days=${l.value}&limit=5`);if(!y.ok)throw new Error("Failed to fetch upcoming tasks");const w=await y.json();r.value=w.data.tasks}catch(y){console.error("Error fetching upcoming tasks:",y),r.value=[]}},k=async()=>{try{const y=await fetch("/api/dashboard/recent-activities?limit=5");if(!y.ok)throw new Error("Failed to fetch recent activities");const w=await y.json();m.value=w.data.activities}catch(y){console.error("Error fetching recent activities:",y),m.value=[]}},z=async()=>{try{const y=await fetch("/api/dashboard/kpis?limit=3");if(!y.ok)throw new Error("Failed to fetch KPIs");const w=await y.json();u.value=w.data.kpis}catch(y){console.error("Error fetching KPIs:",y),u.value=[]}},T=async()=>{try{const y=await fetch("/api/dashboard/charts/project-status");if(!y.ok)throw new Error("Failed to fetch project chart data");const w=await y.json();_(w.data.chart)}catch(y){console.error("Error fetching project chart:",y)}},j=async()=>{try{const y=await fetch("/api/dashboard/charts/task-status");if(!y.ok)throw new Error("Failed to fetch task chart data");const w=await y.json();L(w.data.chart)}catch(y){console.error("Error fetching task chart:",y)}},_=y=>{if(!p.value)return;const w=p.value.getContext("2d");d&&d.destroy(),d=new le(w,{type:"doughnut",data:{labels:y.labels,datasets:[{data:y.data,backgroundColor:["#3B82F6","#10B981","#F59E0B","#EF4444","#8B5CF6"],borderWidth:2,borderColor:"#ffffff"}]},options:{responsive:!0,maintainAspectRatio:!1,plugins:{legend:{position:"bottom",labels:{padding:20,usePointStyle:!0}}}}})},L=y=>{if(!v.value)return;const w=v.value.getContext("2d");a&&a.destroy(),a=new le(w,{type:"bar",data:{labels:y.labels,datasets:[{label:"Tasks",data:y.data,backgroundColor:["#60A5FA","#34D399","#FBBF24","#F87171"],borderColor:["#3B82F6","#10B981","#F59E0B","#EF4444"],borderWidth:1}]},options:{responsive:!0,maintainAspectRatio:!1,plugins:{legend:{display:!1}},scales:{y:{beginAtZero:!0,ticks:{stepSize:1}}}}})},W=async()=>{s.value=!0;try{await Promise.all([g(),x(),k(),z(),T(),j()])}finally{s.value=!1}},D=y=>new Date(y).toLocaleDateString("it-IT"),M=y=>{const w=new Date(y),G=Math.floor((new Date-w)/(1e3*60));return G<60?`${G} minuti fa`:G<1440?`${Math.floor(G/60)} ore fa`:`${Math.floor(G/1440)} giorni fa`},h=y=>{const w={high:"bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200",medium:"bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200",low:"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200"};return w[y]||w.medium},E=y=>{const w={todo:"bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300","in-progress":"bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200",review:"bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200",done:"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200"};return w[y]||w.todo},H=y=>{const w={task:{template:`<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
      </svg>`},timesheet:{template:`<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
      </svg>`},event:{template:`<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
      </svg>`}};return w[y]||w.task},B=y=>{const w={task:"bg-blue-100 text-blue-600",timesheet:"bg-green-100 text-green-600",event:"bg-purple-100 text-purple-600"};return w[y]||w.task},I=y=>y>=90?"bg-green-500":y>=70?"bg-yellow-500":"bg-red-500";return J(async()=>{await W(),await Se(),p.value&&v.value&&(await T(),await j())}),(y,w)=>{var G,fe,xe,ye,_e,be,ke,we;const oe=R("router-link");return o(),i("div",pn,[e("div",vn,[w[4]||(w[4]=e("div",null,[e("h1",{class:"text-2xl font-bold text-gray-900 dark:text-white"},"Dashboard"),e("p",{class:"mt-1 text-sm text-gray-600 dark:text-gray-400"}," Benvenuto! Ecco una panoramica delle attività della tua azienda. ")],-1)),e("div",gn,[e("div",hn,[K(e("select",{"onUpdate:modelValue":w[0]||(w[0]=V=>l.value=V),onChange:W,class:"bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-md py-2 pl-3 pr-10 text-sm font-medium text-gray-700 dark:text-gray-300 focus:outline-none focus:ring-primary-500 focus:border-primary-500"},w[1]||(w[1]=[e("option",{value:"7"},"Ultimi 7 giorni",-1),e("option",{value:"30"},"Ultimo mese",-1),e("option",{value:"90"},"Ultimi 3 mesi",-1)]),544),[[de,l.value]])]),e("button",{onClick:W,disabled:s.value,type:"button",class:"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50"},[(o(),i("svg",{xmlns:"http://www.w3.org/2000/svg",class:A(["h-4 w-4 mr-2",{"animate-spin":s.value}]),viewBox:"0 0 20 20",fill:"currentColor"},w[2]||(w[2]=[e("path",{"fill-rule":"evenodd",d:"M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z","clip-rule":"evenodd"},null,-1)]),2)),w[3]||(w[3]=S(" Aggiorna "))],8,fn)])]),e("div",xn,[$(ae,{title:"Progetti Attivi",value:((G=t.value.projects)==null?void 0:G.active)||0,subtitle:`di ${((fe=t.value.projects)==null?void 0:fe.total)||0} totali`,icon:"project",color:"primary",link:"/app/projects?status=active"},null,8,["value","subtitle"]),$(ae,{title:"Clienti",value:((xe=t.value.team)==null?void 0:xe.clients)||0,icon:"users",color:"secondary",link:"/app/crm/clients"},null,8,["value"]),$(ae,{title:"Task Pendenti",value:((ye=t.value.tasks)==null?void 0:ye.pending)||0,subtitle:`${((_e=t.value.tasks)==null?void 0:_e.overdue)||0} in ritardo`,icon:"clock",color:((be=t.value.tasks)==null?void 0:be.overdue)>0?"red":"yellow",link:"/app/tasks?status=pending"},null,8,["value","subtitle","color"]),$(ae,{title:"Team Members",value:((ke=t.value.team)==null?void 0:ke.users)||0,subtitle:`${((we=t.value.team)==null?void 0:we.departments)||0} dipartimenti`,icon:"team",color:"blue",link:"/app/personnel"},null,8,["value","subtitle"])]),e("div",yn,[e("div",_n,[w[5]||(w[5]=e("div",{class:"flex items-center justify-between mb-4"},[e("h2",{class:"text-lg font-medium text-gray-900 dark:text-white"},"Stato Progetti")],-1)),e("div",bn,[e("canvas",{ref_key:"projectChart",ref:p},null,512)])]),e("div",kn,[w[6]||(w[6]=e("div",{class:"flex items-center justify-between mb-4"},[e("h2",{class:"text-lg font-medium text-gray-900 dark:text-white"},"Stato Attività")],-1)),e("div",wn,[e("canvas",{ref_key:"taskChart",ref:v},null,512)])])]),e("div",$n,[e("div",Cn,[e("div",jn,[w[7]||(w[7]=e("h2",{class:"text-lg font-medium text-gray-900 dark:text-white mb-4"},"Attività in Scadenza",-1)),r.value.length===0?(o(),i("div",Mn," Nessuna attività in scadenza ")):(o(),i("div",zn,[(o(!0),i(U,null,F(r.value,V=>(o(),i("div",{key:V.id,class:"border-b border-gray-200 dark:border-gray-700 pb-4 last:border-b-0 last:pb-0"},[e("div",Pn,[e("div",Sn,[e("h3",An,c(V.name),1),e("p",En,c(V.project_name),1)]),e("span",{class:A(["inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium",h(V.priority)])},c(V.priority),3)]),e("div",In,[e("span",Vn," Scadenza: "+c(D(V.due_date)),1),e("span",{class:A(["inline-flex items-center px-2 py-1 rounded-full text-xs font-medium",E(V.status)])},c(V.status),3)])]))),128))]))]),e("div",Dn,[e("div",Bn,[$(oe,{to:"/app/tasks",class:"font-medium text-primary-600 dark:text-primary-400 hover:text-primary-500"},{default:P(()=>w[8]||(w[8]=[S(" Vedi tutte le attività ")])),_:1,__:[8]})])])]),e("div",Tn,[e("div",Ln,[w[9]||(w[9]=e("h2",{class:"text-lg font-medium text-gray-900 dark:text-white mb-4"},"Attività Recenti",-1)),m.value.length===0?(o(),i("div",Hn," Nessuna attività recente ")):(o(),i("div",qn,[(o(!0),i(U,null,F(m.value,V=>(o(),i("div",{key:`${V.type}-${V.id}`,class:"flex items-start space-x-3"},[e("div",Rn,[e("div",{class:A(["w-8 h-8 rounded-full flex items-center justify-center",B(V.type)])},[(o(),q(Ae(H(V.type)),{class:"w-4 h-4"}))],2)]),e("div",Nn,[e("p",Un,c(V.title),1),e("p",Fn,c(V.description),1),e("p",On,c(M(V.timestamp)),1)])]))),128))]))])]),e("div",Kn,[e("div",Gn,[w[10]||(w[10]=e("h2",{class:"text-lg font-medium text-gray-900 dark:text-white mb-4"},"KPIs Principali",-1)),u.value.length===0?(o(),i("div",Qn," Nessun KPI configurato ")):(o(),i("div",Wn,[(o(!0),i(U,null,F(u.value,V=>(o(),i("div",{key:V.id,class:"border-b border-gray-200 dark:border-gray-700 pb-4 last:border-b-0 last:pb-0"},[e("div",Jn,[e("div",Yn,[e("h3",Xn,c(V.name),1),e("p",Zn,c(V.description),1)]),e("div",ei,[e("p",ti,c(V.current_value)+c(V.unit),1),e("p",si," Target: "+c(V.target_value)+c(V.unit),1)])]),e("div",oi,[e("div",ri,[e("div",{class:A(["h-2 rounded-full",I(V.performance_percentage)]),style:ve({width:Math.min(V.performance_percentage,100)+"%"})},null,6)]),e("p",ai,c(Math.round(V.performance_percentage))+"% del target",1)])]))),128))]))])])])])}}},ii=ue("projects",()=>{const n=C([]),s=C(null),l=C(!1),t=C(null),r=C(new Map),m=C({page:1,perPage:20,total:0,totalPages:0}),u=C({search:"",status:"",client:"",type:""}),p=f(()=>{let h=n.value;if(u.value.search){const E=u.value.search.toLowerCase();h=h.filter(H=>{var B,I,y;return H.name.toLowerCase().includes(E)||((B=H.description)==null?void 0:B.toLowerCase().includes(E))||((y=(I=H.client)==null?void 0:I.name)==null?void 0:y.toLowerCase().includes(E))})}return u.value.status&&(h=h.filter(E=>E.status===u.value.status)),u.value.client&&(h=h.filter(E=>E.client_id===u.value.client)),u.value.type&&(h=h.filter(E=>E.project_type===u.value.type)),h}),v=f(()=>{const h={};return n.value.forEach(E=>{h[E.status]||(h[E.status]=[]),h[E.status].push(E)}),h}),d=async(h={})=>{var E,H;l.value=!0,t.value=null;try{const B=new URLSearchParams({page:h.page||m.value.page,per_page:h.perPage||m.value.perPage,search:h.search||u.value.search,status:h.status||u.value.status,client:h.client||u.value.client,type:h.type||u.value.type}),I=await O.get(`/api/projects?${B}`);I.data.success&&(n.value=I.data.data.projects,m.value=I.data.data.pagination)}catch(B){t.value=((H=(E=B.response)==null?void 0:E.data)==null?void 0:H.message)||"Errore nel caricamento progetti",console.error("Error fetching projects:",B)}finally{l.value=!1}},a=async(h,E=!1)=>{var H,B;if(!E&&r.value.has(h)){const I=r.value.get(h);return s.value=I,I}l.value=!0,t.value=null;try{const I=await O.get(`/api/projects/${h}`);if(I.data.success){const y=I.data.data.project;return s.value=y,r.value.set(h,y),y}}catch(I){throw t.value=((B=(H=I.response)==null?void 0:H.data)==null?void 0:B.message)||"Errore nel caricamento progetto",console.error("Error fetching project:",I),I}finally{l.value=!1}};return{projects:n,currentProject:s,loading:l,error:t,pagination:m,filters:u,filteredProjects:p,projectsByStatus:v,fetchProjects:d,fetchProject:a,createProject:async h=>{var E,H;l.value=!0,t.value=null;try{const B=await O.post("/api/projects",h);if(B.data.success){const I=B.data.data.project;return n.value.unshift(I),I}}catch(B){throw t.value=((H=(E=B.response)==null?void 0:E.data)==null?void 0:H.message)||"Errore nella creazione progetto",console.error("Error creating project:",B),B}finally{l.value=!1}},updateProject:async(h,E)=>{var H,B,I;l.value=!0,t.value=null;try{const y=await O.put(`/api/projects/${h}`,E);if(y.data.success){const w=y.data.data.project,oe=n.value.findIndex(G=>G.id===h);return oe!==-1&&(n.value[oe]=w),((H=s.value)==null?void 0:H.id)===h&&(s.value=w),r.value.set(h,w),w}}catch(y){throw t.value=((I=(B=y.response)==null?void 0:B.data)==null?void 0:I.message)||"Errore nell'aggiornamento progetto",console.error("Error updating project:",y),y}finally{l.value=!1}},deleteProject:async h=>{var E,H,B;l.value=!0,t.value=null;try{(await O.delete(`/api/projects/${h}`)).data.success&&(n.value=n.value.filter(y=>y.id!==h),((E=s.value)==null?void 0:E.id)===h&&(s.value=null),r.value.delete(h))}catch(I){throw t.value=((B=(H=I.response)==null?void 0:H.data)==null?void 0:B.message)||"Errore nell'eliminazione progetto",console.error("Error deleting project:",I),I}finally{l.value=!1}},setFilters:h=>{u.value={...u.value,...h}},clearFilters:()=>{u.value={search:"",status:"",client:"",type:""}},setCurrentProject:h=>{s.value=h},clearCurrentProject:()=>{s.value=null},clearCache:()=>{r.value.clear()},refreshProject:async h=>await a(h,!0),getCachedProject:h=>r.value.get(h),$reset:()=>{n.value=[],s.value=null,l.value=!1,t.value=null,r.value.clear(),m.value={page:1,perPage:20,total:0,totalPages:0},u.value={search:"",status:"",client:"",type:""}}}}),li={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6 mb-6"},di={class:"grid grid-cols-1 md:grid-cols-4 gap-4"},ci=["value"],ui={class:"bg-white dark:bg-gray-800 shadow rounded-lg"},mi={class:"px-6 py-4 border-b border-gray-200 dark:border-gray-700"},pi={class:"text-lg font-medium text-gray-900 dark:text-white"},vi={key:0,class:"p-6 text-center"},gi={key:1,class:"p-6 text-center"},hi={key:2,class:"divide-y divide-gray-200 dark:divide-gray-700"},fi=["onClick"],xi={class:"flex items-center justify-between"},yi={class:"flex-1"},_i={class:"flex items-center"},bi={class:"text-lg font-medium text-gray-900 dark:text-white"},ki={class:"mt-1 text-sm text-gray-600 dark:text-gray-400"},wi={class:"mt-2 flex items-center text-sm text-gray-500 dark:text-gray-400"},$i={key:0},Ci={key:1,class:"mx-2"},ji={key:2},Mi={key:3,class:"mx-2"},zi={key:4},Pi={class:"ml-4 flex items-center space-x-2"},Si={class:"text-right"},Ai={class:"text-sm font-medium text-gray-900 dark:text-white"},Ei={class:"w-20 bg-gray-200 dark:bg-gray-600 rounded-full h-2 mt-1"},Ii={__name:"Projects",setup(n){const s=te(),l=ii(),t=C(!0),r=C(""),m=C({status:"",client:""}),u=f(()=>l.projects),p=C([]),v=f(()=>{let D=u.value;if(m.value.status&&(D=D.filter(M=>M.status===m.value.status)),m.value.client&&(D=D.filter(M=>M.client_id==m.value.client)),r.value){const M=r.value.toLowerCase();D=D.filter(h=>h.name.toLowerCase().includes(M)||h.description&&h.description.toLowerCase().includes(M)||h.client&&h.client.name&&h.client.name.toLowerCase().includes(M))}return D}),d=async()=>{t.value=!0;try{await l.fetchProjects(),p.value=[]}catch(D){console.error("Error loading projects:",D)}finally{t.value=!1}},a=()=>{},g=()=>{},x=()=>{m.value={status:"",client:""},r.value=""},k=()=>{s.push("/app/projects/create")},z=D=>{s.push(`/app/projects/${D}`)},T=D=>({planning:"bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400",active:"bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400",completed:"bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400","on-hold":"bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400"})[D]||"bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400",j=D=>({planning:"Pianificazione",active:"Attivo",completed:"Completato","on-hold":"In Pausa"})[D]||D,_=D=>new Date(D).toLocaleDateString("it-IT"),L=D=>new Intl.NumberFormat("it-IT",{style:"currency",currency:"EUR"}).format(D),W=D=>({planning:10,active:50,completed:100,"on-hold":25})[D.status]||0;return J(()=>{d()}),(D,M)=>(o(),i("div",null,[e("div",{class:"mb-6"},[e("div",{class:"flex flex-col sm:flex-row sm:items-center sm:justify-between"},[M[4]||(M[4]=e("div",null,[e("h1",{class:"text-2xl font-bold text-gray-900 dark:text-white"},"Progetti"),e("p",{class:"mt-1 text-sm text-gray-500 dark:text-gray-400"}," Gestisci e monitora tutti i progetti aziendali ")],-1)),e("div",{class:"mt-4 sm:mt-0"},[e("button",{onClick:k,class:"inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"},M[3]||(M[3]=[e("svg",{class:"h-4 w-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 4v16m8-8H4"})],-1),S(" Nuovo Progetto ")]))])])]),e("div",li,[e("div",di,[e("div",null,[M[6]||(M[6]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"},"Stato",-1)),K(e("select",{"onUpdate:modelValue":M[0]||(M[0]=h=>m.value.status=h),onChange:g,class:"w-full border border-gray-300 dark:border-gray-600 rounded-md px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary-500"},M[5]||(M[5]=[He('<option value="">Tutti gli stati</option><option value="planning">Pianificazione</option><option value="active">Attivo</option><option value="completed">Completato</option><option value="on-hold">In Pausa</option>',5)]),544),[[de,m.value.status]])]),e("div",null,[M[8]||(M[8]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"},"Cliente",-1)),K(e("select",{"onUpdate:modelValue":M[1]||(M[1]=h=>m.value.client=h),onChange:g,class:"w-full border border-gray-300 dark:border-gray-600 rounded-md px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary-500"},[M[7]||(M[7]=e("option",{value:""},"Tutti i clienti",-1)),(o(!0),i(U,null,F(p.value,h=>(o(),i("option",{key:h.id,value:h.id},c(h.name),9,ci))),128))],544),[[de,m.value.client]])]),e("div",null,[M[9]||(M[9]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"},"Ricerca",-1)),K(e("input",{"onUpdate:modelValue":M[2]||(M[2]=h=>r.value=h),onInput:a,type:"text",placeholder:"Cerca progetti...",class:"w-full border border-gray-300 dark:border-gray-600 rounded-md px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary-500"},null,544),[[ee,r.value]])]),e("div",{class:"flex items-end"},[e("button",{onClick:x,class:"w-full px-4 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600"}," Reset Filtri ")])])]),e("div",ui,[e("div",mi,[e("h3",pi," Progetti ("+c(v.value.length)+") ",1)]),t.value?(o(),i("div",vi,M[10]||(M[10]=[e("div",{class:"animate-spin rounded-full h-8 w-8 border-b-2 border-primary-500 mx-auto"},null,-1),e("p",{class:"mt-2 text-gray-600 dark:text-gray-400"},"Caricamento progetti...",-1)]))):v.value.length===0?(o(),i("div",gi,M[11]||(M[11]=[e("svg",{class:"mx-auto h-12 w-12 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})],-1),e("h3",{class:"mt-2 text-sm font-medium text-gray-900 dark:text-white"},"Nessun progetto",-1),e("p",{class:"mt-1 text-sm text-gray-500 dark:text-gray-400"},"Inizia creando il tuo primo progetto.",-1)]))):(o(),i("div",hi,[(o(!0),i(U,null,F(v.value,h=>(o(),i("div",{key:h.id,class:"p-6 hover:bg-gray-50 dark:hover:bg-gray-700 cursor-pointer",onClick:E=>z(h.id)},[e("div",xi,[e("div",yi,[e("div",_i,[e("h4",bi,c(h.name),1),e("span",{class:A([T(h.status),"ml-3 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"])},c(j(h.status)),3)]),e("p",ki,c(h.description),1),e("div",wi,[h.client?(o(),i("span",$i,"Cliente: "+c(h.client.name),1)):b("",!0),h.client?(o(),i("span",Ci,"•")):b("",!0),h.end_date?(o(),i("span",ji,"Scadenza: "+c(_(h.end_date)),1)):b("",!0),h.end_date&&h.budget?(o(),i("span",Mi,"•")):b("",!0),h.budget?(o(),i("span",zi,"Budget: "+c(L(h.budget)),1)):b("",!0)])]),e("div",Pi,[e("div",Si,[e("div",Ai,c(W(h))+"% ",1),e("div",Ei,[e("div",{class:"bg-primary-600 h-2 rounded-full",style:ve({width:W(h)+"%"})},null,4)])]),M[12]||(M[12]=e("svg",{class:"h-5 w-5 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 5l7 7-7 7"})],-1))])])],8,fi))),128))]))])]))}},Vi={};function Di(n,s){return o(),i("div",null,s[0]||(s[0]=[e("h1",{class:"text-2xl font-bold text-gray-900 mb-6"},"Personale",-1),e("div",{class:"card"},[e("p",{class:"text-gray-600"},"Gestione personale in fase di migrazione...")],-1)]))}const Bi=he(Vi,[["render",Di]]),Ti=[{path:"/",component:Pe,children:[{path:"",name:"home",component:_r},{path:"about",name:"about",component:Wr},{path:"contact",name:"contact",component:Ca},{path:"services",name:"services",component:Ra}]},{path:"/auth",component:Pe,children:[{path:"login",name:"login",component:Xa},{path:"register",name:"register",component:sn}]},{path:"/app",component:xo,meta:{requiresAuth:!0},children:[{path:"",redirect:"/app/dashboard"},{path:"dashboard",name:"dashboard",component:ni,meta:{requiresAuth:!0,requiredPermission:"view_dashboard"}},{path:"projects",name:"projects",component:Ii,meta:{requiresAuth:!0,requiredPermission:"view_all_projects"}},{path:"projects/create",name:"projects-create",component:()=>Z(()=>import("./ProjectCreate.js"),__vite__mapDeps([0,1])),meta:{requiresAuth:!0,requiredPermission:"edit_project"}},{path:"projects/:id",name:"project-view",component:()=>Z(()=>import("./ProjectView.js"),__vite__mapDeps([2,1,3])),meta:{requiresAuth:!0,requiredPermission:"view_all_projects"}},{path:"projects/:id/edit",name:"project-edit",component:()=>Z(()=>import("./ProjectEdit.js"),__vite__mapDeps([4,1])),meta:{requiresAuth:!0,requiredPermission:"edit_project"}},{path:"personnel",name:"personnel",component:Bi,meta:{requiresAuth:!0,requiredPermission:"view_personnel_data"}},{path:"admin",redirect:"/app/admin/users"},{path:"admin/users",name:"admin-users",component:()=>Z(()=>import("./Admin.js"),__vite__mapDeps([5,1])),meta:{requiresAuth:!0,requiredPermission:"admin_access"}},{path:"admin/kpi-templates",name:"admin-kpi-templates",component:()=>Z(()=>import("./KPITemplates.js"),__vite__mapDeps([6,1])),meta:{requiresAuth:!0,requiredPermission:"admin_access"}},{path:"profile",name:"profile",component:()=>Z(()=>import("./Profile.js"),__vite__mapDeps([7,1])),meta:{requiresAuth:!0}},{path:"settings",name:"settings",component:()=>Z(()=>import("./Settings.js"),__vite__mapDeps([8,1])),meta:{requiresAuth:!0}}]}],Ie=qe({history:Re(),routes:Ti});Ie.beforeEach(async(n,s,l)=>{const t=X();if(n.meta.requiresAuth){if(t.sessionChecked||await t.initializeAuth(),!t.isAuthenticated){l("/auth/login");return}if(n.meta.requiredPermission&&!t.hasPermission(n.meta.requiredPermission)){console.warn(`Accesso negato a ${n.path}: permesso '${n.meta.requiredPermission}' richiesto`),l("/app/dashboard");return}}l()});const ie=Ne(Ke),Li=Ue();ie.use(Li);ie.use(Ie);const Hi=X();Hi.initializeAuth().then(()=>{console.log("Auth initialized successfully"),ie.mount("#app")}).catch(n=>{console.error("Auth initialization failed:",n),ie.mount("#app")});export{he as _,We as a,ii as b,O as c,ge as d,X as u};
