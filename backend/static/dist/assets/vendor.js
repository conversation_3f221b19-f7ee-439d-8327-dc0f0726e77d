var Yd=Object.defineProperty;var Xd=(e,t,n)=>t in e?Yd(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n;var N=(e,t,n)=>Xd(e,typeof t!="symbol"?t+"":t,n);/**
* @vue/shared v3.5.16
* (c) 2018-present <PERSON><PERSON> (<PERSON>) <PERSON> and Vue contributors
* @license MIT
**//*! #__NO_SIDE_EFFECTS__ */function ga(e){const t=Object.create(null);for(const n of e.split(","))t[n]=1;return n=>n in t}const pt={},Zn=[],Le=()=>{},Gd=()=>!1,Mo=e=>e.charCodeAt(0)===111&&e.charCodeAt(1)===110&&(e.charCodeAt(2)>122||e.charCodeAt(2)<97),ma=e=>e.startsWith("onUpdate:"),Dt=Object.assign,ba=(e,t)=>{const n=e.indexOf(t);n>-1&&e.splice(n,1)},Jd=Object.prototype.hasOwnProperty,ut=(e,t)=>Jd.call(e,t),q=Array.isArray,ts=e=>ms(e)==="[object Map]",gs=e=>ms(e)==="[object Set]",ll=e=>ms(e)==="[object Date]",Qd=e=>ms(e)==="[object RegExp]",tt=e=>typeof e=="function",Mt=e=>typeof e=="string",_e=e=>typeof e=="symbol",gt=e=>e!==null&&typeof e=="object",Uu=e=>(gt(e)||tt(e))&&tt(e.then)&&tt(e.catch),Ku=Object.prototype.toString,ms=e=>Ku.call(e),Zd=e=>ms(e).slice(8,-1),qu=e=>ms(e)==="[object Object]",ya=e=>Mt(e)&&e!=="NaN"&&e[0]!=="-"&&""+parseInt(e,10)===e,Ns=ga(",key,ref,ref_for,ref_key,onVnodeBeforeMount,onVnodeMounted,onVnodeBeforeUpdate,onVnodeUpdated,onVnodeBeforeUnmount,onVnodeUnmounted"),Co=e=>{const t=Object.create(null);return n=>t[n]||(t[n]=e(n))},tp=/-(\w)/g,ge=Co(e=>e.replace(tp,(t,n)=>n?n.toUpperCase():"")),ep=/\B([A-Z])/g,_n=Co(e=>e.replace(ep,"-$1").toLowerCase()),Po=Co(e=>e.charAt(0).toUpperCase()+e.slice(1)),Jo=Co(e=>e?`on${Po(e)}`:""),fn=(e,t)=>!Object.is(e,t),es=(e,...t)=>{for(let n=0;n<e.length;n++)e[n](...t)},Yu=(e,t,n,s=!1)=>{Object.defineProperty(e,t,{configurable:!0,enumerable:!1,writable:s,value:n})},eo=e=>{const t=parseFloat(e);return isNaN(t)?e:t},np=e=>{const t=Mt(e)?Number(e):NaN;return isNaN(t)?e:t};let cl;const Eo=()=>cl||(cl=typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:typeof global<"u"?global:{});function _a(e){if(q(e)){const t={};for(let n=0;n<e.length;n++){const s=e[n],i=Mt(s)?rp(s):_a(s);if(i)for(const o in i)t[o]=i[o]}return t}else if(Mt(e)||gt(e))return e}const sp=/;(?![^(]*\))/g,ip=/:([^]+)/,op=/\/\*[^]*?\*\//g;function rp(e){const t={};return e.replace(op,"").split(sp).forEach(n=>{if(n){const s=n.split(ip);s.length>1&&(t[s[0].trim()]=s[1].trim())}}),t}function xa(e){let t="";if(Mt(e))t=e;else if(q(e))for(let n=0;n<e.length;n++){const s=xa(e[n]);s&&(t+=s+" ")}else if(gt(e))for(const n in e)e[n]&&(t+=n+" ");return t.trim()}const ap="itemscope,allowfullscreen,formnovalidate,ismap,nomodule,novalidate,readonly",lp=ga(ap);function Xu(e){return!!e||e===""}function cp(e,t){if(e.length!==t.length)return!1;let n=!0;for(let s=0;n&&s<e.length;s++)n=jn(e[s],t[s]);return n}function jn(e,t){if(e===t)return!0;let n=ll(e),s=ll(t);if(n||s)return n&&s?e.getTime()===t.getTime():!1;if(n=_e(e),s=_e(t),n||s)return e===t;if(n=q(e),s=q(t),n||s)return n&&s?cp(e,t):!1;if(n=gt(e),s=gt(t),n||s){if(!n||!s)return!1;const i=Object.keys(e).length,o=Object.keys(t).length;if(i!==o)return!1;for(const r in e){const a=e.hasOwnProperty(r),l=t.hasOwnProperty(r);if(a&&!l||!a&&l||!jn(e[r],t[r]))return!1}}return String(e)===String(t)}function va(e,t){return e.findIndex(n=>jn(n,t))}const Gu=e=>!!(e&&e.__v_isRef===!0),up=e=>Mt(e)?e:e==null?"":q(e)||gt(e)&&(e.toString===Ku||!tt(e.toString))?Gu(e)?up(e.value):JSON.stringify(e,Ju,2):String(e),Ju=(e,t)=>Gu(t)?Ju(e,t.value):ts(t)?{[`Map(${t.size})`]:[...t.entries()].reduce((n,[s,i],o)=>(n[Qo(s,o)+" =>"]=i,n),{})}:gs(t)?{[`Set(${t.size})`]:[...t.values()].map(n=>Qo(n))}:_e(t)?Qo(t):gt(t)&&!q(t)&&!qu(t)?String(t):t,Qo=(e,t="")=>{var n;return _e(e)?`Symbol(${(n=e.description)!=null?n:t})`:e};/**
* @vue/reactivity v3.5.16
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/let zt;class Qu{constructor(t=!1){this.detached=t,this._active=!0,this._on=0,this.effects=[],this.cleanups=[],this._isPaused=!1,this.parent=zt,!t&&zt&&(this.index=(zt.scopes||(zt.scopes=[])).push(this)-1)}get active(){return this._active}pause(){if(this._active){this._isPaused=!0;let t,n;if(this.scopes)for(t=0,n=this.scopes.length;t<n;t++)this.scopes[t].pause();for(t=0,n=this.effects.length;t<n;t++)this.effects[t].pause()}}resume(){if(this._active&&this._isPaused){this._isPaused=!1;let t,n;if(this.scopes)for(t=0,n=this.scopes.length;t<n;t++)this.scopes[t].resume();for(t=0,n=this.effects.length;t<n;t++)this.effects[t].resume()}}run(t){if(this._active){const n=zt;try{return zt=this,t()}finally{zt=n}}}on(){++this._on===1&&(this.prevScope=zt,zt=this)}off(){this._on>0&&--this._on===0&&(zt=this.prevScope,this.prevScope=void 0)}stop(t){if(this._active){this._active=!1;let n,s;for(n=0,s=this.effects.length;n<s;n++)this.effects[n].stop();for(this.effects.length=0,n=0,s=this.cleanups.length;n<s;n++)this.cleanups[n]();if(this.cleanups.length=0,this.scopes){for(n=0,s=this.scopes.length;n<s;n++)this.scopes[n].stop(!0);this.scopes.length=0}if(!this.detached&&this.parent&&!t){const i=this.parent.scopes.pop();i&&i!==this&&(this.parent.scopes[this.index]=i,i.index=this.index)}this.parent=void 0}}}function Zu(e){return new Qu(e)}function tf(){return zt}function fp(e,t=!1){zt&&zt.cleanups.push(e)}let yt;const Zo=new WeakSet;class ef{constructor(t){this.fn=t,this.deps=void 0,this.depsTail=void 0,this.flags=5,this.next=void 0,this.cleanup=void 0,this.scheduler=void 0,zt&&zt.active&&zt.effects.push(this)}pause(){this.flags|=64}resume(){this.flags&64&&(this.flags&=-65,Zo.has(this)&&(Zo.delete(this),this.trigger()))}notify(){this.flags&2&&!(this.flags&32)||this.flags&8||sf(this)}run(){if(!(this.flags&1))return this.fn();this.flags|=2,ul(this),of(this);const t=yt,n=be;yt=this,be=!0;try{return this.fn()}finally{rf(this),yt=t,be=n,this.flags&=-3}}stop(){if(this.flags&1){for(let t=this.deps;t;t=t.nextDep)Ma(t);this.deps=this.depsTail=void 0,ul(this),this.onStop&&this.onStop(),this.flags&=-2}}trigger(){this.flags&64?Zo.add(this):this.scheduler?this.scheduler():this.runIfDirty()}runIfDirty(){kr(this)&&this.run()}get dirty(){return kr(this)}}let nf=0,Bs,zs;function sf(e,t=!1){if(e.flags|=8,t){e.next=zs,zs=e;return}e.next=Bs,Bs=e}function wa(){nf++}function Sa(){if(--nf>0)return;if(zs){let t=zs;for(zs=void 0;t;){const n=t.next;t.next=void 0,t.flags&=-9,t=n}}let e;for(;Bs;){let t=Bs;for(Bs=void 0;t;){const n=t.next;if(t.next=void 0,t.flags&=-9,t.flags&1)try{t.trigger()}catch(s){e||(e=s)}t=n}}if(e)throw e}function of(e){for(let t=e.deps;t;t=t.nextDep)t.version=-1,t.prevActiveLink=t.dep.activeLink,t.dep.activeLink=t}function rf(e){let t,n=e.depsTail,s=n;for(;s;){const i=s.prevDep;s.version===-1?(s===n&&(n=i),Ma(s),hp(s)):t=s,s.dep.activeLink=s.prevActiveLink,s.prevActiveLink=void 0,s=i}e.deps=t,e.depsTail=n}function kr(e){for(let t=e.deps;t;t=t.nextDep)if(t.dep.version!==t.version||t.dep.computed&&(af(t.dep.computed)||t.dep.version!==t.version))return!0;return!!e._dirty}function af(e){if(e.flags&4&&!(e.flags&16)||(e.flags&=-17,e.globalVersion===Gs)||(e.globalVersion=Gs,!e.isSSR&&e.flags&128&&(!e.deps&&!e._dirty||!kr(e))))return;e.flags|=2;const t=e.dep,n=yt,s=be;yt=e,be=!0;try{of(e);const i=e.fn(e._value);(t.version===0||fn(i,e._value))&&(e.flags|=128,e._value=i,t.version++)}catch(i){throw t.version++,i}finally{yt=n,be=s,rf(e),e.flags&=-3}}function Ma(e,t=!1){const{dep:n,prevSub:s,nextSub:i}=e;if(s&&(s.nextSub=i,e.prevSub=void 0),i&&(i.prevSub=s,e.nextSub=void 0),n.subs===e&&(n.subs=s,!s&&n.computed)){n.computed.flags&=-5;for(let o=n.computed.deps;o;o=o.nextDep)Ma(o,!0)}!t&&!--n.sc&&n.map&&n.map.delete(n.key)}function hp(e){const{prevDep:t,nextDep:n}=e;t&&(t.nextDep=n,e.prevDep=void 0),n&&(n.prevDep=t,e.nextDep=void 0)}let be=!0;const lf=[];function Xe(){lf.push(be),be=!1}function Ge(){const e=lf.pop();be=e===void 0?!0:e}function ul(e){const{cleanup:t}=e;if(e.cleanup=void 0,t){const n=yt;yt=void 0;try{t()}finally{yt=n}}}let Gs=0;class dp{constructor(t,n){this.sub=t,this.dep=n,this.version=n.version,this.nextDep=this.prevDep=this.nextSub=this.prevSub=this.prevActiveLink=void 0}}class Ca{constructor(t){this.computed=t,this.version=0,this.activeLink=void 0,this.subs=void 0,this.map=void 0,this.key=void 0,this.sc=0}track(t){if(!yt||!be||yt===this.computed)return;let n=this.activeLink;if(n===void 0||n.sub!==yt)n=this.activeLink=new dp(yt,this),yt.deps?(n.prevDep=yt.depsTail,yt.depsTail.nextDep=n,yt.depsTail=n):yt.deps=yt.depsTail=n,cf(n);else if(n.version===-1&&(n.version=this.version,n.nextDep)){const s=n.nextDep;s.prevDep=n.prevDep,n.prevDep&&(n.prevDep.nextDep=s),n.prevDep=yt.depsTail,n.nextDep=void 0,yt.depsTail.nextDep=n,yt.depsTail=n,yt.deps===n&&(yt.deps=s)}return n}trigger(t){this.version++,Gs++,this.notify(t)}notify(t){wa();try{for(let n=this.subs;n;n=n.prevSub)n.sub.notify()&&n.sub.dep.notify()}finally{Sa()}}}function cf(e){if(e.dep.sc++,e.sub.flags&4){const t=e.dep.computed;if(t&&!e.dep.subs){t.flags|=20;for(let s=t.deps;s;s=s.nextDep)cf(s)}const n=e.dep.subs;n!==e&&(e.prevSub=n,n&&(n.nextSub=e)),e.dep.subs=e}}const no=new WeakMap,Fn=Symbol(""),Ar=Symbol(""),Js=Symbol("");function Ht(e,t,n){if(be&&yt){let s=no.get(e);s||no.set(e,s=new Map);let i=s.get(n);i||(s.set(n,i=new Ca),i.map=s,i.key=n),i.track()}}function We(e,t,n,s,i,o){const r=no.get(e);if(!r){Gs++;return}const a=l=>{l&&l.trigger()};if(wa(),t==="clear")r.forEach(a);else{const l=q(e),c=l&&ya(n);if(l&&n==="length"){const u=Number(s);r.forEach((f,h)=>{(h==="length"||h===Js||!_e(h)&&h>=u)&&a(f)})}else switch((n!==void 0||r.has(void 0))&&a(r.get(n)),c&&a(r.get(Js)),t){case"add":l?c&&a(r.get("length")):(a(r.get(Fn)),ts(e)&&a(r.get(Ar)));break;case"delete":l||(a(r.get(Fn)),ts(e)&&a(r.get(Ar)));break;case"set":ts(e)&&a(r.get(Fn));break}}Sa()}function pp(e,t){const n=no.get(e);return n&&n.get(t)}function Kn(e){const t=lt(e);return t===e?t:(Ht(t,"iterate",Js),he(e)?t:t.map(Nt))}function ko(e){return Ht(e=lt(e),"iterate",Js),e}const gp={__proto__:null,[Symbol.iterator](){return tr(this,Symbol.iterator,Nt)},concat(...e){return Kn(this).concat(...e.map(t=>q(t)?Kn(t):t))},entries(){return tr(this,"entries",e=>(e[1]=Nt(e[1]),e))},every(e,t){return Ie(this,"every",e,t,void 0,arguments)},filter(e,t){return Ie(this,"filter",e,t,n=>n.map(Nt),arguments)},find(e,t){return Ie(this,"find",e,t,Nt,arguments)},findIndex(e,t){return Ie(this,"findIndex",e,t,void 0,arguments)},findLast(e,t){return Ie(this,"findLast",e,t,Nt,arguments)},findLastIndex(e,t){return Ie(this,"findLastIndex",e,t,void 0,arguments)},forEach(e,t){return Ie(this,"forEach",e,t,void 0,arguments)},includes(...e){return er(this,"includes",e)},indexOf(...e){return er(this,"indexOf",e)},join(e){return Kn(this).join(e)},lastIndexOf(...e){return er(this,"lastIndexOf",e)},map(e,t){return Ie(this,"map",e,t,void 0,arguments)},pop(){return xs(this,"pop")},push(...e){return xs(this,"push",e)},reduce(e,...t){return fl(this,"reduce",e,t)},reduceRight(e,...t){return fl(this,"reduceRight",e,t)},shift(){return xs(this,"shift")},some(e,t){return Ie(this,"some",e,t,void 0,arguments)},splice(...e){return xs(this,"splice",e)},toReversed(){return Kn(this).toReversed()},toSorted(e){return Kn(this).toSorted(e)},toSpliced(...e){return Kn(this).toSpliced(...e)},unshift(...e){return xs(this,"unshift",e)},values(){return tr(this,"values",Nt)}};function tr(e,t,n){const s=ko(e),i=s[t]();return s!==e&&!he(e)&&(i._next=i.next,i.next=()=>{const o=i._next();return o.value&&(o.value=n(o.value)),o}),i}const mp=Array.prototype;function Ie(e,t,n,s,i,o){const r=ko(e),a=r!==e&&!he(e),l=r[t];if(l!==mp[t]){const f=l.apply(e,o);return a?Nt(f):f}let c=n;r!==e&&(a?c=function(f,h){return n.call(this,Nt(f),h,e)}:n.length>2&&(c=function(f,h){return n.call(this,f,h,e)}));const u=l.call(r,c,s);return a&&i?i(u):u}function fl(e,t,n,s){const i=ko(e);let o=n;return i!==e&&(he(e)?n.length>3&&(o=function(r,a,l){return n.call(this,r,a,l,e)}):o=function(r,a,l){return n.call(this,r,Nt(a),l,e)}),i[t](o,...s)}function er(e,t,n){const s=lt(e);Ht(s,"iterate",Js);const i=s[t](...n);return(i===-1||i===!1)&&ka(n[0])?(n[0]=lt(n[0]),s[t](...n)):i}function xs(e,t,n=[]){Xe(),wa();const s=lt(e)[t].apply(e,n);return Sa(),Ge(),s}const bp=ga("__proto__,__v_isRef,__isVue"),uf=new Set(Object.getOwnPropertyNames(Symbol).filter(e=>e!=="arguments"&&e!=="caller").map(e=>Symbol[e]).filter(_e));function yp(e){_e(e)||(e=String(e));const t=lt(this);return Ht(t,"has",e),t.hasOwnProperty(e)}class ff{constructor(t=!1,n=!1){this._isReadonly=t,this._isShallow=n}get(t,n,s){if(n==="__v_skip")return t.__v_skip;const i=this._isReadonly,o=this._isShallow;if(n==="__v_isReactive")return!i;if(n==="__v_isReadonly")return i;if(n==="__v_isShallow")return o;if(n==="__v_raw")return s===(i?o?kp:gf:o?pf:df).get(t)||Object.getPrototypeOf(t)===Object.getPrototypeOf(s)?t:void 0;const r=q(t);if(!i){let l;if(r&&(l=gp[n]))return l;if(n==="hasOwnProperty")return yp}const a=Reflect.get(t,n,kt(t)?t:s);return(_e(n)?uf.has(n):bp(n))||(i||Ht(t,"get",n),o)?a:kt(a)?r&&ya(n)?a:a.value:gt(a)?i?bf(a):hi(a):a}}class hf extends ff{constructor(t=!1){super(!1,t)}set(t,n,s,i){let o=t[n];if(!this._isShallow){const l=pn(o);if(!he(s)&&!pn(s)&&(o=lt(o),s=lt(s)),!q(t)&&kt(o)&&!kt(s))return l?!1:(o.value=s,!0)}const r=q(t)&&ya(n)?Number(n)<t.length:ut(t,n),a=Reflect.set(t,n,s,kt(t)?t:i);return t===lt(i)&&(r?fn(s,o)&&We(t,"set",n,s):We(t,"add",n,s)),a}deleteProperty(t,n){const s=ut(t,n);t[n];const i=Reflect.deleteProperty(t,n);return i&&s&&We(t,"delete",n,void 0),i}has(t,n){const s=Reflect.has(t,n);return(!_e(n)||!uf.has(n))&&Ht(t,"has",n),s}ownKeys(t){return Ht(t,"iterate",q(t)?"length":Fn),Reflect.ownKeys(t)}}class _p extends ff{constructor(t=!1){super(!0,t)}set(t,n){return!0}deleteProperty(t,n){return!0}}const xp=new hf,vp=new _p,wp=new hf(!0);const Or=e=>e,_i=e=>Reflect.getPrototypeOf(e);function Sp(e,t,n){return function(...s){const i=this.__v_raw,o=lt(i),r=ts(o),a=e==="entries"||e===Symbol.iterator&&r,l=e==="keys"&&r,c=i[e](...s),u=n?Or:t?so:Nt;return!t&&Ht(o,"iterate",l?Ar:Fn),{next(){const{value:f,done:h}=c.next();return h?{value:f,done:h}:{value:a?[u(f[0]),u(f[1])]:u(f),done:h}},[Symbol.iterator](){return this}}}}function xi(e){return function(...t){return e==="delete"?!1:e==="clear"?void 0:this}}function Mp(e,t){const n={get(i){const o=this.__v_raw,r=lt(o),a=lt(i);e||(fn(i,a)&&Ht(r,"get",i),Ht(r,"get",a));const{has:l}=_i(r),c=t?Or:e?so:Nt;if(l.call(r,i))return c(o.get(i));if(l.call(r,a))return c(o.get(a));o!==r&&o.get(i)},get size(){const i=this.__v_raw;return!e&&Ht(lt(i),"iterate",Fn),Reflect.get(i,"size",i)},has(i){const o=this.__v_raw,r=lt(o),a=lt(i);return e||(fn(i,a)&&Ht(r,"has",i),Ht(r,"has",a)),i===a?o.has(i):o.has(i)||o.has(a)},forEach(i,o){const r=this,a=r.__v_raw,l=lt(a),c=t?Or:e?so:Nt;return!e&&Ht(l,"iterate",Fn),a.forEach((u,f)=>i.call(o,c(u),c(f),r))}};return Dt(n,e?{add:xi("add"),set:xi("set"),delete:xi("delete"),clear:xi("clear")}:{add(i){!t&&!he(i)&&!pn(i)&&(i=lt(i));const o=lt(this);return _i(o).has.call(o,i)||(o.add(i),We(o,"add",i,i)),this},set(i,o){!t&&!he(o)&&!pn(o)&&(o=lt(o));const r=lt(this),{has:a,get:l}=_i(r);let c=a.call(r,i);c||(i=lt(i),c=a.call(r,i));const u=l.call(r,i);return r.set(i,o),c?fn(o,u)&&We(r,"set",i,o):We(r,"add",i,o),this},delete(i){const o=lt(this),{has:r,get:a}=_i(o);let l=r.call(o,i);l||(i=lt(i),l=r.call(o,i)),a&&a.call(o,i);const c=o.delete(i);return l&&We(o,"delete",i,void 0),c},clear(){const i=lt(this),o=i.size!==0,r=i.clear();return o&&We(i,"clear",void 0,void 0),r}}),["keys","values","entries",Symbol.iterator].forEach(i=>{n[i]=Sp(i,e,t)}),n}function Pa(e,t){const n=Mp(e,t);return(s,i,o)=>i==="__v_isReactive"?!e:i==="__v_isReadonly"?e:i==="__v_raw"?s:Reflect.get(ut(n,i)&&i in s?n:s,i,o)}const Cp={get:Pa(!1,!1)},Pp={get:Pa(!1,!0)},Ep={get:Pa(!0,!1)};const df=new WeakMap,pf=new WeakMap,gf=new WeakMap,kp=new WeakMap;function Ap(e){switch(e){case"Object":case"Array":return 1;case"Map":case"Set":case"WeakMap":case"WeakSet":return 2;default:return 0}}function Op(e){return e.__v_skip||!Object.isExtensible(e)?0:Ap(Zd(e))}function hi(e){return pn(e)?e:Ea(e,!1,xp,Cp,df)}function mf(e){return Ea(e,!1,wp,Pp,pf)}function bf(e){return Ea(e,!0,vp,Ep,gf)}function Ea(e,t,n,s,i){if(!gt(e)||e.__v_raw&&!(t&&e.__v_isReactive))return e;const o=Op(e);if(o===0)return e;const r=i.get(e);if(r)return r;const a=new Proxy(e,o===2?s:n);return i.set(e,a),a}function hn(e){return pn(e)?hn(e.__v_raw):!!(e&&e.__v_isReactive)}function pn(e){return!!(e&&e.__v_isReadonly)}function he(e){return!!(e&&e.__v_isShallow)}function ka(e){return e?!!e.__v_raw:!1}function lt(e){const t=e&&e.__v_raw;return t?lt(t):e}function Aa(e){return!ut(e,"__v_skip")&&Object.isExtensible(e)&&Yu(e,"__v_skip",!0),e}const Nt=e=>gt(e)?hi(e):e,so=e=>gt(e)?bf(e):e;function kt(e){return e?e.__v_isRef===!0:!1}function Oa(e){return yf(e,!1)}function Rp(e){return yf(e,!0)}function yf(e,t){return kt(e)?e:new Tp(e,t)}class Tp{constructor(t,n){this.dep=new Ca,this.__v_isRef=!0,this.__v_isShallow=!1,this._rawValue=n?t:lt(t),this._value=n?t:Nt(t),this.__v_isShallow=n}get value(){return this.dep.track(),this._value}set value(t){const n=this._rawValue,s=this.__v_isShallow||he(t)||pn(t);t=s?t:lt(t),fn(t,n)&&(this._rawValue=t,this._value=s?t:Nt(t),this.dep.trigger())}}function ns(e){return kt(e)?e.value:e}const Dp={get:(e,t,n)=>t==="__v_raw"?e:ns(Reflect.get(e,t,n)),set:(e,t,n,s)=>{const i=e[t];return kt(i)&&!kt(n)?(i.value=n,!0):Reflect.set(e,t,n,s)}};function _f(e){return hn(e)?e:new Proxy(e,Dp)}function Lp(e){const t=q(e)?new Array(e.length):{};for(const n in e)t[n]=Ip(e,n);return t}class Fp{constructor(t,n,s){this._object=t,this._key=n,this._defaultValue=s,this.__v_isRef=!0,this._value=void 0}get value(){const t=this._object[this._key];return this._value=t===void 0?this._defaultValue:t}set value(t){this._object[this._key]=t}get dep(){return pp(lt(this._object),this._key)}}function Ip(e,t,n){const s=e[t];return kt(s)?s:new Fp(e,t,n)}class Np{constructor(t,n,s){this.fn=t,this.setter=n,this._value=void 0,this.dep=new Ca(this),this.__v_isRef=!0,this.deps=void 0,this.depsTail=void 0,this.flags=16,this.globalVersion=Gs-1,this.next=void 0,this.effect=this,this.__v_isReadonly=!n,this.isSSR=s}notify(){if(this.flags|=16,!(this.flags&8)&&yt!==this)return sf(this,!0),!0}get value(){const t=this.dep.track();return af(this),t&&(t.version=this.dep.version),this._value}set value(t){this.setter&&this.setter(t)}}function Bp(e,t,n=!1){let s,i;return tt(e)?s=e:(s=e.get,i=e.set),new Np(s,i,n)}const vi={},io=new WeakMap;let kn;function zp(e,t=!1,n=kn){if(n){let s=io.get(n);s||io.set(n,s=[]),s.push(e)}}function Hp(e,t,n=pt){const{immediate:s,deep:i,once:o,scheduler:r,augmentJob:a,call:l}=n,c=v=>i?v:he(v)||i===!1||i===0?$e(v,1):$e(v);let u,f,h,d,p=!1,g=!1;if(kt(e)?(f=()=>e.value,p=he(e)):hn(e)?(f=()=>c(e),p=!0):q(e)?(g=!0,p=e.some(v=>hn(v)||he(v)),f=()=>e.map(v=>{if(kt(v))return v.value;if(hn(v))return c(v);if(tt(v))return l?l(v,2):v()})):tt(e)?t?f=l?()=>l(e,2):e:f=()=>{if(h){Xe();try{h()}finally{Ge()}}const v=kn;kn=u;try{return l?l(e,3,[d]):e(d)}finally{kn=v}}:f=Le,t&&i){const v=f,S=i===!0?1/0:i;f=()=>$e(v(),S)}const m=tf(),y=()=>{u.stop(),m&&m.active&&ba(m.effects,u)};if(o&&t){const v=t;t=(...S)=>{v(...S),y()}}let b=g?new Array(e.length).fill(vi):vi;const w=v=>{if(!(!(u.flags&1)||!u.dirty&&!v))if(t){const S=u.run();if(i||p||(g?S.some((A,P)=>fn(A,b[P])):fn(S,b))){h&&h();const A=kn;kn=u;try{const P=[S,b===vi?void 0:g&&b[0]===vi?[]:b,d];b=S,l?l(t,3,P):t(...P)}finally{kn=A}}}else u.run()};return a&&a(w),u=new ef(f),u.scheduler=r?()=>r(w,!1):w,d=v=>zp(v,!1,u),h=u.onStop=()=>{const v=io.get(u);if(v){if(l)l(v,4);else for(const S of v)S();io.delete(u)}},t?s?w(!0):b=u.run():r?r(w.bind(null,!0),!0):u.run(),y.pause=u.pause.bind(u),y.resume=u.resume.bind(u),y.stop=y,y}function $e(e,t=1/0,n){if(t<=0||!gt(e)||e.__v_skip||(n=n||new Set,n.has(e)))return e;if(n.add(e),t--,kt(e))$e(e.value,t,n);else if(q(e))for(let s=0;s<e.length;s++)$e(e[s],t,n);else if(gs(e)||ts(e))e.forEach(s=>{$e(s,t,n)});else if(qu(e)){for(const s in e)$e(e[s],t,n);for(const s of Object.getOwnPropertySymbols(e))Object.prototype.propertyIsEnumerable.call(e,s)&&$e(e[s],t,n)}return e}/**
* @vue/runtime-core v3.5.16
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/function di(e,t,n,s){try{return s?e(...s):e()}catch(i){Ao(i,t,n)}}function xe(e,t,n,s){if(tt(e)){const i=di(e,t,n,s);return i&&Uu(i)&&i.catch(o=>{Ao(o,t,n)}),i}if(q(e)){const i=[];for(let o=0;o<e.length;o++)i.push(xe(e[o],t,n,s));return i}}function Ao(e,t,n,s=!0){const i=t?t.vnode:null,{errorHandler:o,throwUnhandledErrorInProduction:r}=t&&t.appContext.config||pt;if(t){let a=t.parent;const l=t.proxy,c=`https://vuejs.org/error-reference/#runtime-${n}`;for(;a;){const u=a.ec;if(u){for(let f=0;f<u.length;f++)if(u[f](e,l,c)===!1)return}a=a.parent}if(o){Xe(),di(o,null,10,[e,l,c]),Ge();return}}jp(e,n,i,s,r)}function jp(e,t,n,s=!0,i=!1){if(i)throw e;console.error(e)}const Yt=[];let Re=-1;const ss=[];let sn=null,Jn=0;const xf=Promise.resolve();let oo=null;function Oo(e){const t=oo||xf;return e?t.then(this?e.bind(this):e):t}function Vp(e){let t=Re+1,n=Yt.length;for(;t<n;){const s=t+n>>>1,i=Yt[s],o=Qs(i);o<e||o===e&&i.flags&2?t=s+1:n=s}return t}function Ra(e){if(!(e.flags&1)){const t=Qs(e),n=Yt[Yt.length-1];!n||!(e.flags&2)&&t>=Qs(n)?Yt.push(e):Yt.splice(Vp(t),0,e),e.flags|=1,vf()}}function vf(){oo||(oo=xf.then(Sf))}function Wp(e){q(e)?ss.push(...e):sn&&e.id===-1?sn.splice(Jn+1,0,e):e.flags&1||(ss.push(e),e.flags|=1),vf()}function hl(e,t,n=Re+1){for(;n<Yt.length;n++){const s=Yt[n];if(s&&s.flags&2){if(e&&s.id!==e.uid)continue;Yt.splice(n,1),n--,s.flags&4&&(s.flags&=-2),s(),s.flags&4||(s.flags&=-2)}}}function wf(e){if(ss.length){const t=[...new Set(ss)].sort((n,s)=>Qs(n)-Qs(s));if(ss.length=0,sn){sn.push(...t);return}for(sn=t,Jn=0;Jn<sn.length;Jn++){const n=sn[Jn];n.flags&4&&(n.flags&=-2),n.flags&8||n(),n.flags&=-2}sn=null,Jn=0}}const Qs=e=>e.id==null?e.flags&2?-1:1/0:e.id;function Sf(e){try{for(Re=0;Re<Yt.length;Re++){const t=Yt[Re];t&&!(t.flags&8)&&(t.flags&4&&(t.flags&=-2),di(t,t.i,t.i?15:14),t.flags&4||(t.flags&=-2))}}finally{for(;Re<Yt.length;Re++){const t=Yt[Re];t&&(t.flags&=-2)}Re=-1,Yt.length=0,wf(),oo=null,(Yt.length||ss.length)&&Sf()}}let Rt=null,Mf=null;function ro(e){const t=Rt;return Rt=e,Mf=e&&e.type.__scopeId||null,t}function $p(e,t=Rt,n){if(!t||e._n)return e;const s=(...i)=>{s._d&&vl(-1);const o=ro(t);let r;try{r=e(...i)}finally{ro(o),s._d&&vl(1)}return r};return s._n=!0,s._c=!0,s._d=!0,s}function xS(e,t){if(Rt===null)return e;const n=Lo(Rt),s=e.dirs||(e.dirs=[]);for(let i=0;i<t.length;i++){let[o,r,a,l=pt]=t[i];o&&(tt(o)&&(o={mounted:o,updated:o}),o.deep&&$e(r),s.push({dir:o,instance:n,value:r,oldValue:void 0,arg:a,modifiers:l}))}return e}function vn(e,t,n,s){const i=e.dirs,o=t&&t.dirs;for(let r=0;r<i.length;r++){const a=i[r];o&&(a.oldValue=o[r].value);let l=a.dir[s];l&&(Xe(),xe(l,n,8,[e.el,a,e,t]),Ge())}}const Up=Symbol("_vte"),Kp=e=>e.__isTeleport,qn=Symbol("_leaveCb"),wi=Symbol("_enterCb");function qp(){const e={isMounted:!1,isLeaving:!1,isUnmounting:!1,leavingVNodes:new Map};return Ta(()=>{e.isMounted=!0}),La(()=>{e.isUnmounting=!0}),e}const le=[Function,Array],Yp={mode:String,appear:Boolean,persisted:Boolean,onBeforeEnter:le,onEnter:le,onAfterEnter:le,onEnterCancelled:le,onBeforeLeave:le,onLeave:le,onAfterLeave:le,onLeaveCancelled:le,onBeforeAppear:le,onAppear:le,onAfterAppear:le,onAppearCancelled:le};function Xp(e,t){const{leavingVNodes:n}=e;let s=n.get(t.type);return s||(s=Object.create(null),n.set(t.type,s)),s}function Rr(e,t,n,s,i){const{appear:o,mode:r,persisted:a=!1,onBeforeEnter:l,onEnter:c,onAfterEnter:u,onEnterCancelled:f,onBeforeLeave:h,onLeave:d,onAfterLeave:p,onLeaveCancelled:g,onBeforeAppear:m,onAppear:y,onAfterAppear:b,onAppearCancelled:w}=t,v=String(e.key),S=Xp(n,e),A=(k,I)=>{k&&xe(k,s,9,I)},P=(k,I)=>{const H=I[1];A(k,I),q(k)?k.every(L=>L.length<=1)&&H():k.length<=1&&H()},E={mode:r,persisted:a,beforeEnter(k){let I=l;if(!n.isMounted)if(o)I=m||l;else return;k[qn]&&k[qn](!0);const H=S[v];H&&Tn(e,H)&&H.el[qn]&&H.el[qn](),A(I,[k])},enter(k){let I=c,H=u,L=f;if(!n.isMounted)if(o)I=y||c,H=b||u,L=w||f;else return;let X=!1;const rt=k[wi]=Z=>{X||(X=!0,Z?A(L,[k]):A(H,[k]),E.delayedLeave&&E.delayedLeave(),k[wi]=void 0)};I?P(I,[k,rt]):rt()},leave(k,I){const H=String(e.key);if(k[wi]&&k[wi](!0),n.isUnmounting)return I();A(h,[k]);let L=!1;const X=k[qn]=rt=>{L||(L=!0,I(),rt?A(g,[k]):A(p,[k]),k[qn]=void 0,S[H]===e&&delete S[H])};S[H]=e,d?P(d,[k,X]):X()},clone(k){return Rr(k,t,n,s)}};return E}function rs(e,t){e.shapeFlag&6&&e.component?(e.transition=t,rs(e.component.subTree,t)):e.shapeFlag&128?(e.ssContent.transition=t.clone(e.ssContent),e.ssFallback.transition=t.clone(e.ssFallback)):e.transition=t}function Cf(e,t=!1,n){let s=[],i=0;for(let o=0;o<e.length;o++){let r=e[o];const a=n==null?r.key:String(n)+String(r.key!=null?r.key:o);r.type===Qt?(r.patchFlag&128&&i++,s=s.concat(Cf(r.children,t,a))):(t||r.type!==ve)&&s.push(a!=null?gn(r,{key:a}):r)}if(i>1)for(let o=0;o<s.length;o++)s[o].patchFlag=-2;return s}/*! #__NO_SIDE_EFFECTS__ */function Pf(e,t){return tt(e)?Dt({name:e.name},t,{setup:e}):e}function Ef(e){e.ids=[e.ids[0]+e.ids[2]+++"-",0,0]}function ao(e,t,n,s,i=!1){if(q(e)){e.forEach((p,g)=>ao(p,t&&(q(t)?t[g]:t),n,s,i));return}if(In(s)&&!i){s.shapeFlag&512&&s.type.__asyncResolved&&s.component.subTree.component&&ao(e,t,n,s.component.subTree);return}const o=s.shapeFlag&4?Lo(s.component):s.el,r=i?null:o,{i:a,r:l}=e,c=t&&t.r,u=a.refs===pt?a.refs={}:a.refs,f=a.setupState,h=lt(f),d=f===pt?()=>!1:p=>ut(h,p);if(c!=null&&c!==l&&(Mt(c)?(u[c]=null,d(c)&&(f[c]=null)):kt(c)&&(c.value=null)),tt(l))di(l,a,12,[r,u]);else{const p=Mt(l),g=kt(l);if(p||g){const m=()=>{if(e.f){const y=p?d(l)?f[l]:u[l]:l.value;i?q(y)&&ba(y,o):q(y)?y.includes(o)||y.push(o):p?(u[l]=[o],d(l)&&(f[l]=u[l])):(l.value=[o],e.k&&(u[e.k]=l.value))}else p?(u[l]=r,d(l)&&(f[l]=r)):g&&(l.value=r,e.k&&(u[e.k]=r))};r?(m.id=-1,It(m,n)):m()}}}Eo().requestIdleCallback;Eo().cancelIdleCallback;const In=e=>!!e.type.__asyncLoader,kf=e=>e.type.__isKeepAlive,Gp={name:"KeepAlive",__isKeepAlive:!0,props:{include:[String,RegExp,Array],exclude:[String,RegExp,Array],max:[String,Number]},setup(e,{slots:t}){const n=th(),s=n.ctx;if(!s.renderer)return()=>{const b=t.default&&t.default();return b&&b.length===1?b[0]:b};const i=new Map,o=new Set;let r=null;const a=n.suspense,{renderer:{p:l,m:c,um:u,o:{createElement:f}}}=s,h=f("div");s.activate=(b,w,v,S,A)=>{const P=b.component;c(b,w,v,0,a),l(P.vnode,b,w,v,P,a,S,b.slotScopeIds,A),It(()=>{P.isDeactivated=!1,P.a&&es(P.a);const E=b.props&&b.props.onVnodeMounted;E&&ue(E,P.parent,b)},a)},s.deactivate=b=>{const w=b.component;co(w.m),co(w.a),c(b,h,null,1,a),It(()=>{w.da&&es(w.da);const v=b.props&&b.props.onVnodeUnmounted;v&&ue(v,w.parent,b),w.isDeactivated=!0},a)};function d(b){nr(b),u(b,n,a,!0)}function p(b){i.forEach((w,v)=>{const S=zr(w.type);S&&!b(S)&&g(v)})}function g(b){const w=i.get(b);w&&(!r||!Tn(w,r))?d(w):r&&nr(r),i.delete(b),o.delete(b)}is(()=>[e.include,e.exclude],([b,w])=>{b&&p(v=>As(b,v)),w&&p(v=>!As(w,v))},{flush:"post",deep:!0});let m=null;const y=()=>{m!=null&&(uo(n.subTree.type)?It(()=>{i.set(m,Si(n.subTree))},n.subTree.suspense):i.set(m,Si(n.subTree)))};return Ta(y),Da(y),La(()=>{i.forEach(b=>{const{subTree:w,suspense:v}=n,S=Si(w);if(b.type===S.type&&b.key===S.key){nr(S);const A=S.component.da;A&&It(A,v);return}d(b)})}),()=>{if(m=null,!t.default)return r=null;const b=t.default(),w=b[0];if(b.length>1)return r=null,b;if(!as(w)||!(w.shapeFlag&4)&&!(w.shapeFlag&128))return r=null,w;let v=Si(w);if(v.type===ve)return r=null,v;const S=v.type,A=zr(In(v)?v.type.__asyncResolved||{}:S),{include:P,exclude:E,max:k}=e;if(P&&(!A||!As(P,A))||E&&A&&As(E,A))return v.shapeFlag&=-257,r=v,w;const I=v.key==null?S:v.key,H=i.get(I);return v.el&&(v=gn(v),w.shapeFlag&128&&(w.ssContent=v)),m=I,H?(v.el=H.el,v.component=H.component,v.transition&&rs(v,v.transition),v.shapeFlag|=512,o.delete(I),o.add(I)):(o.add(I),k&&o.size>parseInt(k,10)&&g(o.values().next().value)),v.shapeFlag|=256,r=v,uo(w.type)?w:v}}},vS=Gp;function As(e,t){return q(e)?e.some(n=>As(n,t)):Mt(e)?e.split(",").includes(t):Qd(e)?(e.lastIndex=0,e.test(t)):!1}function Jp(e,t){Af(e,"a",t)}function Qp(e,t){Af(e,"da",t)}function Af(e,t,n=Lt){const s=e.__wdc||(e.__wdc=()=>{let i=n;for(;i;){if(i.isDeactivated)return;i=i.parent}return e()});if(Ro(t,s,n),n){let i=n.parent;for(;i&&i.parent;)kf(i.parent.vnode)&&Zp(s,t,n,i),i=i.parent}}function Zp(e,t,n,s){const i=Ro(t,e,s,!0);Of(()=>{ba(s[t],i)},n)}function nr(e){e.shapeFlag&=-257,e.shapeFlag&=-513}function Si(e){return e.shapeFlag&128?e.ssContent:e}function Ro(e,t,n=Lt,s=!1){if(n){const i=n[e]||(n[e]=[]),o=t.__weh||(t.__weh=(...r)=>{Xe();const a=pi(n),l=xe(t,n,e,r);return a(),Ge(),l});return s?i.unshift(o):i.push(o),o}}const Je=e=>(t,n=Lt)=>{(!ti||e==="sp")&&Ro(e,(...s)=>t(...s),n)},tg=Je("bm"),Ta=Je("m"),eg=Je("bu"),Da=Je("u"),La=Je("bum"),Of=Je("um"),ng=Je("sp"),sg=Je("rtg"),ig=Je("rtc");function og(e,t=Lt){Ro("ec",e,t)}const Rf="components";function wS(e,t){return Df(Rf,e,!0,t)||e}const Tf=Symbol.for("v-ndc");function SS(e){return Mt(e)?Df(Rf,e,!1)||e:e||Tf}function Df(e,t,n=!0,s=!1){const i=Rt||Lt;if(i){const o=i.type;{const a=zr(o,!1);if(a&&(a===t||a===ge(t)||a===Po(ge(t))))return o}const r=dl(i[e]||o[e],t)||dl(i.appContext[e],t);return!r&&s?o:r}}function dl(e,t){return e&&(e[t]||e[ge(t)]||e[Po(ge(t))])}function MS(e,t,n,s){let i;const o=n,r=q(e);if(r||Mt(e)){const a=r&&hn(e);let l=!1,c=!1;a&&(l=!he(e),c=pn(e),e=ko(e)),i=new Array(e.length);for(let u=0,f=e.length;u<f;u++)i[u]=t(l?c?so(Nt(e[u])):Nt(e[u]):e[u],u,void 0,o)}else if(typeof e=="number"){i=new Array(e);for(let a=0;a<e;a++)i[a]=t(a+1,a,void 0,o)}else if(gt(e))if(e[Symbol.iterator])i=Array.from(e,(a,l)=>t(a,l,void 0,o));else{const a=Object.keys(e);i=new Array(a.length);for(let l=0,c=a.length;l<c;l++){const u=a[l];i[l]=t(e[u],u,l,o)}}else i=[];return i}function CS(e,t,n={},s,i){if(Rt.ce||Rt.parent&&In(Rt.parent)&&Rt.parent.ce)return n.name=t,Ir(),Nr(Qt,null,[Vt("slot",n,s)],64);let o=e[t];o&&o._c&&(o._d=!1),Ir();const r=o&&Lf(o(n)),a=n.key||r&&r.key,l=Nr(Qt,{key:(a&&!_e(a)?a:`_${t}`)+""},r||[],r&&e._===1?64:-2);return l.scopeId&&(l.slotScopeIds=[l.scopeId+"-s"]),o&&o._c&&(o._d=!0),l}function Lf(e){return e.some(t=>as(t)?!(t.type===ve||t.type===Qt&&!Lf(t.children)):!0)?e:null}const Tr=e=>e?eh(e)?Lo(e):Tr(e.parent):null,Hs=Dt(Object.create(null),{$:e=>e,$el:e=>e.vnode.el,$data:e=>e.data,$props:e=>e.props,$attrs:e=>e.attrs,$slots:e=>e.slots,$refs:e=>e.refs,$parent:e=>Tr(e.parent),$root:e=>Tr(e.root),$host:e=>e.ce,$emit:e=>e.emit,$options:e=>If(e),$forceUpdate:e=>e.f||(e.f=()=>{Ra(e.update)}),$nextTick:e=>e.n||(e.n=Oo.bind(e.proxy)),$watch:e=>Eg.bind(e)}),sr=(e,t)=>e!==pt&&!e.__isScriptSetup&&ut(e,t),rg={get({_:e},t){if(t==="__v_skip")return!0;const{ctx:n,setupState:s,data:i,props:o,accessCache:r,type:a,appContext:l}=e;let c;if(t[0]!=="$"){const d=r[t];if(d!==void 0)switch(d){case 1:return s[t];case 2:return i[t];case 4:return n[t];case 3:return o[t]}else{if(sr(s,t))return r[t]=1,s[t];if(i!==pt&&ut(i,t))return r[t]=2,i[t];if((c=e.propsOptions[0])&&ut(c,t))return r[t]=3,o[t];if(n!==pt&&ut(n,t))return r[t]=4,n[t];Dr&&(r[t]=0)}}const u=Hs[t];let f,h;if(u)return t==="$attrs"&&Ht(e.attrs,"get",""),u(e);if((f=a.__cssModules)&&(f=f[t]))return f;if(n!==pt&&ut(n,t))return r[t]=4,n[t];if(h=l.config.globalProperties,ut(h,t))return h[t]},set({_:e},t,n){const{data:s,setupState:i,ctx:o}=e;return sr(i,t)?(i[t]=n,!0):s!==pt&&ut(s,t)?(s[t]=n,!0):ut(e.props,t)||t[0]==="$"&&t.slice(1)in e?!1:(o[t]=n,!0)},has({_:{data:e,setupState:t,accessCache:n,ctx:s,appContext:i,propsOptions:o}},r){let a;return!!n[r]||e!==pt&&ut(e,r)||sr(t,r)||(a=o[0])&&ut(a,r)||ut(s,r)||ut(Hs,r)||ut(i.config.globalProperties,r)},defineProperty(e,t,n){return n.get!=null?e._.accessCache[t]=0:ut(n,"value")&&this.set(e,t,n.value,null),Reflect.defineProperty(e,t,n)}};function pl(e){return q(e)?e.reduce((t,n)=>(t[n]=null,t),{}):e}let Dr=!0;function ag(e){const t=If(e),n=e.proxy,s=e.ctx;Dr=!1,t.beforeCreate&&gl(t.beforeCreate,e,"bc");const{data:i,computed:o,methods:r,watch:a,provide:l,inject:c,created:u,beforeMount:f,mounted:h,beforeUpdate:d,updated:p,activated:g,deactivated:m,beforeDestroy:y,beforeUnmount:b,destroyed:w,unmounted:v,render:S,renderTracked:A,renderTriggered:P,errorCaptured:E,serverPrefetch:k,expose:I,inheritAttrs:H,components:L,directives:X,filters:rt}=t;if(c&&lg(c,s,null),r)for(const U in r){const et=r[U];tt(et)&&(s[U]=et.bind(n))}if(i){const U=i.call(n,n);gt(U)&&(e.data=hi(U))}if(Dr=!0,o)for(const U in o){const et=o[U],mt=tt(et)?et.bind(n,n):tt(et.get)?et.get.bind(n,n):Le,Ut=!tt(et)&&tt(et.set)?et.set.bind(n):Le,Kt=fe({get:mt,set:Ut});Object.defineProperty(s,U,{enumerable:!0,configurable:!0,get:()=>Kt.value,set:Pt=>Kt.value=Pt})}if(a)for(const U in a)Ff(a[U],s,n,U);if(l){const U=tt(l)?l.call(n):l;Reflect.ownKeys(U).forEach(et=>{zi(et,U[et])})}u&&gl(u,e,"c");function G(U,et){q(et)?et.forEach(mt=>U(mt.bind(n))):et&&U(et.bind(n))}if(G(tg,f),G(Ta,h),G(eg,d),G(Da,p),G(Jp,g),G(Qp,m),G(og,E),G(ig,A),G(sg,P),G(La,b),G(Of,v),G(ng,k),q(I))if(I.length){const U=e.exposed||(e.exposed={});I.forEach(et=>{Object.defineProperty(U,et,{get:()=>n[et],set:mt=>n[et]=mt})})}else e.exposed||(e.exposed={});S&&e.render===Le&&(e.render=S),H!=null&&(e.inheritAttrs=H),L&&(e.components=L),X&&(e.directives=X),k&&Ef(e)}function lg(e,t,n=Le){q(e)&&(e=Lr(e));for(const s in e){const i=e[s];let o;gt(i)?"default"in i?o=de(i.from||s,i.default,!0):o=de(i.from||s):o=de(i),kt(o)?Object.defineProperty(t,s,{enumerable:!0,configurable:!0,get:()=>o.value,set:r=>o.value=r}):t[s]=o}}function gl(e,t,n){xe(q(e)?e.map(s=>s.bind(t.proxy)):e.bind(t.proxy),t,n)}function Ff(e,t,n,s){let i=s.includes(".")?Xf(n,s):()=>n[s];if(Mt(e)){const o=t[e];tt(o)&&is(i,o)}else if(tt(e))is(i,e.bind(n));else if(gt(e))if(q(e))e.forEach(o=>Ff(o,t,n,s));else{const o=tt(e.handler)?e.handler.bind(n):t[e.handler];tt(o)&&is(i,o,e)}}function If(e){const t=e.type,{mixins:n,extends:s}=t,{mixins:i,optionsCache:o,config:{optionMergeStrategies:r}}=e.appContext,a=o.get(t);let l;return a?l=a:!i.length&&!n&&!s?l=t:(l={},i.length&&i.forEach(c=>lo(l,c,r,!0)),lo(l,t,r)),gt(t)&&o.set(t,l),l}function lo(e,t,n,s=!1){const{mixins:i,extends:o}=t;o&&lo(e,o,n,!0),i&&i.forEach(r=>lo(e,r,n,!0));for(const r in t)if(!(s&&r==="expose")){const a=cg[r]||n&&n[r];e[r]=a?a(e[r],t[r]):t[r]}return e}const cg={data:ml,props:bl,emits:bl,methods:Os,computed:Os,beforeCreate:qt,created:qt,beforeMount:qt,mounted:qt,beforeUpdate:qt,updated:qt,beforeDestroy:qt,beforeUnmount:qt,destroyed:qt,unmounted:qt,activated:qt,deactivated:qt,errorCaptured:qt,serverPrefetch:qt,components:Os,directives:Os,watch:fg,provide:ml,inject:ug};function ml(e,t){return t?e?function(){return Dt(tt(e)?e.call(this,this):e,tt(t)?t.call(this,this):t)}:t:e}function ug(e,t){return Os(Lr(e),Lr(t))}function Lr(e){if(q(e)){const t={};for(let n=0;n<e.length;n++)t[e[n]]=e[n];return t}return e}function qt(e,t){return e?[...new Set([].concat(e,t))]:t}function Os(e,t){return e?Dt(Object.create(null),e,t):t}function bl(e,t){return e?q(e)&&q(t)?[...new Set([...e,...t])]:Dt(Object.create(null),pl(e),pl(t??{})):t}function fg(e,t){if(!e)return t;if(!t)return e;const n=Dt(Object.create(null),e);for(const s in t)n[s]=qt(e[s],t[s]);return n}function Nf(){return{app:null,config:{isNativeTag:Gd,performance:!1,globalProperties:{},optionMergeStrategies:{},errorHandler:void 0,warnHandler:void 0,compilerOptions:{}},mixins:[],components:{},directives:{},provides:Object.create(null),optionsCache:new WeakMap,propsCache:new WeakMap,emitsCache:new WeakMap}}let hg=0;function dg(e,t){return function(s,i=null){tt(s)||(s=Dt({},s)),i!=null&&!gt(i)&&(i=null);const o=Nf(),r=new WeakSet,a=[];let l=!1;const c=o.app={_uid:hg++,_component:s,_props:i,_container:null,_context:o,_instance:null,version:Yg,get config(){return o.config},set config(u){},use(u,...f){return r.has(u)||(u&&tt(u.install)?(r.add(u),u.install(c,...f)):tt(u)&&(r.add(u),u(c,...f))),c},mixin(u){return o.mixins.includes(u)||o.mixins.push(u),c},component(u,f){return f?(o.components[u]=f,c):o.components[u]},directive(u,f){return f?(o.directives[u]=f,c):o.directives[u]},mount(u,f,h){if(!l){const d=c._ceVNode||Vt(s,i);return d.appContext=o,h===!0?h="svg":h===!1&&(h=void 0),e(d,u,h),l=!0,c._container=u,u.__vue_app__=c,Lo(d.component)}},onUnmount(u){a.push(u)},unmount(){l&&(xe(a,c._instance,16),e(null,c._container),delete c._container.__vue_app__)},provide(u,f){return o.provides[u]=f,c},runWithContext(u){const f=Nn;Nn=c;try{return u()}finally{Nn=f}}};return c}}let Nn=null;function zi(e,t){if(Lt){let n=Lt.provides;const s=Lt.parent&&Lt.parent.provides;s===n&&(n=Lt.provides=Object.create(s)),n[e]=t}}function de(e,t,n=!1){const s=Lt||Rt;if(s||Nn){let i=Nn?Nn._context.provides:s?s.parent==null||s.ce?s.vnode.appContext&&s.vnode.appContext.provides:s.parent.provides:void 0;if(i&&e in i)return i[e];if(arguments.length>1)return n&&tt(t)?t.call(s&&s.proxy):t}}function pg(){return!!(Lt||Rt||Nn)}const Bf={},zf=()=>Object.create(Bf),Hf=e=>Object.getPrototypeOf(e)===Bf;function gg(e,t,n,s=!1){const i={},o=zf();e.propsDefaults=Object.create(null),jf(e,t,i,o);for(const r in e.propsOptions[0])r in i||(i[r]=void 0);n?e.props=s?i:mf(i):e.type.props?e.props=i:e.props=o,e.attrs=o}function mg(e,t,n,s){const{props:i,attrs:o,vnode:{patchFlag:r}}=e,a=lt(i),[l]=e.propsOptions;let c=!1;if((s||r>0)&&!(r&16)){if(r&8){const u=e.vnode.dynamicProps;for(let f=0;f<u.length;f++){let h=u[f];if(To(e.emitsOptions,h))continue;const d=t[h];if(l)if(ut(o,h))d!==o[h]&&(o[h]=d,c=!0);else{const p=ge(h);i[p]=Fr(l,a,p,d,e,!1)}else d!==o[h]&&(o[h]=d,c=!0)}}}else{jf(e,t,i,o)&&(c=!0);let u;for(const f in a)(!t||!ut(t,f)&&((u=_n(f))===f||!ut(t,u)))&&(l?n&&(n[f]!==void 0||n[u]!==void 0)&&(i[f]=Fr(l,a,f,void 0,e,!0)):delete i[f]);if(o!==a)for(const f in o)(!t||!ut(t,f))&&(delete o[f],c=!0)}c&&We(e.attrs,"set","")}function jf(e,t,n,s){const[i,o]=e.propsOptions;let r=!1,a;if(t)for(let l in t){if(Ns(l))continue;const c=t[l];let u;i&&ut(i,u=ge(l))?!o||!o.includes(u)?n[u]=c:(a||(a={}))[u]=c:To(e.emitsOptions,l)||(!(l in s)||c!==s[l])&&(s[l]=c,r=!0)}if(o){const l=lt(n),c=a||pt;for(let u=0;u<o.length;u++){const f=o[u];n[f]=Fr(i,l,f,c[f],e,!ut(c,f))}}return r}function Fr(e,t,n,s,i,o){const r=e[n];if(r!=null){const a=ut(r,"default");if(a&&s===void 0){const l=r.default;if(r.type!==Function&&!r.skipFactory&&tt(l)){const{propsDefaults:c}=i;if(n in c)s=c[n];else{const u=pi(i);s=c[n]=l.call(null,t),u()}}else s=l;i.ce&&i.ce._setProp(n,s)}r[0]&&(o&&!a?s=!1:r[1]&&(s===""||s===_n(n))&&(s=!0))}return s}const bg=new WeakMap;function Vf(e,t,n=!1){const s=n?bg:t.propsCache,i=s.get(e);if(i)return i;const o=e.props,r={},a=[];let l=!1;if(!tt(e)){const u=f=>{l=!0;const[h,d]=Vf(f,t,!0);Dt(r,h),d&&a.push(...d)};!n&&t.mixins.length&&t.mixins.forEach(u),e.extends&&u(e.extends),e.mixins&&e.mixins.forEach(u)}if(!o&&!l)return gt(e)&&s.set(e,Zn),Zn;if(q(o))for(let u=0;u<o.length;u++){const f=ge(o[u]);yl(f)&&(r[f]=pt)}else if(o)for(const u in o){const f=ge(u);if(yl(f)){const h=o[u],d=r[f]=q(h)||tt(h)?{type:h}:Dt({},h),p=d.type;let g=!1,m=!0;if(q(p))for(let y=0;y<p.length;++y){const b=p[y],w=tt(b)&&b.name;if(w==="Boolean"){g=!0;break}else w==="String"&&(m=!1)}else g=tt(p)&&p.name==="Boolean";d[0]=g,d[1]=m,(g||ut(d,"default"))&&a.push(f)}}const c=[r,a];return gt(e)&&s.set(e,c),c}function yl(e){return e[0]!=="$"&&!Ns(e)}const Fa=e=>e[0]==="_"||e==="$stable",Ia=e=>q(e)?e.map(De):[De(e)],yg=(e,t,n)=>{if(t._n)return t;const s=$p((...i)=>Ia(t(...i)),n);return s._c=!1,s},Wf=(e,t,n)=>{const s=e._ctx;for(const i in e){if(Fa(i))continue;const o=e[i];if(tt(o))t[i]=yg(i,o,s);else if(o!=null){const r=Ia(o);t[i]=()=>r}}},$f=(e,t)=>{const n=Ia(t);e.slots.default=()=>n},Uf=(e,t,n)=>{for(const s in t)(n||!Fa(s))&&(e[s]=t[s])},_g=(e,t,n)=>{const s=e.slots=zf();if(e.vnode.shapeFlag&32){const i=t._;i?(Uf(s,t,n),n&&Yu(s,"_",i,!0)):Wf(t,s)}else t&&$f(e,t)},xg=(e,t,n)=>{const{vnode:s,slots:i}=e;let o=!0,r=pt;if(s.shapeFlag&32){const a=t._;a?n&&a===1?o=!1:Uf(i,t,n):(o=!t.$stable,Wf(t,i)),r=t}else t&&($f(e,t),r={default:1});if(o)for(const a in i)!Fa(a)&&r[a]==null&&delete i[a]},It=Lg;function vg(e){return wg(e)}function wg(e,t){const n=Eo();n.__VUE__=!0;const{insert:s,remove:i,patchProp:o,createElement:r,createText:a,createComment:l,setText:c,setElementText:u,parentNode:f,nextSibling:h,setScopeId:d=Le,insertStaticContent:p}=e,g=(_,x,M,O=null,D=null,T=null,j=void 0,z=null,B=!!x.dynamicChildren)=>{if(_===x)return;_&&!Tn(_,x)&&(O=R(_),Pt(_,D,T,!0),_=null),x.patchFlag===-2&&(B=!1,x.dynamicChildren=null);const{type:F,ref:J,shapeFlag:W}=x;switch(F){case Do:m(_,x,M,O);break;case ve:y(_,x,M,O);break;case Hi:_==null&&b(x,M,O,j);break;case Qt:L(_,x,M,O,D,T,j,z,B);break;default:W&1?S(_,x,M,O,D,T,j,z,B):W&6?X(_,x,M,O,D,T,j,z,B):(W&64||W&128)&&F.process(_,x,M,O,D,T,j,z,B,K)}J!=null&&D&&ao(J,_&&_.ref,T,x||_,!x)},m=(_,x,M,O)=>{if(_==null)s(x.el=a(x.children),M,O);else{const D=x.el=_.el;x.children!==_.children&&c(D,x.children)}},y=(_,x,M,O)=>{_==null?s(x.el=l(x.children||""),M,O):x.el=_.el},b=(_,x,M,O)=>{[_.el,_.anchor]=p(_.children,x,M,O,_.el,_.anchor)},w=({el:_,anchor:x},M,O)=>{let D;for(;_&&_!==x;)D=h(_),s(_,M,O),_=D;s(x,M,O)},v=({el:_,anchor:x})=>{let M;for(;_&&_!==x;)M=h(_),i(_),_=M;i(x)},S=(_,x,M,O,D,T,j,z,B)=>{x.type==="svg"?j="svg":x.type==="math"&&(j="mathml"),_==null?A(x,M,O,D,T,j,z,B):k(_,x,D,T,j,z,B)},A=(_,x,M,O,D,T,j,z)=>{let B,F;const{props:J,shapeFlag:W,transition:Y,dirs:Q}=_;if(B=_.el=r(_.type,T,J&&J.is,J),W&8?u(B,_.children):W&16&&E(_.children,B,null,O,D,ir(_,T),j,z),Q&&vn(_,null,O,"created"),P(B,_,_.scopeId,j,O),J){for(const bt in J)bt!=="value"&&!Ns(bt)&&o(B,bt,null,J[bt],T,O);"value"in J&&o(B,"value",null,J.value,T),(F=J.onVnodeBeforeMount)&&ue(F,O,_)}Q&&vn(_,null,O,"beforeMount");const at=Sg(D,Y);at&&Y.beforeEnter(B),s(B,x,M),((F=J&&J.onVnodeMounted)||at||Q)&&It(()=>{F&&ue(F,O,_),at&&Y.enter(B),Q&&vn(_,null,O,"mounted")},D)},P=(_,x,M,O,D)=>{if(M&&d(_,M),O)for(let T=0;T<O.length;T++)d(_,O[T]);if(D){let T=D.subTree;if(x===T||uo(T.type)&&(T.ssContent===x||T.ssFallback===x)){const j=D.vnode;P(_,j,j.scopeId,j.slotScopeIds,D.parent)}}},E=(_,x,M,O,D,T,j,z,B=0)=>{for(let F=B;F<_.length;F++){const J=_[F]=z?on(_[F]):De(_[F]);g(null,J,x,M,O,D,T,j,z)}},k=(_,x,M,O,D,T,j)=>{const z=x.el=_.el;let{patchFlag:B,dynamicChildren:F,dirs:J}=x;B|=_.patchFlag&16;const W=_.props||pt,Y=x.props||pt;let Q;if(M&&wn(M,!1),(Q=Y.onVnodeBeforeUpdate)&&ue(Q,M,x,_),J&&vn(x,_,M,"beforeUpdate"),M&&wn(M,!0),(W.innerHTML&&Y.innerHTML==null||W.textContent&&Y.textContent==null)&&u(z,""),F?I(_.dynamicChildren,F,z,M,O,ir(x,D),T):j||et(_,x,z,null,M,O,ir(x,D),T,!1),B>0){if(B&16)H(z,W,Y,M,D);else if(B&2&&W.class!==Y.class&&o(z,"class",null,Y.class,D),B&4&&o(z,"style",W.style,Y.style,D),B&8){const at=x.dynamicProps;for(let bt=0;bt<at.length;bt++){const ft=at[bt],ne=W[ft],Gt=Y[ft];(Gt!==ne||ft==="value")&&o(z,ft,ne,Gt,D,M)}}B&1&&_.children!==x.children&&u(z,x.children)}else!j&&F==null&&H(z,W,Y,M,D);((Q=Y.onVnodeUpdated)||J)&&It(()=>{Q&&ue(Q,M,x,_),J&&vn(x,_,M,"updated")},O)},I=(_,x,M,O,D,T,j)=>{for(let z=0;z<x.length;z++){const B=_[z],F=x[z],J=B.el&&(B.type===Qt||!Tn(B,F)||B.shapeFlag&198)?f(B.el):M;g(B,F,J,null,O,D,T,j,!0)}},H=(_,x,M,O,D)=>{if(x!==M){if(x!==pt)for(const T in x)!Ns(T)&&!(T in M)&&o(_,T,x[T],null,D,O);for(const T in M){if(Ns(T))continue;const j=M[T],z=x[T];j!==z&&T!=="value"&&o(_,T,z,j,D,O)}"value"in M&&o(_,"value",x.value,M.value,D)}},L=(_,x,M,O,D,T,j,z,B)=>{const F=x.el=_?_.el:a(""),J=x.anchor=_?_.anchor:a("");let{patchFlag:W,dynamicChildren:Y,slotScopeIds:Q}=x;Q&&(z=z?z.concat(Q):Q),_==null?(s(F,M,O),s(J,M,O),E(x.children||[],M,J,D,T,j,z,B)):W>0&&W&64&&Y&&_.dynamicChildren?(I(_.dynamicChildren,Y,M,D,T,j,z),(x.key!=null||D&&x===D.subTree)&&Kf(_,x,!0)):et(_,x,M,J,D,T,j,z,B)},X=(_,x,M,O,D,T,j,z,B)=>{x.slotScopeIds=z,_==null?x.shapeFlag&512?D.ctx.activate(x,M,O,j,B):rt(x,M,O,D,T,j,B):Z(_,x,B)},rt=(_,x,M,O,D,T,j)=>{const z=_.component=Vg(_,O,D);if(kf(_)&&(z.ctx.renderer=K),Wg(z,!1,j),z.asyncDep){if(D&&D.registerDep(z,G,j),!_.el){const B=z.subTree=Vt(ve);y(null,B,x,M)}}else G(z,_,x,M,D,T,j)},Z=(_,x,M)=>{const O=x.component=_.component;if(Tg(_,x,M))if(O.asyncDep&&!O.asyncResolved){U(O,x,M);return}else O.next=x,O.update();else x.el=_.el,O.vnode=x},G=(_,x,M,O,D,T,j)=>{const z=()=>{if(_.isMounted){let{next:W,bu:Y,u:Q,parent:at,vnode:bt}=_;{const Pe=qf(_);if(Pe){W&&(W.el=bt.el,U(_,W,j)),Pe.asyncDep.then(()=>{_.isUnmounted||z()});return}}let ft=W,ne;wn(_,!1),W?(W.el=bt.el,U(_,W,j)):W=bt,Y&&es(Y),(ne=W.props&&W.props.onVnodeBeforeUpdate)&&ue(ne,at,W,bt),wn(_,!0);const Gt=_l(_),Ce=_.subTree;_.subTree=Gt,g(Ce,Gt,f(Ce.el),R(Ce),_,D,T),W.el=Gt.el,ft===null&&Dg(_,Gt.el),Q&&It(Q,D),(ne=W.props&&W.props.onVnodeUpdated)&&It(()=>ue(ne,at,W,bt),D)}else{let W;const{el:Y,props:Q}=x,{bm:at,m:bt,parent:ft,root:ne,type:Gt}=_,Ce=In(x);wn(_,!1),at&&es(at),!Ce&&(W=Q&&Q.onVnodeBeforeMount)&&ue(W,ft,x),wn(_,!0);{ne.ce&&ne.ce._injectChildStyle(Gt);const Pe=_.subTree=_l(_);g(null,Pe,M,O,_,D,T),x.el=Pe.el}if(bt&&It(bt,D),!Ce&&(W=Q&&Q.onVnodeMounted)){const Pe=x;It(()=>ue(W,ft,Pe),D)}(x.shapeFlag&256||ft&&In(ft.vnode)&&ft.vnode.shapeFlag&256)&&_.a&&It(_.a,D),_.isMounted=!0,x=M=O=null}};_.scope.on();const B=_.effect=new ef(z);_.scope.off();const F=_.update=B.run.bind(B),J=_.job=B.runIfDirty.bind(B);J.i=_,J.id=_.uid,B.scheduler=()=>Ra(J),wn(_,!0),F()},U=(_,x,M)=>{x.component=_;const O=_.vnode.props;_.vnode=x,_.next=null,mg(_,x.props,O,M),xg(_,x.children,M),Xe(),hl(_),Ge()},et=(_,x,M,O,D,T,j,z,B=!1)=>{const F=_&&_.children,J=_?_.shapeFlag:0,W=x.children,{patchFlag:Y,shapeFlag:Q}=x;if(Y>0){if(Y&128){Ut(F,W,M,O,D,T,j,z,B);return}else if(Y&256){mt(F,W,M,O,D,T,j,z,B);return}}Q&8?(J&16&&At(F,D,T),W!==F&&u(M,W)):J&16?Q&16?Ut(F,W,M,O,D,T,j,z,B):At(F,D,T,!0):(J&8&&u(M,""),Q&16&&E(W,M,O,D,T,j,z,B))},mt=(_,x,M,O,D,T,j,z,B)=>{_=_||Zn,x=x||Zn;const F=_.length,J=x.length,W=Math.min(F,J);let Y;for(Y=0;Y<W;Y++){const Q=x[Y]=B?on(x[Y]):De(x[Y]);g(_[Y],Q,M,null,D,T,j,z,B)}F>J?At(_,D,T,!0,!1,W):E(x,M,O,D,T,j,z,B,W)},Ut=(_,x,M,O,D,T,j,z,B)=>{let F=0;const J=x.length;let W=_.length-1,Y=J-1;for(;F<=W&&F<=Y;){const Q=_[F],at=x[F]=B?on(x[F]):De(x[F]);if(Tn(Q,at))g(Q,at,M,null,D,T,j,z,B);else break;F++}for(;F<=W&&F<=Y;){const Q=_[W],at=x[Y]=B?on(x[Y]):De(x[Y]);if(Tn(Q,at))g(Q,at,M,null,D,T,j,z,B);else break;W--,Y--}if(F>W){if(F<=Y){const Q=Y+1,at=Q<J?x[Q].el:O;for(;F<=Y;)g(null,x[F]=B?on(x[F]):De(x[F]),M,at,D,T,j,z,B),F++}}else if(F>Y)for(;F<=W;)Pt(_[F],D,T,!0),F++;else{const Q=F,at=F,bt=new Map;for(F=at;F<=Y;F++){const se=x[F]=B?on(x[F]):De(x[F]);se.key!=null&&bt.set(se.key,F)}let ft,ne=0;const Gt=Y-at+1;let Ce=!1,Pe=0;const _s=new Array(Gt);for(F=0;F<Gt;F++)_s[F]=0;for(F=Q;F<=W;F++){const se=_[F];if(ne>=Gt){Pt(se,D,T,!0);continue}let Ee;if(se.key!=null)Ee=bt.get(se.key);else for(ft=at;ft<=Y;ft++)if(_s[ft-at]===0&&Tn(se,x[ft])){Ee=ft;break}Ee===void 0?Pt(se,D,T,!0):(_s[Ee-at]=F+1,Ee>=Pe?Pe=Ee:Ce=!0,g(se,x[Ee],M,null,D,T,j,z,B),ne++)}const rl=Ce?Mg(_s):Zn;for(ft=rl.length-1,F=Gt-1;F>=0;F--){const se=at+F,Ee=x[se],al=se+1<J?x[se+1].el:O;_s[F]===0?g(null,Ee,M,al,D,T,j,z,B):Ce&&(ft<0||F!==rl[ft]?Kt(Ee,M,al,2):ft--)}}},Kt=(_,x,M,O,D=null)=>{const{el:T,type:j,transition:z,children:B,shapeFlag:F}=_;if(F&6){Kt(_.component.subTree,x,M,O);return}if(F&128){_.suspense.move(x,M,O);return}if(F&64){j.move(_,x,M,K);return}if(j===Qt){s(T,x,M);for(let W=0;W<B.length;W++)Kt(B[W],x,M,O);s(_.anchor,x,M);return}if(j===Hi){w(_,x,M);return}if(O!==2&&F&1&&z)if(O===0)z.beforeEnter(T),s(T,x,M),It(()=>z.enter(T),D);else{const{leave:W,delayLeave:Y,afterLeave:Q}=z,at=()=>{_.ctx.isUnmounted?i(T):s(T,x,M)},bt=()=>{W(T,()=>{at(),Q&&Q()})};Y?Y(T,at,bt):bt()}else s(T,x,M)},Pt=(_,x,M,O=!1,D=!1)=>{const{type:T,props:j,ref:z,children:B,dynamicChildren:F,shapeFlag:J,patchFlag:W,dirs:Y,cacheIndex:Q}=_;if(W===-2&&(D=!1),z!=null&&(Xe(),ao(z,null,M,_,!0),Ge()),Q!=null&&(x.renderCache[Q]=void 0),J&256){x.ctx.deactivate(_);return}const at=J&1&&Y,bt=!In(_);let ft;if(bt&&(ft=j&&j.onVnodeBeforeUnmount)&&ue(ft,x,_),J&6)Me(_.component,M,O);else{if(J&128){_.suspense.unmount(M,O);return}at&&vn(_,null,x,"beforeUnmount"),J&64?_.type.remove(_,x,M,K,O):F&&!F.hasOnce&&(T!==Qt||W>0&&W&64)?At(F,x,M,!1,!0):(T===Qt&&W&384||!D&&J&16)&&At(B,x,M),O&&ae(_)}(bt&&(ft=j&&j.onVnodeUnmounted)||at)&&It(()=>{ft&&ue(ft,x,_),at&&vn(_,null,x,"unmounted")},M)},ae=_=>{const{type:x,el:M,anchor:O,transition:D}=_;if(x===Qt){Xt(M,O);return}if(x===Hi){v(_);return}const T=()=>{i(M),D&&!D.persisted&&D.afterLeave&&D.afterLeave()};if(_.shapeFlag&1&&D&&!D.persisted){const{leave:j,delayLeave:z}=D,B=()=>j(M,T);z?z(_.el,T,B):B()}else T()},Xt=(_,x)=>{let M;for(;_!==x;)M=h(_),i(_),_=M;i(x)},Me=(_,x,M)=>{const{bum:O,scope:D,job:T,subTree:j,um:z,m:B,a:F,parent:J,slots:{__:W}}=_;co(B),co(F),O&&es(O),J&&q(W)&&W.forEach(Y=>{J.renderCache[Y]=void 0}),D.stop(),T&&(T.flags|=8,Pt(j,_,x,M)),z&&It(z,x),It(()=>{_.isUnmounted=!0},x),x&&x.pendingBranch&&!x.isUnmounted&&_.asyncDep&&!_.asyncResolved&&_.suspenseId===x.pendingId&&(x.deps--,x.deps===0&&x.resolve())},At=(_,x,M,O=!1,D=!1,T=0)=>{for(let j=T;j<_.length;j++)Pt(_[j],x,M,O,D)},R=_=>{if(_.shapeFlag&6)return R(_.component.subTree);if(_.shapeFlag&128)return _.suspense.next();const x=h(_.anchor||_.el),M=x&&x[Up];return M?h(M):x};let $=!1;const V=(_,x,M)=>{_==null?x._vnode&&Pt(x._vnode,null,null,!0):g(x._vnode||null,_,x,null,null,null,M),x._vnode=_,$||($=!0,hl(),wf(),$=!1)},K={p:g,um:Pt,m:Kt,r:ae,mt:rt,mc:E,pc:et,pbc:I,n:R,o:e};return{render:V,hydrate:void 0,createApp:dg(V)}}function ir({type:e,props:t},n){return n==="svg"&&e==="foreignObject"||n==="mathml"&&e==="annotation-xml"&&t&&t.encoding&&t.encoding.includes("html")?void 0:n}function wn({effect:e,job:t},n){n?(e.flags|=32,t.flags|=4):(e.flags&=-33,t.flags&=-5)}function Sg(e,t){return(!e||e&&!e.pendingBranch)&&t&&!t.persisted}function Kf(e,t,n=!1){const s=e.children,i=t.children;if(q(s)&&q(i))for(let o=0;o<s.length;o++){const r=s[o];let a=i[o];a.shapeFlag&1&&!a.dynamicChildren&&((a.patchFlag<=0||a.patchFlag===32)&&(a=i[o]=on(i[o]),a.el=r.el),!n&&a.patchFlag!==-2&&Kf(r,a)),a.type===Do&&(a.el=r.el),a.type===ve&&!a.el&&(a.el=r.el)}}function Mg(e){const t=e.slice(),n=[0];let s,i,o,r,a;const l=e.length;for(s=0;s<l;s++){const c=e[s];if(c!==0){if(i=n[n.length-1],e[i]<c){t[s]=i,n.push(s);continue}for(o=0,r=n.length-1;o<r;)a=o+r>>1,e[n[a]]<c?o=a+1:r=a;c<e[n[o]]&&(o>0&&(t[s]=n[o-1]),n[o]=s)}}for(o=n.length,r=n[o-1];o-- >0;)n[o]=r,r=t[r];return n}function qf(e){const t=e.subTree.component;if(t)return t.asyncDep&&!t.asyncResolved?t:qf(t)}function co(e){if(e)for(let t=0;t<e.length;t++)e[t].flags|=8}const Cg=Symbol.for("v-scx"),Pg=()=>de(Cg);function is(e,t,n){return Yf(e,t,n)}function Yf(e,t,n=pt){const{immediate:s,deep:i,flush:o,once:r}=n,a=Dt({},n),l=t&&s||!t&&o!=="post";let c;if(ti){if(o==="sync"){const d=Pg();c=d.__watcherHandles||(d.__watcherHandles=[])}else if(!l){const d=()=>{};return d.stop=Le,d.resume=Le,d.pause=Le,d}}const u=Lt;a.call=(d,p,g)=>xe(d,u,p,g);let f=!1;o==="post"?a.scheduler=d=>{It(d,u&&u.suspense)}:o!=="sync"&&(f=!0,a.scheduler=(d,p)=>{p?d():Ra(d)}),a.augmentJob=d=>{t&&(d.flags|=4),f&&(d.flags|=2,u&&(d.id=u.uid,d.i=u))};const h=Hp(e,t,a);return ti&&(c?c.push(h):l&&h()),h}function Eg(e,t,n){const s=this.proxy,i=Mt(e)?e.includes(".")?Xf(s,e):()=>s[e]:e.bind(s,s);let o;tt(t)?o=t:(o=t.handler,n=t);const r=pi(this),a=Yf(i,o.bind(s),n);return r(),a}function Xf(e,t){const n=t.split(".");return()=>{let s=e;for(let i=0;i<n.length&&s;i++)s=s[n[i]];return s}}const kg=(e,t)=>t==="modelValue"||t==="model-value"?e.modelModifiers:e[`${t}Modifiers`]||e[`${ge(t)}Modifiers`]||e[`${_n(t)}Modifiers`];function Ag(e,t,...n){if(e.isUnmounted)return;const s=e.vnode.props||pt;let i=n;const o=t.startsWith("update:"),r=o&&kg(s,t.slice(7));r&&(r.trim&&(i=n.map(u=>Mt(u)?u.trim():u)),r.number&&(i=n.map(eo)));let a,l=s[a=Jo(t)]||s[a=Jo(ge(t))];!l&&o&&(l=s[a=Jo(_n(t))]),l&&xe(l,e,6,i);const c=s[a+"Once"];if(c){if(!e.emitted)e.emitted={};else if(e.emitted[a])return;e.emitted[a]=!0,xe(c,e,6,i)}}function Gf(e,t,n=!1){const s=t.emitsCache,i=s.get(e);if(i!==void 0)return i;const o=e.emits;let r={},a=!1;if(!tt(e)){const l=c=>{const u=Gf(c,t,!0);u&&(a=!0,Dt(r,u))};!n&&t.mixins.length&&t.mixins.forEach(l),e.extends&&l(e.extends),e.mixins&&e.mixins.forEach(l)}return!o&&!a?(gt(e)&&s.set(e,null),null):(q(o)?o.forEach(l=>r[l]=null):Dt(r,o),gt(e)&&s.set(e,r),r)}function To(e,t){return!e||!Mo(t)?!1:(t=t.slice(2).replace(/Once$/,""),ut(e,t[0].toLowerCase()+t.slice(1))||ut(e,_n(t))||ut(e,t))}function _l(e){const{type:t,vnode:n,proxy:s,withProxy:i,propsOptions:[o],slots:r,attrs:a,emit:l,render:c,renderCache:u,props:f,data:h,setupState:d,ctx:p,inheritAttrs:g}=e,m=ro(e);let y,b;try{if(n.shapeFlag&4){const v=i||s,S=v;y=De(c.call(S,v,u,f,d,h,p)),b=a}else{const v=t;y=De(v.length>1?v(f,{attrs:a,slots:r,emit:l}):v(f,null)),b=t.props?a:Og(a)}}catch(v){js.length=0,Ao(v,e,1),y=Vt(ve)}let w=y;if(b&&g!==!1){const v=Object.keys(b),{shapeFlag:S}=w;v.length&&S&7&&(o&&v.some(ma)&&(b=Rg(b,o)),w=gn(w,b,!1,!0))}return n.dirs&&(w=gn(w,null,!1,!0),w.dirs=w.dirs?w.dirs.concat(n.dirs):n.dirs),n.transition&&rs(w,n.transition),y=w,ro(m),y}const Og=e=>{let t;for(const n in e)(n==="class"||n==="style"||Mo(n))&&((t||(t={}))[n]=e[n]);return t},Rg=(e,t)=>{const n={};for(const s in e)(!ma(s)||!(s.slice(9)in t))&&(n[s]=e[s]);return n};function Tg(e,t,n){const{props:s,children:i,component:o}=e,{props:r,children:a,patchFlag:l}=t,c=o.emitsOptions;if(t.dirs||t.transition)return!0;if(n&&l>=0){if(l&1024)return!0;if(l&16)return s?xl(s,r,c):!!r;if(l&8){const u=t.dynamicProps;for(let f=0;f<u.length;f++){const h=u[f];if(r[h]!==s[h]&&!To(c,h))return!0}}}else return(i||a)&&(!a||!a.$stable)?!0:s===r?!1:s?r?xl(s,r,c):!0:!!r;return!1}function xl(e,t,n){const s=Object.keys(t);if(s.length!==Object.keys(e).length)return!0;for(let i=0;i<s.length;i++){const o=s[i];if(t[o]!==e[o]&&!To(n,o))return!0}return!1}function Dg({vnode:e,parent:t},n){for(;t;){const s=t.subTree;if(s.suspense&&s.suspense.activeBranch===e&&(s.el=e.el),s===e)(e=t.vnode).el=n,t=t.parent;else break}}const uo=e=>e.__isSuspense;function Lg(e,t){t&&t.pendingBranch?q(e)?t.effects.push(...e):t.effects.push(e):Wp(e)}const Qt=Symbol.for("v-fgt"),Do=Symbol.for("v-txt"),ve=Symbol.for("v-cmt"),Hi=Symbol.for("v-stc"),js=[];let re=null;function Ir(e=!1){js.push(re=e?null:[])}function Fg(){js.pop(),re=js[js.length-1]||null}let Zs=1;function vl(e,t=!1){Zs+=e,e<0&&re&&t&&(re.hasOnce=!0)}function Jf(e){return e.dynamicChildren=Zs>0?re||Zn:null,Fg(),Zs>0&&re&&re.push(e),e}function PS(e,t,n,s,i,o){return Jf(Zf(e,t,n,s,i,o,!0))}function Nr(e,t,n,s,i){return Jf(Vt(e,t,n,s,i,!0))}function as(e){return e?e.__v_isVNode===!0:!1}function Tn(e,t){return e.type===t.type&&e.key===t.key}const Qf=({key:e})=>e??null,ji=({ref:e,ref_key:t,ref_for:n})=>(typeof e=="number"&&(e=""+e),e!=null?Mt(e)||kt(e)||tt(e)?{i:Rt,r:e,k:t,f:!!n}:e:null);function Zf(e,t=null,n=null,s=0,i=null,o=e===Qt?0:1,r=!1,a=!1){const l={__v_isVNode:!0,__v_skip:!0,type:e,props:t,key:t&&Qf(t),ref:t&&ji(t),scopeId:Mf,slotScopeIds:null,children:n,component:null,suspense:null,ssContent:null,ssFallback:null,dirs:null,transition:null,el:null,anchor:null,target:null,targetStart:null,targetAnchor:null,staticCount:0,shapeFlag:o,patchFlag:s,dynamicProps:i,dynamicChildren:null,appContext:null,ctx:Rt};return a?(Na(l,n),o&128&&e.normalize(l)):n&&(l.shapeFlag|=Mt(n)?8:16),Zs>0&&!r&&re&&(l.patchFlag>0||o&6)&&l.patchFlag!==32&&re.push(l),l}const Vt=Ig;function Ig(e,t=null,n=null,s=0,i=null,o=!1){if((!e||e===Tf)&&(e=ve),as(e)){const a=gn(e,t,!0);return n&&Na(a,n),Zs>0&&!o&&re&&(a.shapeFlag&6?re[re.indexOf(e)]=a:re.push(a)),a.patchFlag=-2,a}if(qg(e)&&(e=e.__vccOpts),t){t=Ng(t);let{class:a,style:l}=t;a&&!Mt(a)&&(t.class=xa(a)),gt(l)&&(ka(l)&&!q(l)&&(l=Dt({},l)),t.style=_a(l))}const r=Mt(e)?1:uo(e)?128:Kp(e)?64:gt(e)?4:tt(e)?2:0;return Zf(e,t,n,s,i,r,o,!0)}function Ng(e){return e?ka(e)||Hf(e)?Dt({},e):e:null}function gn(e,t,n=!1,s=!1){const{props:i,ref:o,patchFlag:r,children:a,transition:l}=e,c=t?zg(i||{},t):i,u={__v_isVNode:!0,__v_skip:!0,type:e.type,props:c,key:c&&Qf(c),ref:t&&t.ref?n&&o?q(o)?o.concat(ji(t)):[o,ji(t)]:ji(t):o,scopeId:e.scopeId,slotScopeIds:e.slotScopeIds,children:a,target:e.target,targetStart:e.targetStart,targetAnchor:e.targetAnchor,staticCount:e.staticCount,shapeFlag:e.shapeFlag,patchFlag:t&&e.type!==Qt?r===-1?16:r|16:r,dynamicProps:e.dynamicProps,dynamicChildren:e.dynamicChildren,appContext:e.appContext,dirs:e.dirs,transition:l,component:e.component,suspense:e.suspense,ssContent:e.ssContent&&gn(e.ssContent),ssFallback:e.ssFallback&&gn(e.ssFallback),el:e.el,anchor:e.anchor,ctx:e.ctx,ce:e.ce};return l&&s&&rs(u,l.clone(u)),u}function Bg(e=" ",t=0){return Vt(Do,null,e,t)}function ES(e,t){const n=Vt(Hi,null,e);return n.staticCount=t,n}function kS(e="",t=!1){return t?(Ir(),Nr(ve,null,e)):Vt(ve,null,e)}function De(e){return e==null||typeof e=="boolean"?Vt(ve):q(e)?Vt(Qt,null,e.slice()):as(e)?on(e):Vt(Do,null,String(e))}function on(e){return e.el===null&&e.patchFlag!==-1||e.memo?e:gn(e)}function Na(e,t){let n=0;const{shapeFlag:s}=e;if(t==null)t=null;else if(q(t))n=16;else if(typeof t=="object")if(s&65){const i=t.default;i&&(i._c&&(i._d=!1),Na(e,i()),i._c&&(i._d=!0));return}else{n=32;const i=t._;!i&&!Hf(t)?t._ctx=Rt:i===3&&Rt&&(Rt.slots._===1?t._=1:(t._=2,e.patchFlag|=1024))}else tt(t)?(t={default:t,_ctx:Rt},n=32):(t=String(t),s&64?(n=16,t=[Bg(t)]):n=8);e.children=t,e.shapeFlag|=n}function zg(...e){const t={};for(let n=0;n<e.length;n++){const s=e[n];for(const i in s)if(i==="class")t.class!==s.class&&(t.class=xa([t.class,s.class]));else if(i==="style")t.style=_a([t.style,s.style]);else if(Mo(i)){const o=t[i],r=s[i];r&&o!==r&&!(q(o)&&o.includes(r))&&(t[i]=o?[].concat(o,r):r)}else i!==""&&(t[i]=s[i])}return t}function ue(e,t,n,s=null){xe(e,t,7,[n,s])}const Hg=Nf();let jg=0;function Vg(e,t,n){const s=e.type,i=(t?t.appContext:e.appContext)||Hg,o={uid:jg++,vnode:e,type:s,parent:t,appContext:i,root:null,next:null,subTree:null,effect:null,update:null,job:null,scope:new Qu(!0),render:null,proxy:null,exposed:null,exposeProxy:null,withProxy:null,provides:t?t.provides:Object.create(i.provides),ids:t?t.ids:["",0,0],accessCache:null,renderCache:[],components:null,directives:null,propsOptions:Vf(s,i),emitsOptions:Gf(s,i),emit:null,emitted:null,propsDefaults:pt,inheritAttrs:s.inheritAttrs,ctx:pt,data:pt,props:pt,attrs:pt,slots:pt,refs:pt,setupState:pt,setupContext:null,suspense:n,suspenseId:n?n.pendingId:0,asyncDep:null,asyncResolved:!1,isMounted:!1,isUnmounted:!1,isDeactivated:!1,bc:null,c:null,bm:null,m:null,bu:null,u:null,um:null,bum:null,da:null,a:null,rtg:null,rtc:null,ec:null,sp:null};return o.ctx={_:o},o.root=t?t.root:o,o.emit=Ag.bind(null,o),e.ce&&e.ce(o),o}let Lt=null;const th=()=>Lt||Rt;let fo,Br;{const e=Eo(),t=(n,s)=>{let i;return(i=e[n])||(i=e[n]=[]),i.push(s),o=>{i.length>1?i.forEach(r=>r(o)):i[0](o)}};fo=t("__VUE_INSTANCE_SETTERS__",n=>Lt=n),Br=t("__VUE_SSR_SETTERS__",n=>ti=n)}const pi=e=>{const t=Lt;return fo(e),e.scope.on(),()=>{e.scope.off(),fo(t)}},wl=()=>{Lt&&Lt.scope.off(),fo(null)};function eh(e){return e.vnode.shapeFlag&4}let ti=!1;function Wg(e,t=!1,n=!1){t&&Br(t);const{props:s,children:i}=e.vnode,o=eh(e);gg(e,s,o,t),_g(e,i,n||t);const r=o?$g(e,t):void 0;return t&&Br(!1),r}function $g(e,t){const n=e.type;e.accessCache=Object.create(null),e.proxy=new Proxy(e.ctx,rg);const{setup:s}=n;if(s){Xe();const i=e.setupContext=s.length>1?Kg(e):null,o=pi(e),r=di(s,e,0,[e.props,i]),a=Uu(r);if(Ge(),o(),(a||e.sp)&&!In(e)&&Ef(e),a){if(r.then(wl,wl),t)return r.then(l=>{Sl(e,l)}).catch(l=>{Ao(l,e,0)});e.asyncDep=r}else Sl(e,r)}else nh(e)}function Sl(e,t,n){tt(t)?e.type.__ssrInlineRender?e.ssrRender=t:e.render=t:gt(t)&&(e.setupState=_f(t)),nh(e)}function nh(e,t,n){const s=e.type;e.render||(e.render=s.render||Le);{const i=pi(e);Xe();try{ag(e)}finally{Ge(),i()}}}const Ug={get(e,t){return Ht(e,"get",""),e[t]}};function Kg(e){const t=n=>{e.exposed=n||{}};return{attrs:new Proxy(e.attrs,Ug),slots:e.slots,emit:e.emit,expose:t}}function Lo(e){return e.exposed?e.exposeProxy||(e.exposeProxy=new Proxy(_f(Aa(e.exposed)),{get(t,n){if(n in t)return t[n];if(n in Hs)return Hs[n](e)},has(t,n){return n in t||n in Hs}})):e.proxy}function zr(e,t=!0){return tt(e)?e.displayName||e.name:e.name||t&&e.__name}function qg(e){return tt(e)&&"__vccOpts"in e}const fe=(e,t)=>Bp(e,t,ti);function sh(e,t,n){const s=arguments.length;return s===2?gt(t)&&!q(t)?as(t)?Vt(e,null,[t]):Vt(e,t):Vt(e,null,t):(s>3?n=Array.prototype.slice.call(arguments,2):s===3&&as(n)&&(n=[n]),Vt(e,t,n))}const Yg="3.5.16";/**
* @vue/runtime-dom v3.5.16
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/let Hr;const Ml=typeof window<"u"&&window.trustedTypes;if(Ml)try{Hr=Ml.createPolicy("vue",{createHTML:e=>e})}catch{}const ih=Hr?e=>Hr.createHTML(e):e=>e,Xg="http://www.w3.org/2000/svg",Gg="http://www.w3.org/1998/Math/MathML",je=typeof document<"u"?document:null,Cl=je&&je.createElement("template"),Jg={insert:(e,t,n)=>{t.insertBefore(e,n||null)},remove:e=>{const t=e.parentNode;t&&t.removeChild(e)},createElement:(e,t,n,s)=>{const i=t==="svg"?je.createElementNS(Xg,e):t==="mathml"?je.createElementNS(Gg,e):n?je.createElement(e,{is:n}):je.createElement(e);return e==="select"&&s&&s.multiple!=null&&i.setAttribute("multiple",s.multiple),i},createText:e=>je.createTextNode(e),createComment:e=>je.createComment(e),setText:(e,t)=>{e.nodeValue=t},setElementText:(e,t)=>{e.textContent=t},parentNode:e=>e.parentNode,nextSibling:e=>e.nextSibling,querySelector:e=>je.querySelector(e),setScopeId(e,t){e.setAttribute(t,"")},insertStaticContent(e,t,n,s,i,o){const r=n?n.previousSibling:t.lastChild;if(i&&(i===o||i.nextSibling))for(;t.insertBefore(i.cloneNode(!0),n),!(i===o||!(i=i.nextSibling)););else{Cl.innerHTML=ih(s==="svg"?`<svg>${e}</svg>`:s==="mathml"?`<math>${e}</math>`:e);const a=Cl.content;if(s==="svg"||s==="mathml"){const l=a.firstChild;for(;l.firstChild;)a.appendChild(l.firstChild);a.removeChild(l)}t.insertBefore(a,n)}return[r?r.nextSibling:t.firstChild,n?n.previousSibling:t.lastChild]}},Ze="transition",vs="animation",ls=Symbol("_vtc"),oh={name:String,type:String,css:{type:Boolean,default:!0},duration:[String,Number,Object],enterFromClass:String,enterActiveClass:String,enterToClass:String,appearFromClass:String,appearActiveClass:String,appearToClass:String,leaveFromClass:String,leaveActiveClass:String,leaveToClass:String},Qg=Dt({},Yp,oh),Sn=(e,t=[])=>{q(e)?e.forEach(n=>n(...t)):e&&e(...t)},Pl=e=>e?q(e)?e.some(t=>t.length>1):e.length>1:!1;function Zg(e){const t={};for(const L in e)L in oh||(t[L]=e[L]);if(e.css===!1)return t;const{name:n="v",type:s,duration:i,enterFromClass:o=`${n}-enter-from`,enterActiveClass:r=`${n}-enter-active`,enterToClass:a=`${n}-enter-to`,appearFromClass:l=o,appearActiveClass:c=r,appearToClass:u=a,leaveFromClass:f=`${n}-leave-from`,leaveActiveClass:h=`${n}-leave-active`,leaveToClass:d=`${n}-leave-to`}=e,p=tm(i),g=p&&p[0],m=p&&p[1],{onBeforeEnter:y,onEnter:b,onEnterCancelled:w,onLeave:v,onLeaveCancelled:S,onBeforeAppear:A=y,onAppear:P=b,onAppearCancelled:E=w}=t,k=(L,X,rt,Z)=>{L._enterCancelled=Z,en(L,X?u:a),en(L,X?c:r),rt&&rt()},I=(L,X)=>{L._isLeaving=!1,en(L,f),en(L,d),en(L,h),X&&X()},H=L=>(X,rt)=>{const Z=L?P:b,G=()=>k(X,L,rt);Sn(Z,[X,G]),El(()=>{en(X,L?l:o),Ae(X,L?u:a),Pl(Z)||kl(X,s,g,G)})};return Dt(t,{onBeforeEnter(L){Sn(y,[L]),Ae(L,o),Ae(L,r)},onBeforeAppear(L){Sn(A,[L]),Ae(L,l),Ae(L,c)},onEnter:H(!1),onAppear:H(!0),onLeave(L,X){L._isLeaving=!0;const rt=()=>I(L,X);Ae(L,f),L._enterCancelled?(Ae(L,h),jr()):(jr(),Ae(L,h)),El(()=>{L._isLeaving&&(en(L,f),Ae(L,d),Pl(v)||kl(L,s,m,rt))}),Sn(v,[L,rt])},onEnterCancelled(L){k(L,!1,void 0,!0),Sn(w,[L])},onAppearCancelled(L){k(L,!0,void 0,!0),Sn(E,[L])},onLeaveCancelled(L){I(L),Sn(S,[L])}})}function tm(e){if(e==null)return null;if(gt(e))return[or(e.enter),or(e.leave)];{const t=or(e);return[t,t]}}function or(e){return np(e)}function Ae(e,t){t.split(/\s+/).forEach(n=>n&&e.classList.add(n)),(e[ls]||(e[ls]=new Set)).add(t)}function en(e,t){t.split(/\s+/).forEach(s=>s&&e.classList.remove(s));const n=e[ls];n&&(n.delete(t),n.size||(e[ls]=void 0))}function El(e){requestAnimationFrame(()=>{requestAnimationFrame(e)})}let em=0;function kl(e,t,n,s){const i=e._endId=++em,o=()=>{i===e._endId&&s()};if(n!=null)return setTimeout(o,n);const{type:r,timeout:a,propCount:l}=rh(e,t);if(!r)return s();const c=r+"end";let u=0;const f=()=>{e.removeEventListener(c,h),o()},h=d=>{d.target===e&&++u>=l&&f()};setTimeout(()=>{u<l&&f()},a+1),e.addEventListener(c,h)}function rh(e,t){const n=window.getComputedStyle(e),s=p=>(n[p]||"").split(", "),i=s(`${Ze}Delay`),o=s(`${Ze}Duration`),r=Al(i,o),a=s(`${vs}Delay`),l=s(`${vs}Duration`),c=Al(a,l);let u=null,f=0,h=0;t===Ze?r>0&&(u=Ze,f=r,h=o.length):t===vs?c>0&&(u=vs,f=c,h=l.length):(f=Math.max(r,c),u=f>0?r>c?Ze:vs:null,h=u?u===Ze?o.length:l.length:0);const d=u===Ze&&/\b(transform|all)(,|$)/.test(s(`${Ze}Property`).toString());return{type:u,timeout:f,propCount:h,hasTransform:d}}function Al(e,t){for(;e.length<t.length;)e=e.concat(e);return Math.max(...t.map((n,s)=>Ol(n)+Ol(e[s])))}function Ol(e){return e==="auto"?0:Number(e.slice(0,-1).replace(",","."))*1e3}function jr(){return document.body.offsetHeight}function nm(e,t,n){const s=e[ls];s&&(t=(t?[t,...s]:[...s]).join(" ")),t==null?e.removeAttribute("class"):n?e.setAttribute("class",t):e.className=t}const Rl=Symbol("_vod"),sm=Symbol("_vsh"),im=Symbol(""),om=/(^|;)\s*display\s*:/;function rm(e,t,n){const s=e.style,i=Mt(n);let o=!1;if(n&&!i){if(t)if(Mt(t))for(const r of t.split(";")){const a=r.slice(0,r.indexOf(":")).trim();n[a]==null&&Vi(s,a,"")}else for(const r in t)n[r]==null&&Vi(s,r,"");for(const r in n)r==="display"&&(o=!0),Vi(s,r,n[r])}else if(i){if(t!==n){const r=s[im];r&&(n+=";"+r),s.cssText=n,o=om.test(n)}}else t&&e.removeAttribute("style");Rl in e&&(e[Rl]=o?s.display:"",e[sm]&&(s.display="none"))}const Tl=/\s*!important$/;function Vi(e,t,n){if(q(n))n.forEach(s=>Vi(e,t,s));else if(n==null&&(n=""),t.startsWith("--"))e.setProperty(t,n);else{const s=am(e,t);Tl.test(n)?e.setProperty(_n(s),n.replace(Tl,""),"important"):e[s]=n}}const Dl=["Webkit","Moz","ms"],rr={};function am(e,t){const n=rr[t];if(n)return n;let s=ge(t);if(s!=="filter"&&s in e)return rr[t]=s;s=Po(s);for(let i=0;i<Dl.length;i++){const o=Dl[i]+s;if(o in e)return rr[t]=o}return t}const Ll="http://www.w3.org/1999/xlink";function Fl(e,t,n,s,i,o=lp(t)){s&&t.startsWith("xlink:")?n==null?e.removeAttributeNS(Ll,t.slice(6,t.length)):e.setAttributeNS(Ll,t,n):n==null||o&&!Xu(n)?e.removeAttribute(t):e.setAttribute(t,o?"":_e(n)?String(n):n)}function Il(e,t,n,s,i){if(t==="innerHTML"||t==="textContent"){n!=null&&(e[t]=t==="innerHTML"?ih(n):n);return}const o=e.tagName;if(t==="value"&&o!=="PROGRESS"&&!o.includes("-")){const a=o==="OPTION"?e.getAttribute("value")||"":e.value,l=n==null?e.type==="checkbox"?"on":"":String(n);(a!==l||!("_value"in e))&&(e.value=l),n==null&&e.removeAttribute(t),e._value=n;return}let r=!1;if(n===""||n==null){const a=typeof e[t];a==="boolean"?n=Xu(n):n==null&&a==="string"?(n="",r=!0):a==="number"&&(n=0,r=!0)}try{e[t]=n}catch{}r&&e.removeAttribute(i||t)}function Ue(e,t,n,s){e.addEventListener(t,n,s)}function lm(e,t,n,s){e.removeEventListener(t,n,s)}const Nl=Symbol("_vei");function cm(e,t,n,s,i=null){const o=e[Nl]||(e[Nl]={}),r=o[t];if(s&&r)r.value=s;else{const[a,l]=um(t);if(s){const c=o[t]=dm(s,i);Ue(e,a,c,l)}else r&&(lm(e,a,r,l),o[t]=void 0)}}const Bl=/(?:Once|Passive|Capture)$/;function um(e){let t;if(Bl.test(e)){t={};let s;for(;s=e.match(Bl);)e=e.slice(0,e.length-s[0].length),t[s[0].toLowerCase()]=!0}return[e[2]===":"?e.slice(3):_n(e.slice(2)),t]}let ar=0;const fm=Promise.resolve(),hm=()=>ar||(fm.then(()=>ar=0),ar=Date.now());function dm(e,t){const n=s=>{if(!s._vts)s._vts=Date.now();else if(s._vts<=n.attached)return;xe(pm(s,n.value),t,5,[s])};return n.value=e,n.attached=hm(),n}function pm(e,t){if(q(t)){const n=e.stopImmediatePropagation;return e.stopImmediatePropagation=()=>{n.call(e),e._stopped=!0},t.map(s=>i=>!i._stopped&&s&&s(i))}else return t}const zl=e=>e.charCodeAt(0)===111&&e.charCodeAt(1)===110&&e.charCodeAt(2)>96&&e.charCodeAt(2)<123,gm=(e,t,n,s,i,o)=>{const r=i==="svg";t==="class"?nm(e,s,r):t==="style"?rm(e,n,s):Mo(t)?ma(t)||cm(e,t,n,s,o):(t[0]==="."?(t=t.slice(1),!0):t[0]==="^"?(t=t.slice(1),!1):mm(e,t,s,r))?(Il(e,t,s),!e.tagName.includes("-")&&(t==="value"||t==="checked"||t==="selected")&&Fl(e,t,s,r,o,t!=="value")):e._isVueCE&&(/[A-Z]/.test(t)||!Mt(s))?Il(e,ge(t),s,o,t):(t==="true-value"?e._trueValue=s:t==="false-value"&&(e._falseValue=s),Fl(e,t,s,r))};function mm(e,t,n,s){if(s)return!!(t==="innerHTML"||t==="textContent"||t in e&&zl(t)&&tt(n));if(t==="spellcheck"||t==="draggable"||t==="translate"||t==="autocorrect"||t==="form"||t==="list"&&e.tagName==="INPUT"||t==="type"&&e.tagName==="TEXTAREA")return!1;if(t==="width"||t==="height"){const i=e.tagName;if(i==="IMG"||i==="VIDEO"||i==="CANVAS"||i==="SOURCE")return!1}return zl(t)&&Mt(n)?!1:t in e}const ah=new WeakMap,lh=new WeakMap,ho=Symbol("_moveCb"),Hl=Symbol("_enterCb"),bm=e=>(delete e.props.mode,e),ym=bm({name:"TransitionGroup",props:Dt({},Qg,{tag:String,moveClass:String}),setup(e,{slots:t}){const n=th(),s=qp();let i,o;return Da(()=>{if(!i.length)return;const r=e.moveClass||`${e.name||"v"}-move`;if(!wm(i[0].el,n.vnode.el,r)){i=[];return}i.forEach(_m),i.forEach(xm);const a=i.filter(vm);jr(),a.forEach(l=>{const c=l.el,u=c.style;Ae(c,r),u.transform=u.webkitTransform=u.transitionDuration="";const f=c[ho]=h=>{h&&h.target!==c||(!h||/transform$/.test(h.propertyName))&&(c.removeEventListener("transitionend",f),c[ho]=null,en(c,r))};c.addEventListener("transitionend",f)}),i=[]}),()=>{const r=lt(e),a=Zg(r);let l=r.tag||Qt;if(i=[],o)for(let c=0;c<o.length;c++){const u=o[c];u.el&&u.el instanceof Element&&(i.push(u),rs(u,Rr(u,a,s,n)),ah.set(u,u.el.getBoundingClientRect()))}o=t.default?Cf(t.default()):[];for(let c=0;c<o.length;c++){const u=o[c];u.key!=null&&rs(u,Rr(u,a,s,n))}return Vt(l,null,o)}}}),AS=ym;function _m(e){const t=e.el;t[ho]&&t[ho](),t[Hl]&&t[Hl]()}function xm(e){lh.set(e,e.el.getBoundingClientRect())}function vm(e){const t=ah.get(e),n=lh.get(e),s=t.left-n.left,i=t.top-n.top;if(s||i){const o=e.el.style;return o.transform=o.webkitTransform=`translate(${s}px,${i}px)`,o.transitionDuration="0s",e}}function wm(e,t,n){const s=e.cloneNode(),i=e[ls];i&&i.forEach(a=>{a.split(/\s+/).forEach(l=>l&&s.classList.remove(l))}),n.split(/\s+/).forEach(a=>a&&s.classList.add(a)),s.style.display="none";const o=t.nodeType===1?t:t.parentNode;o.appendChild(s);const{hasTransform:r}=rh(s);return o.removeChild(s),r}const mn=e=>{const t=e.props["onUpdate:modelValue"]||!1;return q(t)?n=>es(t,n):t};function Sm(e){e.target.composing=!0}function jl(e){const t=e.target;t.composing&&(t.composing=!1,t.dispatchEvent(new Event("input")))}const pe=Symbol("_assign"),OS={created(e,{modifiers:{lazy:t,trim:n,number:s}},i){e[pe]=mn(i);const o=s||i.props&&i.props.type==="number";Ue(e,t?"change":"input",r=>{if(r.target.composing)return;let a=e.value;n&&(a=a.trim()),o&&(a=eo(a)),e[pe](a)}),n&&Ue(e,"change",()=>{e.value=e.value.trim()}),t||(Ue(e,"compositionstart",Sm),Ue(e,"compositionend",jl),Ue(e,"change",jl))},mounted(e,{value:t}){e.value=t??""},beforeUpdate(e,{value:t,oldValue:n,modifiers:{lazy:s,trim:i,number:o}},r){if(e[pe]=mn(r),e.composing)return;const a=(o||e.type==="number")&&!/^0\d/.test(e.value)?eo(e.value):e.value,l=t??"";a!==l&&(document.activeElement===e&&e.type!=="range"&&(s&&t===n||i&&e.value.trim()===l)||(e.value=l))}},RS={deep:!0,created(e,t,n){e[pe]=mn(n),Ue(e,"change",()=>{const s=e._modelValue,i=cs(e),o=e.checked,r=e[pe];if(q(s)){const a=va(s,i),l=a!==-1;if(o&&!l)r(s.concat(i));else if(!o&&l){const c=[...s];c.splice(a,1),r(c)}}else if(gs(s)){const a=new Set(s);o?a.add(i):a.delete(i),r(a)}else r(ch(e,o))})},mounted:Vl,beforeUpdate(e,t,n){e[pe]=mn(n),Vl(e,t,n)}};function Vl(e,{value:t,oldValue:n},s){e._modelValue=t;let i;if(q(t))i=va(t,s.props.value)>-1;else if(gs(t))i=t.has(s.props.value);else{if(t===n)return;i=jn(t,ch(e,!0))}e.checked!==i&&(e.checked=i)}const TS={created(e,{value:t},n){e.checked=jn(t,n.props.value),e[pe]=mn(n),Ue(e,"change",()=>{e[pe](cs(e))})},beforeUpdate(e,{value:t,oldValue:n},s){e[pe]=mn(s),t!==n&&(e.checked=jn(t,s.props.value))}},DS={deep:!0,created(e,{value:t,modifiers:{number:n}},s){const i=gs(t);Ue(e,"change",()=>{const o=Array.prototype.filter.call(e.options,r=>r.selected).map(r=>n?eo(cs(r)):cs(r));e[pe](e.multiple?i?new Set(o):o:o[0]),e._assigning=!0,Oo(()=>{e._assigning=!1})}),e[pe]=mn(s)},mounted(e,{value:t}){Wl(e,t)},beforeUpdate(e,t,n){e[pe]=mn(n)},updated(e,{value:t}){e._assigning||Wl(e,t)}};function Wl(e,t){const n=e.multiple,s=q(t);if(!(n&&!s&&!gs(t))){for(let i=0,o=e.options.length;i<o;i++){const r=e.options[i],a=cs(r);if(n)if(s){const l=typeof a;l==="string"||l==="number"?r.selected=t.some(c=>String(c)===String(a)):r.selected=va(t,a)>-1}else r.selected=t.has(a);else if(jn(cs(r),t)){e.selectedIndex!==i&&(e.selectedIndex=i);return}}!n&&e.selectedIndex!==-1&&(e.selectedIndex=-1)}}function cs(e){return"_value"in e?e._value:e.value}function ch(e,t){const n=t?"_trueValue":"_falseValue";return n in e?e[n]:t}const Mm=["ctrl","shift","alt","meta"],Cm={stop:e=>e.stopPropagation(),prevent:e=>e.preventDefault(),self:e=>e.target!==e.currentTarget,ctrl:e=>!e.ctrlKey,shift:e=>!e.shiftKey,alt:e=>!e.altKey,meta:e=>!e.metaKey,left:e=>"button"in e&&e.button!==0,middle:e=>"button"in e&&e.button!==1,right:e=>"button"in e&&e.button!==2,exact:(e,t)=>Mm.some(n=>e[`${n}Key`]&&!t.includes(n))},LS=(e,t)=>{const n=e._withMods||(e._withMods={}),s=t.join(".");return n[s]||(n[s]=(i,...o)=>{for(let r=0;r<t.length;r++){const a=Cm[t[r]];if(a&&a(i,t))return}return e(i,...o)})},Pm={esc:"escape",space:" ",up:"arrow-up",left:"arrow-left",right:"arrow-right",down:"arrow-down",delete:"backspace"},FS=(e,t)=>{const n=e._withKeys||(e._withKeys={}),s=t.join(".");return n[s]||(n[s]=i=>{if(!("key"in i))return;const o=_n(i.key);if(t.some(r=>r===o||Pm[r]===o))return e(i)})},Em=Dt({patchProp:gm},Jg);let $l;function km(){return $l||($l=vg(Em))}const IS=(...e)=>{const t=km().createApp(...e),{mount:n}=t;return t.mount=s=>{const i=Om(s);if(!i)return;const o=t._component;!tt(o)&&!o.render&&!o.template&&(o.template=i.innerHTML),i.nodeType===1&&(i.textContent="");const r=n(i,!1,Am(i));return i instanceof Element&&(i.removeAttribute("v-cloak"),i.setAttribute("data-v-app","")),r},t};function Am(e){if(e instanceof SVGElement)return"svg";if(typeof MathMLElement=="function"&&e instanceof MathMLElement)return"mathml"}function Om(e){return Mt(e)?document.querySelector(e):e}/*!
 * pinia v2.3.1
 * (c) 2025 Eduardo San Martin Morote
 * @license MIT
 */let uh;const Fo=e=>uh=e,fh=Symbol();function Vr(e){return e&&typeof e=="object"&&Object.prototype.toString.call(e)==="[object Object]"&&typeof e.toJSON!="function"}var Vs;(function(e){e.direct="direct",e.patchObject="patch object",e.patchFunction="patch function"})(Vs||(Vs={}));function NS(){const e=Zu(!0),t=e.run(()=>Oa({}));let n=[],s=[];const i=Aa({install(o){Fo(i),i._a=o,o.provide(fh,i),o.config.globalProperties.$pinia=i,s.forEach(r=>n.push(r)),s=[]},use(o){return this._a?n.push(o):s.push(o),this},_p:n,_a:null,_e:e,_s:new Map,state:t});return i}const hh=()=>{};function Ul(e,t,n,s=hh){e.push(t);const i=()=>{const o=e.indexOf(t);o>-1&&(e.splice(o,1),s())};return!n&&tf()&&fp(i),i}function Yn(e,...t){e.slice().forEach(n=>{n(...t)})}const Rm=e=>e(),Kl=Symbol(),lr=Symbol();function Wr(e,t){e instanceof Map&&t instanceof Map?t.forEach((n,s)=>e.set(s,n)):e instanceof Set&&t instanceof Set&&t.forEach(e.add,e);for(const n in t){if(!t.hasOwnProperty(n))continue;const s=t[n],i=e[n];Vr(i)&&Vr(s)&&e.hasOwnProperty(n)&&!kt(s)&&!hn(s)?e[n]=Wr(i,s):e[n]=s}return e}const Tm=Symbol();function Dm(e){return!Vr(e)||!e.hasOwnProperty(Tm)}const{assign:nn}=Object;function Lm(e){return!!(kt(e)&&e.effect)}function Fm(e,t,n,s){const{state:i,actions:o,getters:r}=t,a=n.state.value[e];let l;function c(){a||(n.state.value[e]=i?i():{});const u=Lp(n.state.value[e]);return nn(u,o,Object.keys(r||{}).reduce((f,h)=>(f[h]=Aa(fe(()=>{Fo(n);const d=n._s.get(e);return r[h].call(d,d)})),f),{}))}return l=dh(e,c,t,n,s,!0),l}function dh(e,t,n={},s,i,o){let r;const a=nn({actions:{}},n),l={deep:!0};let c,u,f=[],h=[],d;const p=s.state.value[e];!o&&!p&&(s.state.value[e]={}),Oa({});let g;function m(E){let k;c=u=!1,typeof E=="function"?(E(s.state.value[e]),k={type:Vs.patchFunction,storeId:e,events:d}):(Wr(s.state.value[e],E),k={type:Vs.patchObject,payload:E,storeId:e,events:d});const I=g=Symbol();Oo().then(()=>{g===I&&(c=!0)}),u=!0,Yn(f,k,s.state.value[e])}const y=o?function(){const{state:k}=n,I=k?k():{};this.$patch(H=>{nn(H,I)})}:hh;function b(){r.stop(),f=[],h=[],s._s.delete(e)}const w=(E,k="")=>{if(Kl in E)return E[lr]=k,E;const I=function(){Fo(s);const H=Array.from(arguments),L=[],X=[];function rt(U){L.push(U)}function Z(U){X.push(U)}Yn(h,{args:H,name:I[lr],store:S,after:rt,onError:Z});let G;try{G=E.apply(this&&this.$id===e?this:S,H)}catch(U){throw Yn(X,U),U}return G instanceof Promise?G.then(U=>(Yn(L,U),U)).catch(U=>(Yn(X,U),Promise.reject(U))):(Yn(L,G),G)};return I[Kl]=!0,I[lr]=k,I},v={_p:s,$id:e,$onAction:Ul.bind(null,h),$patch:m,$reset:y,$subscribe(E,k={}){const I=Ul(f,E,k.detached,()=>H()),H=r.run(()=>is(()=>s.state.value[e],L=>{(k.flush==="sync"?u:c)&&E({storeId:e,type:Vs.direct,events:d},L)},nn({},l,k)));return I},$dispose:b},S=hi(v);s._s.set(e,S);const P=(s._a&&s._a.runWithContext||Rm)(()=>s._e.run(()=>(r=Zu()).run(()=>t({action:w}))));for(const E in P){const k=P[E];if(kt(k)&&!Lm(k)||hn(k))o||(p&&Dm(k)&&(kt(k)?k.value=p[E]:Wr(k,p[E])),s.state.value[e][E]=k);else if(typeof k=="function"){const I=w(k,E);P[E]=I,a.actions[E]=k}}return nn(S,P),nn(lt(S),P),Object.defineProperty(S,"$state",{get:()=>s.state.value[e],set:E=>{m(k=>{nn(k,E)})}}),s._p.forEach(E=>{nn(S,r.run(()=>E({store:S,app:s._a,pinia:s,options:a})))}),p&&o&&n.hydrate&&n.hydrate(S.$state,p),c=!0,u=!0,S}/*! #__NO_SIDE_EFFECTS__ */function BS(e,t,n){let s,i;const o=typeof t=="function";typeof e=="string"?(s=e,i=o?n:t):(i=e,s=e.id);function r(a,l){const c=pg();return a=a||(c?de(fh,null):null),a&&Fo(a),a=uh,a._s.has(s)||(o?dh(s,t,i,a):Fm(s,i,a)),a._s.get(s)}return r.$id=s,r}/*!
  * vue-router v4.5.1
  * (c) 2025 Eduardo San Martin Morote
  * @license MIT
  */const Qn=typeof document<"u";function ph(e){return typeof e=="object"||"displayName"in e||"props"in e||"__vccOpts"in e}function Im(e){return e.__esModule||e[Symbol.toStringTag]==="Module"||e.default&&ph(e.default)}const ct=Object.assign;function cr(e,t){const n={};for(const s in t){const i=t[s];n[s]=we(i)?i.map(e):e(i)}return n}const Ws=()=>{},we=Array.isArray,gh=/#/g,Nm=/&/g,Bm=/\//g,zm=/=/g,Hm=/\?/g,mh=/\+/g,jm=/%5B/g,Vm=/%5D/g,bh=/%5E/g,Wm=/%60/g,yh=/%7B/g,$m=/%7C/g,_h=/%7D/g,Um=/%20/g;function Ba(e){return encodeURI(""+e).replace($m,"|").replace(jm,"[").replace(Vm,"]")}function Km(e){return Ba(e).replace(yh,"{").replace(_h,"}").replace(bh,"^")}function $r(e){return Ba(e).replace(mh,"%2B").replace(Um,"+").replace(gh,"%23").replace(Nm,"%26").replace(Wm,"`").replace(yh,"{").replace(_h,"}").replace(bh,"^")}function qm(e){return $r(e).replace(zm,"%3D")}function Ym(e){return Ba(e).replace(gh,"%23").replace(Hm,"%3F")}function Xm(e){return e==null?"":Ym(e).replace(Bm,"%2F")}function ei(e){try{return decodeURIComponent(""+e)}catch{}return""+e}const Gm=/\/$/,Jm=e=>e.replace(Gm,"");function ur(e,t,n="/"){let s,i={},o="",r="";const a=t.indexOf("#");let l=t.indexOf("?");return a<l&&a>=0&&(l=-1),l>-1&&(s=t.slice(0,l),o=t.slice(l+1,a>-1?a:t.length),i=e(o)),a>-1&&(s=s||t.slice(0,a),r=t.slice(a,t.length)),s=eb(s??t,n),{fullPath:s+(o&&"?")+o+r,path:s,query:i,hash:ei(r)}}function Qm(e,t){const n=t.query?e(t.query):"";return t.path+(n&&"?")+n+(t.hash||"")}function ql(e,t){return!t||!e.toLowerCase().startsWith(t.toLowerCase())?e:e.slice(t.length)||"/"}function Zm(e,t,n){const s=t.matched.length-1,i=n.matched.length-1;return s>-1&&s===i&&us(t.matched[s],n.matched[i])&&xh(t.params,n.params)&&e(t.query)===e(n.query)&&t.hash===n.hash}function us(e,t){return(e.aliasOf||e)===(t.aliasOf||t)}function xh(e,t){if(Object.keys(e).length!==Object.keys(t).length)return!1;for(const n in e)if(!tb(e[n],t[n]))return!1;return!0}function tb(e,t){return we(e)?Yl(e,t):we(t)?Yl(t,e):e===t}function Yl(e,t){return we(t)?e.length===t.length&&e.every((n,s)=>n===t[s]):e.length===1&&e[0]===t}function eb(e,t){if(e.startsWith("/"))return e;if(!e)return t;const n=t.split("/"),s=e.split("/"),i=s[s.length-1];(i===".."||i===".")&&s.push("");let o=n.length-1,r,a;for(r=0;r<s.length;r++)if(a=s[r],a!==".")if(a==="..")o>1&&o--;else break;return n.slice(0,o).join("/")+"/"+s.slice(r).join("/")}const tn={path:"/",name:void 0,params:{},query:{},hash:"",fullPath:"/",matched:[],meta:{},redirectedFrom:void 0};var ni;(function(e){e.pop="pop",e.push="push"})(ni||(ni={}));var $s;(function(e){e.back="back",e.forward="forward",e.unknown=""})($s||($s={}));function nb(e){if(!e)if(Qn){const t=document.querySelector("base");e=t&&t.getAttribute("href")||"/",e=e.replace(/^\w+:\/\/[^\/]+/,"")}else e="/";return e[0]!=="/"&&e[0]!=="#"&&(e="/"+e),Jm(e)}const sb=/^[^#]+#/;function ib(e,t){return e.replace(sb,"#")+t}function ob(e,t){const n=document.documentElement.getBoundingClientRect(),s=e.getBoundingClientRect();return{behavior:t.behavior,left:s.left-n.left-(t.left||0),top:s.top-n.top-(t.top||0)}}const Io=()=>({left:window.scrollX,top:window.scrollY});function rb(e){let t;if("el"in e){const n=e.el,s=typeof n=="string"&&n.startsWith("#"),i=typeof n=="string"?s?document.getElementById(n.slice(1)):document.querySelector(n):n;if(!i)return;t=ob(i,e)}else t=e;"scrollBehavior"in document.documentElement.style?window.scrollTo(t):window.scrollTo(t.left!=null?t.left:window.scrollX,t.top!=null?t.top:window.scrollY)}function Xl(e,t){return(history.state?history.state.position-t:-1)+e}const Ur=new Map;function ab(e,t){Ur.set(e,t)}function lb(e){const t=Ur.get(e);return Ur.delete(e),t}let cb=()=>location.protocol+"//"+location.host;function vh(e,t){const{pathname:n,search:s,hash:i}=t,o=e.indexOf("#");if(o>-1){let a=i.includes(e.slice(o))?e.slice(o).length:1,l=i.slice(a);return l[0]!=="/"&&(l="/"+l),ql(l,"")}return ql(n,e)+s+i}function ub(e,t,n,s){let i=[],o=[],r=null;const a=({state:h})=>{const d=vh(e,location),p=n.value,g=t.value;let m=0;if(h){if(n.value=d,t.value=h,r&&r===p){r=null;return}m=g?h.position-g.position:0}else s(d);i.forEach(y=>{y(n.value,p,{delta:m,type:ni.pop,direction:m?m>0?$s.forward:$s.back:$s.unknown})})};function l(){r=n.value}function c(h){i.push(h);const d=()=>{const p=i.indexOf(h);p>-1&&i.splice(p,1)};return o.push(d),d}function u(){const{history:h}=window;h.state&&h.replaceState(ct({},h.state,{scroll:Io()}),"")}function f(){for(const h of o)h();o=[],window.removeEventListener("popstate",a),window.removeEventListener("beforeunload",u)}return window.addEventListener("popstate",a),window.addEventListener("beforeunload",u,{passive:!0}),{pauseListeners:l,listen:c,destroy:f}}function Gl(e,t,n,s=!1,i=!1){return{back:e,current:t,forward:n,replaced:s,position:window.history.length,scroll:i?Io():null}}function fb(e){const{history:t,location:n}=window,s={value:vh(e,n)},i={value:t.state};i.value||o(s.value,{back:null,current:s.value,forward:null,position:t.length-1,replaced:!0,scroll:null},!0);function o(l,c,u){const f=e.indexOf("#"),h=f>-1?(n.host&&document.querySelector("base")?e:e.slice(f))+l:cb()+e+l;try{t[u?"replaceState":"pushState"](c,"",h),i.value=c}catch(d){console.error(d),n[u?"replace":"assign"](h)}}function r(l,c){const u=ct({},t.state,Gl(i.value.back,l,i.value.forward,!0),c,{position:i.value.position});o(l,u,!0),s.value=l}function a(l,c){const u=ct({},i.value,t.state,{forward:l,scroll:Io()});o(u.current,u,!0);const f=ct({},Gl(s.value,l,null),{position:u.position+1},c);o(l,f,!1),s.value=l}return{location:s,state:i,push:a,replace:r}}function zS(e){e=nb(e);const t=fb(e),n=ub(e,t.state,t.location,t.replace);function s(o,r=!0){r||n.pauseListeners(),history.go(o)}const i=ct({location:"",base:e,go:s,createHref:ib.bind(null,e)},t,n);return Object.defineProperty(i,"location",{enumerable:!0,get:()=>t.location.value}),Object.defineProperty(i,"state",{enumerable:!0,get:()=>t.state.value}),i}function hb(e){return typeof e=="string"||e&&typeof e=="object"}function wh(e){return typeof e=="string"||typeof e=="symbol"}const Sh=Symbol("");var Jl;(function(e){e[e.aborted=4]="aborted",e[e.cancelled=8]="cancelled",e[e.duplicated=16]="duplicated"})(Jl||(Jl={}));function fs(e,t){return ct(new Error,{type:e,[Sh]:!0},t)}function Ne(e,t){return e instanceof Error&&Sh in e&&(t==null||!!(e.type&t))}const Ql="[^/]+?",db={sensitive:!1,strict:!1,start:!0,end:!0},pb=/[.+*?^${}()[\]/\\]/g;function gb(e,t){const n=ct({},db,t),s=[];let i=n.start?"^":"";const o=[];for(const c of e){const u=c.length?[]:[90];n.strict&&!c.length&&(i+="/");for(let f=0;f<c.length;f++){const h=c[f];let d=40+(n.sensitive?.25:0);if(h.type===0)f||(i+="/"),i+=h.value.replace(pb,"\\$&"),d+=40;else if(h.type===1){const{value:p,repeatable:g,optional:m,regexp:y}=h;o.push({name:p,repeatable:g,optional:m});const b=y||Ql;if(b!==Ql){d+=10;try{new RegExp(`(${b})`)}catch(v){throw new Error(`Invalid custom RegExp for param "${p}" (${b}): `+v.message)}}let w=g?`((?:${b})(?:/(?:${b}))*)`:`(${b})`;f||(w=m&&c.length<2?`(?:/${w})`:"/"+w),m&&(w+="?"),i+=w,d+=20,m&&(d+=-8),g&&(d+=-20),b===".*"&&(d+=-50)}u.push(d)}s.push(u)}if(n.strict&&n.end){const c=s.length-1;s[c][s[c].length-1]+=.7000000000000001}n.strict||(i+="/?"),n.end?i+="$":n.strict&&!i.endsWith("/")&&(i+="(?:/|$)");const r=new RegExp(i,n.sensitive?"":"i");function a(c){const u=c.match(r),f={};if(!u)return null;for(let h=1;h<u.length;h++){const d=u[h]||"",p=o[h-1];f[p.name]=d&&p.repeatable?d.split("/"):d}return f}function l(c){let u="",f=!1;for(const h of e){(!f||!u.endsWith("/"))&&(u+="/"),f=!1;for(const d of h)if(d.type===0)u+=d.value;else if(d.type===1){const{value:p,repeatable:g,optional:m}=d,y=p in c?c[p]:"";if(we(y)&&!g)throw new Error(`Provided param "${p}" is an array but it is not repeatable (* or + modifiers)`);const b=we(y)?y.join("/"):y;if(!b)if(m)h.length<2&&(u.endsWith("/")?u=u.slice(0,-1):f=!0);else throw new Error(`Missing required param "${p}"`);u+=b}}return u||"/"}return{re:r,score:s,keys:o,parse:a,stringify:l}}function mb(e,t){let n=0;for(;n<e.length&&n<t.length;){const s=t[n]-e[n];if(s)return s;n++}return e.length<t.length?e.length===1&&e[0]===80?-1:1:e.length>t.length?t.length===1&&t[0]===80?1:-1:0}function Mh(e,t){let n=0;const s=e.score,i=t.score;for(;n<s.length&&n<i.length;){const o=mb(s[n],i[n]);if(o)return o;n++}if(Math.abs(i.length-s.length)===1){if(Zl(s))return 1;if(Zl(i))return-1}return i.length-s.length}function Zl(e){const t=e[e.length-1];return e.length>0&&t[t.length-1]<0}const bb={type:0,value:""},yb=/[a-zA-Z0-9_]/;function _b(e){if(!e)return[[]];if(e==="/")return[[bb]];if(!e.startsWith("/"))throw new Error(`Invalid path "${e}"`);function t(d){throw new Error(`ERR (${n})/"${c}": ${d}`)}let n=0,s=n;const i=[];let o;function r(){o&&i.push(o),o=[]}let a=0,l,c="",u="";function f(){c&&(n===0?o.push({type:0,value:c}):n===1||n===2||n===3?(o.length>1&&(l==="*"||l==="+")&&t(`A repeatable param (${c}) must be alone in its segment. eg: '/:ids+.`),o.push({type:1,value:c,regexp:u,repeatable:l==="*"||l==="+",optional:l==="*"||l==="?"})):t("Invalid state to consume buffer"),c="")}function h(){c+=l}for(;a<e.length;){if(l=e[a++],l==="\\"&&n!==2){s=n,n=4;continue}switch(n){case 0:l==="/"?(c&&f(),r()):l===":"?(f(),n=1):h();break;case 4:h(),n=s;break;case 1:l==="("?n=2:yb.test(l)?h():(f(),n=0,l!=="*"&&l!=="?"&&l!=="+"&&a--);break;case 2:l===")"?u[u.length-1]=="\\"?u=u.slice(0,-1)+l:n=3:u+=l;break;case 3:f(),n=0,l!=="*"&&l!=="?"&&l!=="+"&&a--,u="";break;default:t("Unknown state");break}}return n===2&&t(`Unfinished custom RegExp for param "${c}"`),f(),r(),i}function xb(e,t,n){const s=gb(_b(e.path),n),i=ct(s,{record:e,parent:t,children:[],alias:[]});return t&&!i.record.aliasOf==!t.record.aliasOf&&t.children.push(i),i}function vb(e,t){const n=[],s=new Map;t=sc({strict:!1,end:!0,sensitive:!1},t);function i(f){return s.get(f)}function o(f,h,d){const p=!d,g=ec(f);g.aliasOf=d&&d.record;const m=sc(t,f),y=[g];if("alias"in f){const v=typeof f.alias=="string"?[f.alias]:f.alias;for(const S of v)y.push(ec(ct({},g,{components:d?d.record.components:g.components,path:S,aliasOf:d?d.record:g})))}let b,w;for(const v of y){const{path:S}=v;if(h&&S[0]!=="/"){const A=h.record.path,P=A[A.length-1]==="/"?"":"/";v.path=h.record.path+(S&&P+S)}if(b=xb(v,h,m),d?d.alias.push(b):(w=w||b,w!==b&&w.alias.push(b),p&&f.name&&!nc(b)&&r(f.name)),Ch(b)&&l(b),g.children){const A=g.children;for(let P=0;P<A.length;P++)o(A[P],b,d&&d.children[P])}d=d||b}return w?()=>{r(w)}:Ws}function r(f){if(wh(f)){const h=s.get(f);h&&(s.delete(f),n.splice(n.indexOf(h),1),h.children.forEach(r),h.alias.forEach(r))}else{const h=n.indexOf(f);h>-1&&(n.splice(h,1),f.record.name&&s.delete(f.record.name),f.children.forEach(r),f.alias.forEach(r))}}function a(){return n}function l(f){const h=Mb(f,n);n.splice(h,0,f),f.record.name&&!nc(f)&&s.set(f.record.name,f)}function c(f,h){let d,p={},g,m;if("name"in f&&f.name){if(d=s.get(f.name),!d)throw fs(1,{location:f});m=d.record.name,p=ct(tc(h.params,d.keys.filter(w=>!w.optional).concat(d.parent?d.parent.keys.filter(w=>w.optional):[]).map(w=>w.name)),f.params&&tc(f.params,d.keys.map(w=>w.name))),g=d.stringify(p)}else if(f.path!=null)g=f.path,d=n.find(w=>w.re.test(g)),d&&(p=d.parse(g),m=d.record.name);else{if(d=h.name?s.get(h.name):n.find(w=>w.re.test(h.path)),!d)throw fs(1,{location:f,currentLocation:h});m=d.record.name,p=ct({},h.params,f.params),g=d.stringify(p)}const y=[];let b=d;for(;b;)y.unshift(b.record),b=b.parent;return{name:m,path:g,params:p,matched:y,meta:Sb(y)}}e.forEach(f=>o(f));function u(){n.length=0,s.clear()}return{addRoute:o,resolve:c,removeRoute:r,clearRoutes:u,getRoutes:a,getRecordMatcher:i}}function tc(e,t){const n={};for(const s of t)s in e&&(n[s]=e[s]);return n}function ec(e){const t={path:e.path,redirect:e.redirect,name:e.name,meta:e.meta||{},aliasOf:e.aliasOf,beforeEnter:e.beforeEnter,props:wb(e),children:e.children||[],instances:{},leaveGuards:new Set,updateGuards:new Set,enterCallbacks:{},components:"components"in e?e.components||null:e.component&&{default:e.component}};return Object.defineProperty(t,"mods",{value:{}}),t}function wb(e){const t={},n=e.props||!1;if("component"in e)t.default=n;else for(const s in e.components)t[s]=typeof n=="object"?n[s]:n;return t}function nc(e){for(;e;){if(e.record.aliasOf)return!0;e=e.parent}return!1}function Sb(e){return e.reduce((t,n)=>ct(t,n.meta),{})}function sc(e,t){const n={};for(const s in e)n[s]=s in t?t[s]:e[s];return n}function Mb(e,t){let n=0,s=t.length;for(;n!==s;){const o=n+s>>1;Mh(e,t[o])<0?s=o:n=o+1}const i=Cb(e);return i&&(s=t.lastIndexOf(i,s-1)),s}function Cb(e){let t=e;for(;t=t.parent;)if(Ch(t)&&Mh(e,t)===0)return t}function Ch({record:e}){return!!(e.name||e.components&&Object.keys(e.components).length||e.redirect)}function Pb(e){const t={};if(e===""||e==="?")return t;const s=(e[0]==="?"?e.slice(1):e).split("&");for(let i=0;i<s.length;++i){const o=s[i].replace(mh," "),r=o.indexOf("="),a=ei(r<0?o:o.slice(0,r)),l=r<0?null:ei(o.slice(r+1));if(a in t){let c=t[a];we(c)||(c=t[a]=[c]),c.push(l)}else t[a]=l}return t}function ic(e){let t="";for(let n in e){const s=e[n];if(n=qm(n),s==null){s!==void 0&&(t+=(t.length?"&":"")+n);continue}(we(s)?s.map(o=>o&&$r(o)):[s&&$r(s)]).forEach(o=>{o!==void 0&&(t+=(t.length?"&":"")+n,o!=null&&(t+="="+o))})}return t}function Eb(e){const t={};for(const n in e){const s=e[n];s!==void 0&&(t[n]=we(s)?s.map(i=>i==null?null:""+i):s==null?s:""+s)}return t}const kb=Symbol(""),oc=Symbol(""),No=Symbol(""),za=Symbol(""),Kr=Symbol("");function ws(){let e=[];function t(s){return e.push(s),()=>{const i=e.indexOf(s);i>-1&&e.splice(i,1)}}function n(){e=[]}return{add:t,list:()=>e.slice(),reset:n}}function rn(e,t,n,s,i,o=r=>r()){const r=s&&(s.enterCallbacks[i]=s.enterCallbacks[i]||[]);return()=>new Promise((a,l)=>{const c=h=>{h===!1?l(fs(4,{from:n,to:t})):h instanceof Error?l(h):hb(h)?l(fs(2,{from:t,to:h})):(r&&s.enterCallbacks[i]===r&&typeof h=="function"&&r.push(h),a())},u=o(()=>e.call(s&&s.instances[i],t,n,c));let f=Promise.resolve(u);e.length<3&&(f=f.then(c)),f.catch(h=>l(h))})}function fr(e,t,n,s,i=o=>o()){const o=[];for(const r of e)for(const a in r.components){let l=r.components[a];if(!(t!=="beforeRouteEnter"&&!r.instances[a]))if(ph(l)){const u=(l.__vccOpts||l)[t];u&&o.push(rn(u,n,s,r,a,i))}else{let c=l();o.push(()=>c.then(u=>{if(!u)throw new Error(`Couldn't resolve component "${a}" at "${r.path}"`);const f=Im(u)?u.default:u;r.mods[a]=u,r.components[a]=f;const d=(f.__vccOpts||f)[t];return d&&rn(d,n,s,r,a,i)()}))}}return o}function rc(e){const t=de(No),n=de(za),s=fe(()=>{const l=ns(e.to);return t.resolve(l)}),i=fe(()=>{const{matched:l}=s.value,{length:c}=l,u=l[c-1],f=n.matched;if(!u||!f.length)return-1;const h=f.findIndex(us.bind(null,u));if(h>-1)return h;const d=ac(l[c-2]);return c>1&&ac(u)===d&&f[f.length-1].path!==d?f.findIndex(us.bind(null,l[c-2])):h}),o=fe(()=>i.value>-1&&Db(n.params,s.value.params)),r=fe(()=>i.value>-1&&i.value===n.matched.length-1&&xh(n.params,s.value.params));function a(l={}){if(Tb(l)){const c=t[ns(e.replace)?"replace":"push"](ns(e.to)).catch(Ws);return e.viewTransition&&typeof document<"u"&&"startViewTransition"in document&&document.startViewTransition(()=>c),c}return Promise.resolve()}return{route:s,href:fe(()=>s.value.href),isActive:o,isExactActive:r,navigate:a}}function Ab(e){return e.length===1?e[0]:e}const Ob=Pf({name:"RouterLink",compatConfig:{MODE:3},props:{to:{type:[String,Object],required:!0},replace:Boolean,activeClass:String,exactActiveClass:String,custom:Boolean,ariaCurrentValue:{type:String,default:"page"},viewTransition:Boolean},useLink:rc,setup(e,{slots:t}){const n=hi(rc(e)),{options:s}=de(No),i=fe(()=>({[lc(e.activeClass,s.linkActiveClass,"router-link-active")]:n.isActive,[lc(e.exactActiveClass,s.linkExactActiveClass,"router-link-exact-active")]:n.isExactActive}));return()=>{const o=t.default&&Ab(t.default(n));return e.custom?o:sh("a",{"aria-current":n.isExactActive?e.ariaCurrentValue:null,href:n.href,onClick:n.navigate,class:i.value},o)}}}),Rb=Ob;function Tb(e){if(!(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)&&!e.defaultPrevented&&!(e.button!==void 0&&e.button!==0)){if(e.currentTarget&&e.currentTarget.getAttribute){const t=e.currentTarget.getAttribute("target");if(/\b_blank\b/i.test(t))return}return e.preventDefault&&e.preventDefault(),!0}}function Db(e,t){for(const n in t){const s=t[n],i=e[n];if(typeof s=="string"){if(s!==i)return!1}else if(!we(i)||i.length!==s.length||s.some((o,r)=>o!==i[r]))return!1}return!0}function ac(e){return e?e.aliasOf?e.aliasOf.path:e.path:""}const lc=(e,t,n)=>e??t??n,Lb=Pf({name:"RouterView",inheritAttrs:!1,props:{name:{type:String,default:"default"},route:Object},compatConfig:{MODE:3},setup(e,{attrs:t,slots:n}){const s=de(Kr),i=fe(()=>e.route||s.value),o=de(oc,0),r=fe(()=>{let c=ns(o);const{matched:u}=i.value;let f;for(;(f=u[c])&&!f.components;)c++;return c}),a=fe(()=>i.value.matched[r.value]);zi(oc,fe(()=>r.value+1)),zi(kb,a),zi(Kr,i);const l=Oa();return is(()=>[l.value,a.value,e.name],([c,u,f],[h,d,p])=>{u&&(u.instances[f]=c,d&&d!==u&&c&&c===h&&(u.leaveGuards.size||(u.leaveGuards=d.leaveGuards),u.updateGuards.size||(u.updateGuards=d.updateGuards))),c&&u&&(!d||!us(u,d)||!h)&&(u.enterCallbacks[f]||[]).forEach(g=>g(c))},{flush:"post"}),()=>{const c=i.value,u=e.name,f=a.value,h=f&&f.components[u];if(!h)return cc(n.default,{Component:h,route:c});const d=f.props[u],p=d?d===!0?c.params:typeof d=="function"?d(c):d:null,m=sh(h,ct({},p,t,{onVnodeUnmounted:y=>{y.component.isUnmounted&&(f.instances[u]=null)},ref:l}));return cc(n.default,{Component:m,route:c})||m}}});function cc(e,t){if(!e)return null;const n=e(t);return n.length===1?n[0]:n}const Fb=Lb;function HS(e){const t=vb(e.routes,e),n=e.parseQuery||Pb,s=e.stringifyQuery||ic,i=e.history,o=ws(),r=ws(),a=ws(),l=Rp(tn);let c=tn;Qn&&e.scrollBehavior&&"scrollRestoration"in history&&(history.scrollRestoration="manual");const u=cr.bind(null,R=>""+R),f=cr.bind(null,Xm),h=cr.bind(null,ei);function d(R,$){let V,K;return wh(R)?(V=t.getRecordMatcher(R),K=$):K=R,t.addRoute(K,V)}function p(R){const $=t.getRecordMatcher(R);$&&t.removeRoute($)}function g(){return t.getRoutes().map(R=>R.record)}function m(R){return!!t.getRecordMatcher(R)}function y(R,$){if($=ct({},$||l.value),typeof R=="string"){const M=ur(n,R,$.path),O=t.resolve({path:M.path},$),D=i.createHref(M.fullPath);return ct(M,O,{params:h(O.params),hash:ei(M.hash),redirectedFrom:void 0,href:D})}let V;if(R.path!=null)V=ct({},R,{path:ur(n,R.path,$.path).path});else{const M=ct({},R.params);for(const O in M)M[O]==null&&delete M[O];V=ct({},R,{params:f(M)}),$.params=f($.params)}const K=t.resolve(V,$),dt=R.hash||"";K.params=u(h(K.params));const _=Qm(s,ct({},R,{hash:Km(dt),path:K.path})),x=i.createHref(_);return ct({fullPath:_,hash:dt,query:s===ic?Eb(R.query):R.query||{}},K,{redirectedFrom:void 0,href:x})}function b(R){return typeof R=="string"?ur(n,R,l.value.path):ct({},R)}function w(R,$){if(c!==R)return fs(8,{from:$,to:R})}function v(R){return P(R)}function S(R){return v(ct(b(R),{replace:!0}))}function A(R){const $=R.matched[R.matched.length-1];if($&&$.redirect){const{redirect:V}=$;let K=typeof V=="function"?V(R):V;return typeof K=="string"&&(K=K.includes("?")||K.includes("#")?K=b(K):{path:K},K.params={}),ct({query:R.query,hash:R.hash,params:K.path!=null?{}:R.params},K)}}function P(R,$){const V=c=y(R),K=l.value,dt=R.state,_=R.force,x=R.replace===!0,M=A(V);if(M)return P(ct(b(M),{state:typeof M=="object"?ct({},dt,M.state):dt,force:_,replace:x}),$||V);const O=V;O.redirectedFrom=$;let D;return!_&&Zm(s,K,V)&&(D=fs(16,{to:O,from:K}),Kt(K,K,!0,!1)),(D?Promise.resolve(D):I(O,K)).catch(T=>Ne(T)?Ne(T,2)?T:Ut(T):et(T,O,K)).then(T=>{if(T){if(Ne(T,2))return P(ct({replace:x},b(T.to),{state:typeof T.to=="object"?ct({},dt,T.to.state):dt,force:_}),$||O)}else T=L(O,K,!0,x,dt);return H(O,K,T),T})}function E(R,$){const V=w(R,$);return V?Promise.reject(V):Promise.resolve()}function k(R){const $=Xt.values().next().value;return $&&typeof $.runWithContext=="function"?$.runWithContext(R):R()}function I(R,$){let V;const[K,dt,_]=Ib(R,$);V=fr(K.reverse(),"beforeRouteLeave",R,$);for(const M of K)M.leaveGuards.forEach(O=>{V.push(rn(O,R,$))});const x=E.bind(null,R,$);return V.push(x),At(V).then(()=>{V=[];for(const M of o.list())V.push(rn(M,R,$));return V.push(x),At(V)}).then(()=>{V=fr(dt,"beforeRouteUpdate",R,$);for(const M of dt)M.updateGuards.forEach(O=>{V.push(rn(O,R,$))});return V.push(x),At(V)}).then(()=>{V=[];for(const M of _)if(M.beforeEnter)if(we(M.beforeEnter))for(const O of M.beforeEnter)V.push(rn(O,R,$));else V.push(rn(M.beforeEnter,R,$));return V.push(x),At(V)}).then(()=>(R.matched.forEach(M=>M.enterCallbacks={}),V=fr(_,"beforeRouteEnter",R,$,k),V.push(x),At(V))).then(()=>{V=[];for(const M of r.list())V.push(rn(M,R,$));return V.push(x),At(V)}).catch(M=>Ne(M,8)?M:Promise.reject(M))}function H(R,$,V){a.list().forEach(K=>k(()=>K(R,$,V)))}function L(R,$,V,K,dt){const _=w(R,$);if(_)return _;const x=$===tn,M=Qn?history.state:{};V&&(K||x?i.replace(R.fullPath,ct({scroll:x&&M&&M.scroll},dt)):i.push(R.fullPath,dt)),l.value=R,Kt(R,$,V,x),Ut()}let X;function rt(){X||(X=i.listen((R,$,V)=>{if(!Me.listening)return;const K=y(R),dt=A(K);if(dt){P(ct(dt,{replace:!0,force:!0}),K).catch(Ws);return}c=K;const _=l.value;Qn&&ab(Xl(_.fullPath,V.delta),Io()),I(K,_).catch(x=>Ne(x,12)?x:Ne(x,2)?(P(ct(b(x.to),{force:!0}),K).then(M=>{Ne(M,20)&&!V.delta&&V.type===ni.pop&&i.go(-1,!1)}).catch(Ws),Promise.reject()):(V.delta&&i.go(-V.delta,!1),et(x,K,_))).then(x=>{x=x||L(K,_,!1),x&&(V.delta&&!Ne(x,8)?i.go(-V.delta,!1):V.type===ni.pop&&Ne(x,20)&&i.go(-1,!1)),H(K,_,x)}).catch(Ws)}))}let Z=ws(),G=ws(),U;function et(R,$,V){Ut(R);const K=G.list();return K.length?K.forEach(dt=>dt(R,$,V)):console.error(R),Promise.reject(R)}function mt(){return U&&l.value!==tn?Promise.resolve():new Promise((R,$)=>{Z.add([R,$])})}function Ut(R){return U||(U=!R,rt(),Z.list().forEach(([$,V])=>R?V(R):$()),Z.reset()),R}function Kt(R,$,V,K){const{scrollBehavior:dt}=e;if(!Qn||!dt)return Promise.resolve();const _=!V&&lb(Xl(R.fullPath,0))||(K||!V)&&history.state&&history.state.scroll||null;return Oo().then(()=>dt(R,$,_)).then(x=>x&&rb(x)).catch(x=>et(x,R,$))}const Pt=R=>i.go(R);let ae;const Xt=new Set,Me={currentRoute:l,listening:!0,addRoute:d,removeRoute:p,clearRoutes:t.clearRoutes,hasRoute:m,getRoutes:g,resolve:y,options:e,push:v,replace:S,go:Pt,back:()=>Pt(-1),forward:()=>Pt(1),beforeEach:o.add,beforeResolve:r.add,afterEach:a.add,onError:G.add,isReady:mt,install(R){const $=this;R.component("RouterLink",Rb),R.component("RouterView",Fb),R.config.globalProperties.$router=$,Object.defineProperty(R.config.globalProperties,"$route",{enumerable:!0,get:()=>ns(l)}),Qn&&!ae&&l.value===tn&&(ae=!0,v(i.location).catch(dt=>{}));const V={};for(const dt in tn)Object.defineProperty(V,dt,{get:()=>l.value[dt],enumerable:!0});R.provide(No,$),R.provide(za,mf(V)),R.provide(Kr,l);const K=R.unmount;Xt.add(R),R.unmount=function(){Xt.delete(R),Xt.size<1&&(c=tn,X&&X(),X=null,l.value=tn,ae=!1,U=!1),K()}}};function At(R){return R.reduce(($,V)=>$.then(()=>k(V)),Promise.resolve())}return Me}function Ib(e,t){const n=[],s=[],i=[],o=Math.max(t.matched.length,e.matched.length);for(let r=0;r<o;r++){const a=t.matched[r];a&&(e.matched.find(c=>us(c,a))?s.push(a):n.push(a));const l=e.matched[r];l&&(t.matched.find(c=>us(c,l))||i.push(l))}return[n,s,i]}function jS(){return de(No)}function VS(e){return de(za)}function Ph(e,t){return function(){return e.apply(t,arguments)}}const{toString:Nb}=Object.prototype,{getPrototypeOf:Ha}=Object,{iterator:Bo,toStringTag:Eh}=Symbol,zo=(e=>t=>{const n=Nb.call(t);return e[n]||(e[n]=n.slice(8,-1).toLowerCase())})(Object.create(null)),Se=e=>(e=e.toLowerCase(),t=>zo(t)===e),Ho=e=>t=>typeof t===e,{isArray:bs}=Array,si=Ho("undefined");function Bb(e){return e!==null&&!si(e)&&e.constructor!==null&&!si(e.constructor)&&te(e.constructor.isBuffer)&&e.constructor.isBuffer(e)}const kh=Se("ArrayBuffer");function zb(e){let t;return typeof ArrayBuffer<"u"&&ArrayBuffer.isView?t=ArrayBuffer.isView(e):t=e&&e.buffer&&kh(e.buffer),t}const Hb=Ho("string"),te=Ho("function"),Ah=Ho("number"),jo=e=>e!==null&&typeof e=="object",jb=e=>e===!0||e===!1,Wi=e=>{if(zo(e)!=="object")return!1;const t=Ha(e);return(t===null||t===Object.prototype||Object.getPrototypeOf(t)===null)&&!(Eh in e)&&!(Bo in e)},Vb=Se("Date"),Wb=Se("File"),$b=Se("Blob"),Ub=Se("FileList"),Kb=e=>jo(e)&&te(e.pipe),qb=e=>{let t;return e&&(typeof FormData=="function"&&e instanceof FormData||te(e.append)&&((t=zo(e))==="formdata"||t==="object"&&te(e.toString)&&e.toString()==="[object FormData]"))},Yb=Se("URLSearchParams"),[Xb,Gb,Jb,Qb]=["ReadableStream","Request","Response","Headers"].map(Se),Zb=e=>e.trim?e.trim():e.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,"");function gi(e,t,{allOwnKeys:n=!1}={}){if(e===null||typeof e>"u")return;let s,i;if(typeof e!="object"&&(e=[e]),bs(e))for(s=0,i=e.length;s<i;s++)t.call(null,e[s],s,e);else{const o=n?Object.getOwnPropertyNames(e):Object.keys(e),r=o.length;let a;for(s=0;s<r;s++)a=o[s],t.call(null,e[a],a,e)}}function Oh(e,t){t=t.toLowerCase();const n=Object.keys(e);let s=n.length,i;for(;s-- >0;)if(i=n[s],t===i.toLowerCase())return i;return null}const Dn=typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:global,Rh=e=>!si(e)&&e!==Dn;function qr(){const{caseless:e}=Rh(this)&&this||{},t={},n=(s,i)=>{const o=e&&Oh(t,i)||i;Wi(t[o])&&Wi(s)?t[o]=qr(t[o],s):Wi(s)?t[o]=qr({},s):bs(s)?t[o]=s.slice():t[o]=s};for(let s=0,i=arguments.length;s<i;s++)arguments[s]&&gi(arguments[s],n);return t}const ty=(e,t,n,{allOwnKeys:s}={})=>(gi(t,(i,o)=>{n&&te(i)?e[o]=Ph(i,n):e[o]=i},{allOwnKeys:s}),e),ey=e=>(e.charCodeAt(0)===65279&&(e=e.slice(1)),e),ny=(e,t,n,s)=>{e.prototype=Object.create(t.prototype,s),e.prototype.constructor=e,Object.defineProperty(e,"super",{value:t.prototype}),n&&Object.assign(e.prototype,n)},sy=(e,t,n,s)=>{let i,o,r;const a={};if(t=t||{},e==null)return t;do{for(i=Object.getOwnPropertyNames(e),o=i.length;o-- >0;)r=i[o],(!s||s(r,e,t))&&!a[r]&&(t[r]=e[r],a[r]=!0);e=n!==!1&&Ha(e)}while(e&&(!n||n(e,t))&&e!==Object.prototype);return t},iy=(e,t,n)=>{e=String(e),(n===void 0||n>e.length)&&(n=e.length),n-=t.length;const s=e.indexOf(t,n);return s!==-1&&s===n},oy=e=>{if(!e)return null;if(bs(e))return e;let t=e.length;if(!Ah(t))return null;const n=new Array(t);for(;t-- >0;)n[t]=e[t];return n},ry=(e=>t=>e&&t instanceof e)(typeof Uint8Array<"u"&&Ha(Uint8Array)),ay=(e,t)=>{const s=(e&&e[Bo]).call(e);let i;for(;(i=s.next())&&!i.done;){const o=i.value;t.call(e,o[0],o[1])}},ly=(e,t)=>{let n;const s=[];for(;(n=e.exec(t))!==null;)s.push(n);return s},cy=Se("HTMLFormElement"),uy=e=>e.toLowerCase().replace(/[-_\s]([a-z\d])(\w*)/g,function(n,s,i){return s.toUpperCase()+i}),uc=(({hasOwnProperty:e})=>(t,n)=>e.call(t,n))(Object.prototype),fy=Se("RegExp"),Th=(e,t)=>{const n=Object.getOwnPropertyDescriptors(e),s={};gi(n,(i,o)=>{let r;(r=t(i,o,e))!==!1&&(s[o]=r||i)}),Object.defineProperties(e,s)},hy=e=>{Th(e,(t,n)=>{if(te(e)&&["arguments","caller","callee"].indexOf(n)!==-1)return!1;const s=e[n];if(te(s)){if(t.enumerable=!1,"writable"in t){t.writable=!1;return}t.set||(t.set=()=>{throw Error("Can not rewrite read-only method '"+n+"'")})}})},dy=(e,t)=>{const n={},s=i=>{i.forEach(o=>{n[o]=!0})};return bs(e)?s(e):s(String(e).split(t)),n},py=()=>{},gy=(e,t)=>e!=null&&Number.isFinite(e=+e)?e:t;function my(e){return!!(e&&te(e.append)&&e[Eh]==="FormData"&&e[Bo])}const by=e=>{const t=new Array(10),n=(s,i)=>{if(jo(s)){if(t.indexOf(s)>=0)return;if(!("toJSON"in s)){t[i]=s;const o=bs(s)?[]:{};return gi(s,(r,a)=>{const l=n(r,i+1);!si(l)&&(o[a]=l)}),t[i]=void 0,o}}return s};return n(e,0)},yy=Se("AsyncFunction"),_y=e=>e&&(jo(e)||te(e))&&te(e.then)&&te(e.catch),Dh=((e,t)=>e?setImmediate:t?((n,s)=>(Dn.addEventListener("message",({source:i,data:o})=>{i===Dn&&o===n&&s.length&&s.shift()()},!1),i=>{s.push(i),Dn.postMessage(n,"*")}))(`axios@${Math.random()}`,[]):n=>setTimeout(n))(typeof setImmediate=="function",te(Dn.postMessage)),xy=typeof queueMicrotask<"u"?queueMicrotask.bind(Dn):typeof process<"u"&&process.nextTick||Dh,vy=e=>e!=null&&te(e[Bo]),C={isArray:bs,isArrayBuffer:kh,isBuffer:Bb,isFormData:qb,isArrayBufferView:zb,isString:Hb,isNumber:Ah,isBoolean:jb,isObject:jo,isPlainObject:Wi,isReadableStream:Xb,isRequest:Gb,isResponse:Jb,isHeaders:Qb,isUndefined:si,isDate:Vb,isFile:Wb,isBlob:$b,isRegExp:fy,isFunction:te,isStream:Kb,isURLSearchParams:Yb,isTypedArray:ry,isFileList:Ub,forEach:gi,merge:qr,extend:ty,trim:Zb,stripBOM:ey,inherits:ny,toFlatObject:sy,kindOf:zo,kindOfTest:Se,endsWith:iy,toArray:oy,forEachEntry:ay,matchAll:ly,isHTMLForm:cy,hasOwnProperty:uc,hasOwnProp:uc,reduceDescriptors:Th,freezeMethods:hy,toObjectSet:dy,toCamelCase:uy,noop:py,toFiniteNumber:gy,findKey:Oh,global:Dn,isContextDefined:Rh,isSpecCompliantForm:my,toJSONObject:by,isAsyncFn:yy,isThenable:_y,setImmediate:Dh,asap:xy,isIterable:vy};function nt(e,t,n,s,i){Error.call(this),Error.captureStackTrace?Error.captureStackTrace(this,this.constructor):this.stack=new Error().stack,this.message=e,this.name="AxiosError",t&&(this.code=t),n&&(this.config=n),s&&(this.request=s),i&&(this.response=i,this.status=i.status?i.status:null)}C.inherits(nt,Error,{toJSON:function(){return{message:this.message,name:this.name,description:this.description,number:this.number,fileName:this.fileName,lineNumber:this.lineNumber,columnNumber:this.columnNumber,stack:this.stack,config:C.toJSONObject(this.config),code:this.code,status:this.status}}});const Lh=nt.prototype,Fh={};["ERR_BAD_OPTION_VALUE","ERR_BAD_OPTION","ECONNABORTED","ETIMEDOUT","ERR_NETWORK","ERR_FR_TOO_MANY_REDIRECTS","ERR_DEPRECATED","ERR_BAD_RESPONSE","ERR_BAD_REQUEST","ERR_CANCELED","ERR_NOT_SUPPORT","ERR_INVALID_URL"].forEach(e=>{Fh[e]={value:e}});Object.defineProperties(nt,Fh);Object.defineProperty(Lh,"isAxiosError",{value:!0});nt.from=(e,t,n,s,i,o)=>{const r=Object.create(Lh);return C.toFlatObject(e,r,function(l){return l!==Error.prototype},a=>a!=="isAxiosError"),nt.call(r,e.message,t,n,s,i),r.cause=e,r.name=e.name,o&&Object.assign(r,o),r};const wy=null;function Yr(e){return C.isPlainObject(e)||C.isArray(e)}function Ih(e){return C.endsWith(e,"[]")?e.slice(0,-2):e}function fc(e,t,n){return e?e.concat(t).map(function(i,o){return i=Ih(i),!n&&o?"["+i+"]":i}).join(n?".":""):t}function Sy(e){return C.isArray(e)&&!e.some(Yr)}const My=C.toFlatObject(C,{},null,function(t){return/^is[A-Z]/.test(t)});function Vo(e,t,n){if(!C.isObject(e))throw new TypeError("target must be an object");t=t||new FormData,n=C.toFlatObject(n,{metaTokens:!0,dots:!1,indexes:!1},!1,function(g,m){return!C.isUndefined(m[g])});const s=n.metaTokens,i=n.visitor||u,o=n.dots,r=n.indexes,l=(n.Blob||typeof Blob<"u"&&Blob)&&C.isSpecCompliantForm(t);if(!C.isFunction(i))throw new TypeError("visitor must be a function");function c(p){if(p===null)return"";if(C.isDate(p))return p.toISOString();if(!l&&C.isBlob(p))throw new nt("Blob is not supported. Use a Buffer instead.");return C.isArrayBuffer(p)||C.isTypedArray(p)?l&&typeof Blob=="function"?new Blob([p]):Buffer.from(p):p}function u(p,g,m){let y=p;if(p&&!m&&typeof p=="object"){if(C.endsWith(g,"{}"))g=s?g:g.slice(0,-2),p=JSON.stringify(p);else if(C.isArray(p)&&Sy(p)||(C.isFileList(p)||C.endsWith(g,"[]"))&&(y=C.toArray(p)))return g=Ih(g),y.forEach(function(w,v){!(C.isUndefined(w)||w===null)&&t.append(r===!0?fc([g],v,o):r===null?g:g+"[]",c(w))}),!1}return Yr(p)?!0:(t.append(fc(m,g,o),c(p)),!1)}const f=[],h=Object.assign(My,{defaultVisitor:u,convertValue:c,isVisitable:Yr});function d(p,g){if(!C.isUndefined(p)){if(f.indexOf(p)!==-1)throw Error("Circular reference detected in "+g.join("."));f.push(p),C.forEach(p,function(y,b){(!(C.isUndefined(y)||y===null)&&i.call(t,y,C.isString(b)?b.trim():b,g,h))===!0&&d(y,g?g.concat(b):[b])}),f.pop()}}if(!C.isObject(e))throw new TypeError("data must be an object");return d(e),t}function hc(e){const t={"!":"%21","'":"%27","(":"%28",")":"%29","~":"%7E","%20":"+","%00":"\0"};return encodeURIComponent(e).replace(/[!'()~]|%20|%00/g,function(s){return t[s]})}function ja(e,t){this._pairs=[],e&&Vo(e,this,t)}const Nh=ja.prototype;Nh.append=function(t,n){this._pairs.push([t,n])};Nh.toString=function(t){const n=t?function(s){return t.call(this,s,hc)}:hc;return this._pairs.map(function(i){return n(i[0])+"="+n(i[1])},"").join("&")};function Cy(e){return encodeURIComponent(e).replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}function Bh(e,t,n){if(!t)return e;const s=n&&n.encode||Cy;C.isFunction(n)&&(n={serialize:n});const i=n&&n.serialize;let o;if(i?o=i(t,n):o=C.isURLSearchParams(t)?t.toString():new ja(t,n).toString(s),o){const r=e.indexOf("#");r!==-1&&(e=e.slice(0,r)),e+=(e.indexOf("?")===-1?"?":"&")+o}return e}class dc{constructor(){this.handlers=[]}use(t,n,s){return this.handlers.push({fulfilled:t,rejected:n,synchronous:s?s.synchronous:!1,runWhen:s?s.runWhen:null}),this.handlers.length-1}eject(t){this.handlers[t]&&(this.handlers[t]=null)}clear(){this.handlers&&(this.handlers=[])}forEach(t){C.forEach(this.handlers,function(s){s!==null&&t(s)})}}const zh={silentJSONParsing:!0,forcedJSONParsing:!0,clarifyTimeoutError:!1},Py=typeof URLSearchParams<"u"?URLSearchParams:ja,Ey=typeof FormData<"u"?FormData:null,ky=typeof Blob<"u"?Blob:null,Ay={isBrowser:!0,classes:{URLSearchParams:Py,FormData:Ey,Blob:ky},protocols:["http","https","file","blob","url","data"]},Va=typeof window<"u"&&typeof document<"u",Xr=typeof navigator=="object"&&navigator||void 0,Oy=Va&&(!Xr||["ReactNative","NativeScript","NS"].indexOf(Xr.product)<0),Ry=typeof WorkerGlobalScope<"u"&&self instanceof WorkerGlobalScope&&typeof self.importScripts=="function",Ty=Va&&window.location.href||"http://localhost",Dy=Object.freeze(Object.defineProperty({__proto__:null,hasBrowserEnv:Va,hasStandardBrowserEnv:Oy,hasStandardBrowserWebWorkerEnv:Ry,navigator:Xr,origin:Ty},Symbol.toStringTag,{value:"Module"})),jt={...Dy,...Ay};function Ly(e,t){return Vo(e,new jt.classes.URLSearchParams,Object.assign({visitor:function(n,s,i,o){return jt.isNode&&C.isBuffer(n)?(this.append(s,n.toString("base64")),!1):o.defaultVisitor.apply(this,arguments)}},t))}function Fy(e){return C.matchAll(/\w+|\[(\w*)]/g,e).map(t=>t[0]==="[]"?"":t[1]||t[0])}function Iy(e){const t={},n=Object.keys(e);let s;const i=n.length;let o;for(s=0;s<i;s++)o=n[s],t[o]=e[o];return t}function Hh(e){function t(n,s,i,o){let r=n[o++];if(r==="__proto__")return!0;const a=Number.isFinite(+r),l=o>=n.length;return r=!r&&C.isArray(i)?i.length:r,l?(C.hasOwnProp(i,r)?i[r]=[i[r],s]:i[r]=s,!a):((!i[r]||!C.isObject(i[r]))&&(i[r]=[]),t(n,s,i[r],o)&&C.isArray(i[r])&&(i[r]=Iy(i[r])),!a)}if(C.isFormData(e)&&C.isFunction(e.entries)){const n={};return C.forEachEntry(e,(s,i)=>{t(Fy(s),i,n,0)}),n}return null}function Ny(e,t,n){if(C.isString(e))try{return(t||JSON.parse)(e),C.trim(e)}catch(s){if(s.name!=="SyntaxError")throw s}return(n||JSON.stringify)(e)}const mi={transitional:zh,adapter:["xhr","http","fetch"],transformRequest:[function(t,n){const s=n.getContentType()||"",i=s.indexOf("application/json")>-1,o=C.isObject(t);if(o&&C.isHTMLForm(t)&&(t=new FormData(t)),C.isFormData(t))return i?JSON.stringify(Hh(t)):t;if(C.isArrayBuffer(t)||C.isBuffer(t)||C.isStream(t)||C.isFile(t)||C.isBlob(t)||C.isReadableStream(t))return t;if(C.isArrayBufferView(t))return t.buffer;if(C.isURLSearchParams(t))return n.setContentType("application/x-www-form-urlencoded;charset=utf-8",!1),t.toString();let a;if(o){if(s.indexOf("application/x-www-form-urlencoded")>-1)return Ly(t,this.formSerializer).toString();if((a=C.isFileList(t))||s.indexOf("multipart/form-data")>-1){const l=this.env&&this.env.FormData;return Vo(a?{"files[]":t}:t,l&&new l,this.formSerializer)}}return o||i?(n.setContentType("application/json",!1),Ny(t)):t}],transformResponse:[function(t){const n=this.transitional||mi.transitional,s=n&&n.forcedJSONParsing,i=this.responseType==="json";if(C.isResponse(t)||C.isReadableStream(t))return t;if(t&&C.isString(t)&&(s&&!this.responseType||i)){const r=!(n&&n.silentJSONParsing)&&i;try{return JSON.parse(t)}catch(a){if(r)throw a.name==="SyntaxError"?nt.from(a,nt.ERR_BAD_RESPONSE,this,null,this.response):a}}return t}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,maxBodyLength:-1,env:{FormData:jt.classes.FormData,Blob:jt.classes.Blob},validateStatus:function(t){return t>=200&&t<300},headers:{common:{Accept:"application/json, text/plain, */*","Content-Type":void 0}}};C.forEach(["delete","get","head","post","put","patch"],e=>{mi.headers[e]={}});const By=C.toObjectSet(["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"]),zy=e=>{const t={};let n,s,i;return e&&e.split(`
`).forEach(function(r){i=r.indexOf(":"),n=r.substring(0,i).trim().toLowerCase(),s=r.substring(i+1).trim(),!(!n||t[n]&&By[n])&&(n==="set-cookie"?t[n]?t[n].push(s):t[n]=[s]:t[n]=t[n]?t[n]+", "+s:s)}),t},pc=Symbol("internals");function Ss(e){return e&&String(e).trim().toLowerCase()}function $i(e){return e===!1||e==null?e:C.isArray(e)?e.map($i):String(e)}function Hy(e){const t=Object.create(null),n=/([^\s,;=]+)\s*(?:=\s*([^,;]+))?/g;let s;for(;s=n.exec(e);)t[s[1]]=s[2];return t}const jy=e=>/^[-_a-zA-Z0-9^`|~,!#$%&'*+.]+$/.test(e.trim());function hr(e,t,n,s,i){if(C.isFunction(s))return s.call(this,t,n);if(i&&(t=n),!!C.isString(t)){if(C.isString(s))return t.indexOf(s)!==-1;if(C.isRegExp(s))return s.test(t)}}function Vy(e){return e.trim().toLowerCase().replace(/([a-z\d])(\w*)/g,(t,n,s)=>n.toUpperCase()+s)}function Wy(e,t){const n=C.toCamelCase(" "+t);["get","set","has"].forEach(s=>{Object.defineProperty(e,s+n,{value:function(i,o,r){return this[s].call(this,t,i,o,r)},configurable:!0})})}let ee=class{constructor(t){t&&this.set(t)}set(t,n,s){const i=this;function o(a,l,c){const u=Ss(l);if(!u)throw new Error("header name must be a non-empty string");const f=C.findKey(i,u);(!f||i[f]===void 0||c===!0||c===void 0&&i[f]!==!1)&&(i[f||l]=$i(a))}const r=(a,l)=>C.forEach(a,(c,u)=>o(c,u,l));if(C.isPlainObject(t)||t instanceof this.constructor)r(t,n);else if(C.isString(t)&&(t=t.trim())&&!jy(t))r(zy(t),n);else if(C.isObject(t)&&C.isIterable(t)){let a={},l,c;for(const u of t){if(!C.isArray(u))throw TypeError("Object iterator must return a key-value pair");a[c=u[0]]=(l=a[c])?C.isArray(l)?[...l,u[1]]:[l,u[1]]:u[1]}r(a,n)}else t!=null&&o(n,t,s);return this}get(t,n){if(t=Ss(t),t){const s=C.findKey(this,t);if(s){const i=this[s];if(!n)return i;if(n===!0)return Hy(i);if(C.isFunction(n))return n.call(this,i,s);if(C.isRegExp(n))return n.exec(i);throw new TypeError("parser must be boolean|regexp|function")}}}has(t,n){if(t=Ss(t),t){const s=C.findKey(this,t);return!!(s&&this[s]!==void 0&&(!n||hr(this,this[s],s,n)))}return!1}delete(t,n){const s=this;let i=!1;function o(r){if(r=Ss(r),r){const a=C.findKey(s,r);a&&(!n||hr(s,s[a],a,n))&&(delete s[a],i=!0)}}return C.isArray(t)?t.forEach(o):o(t),i}clear(t){const n=Object.keys(this);let s=n.length,i=!1;for(;s--;){const o=n[s];(!t||hr(this,this[o],o,t,!0))&&(delete this[o],i=!0)}return i}normalize(t){const n=this,s={};return C.forEach(this,(i,o)=>{const r=C.findKey(s,o);if(r){n[r]=$i(i),delete n[o];return}const a=t?Vy(o):String(o).trim();a!==o&&delete n[o],n[a]=$i(i),s[a]=!0}),this}concat(...t){return this.constructor.concat(this,...t)}toJSON(t){const n=Object.create(null);return C.forEach(this,(s,i)=>{s!=null&&s!==!1&&(n[i]=t&&C.isArray(s)?s.join(", "):s)}),n}[Symbol.iterator](){return Object.entries(this.toJSON())[Symbol.iterator]()}toString(){return Object.entries(this.toJSON()).map(([t,n])=>t+": "+n).join(`
`)}getSetCookie(){return this.get("set-cookie")||[]}get[Symbol.toStringTag](){return"AxiosHeaders"}static from(t){return t instanceof this?t:new this(t)}static concat(t,...n){const s=new this(t);return n.forEach(i=>s.set(i)),s}static accessor(t){const s=(this[pc]=this[pc]={accessors:{}}).accessors,i=this.prototype;function o(r){const a=Ss(r);s[a]||(Wy(i,r),s[a]=!0)}return C.isArray(t)?t.forEach(o):o(t),this}};ee.accessor(["Content-Type","Content-Length","Accept","Accept-Encoding","User-Agent","Authorization"]);C.reduceDescriptors(ee.prototype,({value:e},t)=>{let n=t[0].toUpperCase()+t.slice(1);return{get:()=>e,set(s){this[n]=s}}});C.freezeMethods(ee);function dr(e,t){const n=this||mi,s=t||n,i=ee.from(s.headers);let o=s.data;return C.forEach(e,function(a){o=a.call(n,o,i.normalize(),t?t.status:void 0)}),i.normalize(),o}function jh(e){return!!(e&&e.__CANCEL__)}function ys(e,t,n){nt.call(this,e??"canceled",nt.ERR_CANCELED,t,n),this.name="CanceledError"}C.inherits(ys,nt,{__CANCEL__:!0});function Vh(e,t,n){const s=n.config.validateStatus;!n.status||!s||s(n.status)?e(n):t(new nt("Request failed with status code "+n.status,[nt.ERR_BAD_REQUEST,nt.ERR_BAD_RESPONSE][Math.floor(n.status/100)-4],n.config,n.request,n))}function $y(e){const t=/^([-+\w]{1,25})(:?\/\/|:)/.exec(e);return t&&t[1]||""}function Uy(e,t){e=e||10;const n=new Array(e),s=new Array(e);let i=0,o=0,r;return t=t!==void 0?t:1e3,function(l){const c=Date.now(),u=s[o];r||(r=c),n[i]=l,s[i]=c;let f=o,h=0;for(;f!==i;)h+=n[f++],f=f%e;if(i=(i+1)%e,i===o&&(o=(o+1)%e),c-r<t)return;const d=u&&c-u;return d?Math.round(h*1e3/d):void 0}}function Ky(e,t){let n=0,s=1e3/t,i,o;const r=(c,u=Date.now())=>{n=u,i=null,o&&(clearTimeout(o),o=null),e.apply(null,c)};return[(...c)=>{const u=Date.now(),f=u-n;f>=s?r(c,u):(i=c,o||(o=setTimeout(()=>{o=null,r(i)},s-f)))},()=>i&&r(i)]}const po=(e,t,n=3)=>{let s=0;const i=Uy(50,250);return Ky(o=>{const r=o.loaded,a=o.lengthComputable?o.total:void 0,l=r-s,c=i(l),u=r<=a;s=r;const f={loaded:r,total:a,progress:a?r/a:void 0,bytes:l,rate:c||void 0,estimated:c&&a&&u?(a-r)/c:void 0,event:o,lengthComputable:a!=null,[t?"download":"upload"]:!0};e(f)},n)},gc=(e,t)=>{const n=e!=null;return[s=>t[0]({lengthComputable:n,total:e,loaded:s}),t[1]]},mc=e=>(...t)=>C.asap(()=>e(...t)),qy=jt.hasStandardBrowserEnv?((e,t)=>n=>(n=new URL(n,jt.origin),e.protocol===n.protocol&&e.host===n.host&&(t||e.port===n.port)))(new URL(jt.origin),jt.navigator&&/(msie|trident)/i.test(jt.navigator.userAgent)):()=>!0,Yy=jt.hasStandardBrowserEnv?{write(e,t,n,s,i,o){const r=[e+"="+encodeURIComponent(t)];C.isNumber(n)&&r.push("expires="+new Date(n).toGMTString()),C.isString(s)&&r.push("path="+s),C.isString(i)&&r.push("domain="+i),o===!0&&r.push("secure"),document.cookie=r.join("; ")},read(e){const t=document.cookie.match(new RegExp("(^|;\\s*)("+e+")=([^;]*)"));return t?decodeURIComponent(t[3]):null},remove(e){this.write(e,"",Date.now()-864e5)}}:{write(){},read(){return null},remove(){}};function Xy(e){return/^([a-z][a-z\d+\-.]*:)?\/\//i.test(e)}function Gy(e,t){return t?e.replace(/\/?\/$/,"")+"/"+t.replace(/^\/+/,""):e}function Wh(e,t,n){let s=!Xy(t);return e&&(s||n==!1)?Gy(e,t):t}const bc=e=>e instanceof ee?{...e}:e;function Vn(e,t){t=t||{};const n={};function s(c,u,f,h){return C.isPlainObject(c)&&C.isPlainObject(u)?C.merge.call({caseless:h},c,u):C.isPlainObject(u)?C.merge({},u):C.isArray(u)?u.slice():u}function i(c,u,f,h){if(C.isUndefined(u)){if(!C.isUndefined(c))return s(void 0,c,f,h)}else return s(c,u,f,h)}function o(c,u){if(!C.isUndefined(u))return s(void 0,u)}function r(c,u){if(C.isUndefined(u)){if(!C.isUndefined(c))return s(void 0,c)}else return s(void 0,u)}function a(c,u,f){if(f in t)return s(c,u);if(f in e)return s(void 0,c)}const l={url:o,method:o,data:o,baseURL:r,transformRequest:r,transformResponse:r,paramsSerializer:r,timeout:r,timeoutMessage:r,withCredentials:r,withXSRFToken:r,adapter:r,responseType:r,xsrfCookieName:r,xsrfHeaderName:r,onUploadProgress:r,onDownloadProgress:r,decompress:r,maxContentLength:r,maxBodyLength:r,beforeRedirect:r,transport:r,httpAgent:r,httpsAgent:r,cancelToken:r,socketPath:r,responseEncoding:r,validateStatus:a,headers:(c,u,f)=>i(bc(c),bc(u),f,!0)};return C.forEach(Object.keys(Object.assign({},e,t)),function(u){const f=l[u]||i,h=f(e[u],t[u],u);C.isUndefined(h)&&f!==a||(n[u]=h)}),n}const $h=e=>{const t=Vn({},e);let{data:n,withXSRFToken:s,xsrfHeaderName:i,xsrfCookieName:o,headers:r,auth:a}=t;t.headers=r=ee.from(r),t.url=Bh(Wh(t.baseURL,t.url,t.allowAbsoluteUrls),e.params,e.paramsSerializer),a&&r.set("Authorization","Basic "+btoa((a.username||"")+":"+(a.password?unescape(encodeURIComponent(a.password)):"")));let l;if(C.isFormData(n)){if(jt.hasStandardBrowserEnv||jt.hasStandardBrowserWebWorkerEnv)r.setContentType(void 0);else if((l=r.getContentType())!==!1){const[c,...u]=l?l.split(";").map(f=>f.trim()).filter(Boolean):[];r.setContentType([c||"multipart/form-data",...u].join("; "))}}if(jt.hasStandardBrowserEnv&&(s&&C.isFunction(s)&&(s=s(t)),s||s!==!1&&qy(t.url))){const c=i&&o&&Yy.read(o);c&&r.set(i,c)}return t},Jy=typeof XMLHttpRequest<"u",Qy=Jy&&function(e){return new Promise(function(n,s){const i=$h(e);let o=i.data;const r=ee.from(i.headers).normalize();let{responseType:a,onUploadProgress:l,onDownloadProgress:c}=i,u,f,h,d,p;function g(){d&&d(),p&&p(),i.cancelToken&&i.cancelToken.unsubscribe(u),i.signal&&i.signal.removeEventListener("abort",u)}let m=new XMLHttpRequest;m.open(i.method.toUpperCase(),i.url,!0),m.timeout=i.timeout;function y(){if(!m)return;const w=ee.from("getAllResponseHeaders"in m&&m.getAllResponseHeaders()),S={data:!a||a==="text"||a==="json"?m.responseText:m.response,status:m.status,statusText:m.statusText,headers:w,config:e,request:m};Vh(function(P){n(P),g()},function(P){s(P),g()},S),m=null}"onloadend"in m?m.onloadend=y:m.onreadystatechange=function(){!m||m.readyState!==4||m.status===0&&!(m.responseURL&&m.responseURL.indexOf("file:")===0)||setTimeout(y)},m.onabort=function(){m&&(s(new nt("Request aborted",nt.ECONNABORTED,e,m)),m=null)},m.onerror=function(){s(new nt("Network Error",nt.ERR_NETWORK,e,m)),m=null},m.ontimeout=function(){let v=i.timeout?"timeout of "+i.timeout+"ms exceeded":"timeout exceeded";const S=i.transitional||zh;i.timeoutErrorMessage&&(v=i.timeoutErrorMessage),s(new nt(v,S.clarifyTimeoutError?nt.ETIMEDOUT:nt.ECONNABORTED,e,m)),m=null},o===void 0&&r.setContentType(null),"setRequestHeader"in m&&C.forEach(r.toJSON(),function(v,S){m.setRequestHeader(S,v)}),C.isUndefined(i.withCredentials)||(m.withCredentials=!!i.withCredentials),a&&a!=="json"&&(m.responseType=i.responseType),c&&([h,p]=po(c,!0),m.addEventListener("progress",h)),l&&m.upload&&([f,d]=po(l),m.upload.addEventListener("progress",f),m.upload.addEventListener("loadend",d)),(i.cancelToken||i.signal)&&(u=w=>{m&&(s(!w||w.type?new ys(null,e,m):w),m.abort(),m=null)},i.cancelToken&&i.cancelToken.subscribe(u),i.signal&&(i.signal.aborted?u():i.signal.addEventListener("abort",u)));const b=$y(i.url);if(b&&jt.protocols.indexOf(b)===-1){s(new nt("Unsupported protocol "+b+":",nt.ERR_BAD_REQUEST,e));return}m.send(o||null)})},Zy=(e,t)=>{const{length:n}=e=e?e.filter(Boolean):[];if(t||n){let s=new AbortController,i;const o=function(c){if(!i){i=!0,a();const u=c instanceof Error?c:this.reason;s.abort(u instanceof nt?u:new ys(u instanceof Error?u.message:u))}};let r=t&&setTimeout(()=>{r=null,o(new nt(`timeout ${t} of ms exceeded`,nt.ETIMEDOUT))},t);const a=()=>{e&&(r&&clearTimeout(r),r=null,e.forEach(c=>{c.unsubscribe?c.unsubscribe(o):c.removeEventListener("abort",o)}),e=null)};e.forEach(c=>c.addEventListener("abort",o));const{signal:l}=s;return l.unsubscribe=()=>C.asap(a),l}},t_=function*(e,t){let n=e.byteLength;if(n<t){yield e;return}let s=0,i;for(;s<n;)i=s+t,yield e.slice(s,i),s=i},e_=async function*(e,t){for await(const n of n_(e))yield*t_(n,t)},n_=async function*(e){if(e[Symbol.asyncIterator]){yield*e;return}const t=e.getReader();try{for(;;){const{done:n,value:s}=await t.read();if(n)break;yield s}}finally{await t.cancel()}},yc=(e,t,n,s)=>{const i=e_(e,t);let o=0,r,a=l=>{r||(r=!0,s&&s(l))};return new ReadableStream({async pull(l){try{const{done:c,value:u}=await i.next();if(c){a(),l.close();return}let f=u.byteLength;if(n){let h=o+=f;n(h)}l.enqueue(new Uint8Array(u))}catch(c){throw a(c),c}},cancel(l){return a(l),i.return()}},{highWaterMark:2})},Wo=typeof fetch=="function"&&typeof Request=="function"&&typeof Response=="function",Uh=Wo&&typeof ReadableStream=="function",s_=Wo&&(typeof TextEncoder=="function"?(e=>t=>e.encode(t))(new TextEncoder):async e=>new Uint8Array(await new Response(e).arrayBuffer())),Kh=(e,...t)=>{try{return!!e(...t)}catch{return!1}},i_=Uh&&Kh(()=>{let e=!1;const t=new Request(jt.origin,{body:new ReadableStream,method:"POST",get duplex(){return e=!0,"half"}}).headers.has("Content-Type");return e&&!t}),_c=64*1024,Gr=Uh&&Kh(()=>C.isReadableStream(new Response("").body)),go={stream:Gr&&(e=>e.body)};Wo&&(e=>{["text","arrayBuffer","blob","formData","stream"].forEach(t=>{!go[t]&&(go[t]=C.isFunction(e[t])?n=>n[t]():(n,s)=>{throw new nt(`Response type '${t}' is not supported`,nt.ERR_NOT_SUPPORT,s)})})})(new Response);const o_=async e=>{if(e==null)return 0;if(C.isBlob(e))return e.size;if(C.isSpecCompliantForm(e))return(await new Request(jt.origin,{method:"POST",body:e}).arrayBuffer()).byteLength;if(C.isArrayBufferView(e)||C.isArrayBuffer(e))return e.byteLength;if(C.isURLSearchParams(e)&&(e=e+""),C.isString(e))return(await s_(e)).byteLength},r_=async(e,t)=>{const n=C.toFiniteNumber(e.getContentLength());return n??o_(t)},a_=Wo&&(async e=>{let{url:t,method:n,data:s,signal:i,cancelToken:o,timeout:r,onDownloadProgress:a,onUploadProgress:l,responseType:c,headers:u,withCredentials:f="same-origin",fetchOptions:h}=$h(e);c=c?(c+"").toLowerCase():"text";let d=Zy([i,o&&o.toAbortSignal()],r),p;const g=d&&d.unsubscribe&&(()=>{d.unsubscribe()});let m;try{if(l&&i_&&n!=="get"&&n!=="head"&&(m=await r_(u,s))!==0){let S=new Request(t,{method:"POST",body:s,duplex:"half"}),A;if(C.isFormData(s)&&(A=S.headers.get("content-type"))&&u.setContentType(A),S.body){const[P,E]=gc(m,po(mc(l)));s=yc(S.body,_c,P,E)}}C.isString(f)||(f=f?"include":"omit");const y="credentials"in Request.prototype;p=new Request(t,{...h,signal:d,method:n.toUpperCase(),headers:u.normalize().toJSON(),body:s,duplex:"half",credentials:y?f:void 0});let b=await fetch(p);const w=Gr&&(c==="stream"||c==="response");if(Gr&&(a||w&&g)){const S={};["status","statusText","headers"].forEach(k=>{S[k]=b[k]});const A=C.toFiniteNumber(b.headers.get("content-length")),[P,E]=a&&gc(A,po(mc(a),!0))||[];b=new Response(yc(b.body,_c,P,()=>{E&&E(),g&&g()}),S)}c=c||"text";let v=await go[C.findKey(go,c)||"text"](b,e);return!w&&g&&g(),await new Promise((S,A)=>{Vh(S,A,{data:v,headers:ee.from(b.headers),status:b.status,statusText:b.statusText,config:e,request:p})})}catch(y){throw g&&g(),y&&y.name==="TypeError"&&/Load failed|fetch/i.test(y.message)?Object.assign(new nt("Network Error",nt.ERR_NETWORK,e,p),{cause:y.cause||y}):nt.from(y,y&&y.code,e,p)}}),Jr={http:wy,xhr:Qy,fetch:a_};C.forEach(Jr,(e,t)=>{if(e){try{Object.defineProperty(e,"name",{value:t})}catch{}Object.defineProperty(e,"adapterName",{value:t})}});const xc=e=>`- ${e}`,l_=e=>C.isFunction(e)||e===null||e===!1,qh={getAdapter:e=>{e=C.isArray(e)?e:[e];const{length:t}=e;let n,s;const i={};for(let o=0;o<t;o++){n=e[o];let r;if(s=n,!l_(n)&&(s=Jr[(r=String(n)).toLowerCase()],s===void 0))throw new nt(`Unknown adapter '${r}'`);if(s)break;i[r||"#"+o]=s}if(!s){const o=Object.entries(i).map(([a,l])=>`adapter ${a} `+(l===!1?"is not supported by the environment":"is not available in the build"));let r=t?o.length>1?`since :
`+o.map(xc).join(`
`):" "+xc(o[0]):"as no adapter specified";throw new nt("There is no suitable adapter to dispatch the request "+r,"ERR_NOT_SUPPORT")}return s},adapters:Jr};function pr(e){if(e.cancelToken&&e.cancelToken.throwIfRequested(),e.signal&&e.signal.aborted)throw new ys(null,e)}function vc(e){return pr(e),e.headers=ee.from(e.headers),e.data=dr.call(e,e.transformRequest),["post","put","patch"].indexOf(e.method)!==-1&&e.headers.setContentType("application/x-www-form-urlencoded",!1),qh.getAdapter(e.adapter||mi.adapter)(e).then(function(s){return pr(e),s.data=dr.call(e,e.transformResponse,s),s.headers=ee.from(s.headers),s},function(s){return jh(s)||(pr(e),s&&s.response&&(s.response.data=dr.call(e,e.transformResponse,s.response),s.response.headers=ee.from(s.response.headers))),Promise.reject(s)})}const Yh="1.9.0",$o={};["object","boolean","number","function","string","symbol"].forEach((e,t)=>{$o[e]=function(s){return typeof s===e||"a"+(t<1?"n ":" ")+e}});const wc={};$o.transitional=function(t,n,s){function i(o,r){return"[Axios v"+Yh+"] Transitional option '"+o+"'"+r+(s?". "+s:"")}return(o,r,a)=>{if(t===!1)throw new nt(i(r," has been removed"+(n?" in "+n:"")),nt.ERR_DEPRECATED);return n&&!wc[r]&&(wc[r]=!0,console.warn(i(r," has been deprecated since v"+n+" and will be removed in the near future"))),t?t(o,r,a):!0}};$o.spelling=function(t){return(n,s)=>(console.warn(`${s} is likely a misspelling of ${t}`),!0)};function c_(e,t,n){if(typeof e!="object")throw new nt("options must be an object",nt.ERR_BAD_OPTION_VALUE);const s=Object.keys(e);let i=s.length;for(;i-- >0;){const o=s[i],r=t[o];if(r){const a=e[o],l=a===void 0||r(a,o,e);if(l!==!0)throw new nt("option "+o+" must be "+l,nt.ERR_BAD_OPTION_VALUE);continue}if(n!==!0)throw new nt("Unknown option "+o,nt.ERR_BAD_OPTION)}}const Ui={assertOptions:c_,validators:$o},ke=Ui.validators;let Bn=class{constructor(t){this.defaults=t||{},this.interceptors={request:new dc,response:new dc}}async request(t,n){try{return await this._request(t,n)}catch(s){if(s instanceof Error){let i={};Error.captureStackTrace?Error.captureStackTrace(i):i=new Error;const o=i.stack?i.stack.replace(/^.+\n/,""):"";try{s.stack?o&&!String(s.stack).endsWith(o.replace(/^.+\n.+\n/,""))&&(s.stack+=`
`+o):s.stack=o}catch{}}throw s}}_request(t,n){typeof t=="string"?(n=n||{},n.url=t):n=t||{},n=Vn(this.defaults,n);const{transitional:s,paramsSerializer:i,headers:o}=n;s!==void 0&&Ui.assertOptions(s,{silentJSONParsing:ke.transitional(ke.boolean),forcedJSONParsing:ke.transitional(ke.boolean),clarifyTimeoutError:ke.transitional(ke.boolean)},!1),i!=null&&(C.isFunction(i)?n.paramsSerializer={serialize:i}:Ui.assertOptions(i,{encode:ke.function,serialize:ke.function},!0)),n.allowAbsoluteUrls!==void 0||(this.defaults.allowAbsoluteUrls!==void 0?n.allowAbsoluteUrls=this.defaults.allowAbsoluteUrls:n.allowAbsoluteUrls=!0),Ui.assertOptions(n,{baseUrl:ke.spelling("baseURL"),withXsrfToken:ke.spelling("withXSRFToken")},!0),n.method=(n.method||this.defaults.method||"get").toLowerCase();let r=o&&C.merge(o.common,o[n.method]);o&&C.forEach(["delete","get","head","post","put","patch","common"],p=>{delete o[p]}),n.headers=ee.concat(r,o);const a=[];let l=!0;this.interceptors.request.forEach(function(g){typeof g.runWhen=="function"&&g.runWhen(n)===!1||(l=l&&g.synchronous,a.unshift(g.fulfilled,g.rejected))});const c=[];this.interceptors.response.forEach(function(g){c.push(g.fulfilled,g.rejected)});let u,f=0,h;if(!l){const p=[vc.bind(this),void 0];for(p.unshift.apply(p,a),p.push.apply(p,c),h=p.length,u=Promise.resolve(n);f<h;)u=u.then(p[f++],p[f++]);return u}h=a.length;let d=n;for(f=0;f<h;){const p=a[f++],g=a[f++];try{d=p(d)}catch(m){g.call(this,m);break}}try{u=vc.call(this,d)}catch(p){return Promise.reject(p)}for(f=0,h=c.length;f<h;)u=u.then(c[f++],c[f++]);return u}getUri(t){t=Vn(this.defaults,t);const n=Wh(t.baseURL,t.url,t.allowAbsoluteUrls);return Bh(n,t.params,t.paramsSerializer)}};C.forEach(["delete","get","head","options"],function(t){Bn.prototype[t]=function(n,s){return this.request(Vn(s||{},{method:t,url:n,data:(s||{}).data}))}});C.forEach(["post","put","patch"],function(t){function n(s){return function(o,r,a){return this.request(Vn(a||{},{method:t,headers:s?{"Content-Type":"multipart/form-data"}:{},url:o,data:r}))}}Bn.prototype[t]=n(),Bn.prototype[t+"Form"]=n(!0)});let u_=class Xh{constructor(t){if(typeof t!="function")throw new TypeError("executor must be a function.");let n;this.promise=new Promise(function(o){n=o});const s=this;this.promise.then(i=>{if(!s._listeners)return;let o=s._listeners.length;for(;o-- >0;)s._listeners[o](i);s._listeners=null}),this.promise.then=i=>{let o;const r=new Promise(a=>{s.subscribe(a),o=a}).then(i);return r.cancel=function(){s.unsubscribe(o)},r},t(function(o,r,a){s.reason||(s.reason=new ys(o,r,a),n(s.reason))})}throwIfRequested(){if(this.reason)throw this.reason}subscribe(t){if(this.reason){t(this.reason);return}this._listeners?this._listeners.push(t):this._listeners=[t]}unsubscribe(t){if(!this._listeners)return;const n=this._listeners.indexOf(t);n!==-1&&this._listeners.splice(n,1)}toAbortSignal(){const t=new AbortController,n=s=>{t.abort(s)};return this.subscribe(n),t.signal.unsubscribe=()=>this.unsubscribe(n),t.signal}static source(){let t;return{token:new Xh(function(i){t=i}),cancel:t}}};function f_(e){return function(n){return e.apply(null,n)}}function h_(e){return C.isObject(e)&&e.isAxiosError===!0}const Qr={Continue:100,SwitchingProtocols:101,Processing:102,EarlyHints:103,Ok:200,Created:201,Accepted:202,NonAuthoritativeInformation:203,NoContent:204,ResetContent:205,PartialContent:206,MultiStatus:207,AlreadyReported:208,ImUsed:226,MultipleChoices:300,MovedPermanently:301,Found:302,SeeOther:303,NotModified:304,UseProxy:305,Unused:306,TemporaryRedirect:307,PermanentRedirect:308,BadRequest:400,Unauthorized:401,PaymentRequired:402,Forbidden:403,NotFound:404,MethodNotAllowed:405,NotAcceptable:406,ProxyAuthenticationRequired:407,RequestTimeout:408,Conflict:409,Gone:410,LengthRequired:411,PreconditionFailed:412,PayloadTooLarge:413,UriTooLong:414,UnsupportedMediaType:415,RangeNotSatisfiable:416,ExpectationFailed:417,ImATeapot:418,MisdirectedRequest:421,UnprocessableEntity:422,Locked:423,FailedDependency:424,TooEarly:425,UpgradeRequired:426,PreconditionRequired:428,TooManyRequests:429,RequestHeaderFieldsTooLarge:431,UnavailableForLegalReasons:451,InternalServerError:500,NotImplemented:501,BadGateway:502,ServiceUnavailable:503,GatewayTimeout:504,HttpVersionNotSupported:505,VariantAlsoNegotiates:506,InsufficientStorage:507,LoopDetected:508,NotExtended:510,NetworkAuthenticationRequired:511};Object.entries(Qr).forEach(([e,t])=>{Qr[t]=e});function Gh(e){const t=new Bn(e),n=Ph(Bn.prototype.request,t);return C.extend(n,Bn.prototype,t,{allOwnKeys:!0}),C.extend(n,t,null,{allOwnKeys:!0}),n.create=function(i){return Gh(Vn(e,i))},n}const Ot=Gh(mi);Ot.Axios=Bn;Ot.CanceledError=ys;Ot.CancelToken=u_;Ot.isCancel=jh;Ot.VERSION=Yh;Ot.toFormData=Vo;Ot.AxiosError=nt;Ot.Cancel=Ot.CanceledError;Ot.all=function(t){return Promise.all(t)};Ot.spread=f_;Ot.isAxiosError=h_;Ot.mergeConfig=Vn;Ot.AxiosHeaders=ee;Ot.formToJSON=e=>Hh(C.isHTMLForm(e)?new FormData(e):e);Ot.getAdapter=qh.getAdapter;Ot.HttpStatusCode=Qr;Ot.default=Ot;const{Axios:US,AxiosError:KS,CanceledError:qS,isCancel:YS,CancelToken:XS,VERSION:GS,all:JS,Cancel:QS,isAxiosError:ZS,spread:tM,toFormData:eM,AxiosHeaders:nM,HttpStatusCode:sM,formToJSON:iM,getAdapter:oM,mergeConfig:rM}=Ot;/*!
 * @kurkle/color v0.3.4
 * https://github.com/kurkle/color#readme
 * (c) 2024 Jukka Kurkela
 * Released under the MIT License
 */function bi(e){return e+.5|0}const an=(e,t,n)=>Math.max(Math.min(e,n),t);function Rs(e){return an(bi(e*2.55),0,255)}function dn(e){return an(bi(e*255),0,255)}function Ve(e){return an(bi(e/2.55)/100,0,1)}function Sc(e){return an(bi(e*100),0,100)}const ce={0:0,1:1,2:2,3:3,4:4,5:5,6:6,7:7,8:8,9:9,A:10,B:11,C:12,D:13,E:14,F:15,a:10,b:11,c:12,d:13,e:14,f:15},Zr=[..."0123456789ABCDEF"],d_=e=>Zr[e&15],p_=e=>Zr[(e&240)>>4]+Zr[e&15],Mi=e=>(e&240)>>4===(e&15),g_=e=>Mi(e.r)&&Mi(e.g)&&Mi(e.b)&&Mi(e.a);function m_(e){var t=e.length,n;return e[0]==="#"&&(t===4||t===5?n={r:255&ce[e[1]]*17,g:255&ce[e[2]]*17,b:255&ce[e[3]]*17,a:t===5?ce[e[4]]*17:255}:(t===7||t===9)&&(n={r:ce[e[1]]<<4|ce[e[2]],g:ce[e[3]]<<4|ce[e[4]],b:ce[e[5]]<<4|ce[e[6]],a:t===9?ce[e[7]]<<4|ce[e[8]]:255})),n}const b_=(e,t)=>e<255?t(e):"";function y_(e){var t=g_(e)?d_:p_;return e?"#"+t(e.r)+t(e.g)+t(e.b)+b_(e.a,t):void 0}const __=/^(hsla?|hwb|hsv)\(\s*([-+.e\d]+)(?:deg)?[\s,]+([-+.e\d]+)%[\s,]+([-+.e\d]+)%(?:[\s,]+([-+.e\d]+)(%)?)?\s*\)$/;function Jh(e,t,n){const s=t*Math.min(n,1-n),i=(o,r=(o+e/30)%12)=>n-s*Math.max(Math.min(r-3,9-r,1),-1);return[i(0),i(8),i(4)]}function x_(e,t,n){const s=(i,o=(i+e/60)%6)=>n-n*t*Math.max(Math.min(o,4-o,1),0);return[s(5),s(3),s(1)]}function v_(e,t,n){const s=Jh(e,1,.5);let i;for(t+n>1&&(i=1/(t+n),t*=i,n*=i),i=0;i<3;i++)s[i]*=1-t-n,s[i]+=t;return s}function w_(e,t,n,s,i){return e===i?(t-n)/s+(t<n?6:0):t===i?(n-e)/s+2:(e-t)/s+4}function Wa(e){const n=e.r/255,s=e.g/255,i=e.b/255,o=Math.max(n,s,i),r=Math.min(n,s,i),a=(o+r)/2;let l,c,u;return o!==r&&(u=o-r,c=a>.5?u/(2-o-r):u/(o+r),l=w_(n,s,i,u,o),l=l*60+.5),[l|0,c||0,a]}function $a(e,t,n,s){return(Array.isArray(t)?e(t[0],t[1],t[2]):e(t,n,s)).map(dn)}function Ua(e,t,n){return $a(Jh,e,t,n)}function S_(e,t,n){return $a(v_,e,t,n)}function M_(e,t,n){return $a(x_,e,t,n)}function Qh(e){return(e%360+360)%360}function C_(e){const t=__.exec(e);let n=255,s;if(!t)return;t[5]!==s&&(n=t[6]?Rs(+t[5]):dn(+t[5]));const i=Qh(+t[2]),o=+t[3]/100,r=+t[4]/100;return t[1]==="hwb"?s=S_(i,o,r):t[1]==="hsv"?s=M_(i,o,r):s=Ua(i,o,r),{r:s[0],g:s[1],b:s[2],a:n}}function P_(e,t){var n=Wa(e);n[0]=Qh(n[0]+t),n=Ua(n),e.r=n[0],e.g=n[1],e.b=n[2]}function E_(e){if(!e)return;const t=Wa(e),n=t[0],s=Sc(t[1]),i=Sc(t[2]);return e.a<255?`hsla(${n}, ${s}%, ${i}%, ${Ve(e.a)})`:`hsl(${n}, ${s}%, ${i}%)`}const Mc={x:"dark",Z:"light",Y:"re",X:"blu",W:"gr",V:"medium",U:"slate",A:"ee",T:"ol",S:"or",B:"ra",C:"lateg",D:"ights",R:"in",Q:"turquois",E:"hi",P:"ro",O:"al",N:"le",M:"de",L:"yello",F:"en",K:"ch",G:"arks",H:"ea",I:"ightg",J:"wh"},Cc={OiceXe:"f0f8ff",antiquewEte:"faebd7",aqua:"ffff",aquamarRe:"7fffd4",azuY:"f0ffff",beige:"f5f5dc",bisque:"ffe4c4",black:"0",blanKedOmond:"ffebcd",Xe:"ff",XeviTet:"8a2be2",bPwn:"a52a2a",burlywood:"deb887",caMtXe:"5f9ea0",KartYuse:"7fff00",KocTate:"d2691e",cSO:"ff7f50",cSnflowerXe:"6495ed",cSnsilk:"fff8dc",crimson:"dc143c",cyan:"ffff",xXe:"8b",xcyan:"8b8b",xgTMnPd:"b8860b",xWay:"a9a9a9",xgYF:"6400",xgYy:"a9a9a9",xkhaki:"bdb76b",xmagFta:"8b008b",xTivegYF:"556b2f",xSange:"ff8c00",xScEd:"9932cc",xYd:"8b0000",xsOmon:"e9967a",xsHgYF:"8fbc8f",xUXe:"483d8b",xUWay:"2f4f4f",xUgYy:"2f4f4f",xQe:"ced1",xviTet:"9400d3",dAppRk:"ff1493",dApskyXe:"bfff",dimWay:"696969",dimgYy:"696969",dodgerXe:"1e90ff",fiYbrick:"b22222",flSOwEte:"fffaf0",foYstWAn:"228b22",fuKsia:"ff00ff",gaRsbSo:"dcdcdc",ghostwEte:"f8f8ff",gTd:"ffd700",gTMnPd:"daa520",Way:"808080",gYF:"8000",gYFLw:"adff2f",gYy:"808080",honeyMw:"f0fff0",hotpRk:"ff69b4",RdianYd:"cd5c5c",Rdigo:"4b0082",ivSy:"fffff0",khaki:"f0e68c",lavFMr:"e6e6fa",lavFMrXsh:"fff0f5",lawngYF:"7cfc00",NmoncEffon:"fffacd",ZXe:"add8e6",ZcSO:"f08080",Zcyan:"e0ffff",ZgTMnPdLw:"fafad2",ZWay:"d3d3d3",ZgYF:"90ee90",ZgYy:"d3d3d3",ZpRk:"ffb6c1",ZsOmon:"ffa07a",ZsHgYF:"20b2aa",ZskyXe:"87cefa",ZUWay:"778899",ZUgYy:"778899",ZstAlXe:"b0c4de",ZLw:"ffffe0",lime:"ff00",limegYF:"32cd32",lRF:"faf0e6",magFta:"ff00ff",maPon:"800000",VaquamarRe:"66cdaa",VXe:"cd",VScEd:"ba55d3",VpurpN:"9370db",VsHgYF:"3cb371",VUXe:"7b68ee",VsprRggYF:"fa9a",VQe:"48d1cc",VviTetYd:"c71585",midnightXe:"191970",mRtcYam:"f5fffa",mistyPse:"ffe4e1",moccasR:"ffe4b5",navajowEte:"ffdead",navy:"80",Tdlace:"fdf5e6",Tive:"808000",TivedBb:"6b8e23",Sange:"ffa500",SangeYd:"ff4500",ScEd:"da70d6",pOegTMnPd:"eee8aa",pOegYF:"98fb98",pOeQe:"afeeee",pOeviTetYd:"db7093",papayawEp:"ffefd5",pHKpuff:"ffdab9",peru:"cd853f",pRk:"ffc0cb",plum:"dda0dd",powMrXe:"b0e0e6",purpN:"800080",YbeccapurpN:"663399",Yd:"ff0000",Psybrown:"bc8f8f",PyOXe:"4169e1",saddNbPwn:"8b4513",sOmon:"fa8072",sandybPwn:"f4a460",sHgYF:"2e8b57",sHshell:"fff5ee",siFna:"a0522d",silver:"c0c0c0",skyXe:"87ceeb",UXe:"6a5acd",UWay:"708090",UgYy:"708090",snow:"fffafa",sprRggYF:"ff7f",stAlXe:"4682b4",tan:"d2b48c",teO:"8080",tEstN:"d8bfd8",tomato:"ff6347",Qe:"40e0d0",viTet:"ee82ee",JHt:"f5deb3",wEte:"ffffff",wEtesmoke:"f5f5f5",Lw:"ffff00",LwgYF:"9acd32"};function k_(){const e={},t=Object.keys(Cc),n=Object.keys(Mc);let s,i,o,r,a;for(s=0;s<t.length;s++){for(r=a=t[s],i=0;i<n.length;i++)o=n[i],a=a.replace(o,Mc[o]);o=parseInt(Cc[r],16),e[a]=[o>>16&255,o>>8&255,o&255]}return e}let Ci;function A_(e){Ci||(Ci=k_(),Ci.transparent=[0,0,0,0]);const t=Ci[e.toLowerCase()];return t&&{r:t[0],g:t[1],b:t[2],a:t.length===4?t[3]:255}}const O_=/^rgba?\(\s*([-+.\d]+)(%)?[\s,]+([-+.e\d]+)(%)?[\s,]+([-+.e\d]+)(%)?(?:[\s,/]+([-+.e\d]+)(%)?)?\s*\)$/;function R_(e){const t=O_.exec(e);let n=255,s,i,o;if(t){if(t[7]!==s){const r=+t[7];n=t[8]?Rs(r):an(r*255,0,255)}return s=+t[1],i=+t[3],o=+t[5],s=255&(t[2]?Rs(s):an(s,0,255)),i=255&(t[4]?Rs(i):an(i,0,255)),o=255&(t[6]?Rs(o):an(o,0,255)),{r:s,g:i,b:o,a:n}}}function T_(e){return e&&(e.a<255?`rgba(${e.r}, ${e.g}, ${e.b}, ${Ve(e.a)})`:`rgb(${e.r}, ${e.g}, ${e.b})`)}const gr=e=>e<=.0031308?e*12.92:Math.pow(e,1/2.4)*1.055-.055,Xn=e=>e<=.04045?e/12.92:Math.pow((e+.055)/1.055,2.4);function D_(e,t,n){const s=Xn(Ve(e.r)),i=Xn(Ve(e.g)),o=Xn(Ve(e.b));return{r:dn(gr(s+n*(Xn(Ve(t.r))-s))),g:dn(gr(i+n*(Xn(Ve(t.g))-i))),b:dn(gr(o+n*(Xn(Ve(t.b))-o))),a:e.a+n*(t.a-e.a)}}function Pi(e,t,n){if(e){let s=Wa(e);s[t]=Math.max(0,Math.min(s[t]+s[t]*n,t===0?360:1)),s=Ua(s),e.r=s[0],e.g=s[1],e.b=s[2]}}function Zh(e,t){return e&&Object.assign(t||{},e)}function Pc(e){var t={r:0,g:0,b:0,a:255};return Array.isArray(e)?e.length>=3&&(t={r:e[0],g:e[1],b:e[2],a:255},e.length>3&&(t.a=dn(e[3]))):(t=Zh(e,{r:0,g:0,b:0,a:1}),t.a=dn(t.a)),t}function L_(e){return e.charAt(0)==="r"?R_(e):C_(e)}class ii{constructor(t){if(t instanceof ii)return t;const n=typeof t;let s;n==="object"?s=Pc(t):n==="string"&&(s=m_(t)||A_(t)||L_(t)),this._rgb=s,this._valid=!!s}get valid(){return this._valid}get rgb(){var t=Zh(this._rgb);return t&&(t.a=Ve(t.a)),t}set rgb(t){this._rgb=Pc(t)}rgbString(){return this._valid?T_(this._rgb):void 0}hexString(){return this._valid?y_(this._rgb):void 0}hslString(){return this._valid?E_(this._rgb):void 0}mix(t,n){if(t){const s=this.rgb,i=t.rgb;let o;const r=n===o?.5:n,a=2*r-1,l=s.a-i.a,c=((a*l===-1?a:(a+l)/(1+a*l))+1)/2;o=1-c,s.r=255&c*s.r+o*i.r+.5,s.g=255&c*s.g+o*i.g+.5,s.b=255&c*s.b+o*i.b+.5,s.a=r*s.a+(1-r)*i.a,this.rgb=s}return this}interpolate(t,n){return t&&(this._rgb=D_(this._rgb,t._rgb,n)),this}clone(){return new ii(this.rgb)}alpha(t){return this._rgb.a=dn(t),this}clearer(t){const n=this._rgb;return n.a*=1-t,this}greyscale(){const t=this._rgb,n=bi(t.r*.3+t.g*.59+t.b*.11);return t.r=t.g=t.b=n,this}opaquer(t){const n=this._rgb;return n.a*=1+t,this}negate(){const t=this._rgb;return t.r=255-t.r,t.g=255-t.g,t.b=255-t.b,this}lighten(t){return Pi(this._rgb,2,t),this}darken(t){return Pi(this._rgb,2,-t),this}saturate(t){return Pi(this._rgb,1,t),this}desaturate(t){return Pi(this._rgb,1,-t),this}rotate(t){return P_(this._rgb,t),this}}/*!
 * Chart.js v4.4.9
 * https://www.chartjs.org
 * (c) 2025 Chart.js Contributors
 * Released under the MIT License
 */function Be(){}const F_=(()=>{let e=0;return()=>e++})();function it(e){return e==null}function wt(e){if(Array.isArray&&Array.isArray(e))return!0;const t=Object.prototype.toString.call(e);return t.slice(0,7)==="[object"&&t.slice(-6)==="Array]"}function ot(e){return e!==null&&Object.prototype.toString.call(e)==="[object Object]"}function Ct(e){return(typeof e=="number"||e instanceof Number)&&isFinite(+e)}function ie(e,t){return Ct(e)?e:t}function st(e,t){return typeof e>"u"?t:e}const I_=(e,t)=>typeof e=="string"&&e.endsWith("%")?parseFloat(e)/100:+e/t,td=(e,t)=>typeof e=="string"&&e.endsWith("%")?parseFloat(e)/100*t:+e;function _t(e,t,n){if(e&&typeof e.call=="function")return e.apply(n,t)}function ht(e,t,n,s){let i,o,r;if(wt(e))for(o=e.length,i=0;i<o;i++)t.call(n,e[i],i);else if(ot(e))for(r=Object.keys(e),o=r.length,i=0;i<o;i++)t.call(n,e[r[i]],r[i])}function mo(e,t){let n,s,i,o;if(!e||!t||e.length!==t.length)return!1;for(n=0,s=e.length;n<s;++n)if(i=e[n],o=t[n],i.datasetIndex!==o.datasetIndex||i.index!==o.index)return!1;return!0}function bo(e){if(wt(e))return e.map(bo);if(ot(e)){const t=Object.create(null),n=Object.keys(e),s=n.length;let i=0;for(;i<s;++i)t[n[i]]=bo(e[n[i]]);return t}return e}function ed(e){return["__proto__","prototype","constructor"].indexOf(e)===-1}function N_(e,t,n,s){if(!ed(e))return;const i=t[e],o=n[e];ot(i)&&ot(o)?oi(i,o,s):t[e]=bo(o)}function oi(e,t,n){const s=wt(t)?t:[t],i=s.length;if(!ot(e))return e;n=n||{};const o=n.merger||N_;let r;for(let a=0;a<i;++a){if(r=s[a],!ot(r))continue;const l=Object.keys(r);for(let c=0,u=l.length;c<u;++c)o(l[c],e,r,n)}return e}function Us(e,t){return oi(e,t,{merger:B_})}function B_(e,t,n){if(!ed(e))return;const s=t[e],i=n[e];ot(s)&&ot(i)?Us(s,i):Object.prototype.hasOwnProperty.call(t,e)||(t[e]=bo(i))}const Ec={"":e=>e,x:e=>e.x,y:e=>e.y};function z_(e){const t=e.split("."),n=[];let s="";for(const i of t)s+=i,s.endsWith("\\")?s=s.slice(0,-1)+".":(n.push(s),s="");return n}function H_(e){const t=z_(e);return n=>{for(const s of t){if(s==="")break;n=n&&n[s]}return n}}function bn(e,t){return(Ec[t]||(Ec[t]=H_(t)))(e)}function Ka(e){return e.charAt(0).toUpperCase()+e.slice(1)}const ri=e=>typeof e<"u",yn=e=>typeof e=="function",kc=(e,t)=>{if(e.size!==t.size)return!1;for(const n of e)if(!t.has(n))return!1;return!0};function j_(e){return e.type==="mouseup"||e.type==="click"||e.type==="contextmenu"}const vt=Math.PI,xt=2*vt,V_=xt+vt,yo=Number.POSITIVE_INFINITY,W_=vt/180,Et=vt/2,Mn=vt/4,Ac=vt*2/3,ln=Math.log10,Fe=Math.sign;function Ks(e,t,n){return Math.abs(e-t)<n}function Oc(e){const t=Math.round(e);e=Ks(e,t,e/1e3)?t:e;const n=Math.pow(10,Math.floor(ln(e))),s=e/n;return(s<=1?1:s<=2?2:s<=5?5:10)*n}function $_(e){const t=[],n=Math.sqrt(e);let s;for(s=1;s<n;s++)e%s===0&&(t.push(s),t.push(e/s));return n===(n|0)&&t.push(n),t.sort((i,o)=>i-o).pop(),t}function U_(e){return typeof e=="symbol"||typeof e=="object"&&e!==null&&!(Symbol.toPrimitive in e||"toString"in e||"valueOf"in e)}function hs(e){return!U_(e)&&!isNaN(parseFloat(e))&&isFinite(e)}function K_(e,t){const n=Math.round(e);return n-t<=e&&n+t>=e}function nd(e,t,n){let s,i,o;for(s=0,i=e.length;s<i;s++)o=e[s][n],isNaN(o)||(t.min=Math.min(t.min,o),t.max=Math.max(t.max,o))}function me(e){return e*(vt/180)}function qa(e){return e*(180/vt)}function Rc(e){if(!Ct(e))return;let t=1,n=0;for(;Math.round(e*t)/t!==e;)t*=10,n++;return n}function sd(e,t){const n=t.x-e.x,s=t.y-e.y,i=Math.sqrt(n*n+s*s);let o=Math.atan2(s,n);return o<-.5*vt&&(o+=xt),{angle:o,distance:i}}function ta(e,t){return Math.sqrt(Math.pow(t.x-e.x,2)+Math.pow(t.y-e.y,2))}function q_(e,t){return(e-t+V_)%xt-vt}function oe(e){return(e%xt+xt)%xt}function ai(e,t,n,s){const i=oe(e),o=oe(t),r=oe(n),a=oe(o-i),l=oe(r-i),c=oe(i-o),u=oe(i-r);return i===o||i===r||s&&o===r||a>l&&c<u}function Ft(e,t,n){return Math.max(t,Math.min(n,e))}function Y_(e){return Ft(e,-32768,32767)}function Ke(e,t,n,s=1e-6){return e>=Math.min(t,n)-s&&e<=Math.max(t,n)+s}function Ya(e,t,n){n=n||(r=>e[r]<t);let s=e.length-1,i=0,o;for(;s-i>1;)o=i+s>>1,n(o)?i=o:s=o;return{lo:i,hi:s}}const qe=(e,t,n,s)=>Ya(e,n,s?i=>{const o=e[i][t];return o<n||o===n&&e[i+1][t]===n}:i=>e[i][t]<n),X_=(e,t,n)=>Ya(e,n,s=>e[s][t]>=n);function G_(e,t,n){let s=0,i=e.length;for(;s<i&&e[s]<t;)s++;for(;i>s&&e[i-1]>n;)i--;return s>0||i<e.length?e.slice(s,i):e}const id=["push","pop","shift","splice","unshift"];function J_(e,t){if(e._chartjs){e._chartjs.listeners.push(t);return}Object.defineProperty(e,"_chartjs",{configurable:!0,enumerable:!1,value:{listeners:[t]}}),id.forEach(n=>{const s="_onData"+Ka(n),i=e[n];Object.defineProperty(e,n,{configurable:!0,enumerable:!1,value(...o){const r=i.apply(this,o);return e._chartjs.listeners.forEach(a=>{typeof a[s]=="function"&&a[s](...o)}),r}})})}function Tc(e,t){const n=e._chartjs;if(!n)return;const s=n.listeners,i=s.indexOf(t);i!==-1&&s.splice(i,1),!(s.length>0)&&(id.forEach(o=>{delete e[o]}),delete e._chartjs)}function od(e){const t=new Set(e);return t.size===e.length?e:Array.from(t)}const rd=function(){return typeof window>"u"?function(e){return e()}:window.requestAnimationFrame}();function ad(e,t){let n=[],s=!1;return function(...i){n=i,s||(s=!0,rd.call(window,()=>{s=!1,e.apply(t,n)}))}}function Q_(e,t){let n;return function(...s){return t?(clearTimeout(n),n=setTimeout(e,t,s)):e.apply(this,s),t}}const Xa=e=>e==="start"?"left":e==="end"?"right":"center",Bt=(e,t,n)=>e==="start"?t:e==="end"?n:(t+n)/2,Z_=(e,t,n,s)=>e===(s?"left":"right")?n:e==="center"?(t+n)/2:t;function ld(e,t,n){const s=t.length;let i=0,o=s;if(e._sorted){const{iScale:r,vScale:a,_parsed:l}=e,c=e.dataset&&e.dataset.options?e.dataset.options.spanGaps:null,u=r.axis,{min:f,max:h,minDefined:d,maxDefined:p}=r.getUserBounds();if(d){if(i=Math.min(qe(l,u,f).lo,n?s:qe(t,u,r.getPixelForValue(f)).lo),c){const g=l.slice(0,i+1).reverse().findIndex(m=>!it(m[a.axis]));i-=Math.max(0,g)}i=Ft(i,0,s-1)}if(p){let g=Math.max(qe(l,r.axis,h,!0).hi+1,n?0:qe(t,u,r.getPixelForValue(h),!0).hi+1);if(c){const m=l.slice(g-1).findIndex(y=>!it(y[a.axis]));g+=Math.max(0,m)}o=Ft(g,i,s)-i}else o=s-i}return{start:i,count:o}}function cd(e){const{xScale:t,yScale:n,_scaleRanges:s}=e,i={xmin:t.min,xmax:t.max,ymin:n.min,ymax:n.max};if(!s)return e._scaleRanges=i,!0;const o=s.xmin!==t.min||s.xmax!==t.max||s.ymin!==n.min||s.ymax!==n.max;return Object.assign(s,i),o}const Ei=e=>e===0||e===1,Dc=(e,t,n)=>-(Math.pow(2,10*(e-=1))*Math.sin((e-t)*xt/n)),Lc=(e,t,n)=>Math.pow(2,-10*e)*Math.sin((e-t)*xt/n)+1,qs={linear:e=>e,easeInQuad:e=>e*e,easeOutQuad:e=>-e*(e-2),easeInOutQuad:e=>(e/=.5)<1?.5*e*e:-.5*(--e*(e-2)-1),easeInCubic:e=>e*e*e,easeOutCubic:e=>(e-=1)*e*e+1,easeInOutCubic:e=>(e/=.5)<1?.5*e*e*e:.5*((e-=2)*e*e+2),easeInQuart:e=>e*e*e*e,easeOutQuart:e=>-((e-=1)*e*e*e-1),easeInOutQuart:e=>(e/=.5)<1?.5*e*e*e*e:-.5*((e-=2)*e*e*e-2),easeInQuint:e=>e*e*e*e*e,easeOutQuint:e=>(e-=1)*e*e*e*e+1,easeInOutQuint:e=>(e/=.5)<1?.5*e*e*e*e*e:.5*((e-=2)*e*e*e*e+2),easeInSine:e=>-Math.cos(e*Et)+1,easeOutSine:e=>Math.sin(e*Et),easeInOutSine:e=>-.5*(Math.cos(vt*e)-1),easeInExpo:e=>e===0?0:Math.pow(2,10*(e-1)),easeOutExpo:e=>e===1?1:-Math.pow(2,-10*e)+1,easeInOutExpo:e=>Ei(e)?e:e<.5?.5*Math.pow(2,10*(e*2-1)):.5*(-Math.pow(2,-10*(e*2-1))+2),easeInCirc:e=>e>=1?e:-(Math.sqrt(1-e*e)-1),easeOutCirc:e=>Math.sqrt(1-(e-=1)*e),easeInOutCirc:e=>(e/=.5)<1?-.5*(Math.sqrt(1-e*e)-1):.5*(Math.sqrt(1-(e-=2)*e)+1),easeInElastic:e=>Ei(e)?e:Dc(e,.075,.3),easeOutElastic:e=>Ei(e)?e:Lc(e,.075,.3),easeInOutElastic(e){return Ei(e)?e:e<.5?.5*Dc(e*2,.1125,.45):.5+.5*Lc(e*2-1,.1125,.45)},easeInBack(e){return e*e*((1.70158+1)*e-1.70158)},easeOutBack(e){return(e-=1)*e*((1.70158+1)*e********)+1},easeInOutBack(e){let t=1.70158;return(e/=.5)<1?.5*(e*e*(((t*=1.525)+1)*e-t)):.5*((e-=2)*e*(((t*=1.525)+1)*e+t)+2)},easeInBounce:e=>1-qs.easeOutBounce(1-e),easeOutBounce(e){return e<1/2.75?7.5625*e*e:e<2/2.75?7.5625*(e-=1.5/2.75)*e+.75:e<2.5/2.75?7.5625*(e-=2.25/2.75)*e+.9375:7.5625*(e-=2.625/2.75)*e+.984375},easeInOutBounce:e=>e<.5?qs.easeInBounce(e*2)*.5:qs.easeOutBounce(e*2-1)*.5+.5};function Ga(e){if(e&&typeof e=="object"){const t=e.toString();return t==="[object CanvasPattern]"||t==="[object CanvasGradient]"}return!1}function Fc(e){return Ga(e)?e:new ii(e)}function mr(e){return Ga(e)?e:new ii(e).saturate(.5).darken(.1).hexString()}const tx=["x","y","borderWidth","radius","tension"],ex=["color","borderColor","backgroundColor"];function nx(e){e.set("animation",{delay:void 0,duration:1e3,easing:"easeOutQuart",fn:void 0,from:void 0,loop:void 0,to:void 0,type:void 0}),e.describe("animation",{_fallback:!1,_indexable:!1,_scriptable:t=>t!=="onProgress"&&t!=="onComplete"&&t!=="fn"}),e.set("animations",{colors:{type:"color",properties:ex},numbers:{type:"number",properties:tx}}),e.describe("animations",{_fallback:"animation"}),e.set("transitions",{active:{animation:{duration:400}},resize:{animation:{duration:0}},show:{animations:{colors:{from:"transparent"},visible:{type:"boolean",duration:0}}},hide:{animations:{colors:{to:"transparent"},visible:{type:"boolean",easing:"linear",fn:t=>t|0}}}})}function sx(e){e.set("layout",{autoPadding:!0,padding:{top:0,right:0,bottom:0,left:0}})}const Ic=new Map;function ix(e,t){t=t||{};const n=e+JSON.stringify(t);let s=Ic.get(n);return s||(s=new Intl.NumberFormat(e,t),Ic.set(n,s)),s}function yi(e,t,n){return ix(t,n).format(e)}const ud={values(e){return wt(e)?e:""+e},numeric(e,t,n){if(e===0)return"0";const s=this.chart.options.locale;let i,o=e;if(n.length>1){const c=Math.max(Math.abs(n[0].value),Math.abs(n[n.length-1].value));(c<1e-4||c>1e15)&&(i="scientific"),o=ox(e,n)}const r=ln(Math.abs(o)),a=isNaN(r)?1:Math.max(Math.min(-1*Math.floor(r),20),0),l={notation:i,minimumFractionDigits:a,maximumFractionDigits:a};return Object.assign(l,this.options.ticks.format),yi(e,s,l)},logarithmic(e,t,n){if(e===0)return"0";const s=n[t].significand||e/Math.pow(10,Math.floor(ln(e)));return[1,2,3,5,10,15].includes(s)||t>.8*n.length?ud.numeric.call(this,e,t,n):""}};function ox(e,t){let n=t.length>3?t[2].value-t[1].value:t[1].value-t[0].value;return Math.abs(n)>=1&&e!==Math.floor(e)&&(n=e-Math.floor(e)),n}var Uo={formatters:ud};function rx(e){e.set("scale",{display:!0,offset:!1,reverse:!1,beginAtZero:!1,bounds:"ticks",clip:!0,grace:0,grid:{display:!0,lineWidth:1,drawOnChartArea:!0,drawTicks:!0,tickLength:8,tickWidth:(t,n)=>n.lineWidth,tickColor:(t,n)=>n.color,offset:!1},border:{display:!0,dash:[],dashOffset:0,width:1},title:{display:!1,text:"",padding:{top:4,bottom:4}},ticks:{minRotation:0,maxRotation:50,mirror:!1,textStrokeWidth:0,textStrokeColor:"",padding:3,display:!0,autoSkip:!0,autoSkipPadding:3,labelOffset:0,callback:Uo.formatters.values,minor:{},major:{},align:"center",crossAlign:"near",showLabelBackdrop:!1,backdropColor:"rgba(255, 255, 255, 0.75)",backdropPadding:2}}),e.route("scale.ticks","color","","color"),e.route("scale.grid","color","","borderColor"),e.route("scale.border","color","","borderColor"),e.route("scale.title","color","","color"),e.describe("scale",{_fallback:!1,_scriptable:t=>!t.startsWith("before")&&!t.startsWith("after")&&t!=="callback"&&t!=="parser",_indexable:t=>t!=="borderDash"&&t!=="tickBorderDash"&&t!=="dash"}),e.describe("scales",{_fallback:"scale"}),e.describe("scale.ticks",{_scriptable:t=>t!=="backdropPadding"&&t!=="callback",_indexable:t=>t!=="backdropPadding"})}const Wn=Object.create(null),ea=Object.create(null);function Ys(e,t){if(!t)return e;const n=t.split(".");for(let s=0,i=n.length;s<i;++s){const o=n[s];e=e[o]||(e[o]=Object.create(null))}return e}function br(e,t,n){return typeof t=="string"?oi(Ys(e,t),n):oi(Ys(e,""),t)}class ax{constructor(t,n){this.animation=void 0,this.backgroundColor="rgba(0,0,0,0.1)",this.borderColor="rgba(0,0,0,0.1)",this.color="#666",this.datasets={},this.devicePixelRatio=s=>s.chart.platform.getDevicePixelRatio(),this.elements={},this.events=["mousemove","mouseout","click","touchstart","touchmove"],this.font={family:"'Helvetica Neue', 'Helvetica', 'Arial', sans-serif",size:12,style:"normal",lineHeight:1.2,weight:null},this.hover={},this.hoverBackgroundColor=(s,i)=>mr(i.backgroundColor),this.hoverBorderColor=(s,i)=>mr(i.borderColor),this.hoverColor=(s,i)=>mr(i.color),this.indexAxis="x",this.interaction={mode:"nearest",intersect:!0,includeInvisible:!1},this.maintainAspectRatio=!0,this.onHover=null,this.onClick=null,this.parsing=!0,this.plugins={},this.responsive=!0,this.scale=void 0,this.scales={},this.showLine=!0,this.drawActiveElementsOnTop=!0,this.describe(t),this.apply(n)}set(t,n){return br(this,t,n)}get(t){return Ys(this,t)}describe(t,n){return br(ea,t,n)}override(t,n){return br(Wn,t,n)}route(t,n,s,i){const o=Ys(this,t),r=Ys(this,s),a="_"+n;Object.defineProperties(o,{[a]:{value:o[n],writable:!0},[n]:{enumerable:!0,get(){const l=this[a],c=r[i];return ot(l)?Object.assign({},c,l):st(l,c)},set(l){this[a]=l}}})}apply(t){t.forEach(n=>n(this))}}var St=new ax({_scriptable:e=>!e.startsWith("on"),_indexable:e=>e!=="events",hover:{_fallback:"interaction"},interaction:{_scriptable:!1,_indexable:!1}},[nx,sx,rx]);function lx(e){return!e||it(e.size)||it(e.family)?null:(e.style?e.style+" ":"")+(e.weight?e.weight+" ":"")+e.size+"px "+e.family}function _o(e,t,n,s,i){let o=t[i];return o||(o=t[i]=e.measureText(i).width,n.push(i)),o>s&&(s=o),s}function cx(e,t,n,s){s=s||{};let i=s.data=s.data||{},o=s.garbageCollect=s.garbageCollect||[];s.font!==t&&(i=s.data={},o=s.garbageCollect=[],s.font=t),e.save(),e.font=t;let r=0;const a=n.length;let l,c,u,f,h;for(l=0;l<a;l++)if(f=n[l],f!=null&&!wt(f))r=_o(e,i,o,r,f);else if(wt(f))for(c=0,u=f.length;c<u;c++)h=f[c],h!=null&&!wt(h)&&(r=_o(e,i,o,r,h));e.restore();const d=o.length/2;if(d>n.length){for(l=0;l<d;l++)delete i[o[l]];o.splice(0,d)}return r}function Cn(e,t,n){const s=e.currentDevicePixelRatio,i=n!==0?Math.max(n/2,.5):0;return Math.round((t-i)*s)/s+i}function Nc(e,t){!t&&!e||(t=t||e.getContext("2d"),t.save(),t.resetTransform(),t.clearRect(0,0,e.width,e.height),t.restore())}function na(e,t,n,s){fd(e,t,n,s,null)}function fd(e,t,n,s,i){let o,r,a,l,c,u,f,h;const d=t.pointStyle,p=t.rotation,g=t.radius;let m=(p||0)*W_;if(d&&typeof d=="object"&&(o=d.toString(),o==="[object HTMLImageElement]"||o==="[object HTMLCanvasElement]")){e.save(),e.translate(n,s),e.rotate(m),e.drawImage(d,-d.width/2,-d.height/2,d.width,d.height),e.restore();return}if(!(isNaN(g)||g<=0)){switch(e.beginPath(),d){default:i?e.ellipse(n,s,i/2,g,0,0,xt):e.arc(n,s,g,0,xt),e.closePath();break;case"triangle":u=i?i/2:g,e.moveTo(n+Math.sin(m)*u,s-Math.cos(m)*g),m+=Ac,e.lineTo(n+Math.sin(m)*u,s-Math.cos(m)*g),m+=Ac,e.lineTo(n+Math.sin(m)*u,s-Math.cos(m)*g),e.closePath();break;case"rectRounded":c=g*.516,l=g-c,r=Math.cos(m+Mn)*l,f=Math.cos(m+Mn)*(i?i/2-c:l),a=Math.sin(m+Mn)*l,h=Math.sin(m+Mn)*(i?i/2-c:l),e.arc(n-f,s-a,c,m-vt,m-Et),e.arc(n+h,s-r,c,m-Et,m),e.arc(n+f,s+a,c,m,m+Et),e.arc(n-h,s+r,c,m+Et,m+vt),e.closePath();break;case"rect":if(!p){l=Math.SQRT1_2*g,u=i?i/2:l,e.rect(n-u,s-l,2*u,2*l);break}m+=Mn;case"rectRot":f=Math.cos(m)*(i?i/2:g),r=Math.cos(m)*g,a=Math.sin(m)*g,h=Math.sin(m)*(i?i/2:g),e.moveTo(n-f,s-a),e.lineTo(n+h,s-r),e.lineTo(n+f,s+a),e.lineTo(n-h,s+r),e.closePath();break;case"crossRot":m+=Mn;case"cross":f=Math.cos(m)*(i?i/2:g),r=Math.cos(m)*g,a=Math.sin(m)*g,h=Math.sin(m)*(i?i/2:g),e.moveTo(n-f,s-a),e.lineTo(n+f,s+a),e.moveTo(n+h,s-r),e.lineTo(n-h,s+r);break;case"star":f=Math.cos(m)*(i?i/2:g),r=Math.cos(m)*g,a=Math.sin(m)*g,h=Math.sin(m)*(i?i/2:g),e.moveTo(n-f,s-a),e.lineTo(n+f,s+a),e.moveTo(n+h,s-r),e.lineTo(n-h,s+r),m+=Mn,f=Math.cos(m)*(i?i/2:g),r=Math.cos(m)*g,a=Math.sin(m)*g,h=Math.sin(m)*(i?i/2:g),e.moveTo(n-f,s-a),e.lineTo(n+f,s+a),e.moveTo(n+h,s-r),e.lineTo(n-h,s+r);break;case"line":r=i?i/2:Math.cos(m)*g,a=Math.sin(m)*g,e.moveTo(n-r,s-a),e.lineTo(n+r,s+a);break;case"dash":e.moveTo(n,s),e.lineTo(n+Math.cos(m)*(i?i/2:g),s+Math.sin(m)*g);break;case!1:e.closePath();break}e.fill(),t.borderWidth>0&&e.stroke()}}function Ye(e,t,n){return n=n||.5,!t||e&&e.x>t.left-n&&e.x<t.right+n&&e.y>t.top-n&&e.y<t.bottom+n}function Ko(e,t){e.save(),e.beginPath(),e.rect(t.left,t.top,t.right-t.left,t.bottom-t.top),e.clip()}function qo(e){e.restore()}function ux(e,t,n,s,i){if(!t)return e.lineTo(n.x,n.y);if(i==="middle"){const o=(t.x+n.x)/2;e.lineTo(o,t.y),e.lineTo(o,n.y)}else i==="after"!=!!s?e.lineTo(t.x,n.y):e.lineTo(n.x,t.y);e.lineTo(n.x,n.y)}function fx(e,t,n,s){if(!t)return e.lineTo(n.x,n.y);e.bezierCurveTo(s?t.cp1x:t.cp2x,s?t.cp1y:t.cp2y,s?n.cp2x:n.cp1x,s?n.cp2y:n.cp1y,n.x,n.y)}function hx(e,t){t.translation&&e.translate(t.translation[0],t.translation[1]),it(t.rotation)||e.rotate(t.rotation),t.color&&(e.fillStyle=t.color),t.textAlign&&(e.textAlign=t.textAlign),t.textBaseline&&(e.textBaseline=t.textBaseline)}function dx(e,t,n,s,i){if(i.strikethrough||i.underline){const o=e.measureText(s),r=t-o.actualBoundingBoxLeft,a=t+o.actualBoundingBoxRight,l=n-o.actualBoundingBoxAscent,c=n+o.actualBoundingBoxDescent,u=i.strikethrough?(l+c)/2:c;e.strokeStyle=e.fillStyle,e.beginPath(),e.lineWidth=i.decorationWidth||2,e.moveTo(r,u),e.lineTo(a,u),e.stroke()}}function px(e,t){const n=e.fillStyle;e.fillStyle=t.color,e.fillRect(t.left,t.top,t.width,t.height),e.fillStyle=n}function $n(e,t,n,s,i,o={}){const r=wt(t)?t:[t],a=o.strokeWidth>0&&o.strokeColor!=="";let l,c;for(e.save(),e.font=i.string,hx(e,o),l=0;l<r.length;++l)c=r[l],o.backdrop&&px(e,o.backdrop),a&&(o.strokeColor&&(e.strokeStyle=o.strokeColor),it(o.strokeWidth)||(e.lineWidth=o.strokeWidth),e.strokeText(c,n,s,o.maxWidth)),e.fillText(c,n,s,o.maxWidth),dx(e,n,s,c,o),s+=Number(i.lineHeight);e.restore()}function li(e,t){const{x:n,y:s,w:i,h:o,radius:r}=t;e.arc(n+r.topLeft,s+r.topLeft,r.topLeft,1.5*vt,vt,!0),e.lineTo(n,s+o-r.bottomLeft),e.arc(n+r.bottomLeft,s+o-r.bottomLeft,r.bottomLeft,vt,Et,!0),e.lineTo(n+i-r.bottomRight,s+o),e.arc(n+i-r.bottomRight,s+o-r.bottomRight,r.bottomRight,Et,0,!0),e.lineTo(n+i,s+r.topRight),e.arc(n+i-r.topRight,s+r.topRight,r.topRight,0,-Et,!0),e.lineTo(n+r.topLeft,s)}const gx=/^(normal|(\d+(?:\.\d+)?)(px|em|%)?)$/,mx=/^(normal|italic|initial|inherit|unset|(oblique( -?[0-9]?[0-9]deg)?))$/;function bx(e,t){const n=(""+e).match(gx);if(!n||n[1]==="normal")return t*1.2;switch(e=+n[2],n[3]){case"px":return e;case"%":e/=100;break}return t*e}const yx=e=>+e||0;function Ja(e,t){const n={},s=ot(t),i=s?Object.keys(t):t,o=ot(e)?s?r=>st(e[r],e[t[r]]):r=>e[r]:()=>e;for(const r of i)n[r]=yx(o(r));return n}function hd(e){return Ja(e,{top:"y",right:"x",bottom:"y",left:"x"})}function zn(e){return Ja(e,["topLeft","topRight","bottomLeft","bottomRight"])}function $t(e){const t=hd(e);return t.width=t.left+t.right,t.height=t.top+t.bottom,t}function Tt(e,t){e=e||{},t=t||St.font;let n=st(e.size,t.size);typeof n=="string"&&(n=parseInt(n,10));let s=st(e.style,t.style);s&&!(""+s).match(mx)&&(console.warn('Invalid font style specified: "'+s+'"'),s=void 0);const i={family:st(e.family,t.family),lineHeight:bx(st(e.lineHeight,t.lineHeight),n),size:n,style:s,weight:st(e.weight,t.weight),string:""};return i.string=lx(i),i}function Ts(e,t,n,s){let i,o,r;for(i=0,o=e.length;i<o;++i)if(r=e[i],r!==void 0&&r!==void 0)return r}function _x(e,t,n){const{min:s,max:i}=e,o=td(t,(i-s)/2),r=(a,l)=>n&&a===0?0:a+l;return{min:r(s,-Math.abs(o)),max:r(i,o)}}function xn(e,t){return Object.assign(Object.create(e),t)}function Qa(e,t=[""],n,s,i=()=>e[0]){const o=n||e;typeof s>"u"&&(s=md("_fallback",e));const r={[Symbol.toStringTag]:"Object",_cacheable:!0,_scopes:e,_rootScopes:o,_fallback:s,_getTarget:i,override:a=>Qa([a,...e],t,o,s)};return new Proxy(r,{deleteProperty(a,l){return delete a[l],delete a._keys,delete e[0][l],!0},get(a,l){return pd(a,l,()=>Ex(l,t,e,a))},getOwnPropertyDescriptor(a,l){return Reflect.getOwnPropertyDescriptor(a._scopes[0],l)},getPrototypeOf(){return Reflect.getPrototypeOf(e[0])},has(a,l){return zc(a).includes(l)},ownKeys(a){return zc(a)},set(a,l,c){const u=a._storage||(a._storage=i());return a[l]=u[l]=c,delete a._keys,!0}})}function ds(e,t,n,s){const i={_cacheable:!1,_proxy:e,_context:t,_subProxy:n,_stack:new Set,_descriptors:dd(e,s),setContext:o=>ds(e,o,n,s),override:o=>ds(e.override(o),t,n,s)};return new Proxy(i,{deleteProperty(o,r){return delete o[r],delete e[r],!0},get(o,r,a){return pd(o,r,()=>vx(o,r,a))},getOwnPropertyDescriptor(o,r){return o._descriptors.allKeys?Reflect.has(e,r)?{enumerable:!0,configurable:!0}:void 0:Reflect.getOwnPropertyDescriptor(e,r)},getPrototypeOf(){return Reflect.getPrototypeOf(e)},has(o,r){return Reflect.has(e,r)},ownKeys(){return Reflect.ownKeys(e)},set(o,r,a){return e[r]=a,delete o[r],!0}})}function dd(e,t={scriptable:!0,indexable:!0}){const{_scriptable:n=t.scriptable,_indexable:s=t.indexable,_allKeys:i=t.allKeys}=e;return{allKeys:i,scriptable:n,indexable:s,isScriptable:yn(n)?n:()=>n,isIndexable:yn(s)?s:()=>s}}const xx=(e,t)=>e?e+Ka(t):t,Za=(e,t)=>ot(t)&&e!=="adapters"&&(Object.getPrototypeOf(t)===null||t.constructor===Object);function pd(e,t,n){if(Object.prototype.hasOwnProperty.call(e,t)||t==="constructor")return e[t];const s=n();return e[t]=s,s}function vx(e,t,n){const{_proxy:s,_context:i,_subProxy:o,_descriptors:r}=e;let a=s[t];return yn(a)&&r.isScriptable(t)&&(a=wx(t,a,e,n)),wt(a)&&a.length&&(a=Sx(t,a,e,r.isIndexable)),Za(t,a)&&(a=ds(a,i,o&&o[t],r)),a}function wx(e,t,n,s){const{_proxy:i,_context:o,_subProxy:r,_stack:a}=n;if(a.has(e))throw new Error("Recursion detected: "+Array.from(a).join("->")+"->"+e);a.add(e);let l=t(o,r||s);return a.delete(e),Za(e,l)&&(l=tl(i._scopes,i,e,l)),l}function Sx(e,t,n,s){const{_proxy:i,_context:o,_subProxy:r,_descriptors:a}=n;if(typeof o.index<"u"&&s(e))return t[o.index%t.length];if(ot(t[0])){const l=t,c=i._scopes.filter(u=>u!==l);t=[];for(const u of l){const f=tl(c,i,e,u);t.push(ds(f,o,r&&r[e],a))}}return t}function gd(e,t,n){return yn(e)?e(t,n):e}const Mx=(e,t)=>e===!0?t:typeof e=="string"?bn(t,e):void 0;function Cx(e,t,n,s,i){for(const o of t){const r=Mx(n,o);if(r){e.add(r);const a=gd(r._fallback,n,i);if(typeof a<"u"&&a!==n&&a!==s)return a}else if(r===!1&&typeof s<"u"&&n!==s)return null}return!1}function tl(e,t,n,s){const i=t._rootScopes,o=gd(t._fallback,n,s),r=[...e,...i],a=new Set;a.add(s);let l=Bc(a,r,n,o||n,s);return l===null||typeof o<"u"&&o!==n&&(l=Bc(a,r,o,l,s),l===null)?!1:Qa(Array.from(a),[""],i,o,()=>Px(t,n,s))}function Bc(e,t,n,s,i){for(;n;)n=Cx(e,t,n,s,i);return n}function Px(e,t,n){const s=e._getTarget();t in s||(s[t]={});const i=s[t];return wt(i)&&ot(n)?n:i||{}}function Ex(e,t,n,s){let i;for(const o of t)if(i=md(xx(o,e),n),typeof i<"u")return Za(e,i)?tl(n,s,e,i):i}function md(e,t){for(const n of t){if(!n)continue;const s=n[e];if(typeof s<"u")return s}}function zc(e){let t=e._keys;return t||(t=e._keys=kx(e._scopes)),t}function kx(e){const t=new Set;for(const n of e)for(const s of Object.keys(n).filter(i=>!i.startsWith("_")))t.add(s);return Array.from(t)}function bd(e,t,n,s){const{iScale:i}=e,{key:o="r"}=this._parsing,r=new Array(s);let a,l,c,u;for(a=0,l=s;a<l;++a)c=a+n,u=t[c],r[a]={r:i.parse(bn(u,o),c)};return r}const Ax=Number.EPSILON||1e-14,ps=(e,t)=>t<e.length&&!e[t].skip&&e[t],yd=e=>e==="x"?"y":"x";function Ox(e,t,n,s){const i=e.skip?t:e,o=t,r=n.skip?t:n,a=ta(o,i),l=ta(r,o);let c=a/(a+l),u=l/(a+l);c=isNaN(c)?0:c,u=isNaN(u)?0:u;const f=s*c,h=s*u;return{previous:{x:o.x-f*(r.x-i.x),y:o.y-f*(r.y-i.y)},next:{x:o.x+h*(r.x-i.x),y:o.y+h*(r.y-i.y)}}}function Rx(e,t,n){const s=e.length;let i,o,r,a,l,c=ps(e,0);for(let u=0;u<s-1;++u)if(l=c,c=ps(e,u+1),!(!l||!c)){if(Ks(t[u],0,Ax)){n[u]=n[u+1]=0;continue}i=n[u]/t[u],o=n[u+1]/t[u],a=Math.pow(i,2)+Math.pow(o,2),!(a<=9)&&(r=3/Math.sqrt(a),n[u]=i*r*t[u],n[u+1]=o*r*t[u])}}function Tx(e,t,n="x"){const s=yd(n),i=e.length;let o,r,a,l=ps(e,0);for(let c=0;c<i;++c){if(r=a,a=l,l=ps(e,c+1),!a)continue;const u=a[n],f=a[s];r&&(o=(u-r[n])/3,a[`cp1${n}`]=u-o,a[`cp1${s}`]=f-o*t[c]),l&&(o=(l[n]-u)/3,a[`cp2${n}`]=u+o,a[`cp2${s}`]=f+o*t[c])}}function Dx(e,t="x"){const n=yd(t),s=e.length,i=Array(s).fill(0),o=Array(s);let r,a,l,c=ps(e,0);for(r=0;r<s;++r)if(a=l,l=c,c=ps(e,r+1),!!l){if(c){const u=c[t]-l[t];i[r]=u!==0?(c[n]-l[n])/u:0}o[r]=a?c?Fe(i[r-1])!==Fe(i[r])?0:(i[r-1]+i[r])/2:i[r-1]:i[r]}Rx(e,i,o),Tx(e,o,t)}function ki(e,t,n){return Math.max(Math.min(e,n),t)}function Lx(e,t){let n,s,i,o,r,a=Ye(e[0],t);for(n=0,s=e.length;n<s;++n)r=o,o=a,a=n<s-1&&Ye(e[n+1],t),o&&(i=e[n],r&&(i.cp1x=ki(i.cp1x,t.left,t.right),i.cp1y=ki(i.cp1y,t.top,t.bottom)),a&&(i.cp2x=ki(i.cp2x,t.left,t.right),i.cp2y=ki(i.cp2y,t.top,t.bottom)))}function Fx(e,t,n,s,i){let o,r,a,l;if(t.spanGaps&&(e=e.filter(c=>!c.skip)),t.cubicInterpolationMode==="monotone")Dx(e,i);else{let c=s?e[e.length-1]:e[0];for(o=0,r=e.length;o<r;++o)a=e[o],l=Ox(c,a,e[Math.min(o+1,r-(s?0:1))%r],t.tension),a.cp1x=l.previous.x,a.cp1y=l.previous.y,a.cp2x=l.next.x,a.cp2y=l.next.y,c=a}t.capBezierPoints&&Lx(e,n)}function el(){return typeof window<"u"&&typeof document<"u"}function nl(e){let t=e.parentNode;return t&&t.toString()==="[object ShadowRoot]"&&(t=t.host),t}function xo(e,t,n){let s;return typeof e=="string"?(s=parseInt(e,10),e.indexOf("%")!==-1&&(s=s/100*t.parentNode[n])):s=e,s}const Yo=e=>e.ownerDocument.defaultView.getComputedStyle(e,null);function Ix(e,t){return Yo(e).getPropertyValue(t)}const Nx=["top","right","bottom","left"];function Hn(e,t,n){const s={};n=n?"-"+n:"";for(let i=0;i<4;i++){const o=Nx[i];s[o]=parseFloat(e[t+"-"+o+n])||0}return s.width=s.left+s.right,s.height=s.top+s.bottom,s}const Bx=(e,t,n)=>(e>0||t>0)&&(!n||!n.shadowRoot);function zx(e,t){const n=e.touches,s=n&&n.length?n[0]:e,{offsetX:i,offsetY:o}=s;let r=!1,a,l;if(Bx(i,o,e.target))a=i,l=o;else{const c=t.getBoundingClientRect();a=s.clientX-c.left,l=s.clientY-c.top,r=!0}return{x:a,y:l,box:r}}function An(e,t){if("native"in e)return e;const{canvas:n,currentDevicePixelRatio:s}=t,i=Yo(n),o=i.boxSizing==="border-box",r=Hn(i,"padding"),a=Hn(i,"border","width"),{x:l,y:c,box:u}=zx(e,n),f=r.left+(u&&a.left),h=r.top+(u&&a.top);let{width:d,height:p}=t;return o&&(d-=r.width+a.width,p-=r.height+a.height),{x:Math.round((l-f)/d*n.width/s),y:Math.round((c-h)/p*n.height/s)}}function Hx(e,t,n){let s,i;if(t===void 0||n===void 0){const o=e&&nl(e);if(!o)t=e.clientWidth,n=e.clientHeight;else{const r=o.getBoundingClientRect(),a=Yo(o),l=Hn(a,"border","width"),c=Hn(a,"padding");t=r.width-c.width-l.width,n=r.height-c.height-l.height,s=xo(a.maxWidth,o,"clientWidth"),i=xo(a.maxHeight,o,"clientHeight")}}return{width:t,height:n,maxWidth:s||yo,maxHeight:i||yo}}const Ai=e=>Math.round(e*10)/10;function jx(e,t,n,s){const i=Yo(e),o=Hn(i,"margin"),r=xo(i.maxWidth,e,"clientWidth")||yo,a=xo(i.maxHeight,e,"clientHeight")||yo,l=Hx(e,t,n);let{width:c,height:u}=l;if(i.boxSizing==="content-box"){const h=Hn(i,"border","width"),d=Hn(i,"padding");c-=d.width+h.width,u-=d.height+h.height}return c=Math.max(0,c-o.width),u=Math.max(0,s?c/s:u-o.height),c=Ai(Math.min(c,r,l.maxWidth)),u=Ai(Math.min(u,a,l.maxHeight)),c&&!u&&(u=Ai(c/2)),(t!==void 0||n!==void 0)&&s&&l.height&&u>l.height&&(u=l.height,c=Ai(Math.floor(u*s))),{width:c,height:u}}function Hc(e,t,n){const s=t||1,i=Math.floor(e.height*s),o=Math.floor(e.width*s);e.height=Math.floor(e.height),e.width=Math.floor(e.width);const r=e.canvas;return r.style&&(n||!r.style.height&&!r.style.width)&&(r.style.height=`${e.height}px`,r.style.width=`${e.width}px`),e.currentDevicePixelRatio!==s||r.height!==i||r.width!==o?(e.currentDevicePixelRatio=s,r.height=i,r.width=o,e.ctx.setTransform(s,0,0,s,0,0),!0):!1}const Vx=function(){let e=!1;try{const t={get passive(){return e=!0,!1}};el()&&(window.addEventListener("test",null,t),window.removeEventListener("test",null,t))}catch{}return e}();function jc(e,t){const n=Ix(e,t),s=n&&n.match(/^(\d+)(\.\d+)?px$/);return s?+s[1]:void 0}function On(e,t,n,s){return{x:e.x+n*(t.x-e.x),y:e.y+n*(t.y-e.y)}}function Wx(e,t,n,s){return{x:e.x+n*(t.x-e.x),y:s==="middle"?n<.5?e.y:t.y:s==="after"?n<1?e.y:t.y:n>0?t.y:e.y}}function $x(e,t,n,s){const i={x:e.cp2x,y:e.cp2y},o={x:t.cp1x,y:t.cp1y},r=On(e,i,n),a=On(i,o,n),l=On(o,t,n),c=On(r,a,n),u=On(a,l,n);return On(c,u,n)}const Ux=function(e,t){return{x(n){return e+e+t-n},setWidth(n){t=n},textAlign(n){return n==="center"?n:n==="right"?"left":"right"},xPlus(n,s){return n-s},leftForLtr(n,s){return n-s}}},Kx=function(){return{x(e){return e},setWidth(e){},textAlign(e){return e},xPlus(e,t){return e+t},leftForLtr(e,t){return e}}};function os(e,t,n){return e?Ux(t,n):Kx()}function _d(e,t){let n,s;(t==="ltr"||t==="rtl")&&(n=e.canvas.style,s=[n.getPropertyValue("direction"),n.getPropertyPriority("direction")],n.setProperty("direction",t,"important"),e.prevTextDirection=s)}function xd(e,t){t!==void 0&&(delete e.prevTextDirection,e.canvas.style.setProperty("direction",t[0],t[1]))}function vd(e){return e==="angle"?{between:ai,compare:q_,normalize:oe}:{between:Ke,compare:(t,n)=>t-n,normalize:t=>t}}function Vc({start:e,end:t,count:n,loop:s,style:i}){return{start:e%n,end:t%n,loop:s&&(t-e+1)%n===0,style:i}}function qx(e,t,n){const{property:s,start:i,end:o}=n,{between:r,normalize:a}=vd(s),l=t.length;let{start:c,end:u,loop:f}=e,h,d;if(f){for(c+=l,u+=l,h=0,d=l;h<d&&r(a(t[c%l][s]),i,o);++h)c--,u--;c%=l,u%=l}return u<c&&(u+=l),{start:c,end:u,loop:f,style:e.style}}function wd(e,t,n){if(!n)return[e];const{property:s,start:i,end:o}=n,r=t.length,{compare:a,between:l,normalize:c}=vd(s),{start:u,end:f,loop:h,style:d}=qx(e,t,n),p=[];let g=!1,m=null,y,b,w;const v=()=>l(i,w,y)&&a(i,w)!==0,S=()=>a(o,y)===0||l(o,w,y),A=()=>g||v(),P=()=>!g||S();for(let E=u,k=u;E<=f;++E)b=t[E%r],!b.skip&&(y=c(b[s]),y!==w&&(g=l(y,i,o),m===null&&A()&&(m=a(y,i)===0?E:k),m!==null&&P()&&(p.push(Vc({start:m,end:E,loop:h,count:r,style:d})),m=null),k=E,w=y));return m!==null&&p.push(Vc({start:m,end:f,loop:h,count:r,style:d})),p}function Sd(e,t){const n=[],s=e.segments;for(let i=0;i<s.length;i++){const o=wd(s[i],e.points,t);o.length&&n.push(...o)}return n}function Yx(e,t,n,s){let i=0,o=t-1;if(n&&!s)for(;i<t&&!e[i].skip;)i++;for(;i<t&&e[i].skip;)i++;for(i%=t,n&&(o+=i);o>i&&e[o%t].skip;)o--;return o%=t,{start:i,end:o}}function Xx(e,t,n,s){const i=e.length,o=[];let r=t,a=e[t],l;for(l=t+1;l<=n;++l){const c=e[l%i];c.skip||c.stop?a.skip||(s=!1,o.push({start:t%i,end:(l-1)%i,loop:s}),t=r=c.stop?l:null):(r=l,a.skip&&(t=l)),a=c}return r!==null&&o.push({start:t%i,end:r%i,loop:s}),o}function Gx(e,t){const n=e.points,s=e.options.spanGaps,i=n.length;if(!i)return[];const o=!!e._loop,{start:r,end:a}=Yx(n,i,o,s);if(s===!0)return Wc(e,[{start:r,end:a,loop:o}],n,t);const l=a<r?a+i:a,c=!!e._fullLoop&&r===0&&a===i-1;return Wc(e,Xx(n,r,l,c),n,t)}function Wc(e,t,n,s){return!s||!s.setContext||!n?t:Jx(e,t,n,s)}function Jx(e,t,n,s){const i=e._chart.getContext(),o=$c(e.options),{_datasetIndex:r,options:{spanGaps:a}}=e,l=n.length,c=[];let u=o,f=t[0].start,h=f;function d(p,g,m,y){const b=a?-1:1;if(p!==g){for(p+=l;n[p%l].skip;)p-=b;for(;n[g%l].skip;)g+=b;p%l!==g%l&&(c.push({start:p%l,end:g%l,loop:m,style:y}),u=y,f=g%l)}}for(const p of t){f=a?f:p.start;let g=n[f%l],m;for(h=f+1;h<=p.end;h++){const y=n[h%l];m=$c(s.setContext(xn(i,{type:"segment",p0:g,p1:y,p0DataIndex:(h-1)%l,p1DataIndex:h%l,datasetIndex:r}))),Qx(m,u)&&d(f,h-1,p.loop,u),g=y,u=m}f<h-1&&d(f,h-1,p.loop,u)}return c}function $c(e){return{backgroundColor:e.backgroundColor,borderCapStyle:e.borderCapStyle,borderDash:e.borderDash,borderDashOffset:e.borderDashOffset,borderJoinStyle:e.borderJoinStyle,borderWidth:e.borderWidth,borderColor:e.borderColor}}function Qx(e,t){if(!t)return!1;const n=[],s=function(i,o){return Ga(o)?(n.includes(o)||n.push(o),n.indexOf(o)):o};return JSON.stringify(e,s)!==JSON.stringify(t,s)}function Oi(e,t,n){return e.options.clip?e[n]:t[n]}function Zx(e,t){const{xScale:n,yScale:s}=e;return n&&s?{left:Oi(n,t,"left"),right:Oi(n,t,"right"),top:Oi(s,t,"top"),bottom:Oi(s,t,"bottom")}:t}function Md(e,t){const n=t._clip;if(n.disabled)return!1;const s=Zx(t,e.chartArea);return{left:n.left===!1?0:s.left-(n.left===!0?0:n.left),right:n.right===!1?e.width:s.right+(n.right===!0?0:n.right),top:n.top===!1?0:s.top-(n.top===!0?0:n.top),bottom:n.bottom===!1?e.height:s.bottom+(n.bottom===!0?0:n.bottom)}}/*!
 * Chart.js v4.4.9
 * https://www.chartjs.org
 * (c) 2025 Chart.js Contributors
 * Released under the MIT License
 */class t0{constructor(){this._request=null,this._charts=new Map,this._running=!1,this._lastDate=void 0}_notify(t,n,s,i){const o=n.listeners[i],r=n.duration;o.forEach(a=>a({chart:t,initial:n.initial,numSteps:r,currentStep:Math.min(s-n.start,r)}))}_refresh(){this._request||(this._running=!0,this._request=rd.call(window,()=>{this._update(),this._request=null,this._running&&this._refresh()}))}_update(t=Date.now()){let n=0;this._charts.forEach((s,i)=>{if(!s.running||!s.items.length)return;const o=s.items;let r=o.length-1,a=!1,l;for(;r>=0;--r)l=o[r],l._active?(l._total>s.duration&&(s.duration=l._total),l.tick(t),a=!0):(o[r]=o[o.length-1],o.pop());a&&(i.draw(),this._notify(i,s,t,"progress")),o.length||(s.running=!1,this._notify(i,s,t,"complete"),s.initial=!1),n+=o.length}),this._lastDate=t,n===0&&(this._running=!1)}_getAnims(t){const n=this._charts;let s=n.get(t);return s||(s={running:!1,initial:!0,items:[],listeners:{complete:[],progress:[]}},n.set(t,s)),s}listen(t,n,s){this._getAnims(t).listeners[n].push(s)}add(t,n){!n||!n.length||this._getAnims(t).items.push(...n)}has(t){return this._getAnims(t).items.length>0}start(t){const n=this._charts.get(t);n&&(n.running=!0,n.start=Date.now(),n.duration=n.items.reduce((s,i)=>Math.max(s,i._duration),0),this._refresh())}running(t){if(!this._running)return!1;const n=this._charts.get(t);return!(!n||!n.running||!n.items.length)}stop(t){const n=this._charts.get(t);if(!n||!n.items.length)return;const s=n.items;let i=s.length-1;for(;i>=0;--i)s[i].cancel();n.items=[],this._notify(t,n,Date.now(),"complete")}remove(t){return this._charts.delete(t)}}var ze=new t0;const Uc="transparent",e0={boolean(e,t,n){return n>.5?t:e},color(e,t,n){const s=Fc(e||Uc),i=s.valid&&Fc(t||Uc);return i&&i.valid?i.mix(s,n).hexString():t},number(e,t,n){return e+(t-e)*n}};class n0{constructor(t,n,s,i){const o=n[s];i=Ts([t.to,i,o,t.from]);const r=Ts([t.from,o,i]);this._active=!0,this._fn=t.fn||e0[t.type||typeof r],this._easing=qs[t.easing]||qs.linear,this._start=Math.floor(Date.now()+(t.delay||0)),this._duration=this._total=Math.floor(t.duration),this._loop=!!t.loop,this._target=n,this._prop=s,this._from=r,this._to=i,this._promises=void 0}active(){return this._active}update(t,n,s){if(this._active){this._notify(!1);const i=this._target[this._prop],o=s-this._start,r=this._duration-o;this._start=s,this._duration=Math.floor(Math.max(r,t.duration)),this._total+=o,this._loop=!!t.loop,this._to=Ts([t.to,n,i,t.from]),this._from=Ts([t.from,i,n])}}cancel(){this._active&&(this.tick(Date.now()),this._active=!1,this._notify(!1))}tick(t){const n=t-this._start,s=this._duration,i=this._prop,o=this._from,r=this._loop,a=this._to;let l;if(this._active=o!==a&&(r||n<s),!this._active){this._target[i]=a,this._notify(!0);return}if(n<0){this._target[i]=o;return}l=n/s%2,l=r&&l>1?2-l:l,l=this._easing(Math.min(1,Math.max(0,l))),this._target[i]=this._fn(o,a,l)}wait(){const t=this._promises||(this._promises=[]);return new Promise((n,s)=>{t.push({res:n,rej:s})})}_notify(t){const n=t?"res":"rej",s=this._promises||[];for(let i=0;i<s.length;i++)s[i][n]()}}class Cd{constructor(t,n){this._chart=t,this._properties=new Map,this.configure(n)}configure(t){if(!ot(t))return;const n=Object.keys(St.animation),s=this._properties;Object.getOwnPropertyNames(t).forEach(i=>{const o=t[i];if(!ot(o))return;const r={};for(const a of n)r[a]=o[a];(wt(o.properties)&&o.properties||[i]).forEach(a=>{(a===i||!s.has(a))&&s.set(a,r)})})}_animateOptions(t,n){const s=n.options,i=i0(t,s);if(!i)return[];const o=this._createAnimations(i,s);return s.$shared&&s0(t.options.$animations,s).then(()=>{t.options=s},()=>{}),o}_createAnimations(t,n){const s=this._properties,i=[],o=t.$animations||(t.$animations={}),r=Object.keys(n),a=Date.now();let l;for(l=r.length-1;l>=0;--l){const c=r[l];if(c.charAt(0)==="$")continue;if(c==="options"){i.push(...this._animateOptions(t,n));continue}const u=n[c];let f=o[c];const h=s.get(c);if(f)if(h&&f.active()){f.update(h,u,a);continue}else f.cancel();if(!h||!h.duration){t[c]=u;continue}o[c]=f=new n0(h,t,c,u),i.push(f)}return i}update(t,n){if(this._properties.size===0){Object.assign(t,n);return}const s=this._createAnimations(t,n);if(s.length)return ze.add(this._chart,s),!0}}function s0(e,t){const n=[],s=Object.keys(t);for(let i=0;i<s.length;i++){const o=e[s[i]];o&&o.active()&&n.push(o.wait())}return Promise.all(n)}function i0(e,t){if(!t)return;let n=e.options;if(!n){e.options=t;return}return n.$shared&&(e.options=n=Object.assign({},n,{$shared:!1,$animations:{}})),n}function Kc(e,t){const n=e&&e.options||{},s=n.reverse,i=n.min===void 0?t:0,o=n.max===void 0?t:0;return{start:s?o:i,end:s?i:o}}function o0(e,t,n){if(n===!1)return!1;const s=Kc(e,n),i=Kc(t,n);return{top:i.end,right:s.end,bottom:i.start,left:s.start}}function r0(e){let t,n,s,i;return ot(e)?(t=e.top,n=e.right,s=e.bottom,i=e.left):t=n=s=i=e,{top:t,right:n,bottom:s,left:i,disabled:e===!1}}function Pd(e,t){const n=[],s=e._getSortedDatasetMetas(t);let i,o;for(i=0,o=s.length;i<o;++i)n.push(s[i].index);return n}function qc(e,t,n,s={}){const i=e.keys,o=s.mode==="single";let r,a,l,c;if(t===null)return;let u=!1;for(r=0,a=i.length;r<a;++r){if(l=+i[r],l===n){if(u=!0,s.all)continue;break}c=e.values[l],Ct(c)&&(o||t===0||Fe(t)===Fe(c))&&(t+=c)}return!u&&!s.all?0:t}function a0(e,t){const{iScale:n,vScale:s}=t,i=n.axis==="x"?"x":"y",o=s.axis==="x"?"x":"y",r=Object.keys(e),a=new Array(r.length);let l,c,u;for(l=0,c=r.length;l<c;++l)u=r[l],a[l]={[i]:u,[o]:e[u]};return a}function yr(e,t){const n=e&&e.options.stacked;return n||n===void 0&&t.stack!==void 0}function l0(e,t,n){return`${e.id}.${t.id}.${n.stack||n.type}`}function c0(e){const{min:t,max:n,minDefined:s,maxDefined:i}=e.getUserBounds();return{min:s?t:Number.NEGATIVE_INFINITY,max:i?n:Number.POSITIVE_INFINITY}}function u0(e,t,n){const s=e[t]||(e[t]={});return s[n]||(s[n]={})}function Yc(e,t,n,s){for(const i of t.getMatchingVisibleMetas(s).reverse()){const o=e[i.index];if(n&&o>0||!n&&o<0)return i.index}return null}function Xc(e,t){const{chart:n,_cachedMeta:s}=e,i=n._stacks||(n._stacks={}),{iScale:o,vScale:r,index:a}=s,l=o.axis,c=r.axis,u=l0(o,r,s),f=t.length;let h;for(let d=0;d<f;++d){const p=t[d],{[l]:g,[c]:m}=p,y=p._stacks||(p._stacks={});h=y[c]=u0(i,u,g),h[a]=m,h._top=Yc(h,r,!0,s.type),h._bottom=Yc(h,r,!1,s.type);const b=h._visualValues||(h._visualValues={});b[a]=m}}function _r(e,t){const n=e.scales;return Object.keys(n).filter(s=>n[s].axis===t).shift()}function f0(e,t){return xn(e,{active:!1,dataset:void 0,datasetIndex:t,index:t,mode:"default",type:"dataset"})}function h0(e,t,n){return xn(e,{active:!1,dataIndex:t,parsed:void 0,raw:void 0,element:n,index:t,mode:"default",type:"data"})}function Ms(e,t){const n=e.controller.index,s=e.vScale&&e.vScale.axis;if(s){t=t||e._parsed;for(const i of t){const o=i._stacks;if(!o||o[s]===void 0||o[s][n]===void 0)return;delete o[s][n],o[s]._visualValues!==void 0&&o[s]._visualValues[n]!==void 0&&delete o[s]._visualValues[n]}}}const xr=e=>e==="reset"||e==="none",Gc=(e,t)=>t?e:Object.assign({},e),d0=(e,t,n)=>e&&!t.hidden&&t._stacked&&{keys:Pd(n,!0),values:null};class ye{constructor(t,n){this.chart=t,this._ctx=t.ctx,this.index=n,this._cachedDataOpts={},this._cachedMeta=this.getMeta(),this._type=this._cachedMeta.type,this.options=void 0,this._parsing=!1,this._data=void 0,this._objectData=void 0,this._sharedOptions=void 0,this._drawStart=void 0,this._drawCount=void 0,this.enableOptionSharing=!1,this.supportsDecimation=!1,this.$context=void 0,this._syncList=[],this.datasetElementType=new.target.datasetElementType,this.dataElementType=new.target.dataElementType,this.initialize()}initialize(){const t=this._cachedMeta;this.configure(),this.linkScales(),t._stacked=yr(t.vScale,t),this.addElements(),this.options.fill&&!this.chart.isPluginEnabled("filler")&&console.warn("Tried to use the 'fill' option without the 'Filler' plugin enabled. Please import and register the 'Filler' plugin and make sure it is not disabled in the options")}updateIndex(t){this.index!==t&&Ms(this._cachedMeta),this.index=t}linkScales(){const t=this.chart,n=this._cachedMeta,s=this.getDataset(),i=(f,h,d,p)=>f==="x"?h:f==="r"?p:d,o=n.xAxisID=st(s.xAxisID,_r(t,"x")),r=n.yAxisID=st(s.yAxisID,_r(t,"y")),a=n.rAxisID=st(s.rAxisID,_r(t,"r")),l=n.indexAxis,c=n.iAxisID=i(l,o,r,a),u=n.vAxisID=i(l,r,o,a);n.xScale=this.getScaleForId(o),n.yScale=this.getScaleForId(r),n.rScale=this.getScaleForId(a),n.iScale=this.getScaleForId(c),n.vScale=this.getScaleForId(u)}getDataset(){return this.chart.data.datasets[this.index]}getMeta(){return this.chart.getDatasetMeta(this.index)}getScaleForId(t){return this.chart.scales[t]}_getOtherScale(t){const n=this._cachedMeta;return t===n.iScale?n.vScale:n.iScale}reset(){this._update("reset")}_destroy(){const t=this._cachedMeta;this._data&&Tc(this._data,this),t._stacked&&Ms(t)}_dataCheck(){const t=this.getDataset(),n=t.data||(t.data=[]),s=this._data;if(ot(n)){const i=this._cachedMeta;this._data=a0(n,i)}else if(s!==n){if(s){Tc(s,this);const i=this._cachedMeta;Ms(i),i._parsed=[]}n&&Object.isExtensible(n)&&J_(n,this),this._syncList=[],this._data=n}}addElements(){const t=this._cachedMeta;this._dataCheck(),this.datasetElementType&&(t.dataset=new this.datasetElementType)}buildOrUpdateElements(t){const n=this._cachedMeta,s=this.getDataset();let i=!1;this._dataCheck();const o=n._stacked;n._stacked=yr(n.vScale,n),n.stack!==s.stack&&(i=!0,Ms(n),n.stack=s.stack),this._resyncElements(t),(i||o!==n._stacked)&&(Xc(this,n._parsed),n._stacked=yr(n.vScale,n))}configure(){const t=this.chart.config,n=t.datasetScopeKeys(this._type),s=t.getOptionScopes(this.getDataset(),n,!0);this.options=t.createResolver(s,this.getContext()),this._parsing=this.options.parsing,this._cachedDataOpts={}}parse(t,n){const{_cachedMeta:s,_data:i}=this,{iScale:o,_stacked:r}=s,a=o.axis;let l=t===0&&n===i.length?!0:s._sorted,c=t>0&&s._parsed[t-1],u,f,h;if(this._parsing===!1)s._parsed=i,s._sorted=!0,h=i;else{wt(i[t])?h=this.parseArrayData(s,i,t,n):ot(i[t])?h=this.parseObjectData(s,i,t,n):h=this.parsePrimitiveData(s,i,t,n);const d=()=>f[a]===null||c&&f[a]<c[a];for(u=0;u<n;++u)s._parsed[u+t]=f=h[u],l&&(d()&&(l=!1),c=f);s._sorted=l}r&&Xc(this,h)}parsePrimitiveData(t,n,s,i){const{iScale:o,vScale:r}=t,a=o.axis,l=r.axis,c=o.getLabels(),u=o===r,f=new Array(i);let h,d,p;for(h=0,d=i;h<d;++h)p=h+s,f[h]={[a]:u||o.parse(c[p],p),[l]:r.parse(n[p],p)};return f}parseArrayData(t,n,s,i){const{xScale:o,yScale:r}=t,a=new Array(i);let l,c,u,f;for(l=0,c=i;l<c;++l)u=l+s,f=n[u],a[l]={x:o.parse(f[0],u),y:r.parse(f[1],u)};return a}parseObjectData(t,n,s,i){const{xScale:o,yScale:r}=t,{xAxisKey:a="x",yAxisKey:l="y"}=this._parsing,c=new Array(i);let u,f,h,d;for(u=0,f=i;u<f;++u)h=u+s,d=n[h],c[u]={x:o.parse(bn(d,a),h),y:r.parse(bn(d,l),h)};return c}getParsed(t){return this._cachedMeta._parsed[t]}getDataElement(t){return this._cachedMeta.data[t]}applyStack(t,n,s){const i=this.chart,o=this._cachedMeta,r=n[t.axis],a={keys:Pd(i,!0),values:n._stacks[t.axis]._visualValues};return qc(a,r,o.index,{mode:s})}updateRangeFromParsed(t,n,s,i){const o=s[n.axis];let r=o===null?NaN:o;const a=i&&s._stacks[n.axis];i&&a&&(i.values=a,r=qc(i,o,this._cachedMeta.index)),t.min=Math.min(t.min,r),t.max=Math.max(t.max,r)}getMinMax(t,n){const s=this._cachedMeta,i=s._parsed,o=s._sorted&&t===s.iScale,r=i.length,a=this._getOtherScale(t),l=d0(n,s,this.chart),c={min:Number.POSITIVE_INFINITY,max:Number.NEGATIVE_INFINITY},{min:u,max:f}=c0(a);let h,d;function p(){d=i[h];const g=d[a.axis];return!Ct(d[t.axis])||u>g||f<g}for(h=0;h<r&&!(!p()&&(this.updateRangeFromParsed(c,t,d,l),o));++h);if(o){for(h=r-1;h>=0;--h)if(!p()){this.updateRangeFromParsed(c,t,d,l);break}}return c}getAllParsedValues(t){const n=this._cachedMeta._parsed,s=[];let i,o,r;for(i=0,o=n.length;i<o;++i)r=n[i][t.axis],Ct(r)&&s.push(r);return s}getMaxOverflow(){return!1}getLabelAndValue(t){const n=this._cachedMeta,s=n.iScale,i=n.vScale,o=this.getParsed(t);return{label:s?""+s.getLabelForValue(o[s.axis]):"",value:i?""+i.getLabelForValue(o[i.axis]):""}}_update(t){const n=this._cachedMeta;this.update(t||"default"),n._clip=r0(st(this.options.clip,o0(n.xScale,n.yScale,this.getMaxOverflow())))}update(t){}draw(){const t=this._ctx,n=this.chart,s=this._cachedMeta,i=s.data||[],o=n.chartArea,r=[],a=this._drawStart||0,l=this._drawCount||i.length-a,c=this.options.drawActiveElementsOnTop;let u;for(s.dataset&&s.dataset.draw(t,o,a,l),u=a;u<a+l;++u){const f=i[u];f.hidden||(f.active&&c?r.push(f):f.draw(t,o))}for(u=0;u<r.length;++u)r[u].draw(t,o)}getStyle(t,n){const s=n?"active":"default";return t===void 0&&this._cachedMeta.dataset?this.resolveDatasetElementOptions(s):this.resolveDataElementOptions(t||0,s)}getContext(t,n,s){const i=this.getDataset();let o;if(t>=0&&t<this._cachedMeta.data.length){const r=this._cachedMeta.data[t];o=r.$context||(r.$context=h0(this.getContext(),t,r)),o.parsed=this.getParsed(t),o.raw=i.data[t],o.index=o.dataIndex=t}else o=this.$context||(this.$context=f0(this.chart.getContext(),this.index)),o.dataset=i,o.index=o.datasetIndex=this.index;return o.active=!!n,o.mode=s,o}resolveDatasetElementOptions(t){return this._resolveElementOptions(this.datasetElementType.id,t)}resolveDataElementOptions(t,n){return this._resolveElementOptions(this.dataElementType.id,n,t)}_resolveElementOptions(t,n="default",s){const i=n==="active",o=this._cachedDataOpts,r=t+"-"+n,a=o[r],l=this.enableOptionSharing&&ri(s);if(a)return Gc(a,l);const c=this.chart.config,u=c.datasetElementScopeKeys(this._type,t),f=i?[`${t}Hover`,"hover",t,""]:[t,""],h=c.getOptionScopes(this.getDataset(),u),d=Object.keys(St.elements[t]),p=()=>this.getContext(s,i,n),g=c.resolveNamedOptions(h,d,p,f);return g.$shared&&(g.$shared=l,o[r]=Object.freeze(Gc(g,l))),g}_resolveAnimations(t,n,s){const i=this.chart,o=this._cachedDataOpts,r=`animation-${n}`,a=o[r];if(a)return a;let l;if(i.options.animation!==!1){const u=this.chart.config,f=u.datasetAnimationScopeKeys(this._type,n),h=u.getOptionScopes(this.getDataset(),f);l=u.createResolver(h,this.getContext(t,s,n))}const c=new Cd(i,l&&l.animations);return l&&l._cacheable&&(o[r]=Object.freeze(c)),c}getSharedOptions(t){if(t.$shared)return this._sharedOptions||(this._sharedOptions=Object.assign({},t))}includeOptions(t,n){return!n||xr(t)||this.chart._animationsDisabled}_getSharedOptions(t,n){const s=this.resolveDataElementOptions(t,n),i=this._sharedOptions,o=this.getSharedOptions(s),r=this.includeOptions(n,o)||o!==i;return this.updateSharedOptions(o,n,s),{sharedOptions:o,includeOptions:r}}updateElement(t,n,s,i){xr(i)?Object.assign(t,s):this._resolveAnimations(n,i).update(t,s)}updateSharedOptions(t,n,s){t&&!xr(n)&&this._resolveAnimations(void 0,n).update(t,s)}_setStyle(t,n,s,i){t.active=i;const o=this.getStyle(n,i);this._resolveAnimations(n,s,i).update(t,{options:!i&&this.getSharedOptions(o)||o})}removeHoverStyle(t,n,s){this._setStyle(t,s,"active",!1)}setHoverStyle(t,n,s){this._setStyle(t,s,"active",!0)}_removeDatasetHoverStyle(){const t=this._cachedMeta.dataset;t&&this._setStyle(t,void 0,"active",!1)}_setDatasetHoverStyle(){const t=this._cachedMeta.dataset;t&&this._setStyle(t,void 0,"active",!0)}_resyncElements(t){const n=this._data,s=this._cachedMeta.data;for(const[a,l,c]of this._syncList)this[a](l,c);this._syncList=[];const i=s.length,o=n.length,r=Math.min(o,i);r&&this.parse(0,r),o>i?this._insertElements(i,o-i,t):o<i&&this._removeElements(o,i-o)}_insertElements(t,n,s=!0){const i=this._cachedMeta,o=i.data,r=t+n;let a;const l=c=>{for(c.length+=n,a=c.length-1;a>=r;a--)c[a]=c[a-n]};for(l(o),a=t;a<r;++a)o[a]=new this.dataElementType;this._parsing&&l(i._parsed),this.parse(t,n),s&&this.updateElements(o,t,n,"reset")}updateElements(t,n,s,i){}_removeElements(t,n){const s=this._cachedMeta;if(this._parsing){const i=s._parsed.splice(t,n);s._stacked&&Ms(s,i)}s.data.splice(t,n)}_sync(t){if(this._parsing)this._syncList.push(t);else{const[n,s,i]=t;this[n](s,i)}this.chart._dataChanges.push([this.index,...t])}_onDataPush(){const t=arguments.length;this._sync(["_insertElements",this.getDataset().data.length-t,t])}_onDataPop(){this._sync(["_removeElements",this._cachedMeta.data.length-1,1])}_onDataShift(){this._sync(["_removeElements",0,1])}_onDataSplice(t,n){n&&this._sync(["_removeElements",t,n]);const s=arguments.length-2;s&&this._sync(["_insertElements",t,s])}_onDataUnshift(){this._sync(["_insertElements",0,arguments.length])}}N(ye,"defaults",{}),N(ye,"datasetElementType",null),N(ye,"dataElementType",null);function p0(e,t){if(!e._cache.$bar){const n=e.getMatchingVisibleMetas(t);let s=[];for(let i=0,o=n.length;i<o;i++)s=s.concat(n[i].controller.getAllParsedValues(e));e._cache.$bar=od(s.sort((i,o)=>i-o))}return e._cache.$bar}function g0(e){const t=e.iScale,n=p0(t,e.type);let s=t._length,i,o,r,a;const l=()=>{r===32767||r===-32768||(ri(a)&&(s=Math.min(s,Math.abs(r-a)||s)),a=r)};for(i=0,o=n.length;i<o;++i)r=t.getPixelForValue(n[i]),l();for(a=void 0,i=0,o=t.ticks.length;i<o;++i)r=t.getPixelForTick(i),l();return s}function m0(e,t,n,s){const i=n.barThickness;let o,r;return it(i)?(o=t.min*n.categoryPercentage,r=n.barPercentage):(o=i*s,r=1),{chunk:o/s,ratio:r,start:t.pixels[e]-o/2}}function b0(e,t,n,s){const i=t.pixels,o=i[e];let r=e>0?i[e-1]:null,a=e<i.length-1?i[e+1]:null;const l=n.categoryPercentage;r===null&&(r=o-(a===null?t.end-t.start:a-o)),a===null&&(a=o+o-r);const c=o-(o-Math.min(r,a))/2*l;return{chunk:Math.abs(a-r)/2*l/s,ratio:n.barPercentage,start:c}}function y0(e,t,n,s){const i=n.parse(e[0],s),o=n.parse(e[1],s),r=Math.min(i,o),a=Math.max(i,o);let l=r,c=a;Math.abs(r)>Math.abs(a)&&(l=a,c=r),t[n.axis]=c,t._custom={barStart:l,barEnd:c,start:i,end:o,min:r,max:a}}function Ed(e,t,n,s){return wt(e)?y0(e,t,n,s):t[n.axis]=n.parse(e,s),t}function Jc(e,t,n,s){const i=e.iScale,o=e.vScale,r=i.getLabels(),a=i===o,l=[];let c,u,f,h;for(c=n,u=n+s;c<u;++c)h=t[c],f={},f[i.axis]=a||i.parse(r[c],c),l.push(Ed(h,f,o,c));return l}function vr(e){return e&&e.barStart!==void 0&&e.barEnd!==void 0}function _0(e,t,n){return e!==0?Fe(e):(t.isHorizontal()?1:-1)*(t.min>=n?1:-1)}function x0(e){let t,n,s,i,o;return e.horizontal?(t=e.base>e.x,n="left",s="right"):(t=e.base<e.y,n="bottom",s="top"),t?(i="end",o="start"):(i="start",o="end"),{start:n,end:s,reverse:t,top:i,bottom:o}}function v0(e,t,n,s){let i=t.borderSkipped;const o={};if(!i){e.borderSkipped=o;return}if(i===!0){e.borderSkipped={top:!0,right:!0,bottom:!0,left:!0};return}const{start:r,end:a,reverse:l,top:c,bottom:u}=x0(e);i==="middle"&&n&&(e.enableBorderRadius=!0,(n._top||0)===s?i=c:(n._bottom||0)===s?i=u:(o[Qc(u,r,a,l)]=!0,i=c)),o[Qc(i,r,a,l)]=!0,e.borderSkipped=o}function Qc(e,t,n,s){return s?(e=w0(e,t,n),e=Zc(e,n,t)):e=Zc(e,t,n),e}function w0(e,t,n){return e===t?n:e===n?t:e}function Zc(e,t,n){return e==="start"?t:e==="end"?n:e}function S0(e,{inflateAmount:t},n){e.inflateAmount=t==="auto"?n===1?.33:0:t}class Ki extends ye{parsePrimitiveData(t,n,s,i){return Jc(t,n,s,i)}parseArrayData(t,n,s,i){return Jc(t,n,s,i)}parseObjectData(t,n,s,i){const{iScale:o,vScale:r}=t,{xAxisKey:a="x",yAxisKey:l="y"}=this._parsing,c=o.axis==="x"?a:l,u=r.axis==="x"?a:l,f=[];let h,d,p,g;for(h=s,d=s+i;h<d;++h)g=n[h],p={},p[o.axis]=o.parse(bn(g,c),h),f.push(Ed(bn(g,u),p,r,h));return f}updateRangeFromParsed(t,n,s,i){super.updateRangeFromParsed(t,n,s,i);const o=s._custom;o&&n===this._cachedMeta.vScale&&(t.min=Math.min(t.min,o.min),t.max=Math.max(t.max,o.max))}getMaxOverflow(){return 0}getLabelAndValue(t){const n=this._cachedMeta,{iScale:s,vScale:i}=n,o=this.getParsed(t),r=o._custom,a=vr(r)?"["+r.start+", "+r.end+"]":""+i.getLabelForValue(o[i.axis]);return{label:""+s.getLabelForValue(o[s.axis]),value:a}}initialize(){this.enableOptionSharing=!0,super.initialize();const t=this._cachedMeta;t.stack=this.getDataset().stack}update(t){const n=this._cachedMeta;this.updateElements(n.data,0,n.data.length,t)}updateElements(t,n,s,i){const o=i==="reset",{index:r,_cachedMeta:{vScale:a}}=this,l=a.getBasePixel(),c=a.isHorizontal(),u=this._getRuler(),{sharedOptions:f,includeOptions:h}=this._getSharedOptions(n,i);for(let d=n;d<n+s;d++){const p=this.getParsed(d),g=o||it(p[a.axis])?{base:l,head:l}:this._calculateBarValuePixels(d),m=this._calculateBarIndexPixels(d,u),y=(p._stacks||{})[a.axis],b={horizontal:c,base:g.base,enableBorderRadius:!y||vr(p._custom)||r===y._top||r===y._bottom,x:c?g.head:m.center,y:c?m.center:g.head,height:c?m.size:Math.abs(g.size),width:c?Math.abs(g.size):m.size};h&&(b.options=f||this.resolveDataElementOptions(d,t[d].active?"active":i));const w=b.options||t[d].options;v0(b,w,y,r),S0(b,w,u.ratio),this.updateElement(t[d],d,b,i)}}_getStacks(t,n){const{iScale:s}=this._cachedMeta,i=s.getMatchingVisibleMetas(this._type).filter(u=>u.controller.options.grouped),o=s.options.stacked,r=[],a=this._cachedMeta.controller.getParsed(n),l=a&&a[s.axis],c=u=>{const f=u._parsed.find(d=>d[s.axis]===l),h=f&&f[u.vScale.axis];if(it(h)||isNaN(h))return!0};for(const u of i)if(!(n!==void 0&&c(u))&&((o===!1||r.indexOf(u.stack)===-1||o===void 0&&u.stack===void 0)&&r.push(u.stack),u.index===t))break;return r.length||r.push(void 0),r}_getStackCount(t){return this._getStacks(void 0,t).length}_getStackIndex(t,n,s){const i=this._getStacks(t,s),o=n!==void 0?i.indexOf(n):-1;return o===-1?i.length-1:o}_getRuler(){const t=this.options,n=this._cachedMeta,s=n.iScale,i=[];let o,r;for(o=0,r=n.data.length;o<r;++o)i.push(s.getPixelForValue(this.getParsed(o)[s.axis],o));const a=t.barThickness;return{min:a||g0(n),pixels:i,start:s._startPixel,end:s._endPixel,stackCount:this._getStackCount(),scale:s,grouped:t.grouped,ratio:a?1:t.categoryPercentage*t.barPercentage}}_calculateBarValuePixels(t){const{_cachedMeta:{vScale:n,_stacked:s,index:i},options:{base:o,minBarLength:r}}=this,a=o||0,l=this.getParsed(t),c=l._custom,u=vr(c);let f=l[n.axis],h=0,d=s?this.applyStack(n,l,s):f,p,g;d!==f&&(h=d-f,d=f),u&&(f=c.barStart,d=c.barEnd-c.barStart,f!==0&&Fe(f)!==Fe(c.barEnd)&&(h=0),h+=f);const m=!it(o)&&!u?o:h;let y=n.getPixelForValue(m);if(this.chart.getDataVisibility(t)?p=n.getPixelForValue(h+d):p=y,g=p-y,Math.abs(g)<r){g=_0(g,n,a)*r,f===a&&(y-=g/2);const b=n.getPixelForDecimal(0),w=n.getPixelForDecimal(1),v=Math.min(b,w),S=Math.max(b,w);y=Math.max(Math.min(y,S),v),p=y+g,s&&!u&&(l._stacks[n.axis]._visualValues[i]=n.getValueForPixel(p)-n.getValueForPixel(y))}if(y===n.getPixelForValue(a)){const b=Fe(g)*n.getLineWidthForValue(a)/2;y+=b,g-=b}return{size:g,base:y,head:p,center:p+g/2}}_calculateBarIndexPixels(t,n){const s=n.scale,i=this.options,o=i.skipNull,r=st(i.maxBarThickness,1/0);let a,l;if(n.grouped){const c=o?this._getStackCount(t):n.stackCount,u=i.barThickness==="flex"?b0(t,n,i,c):m0(t,n,i,c),f=this._getStackIndex(this.index,this._cachedMeta.stack,o?t:void 0);a=u.start+u.chunk*f+u.chunk/2,l=Math.min(r,u.chunk*u.ratio)}else a=s.getPixelForValue(this.getParsed(t)[s.axis],t),l=Math.min(r,n.min*n.ratio);return{base:a-l/2,head:a+l/2,center:a,size:l}}draw(){const t=this._cachedMeta,n=t.vScale,s=t.data,i=s.length;let o=0;for(;o<i;++o)this.getParsed(o)[n.axis]!==null&&!s[o].hidden&&s[o].draw(this._ctx)}}N(Ki,"id","bar"),N(Ki,"defaults",{datasetElementType:!1,dataElementType:"bar",categoryPercentage:.8,barPercentage:.9,grouped:!0,animations:{numbers:{type:"number",properties:["x","y","base","width","height"]}}}),N(Ki,"overrides",{scales:{_index_:{type:"category",offset:!0,grid:{offset:!0}},_value_:{type:"linear",beginAtZero:!0}}});class qi extends ye{initialize(){this.enableOptionSharing=!0,super.initialize()}parsePrimitiveData(t,n,s,i){const o=super.parsePrimitiveData(t,n,s,i);for(let r=0;r<o.length;r++)o[r]._custom=this.resolveDataElementOptions(r+s).radius;return o}parseArrayData(t,n,s,i){const o=super.parseArrayData(t,n,s,i);for(let r=0;r<o.length;r++){const a=n[s+r];o[r]._custom=st(a[2],this.resolveDataElementOptions(r+s).radius)}return o}parseObjectData(t,n,s,i){const o=super.parseObjectData(t,n,s,i);for(let r=0;r<o.length;r++){const a=n[s+r];o[r]._custom=st(a&&a.r&&+a.r,this.resolveDataElementOptions(r+s).radius)}return o}getMaxOverflow(){const t=this._cachedMeta.data;let n=0;for(let s=t.length-1;s>=0;--s)n=Math.max(n,t[s].size(this.resolveDataElementOptions(s))/2);return n>0&&n}getLabelAndValue(t){const n=this._cachedMeta,s=this.chart.data.labels||[],{xScale:i,yScale:o}=n,r=this.getParsed(t),a=i.getLabelForValue(r.x),l=o.getLabelForValue(r.y),c=r._custom;return{label:s[t]||"",value:"("+a+", "+l+(c?", "+c:"")+")"}}update(t){const n=this._cachedMeta.data;this.updateElements(n,0,n.length,t)}updateElements(t,n,s,i){const o=i==="reset",{iScale:r,vScale:a}=this._cachedMeta,{sharedOptions:l,includeOptions:c}=this._getSharedOptions(n,i),u=r.axis,f=a.axis;for(let h=n;h<n+s;h++){const d=t[h],p=!o&&this.getParsed(h),g={},m=g[u]=o?r.getPixelForDecimal(.5):r.getPixelForValue(p[u]),y=g[f]=o?a.getBasePixel():a.getPixelForValue(p[f]);g.skip=isNaN(m)||isNaN(y),c&&(g.options=l||this.resolveDataElementOptions(h,d.active?"active":i),o&&(g.options.radius=0)),this.updateElement(d,h,g,i)}}resolveDataElementOptions(t,n){const s=this.getParsed(t);let i=super.resolveDataElementOptions(t,n);i.$shared&&(i=Object.assign({},i,{$shared:!1}));const o=i.radius;return n!=="active"&&(i.radius=0),i.radius+=st(s&&s._custom,o),i}}N(qi,"id","bubble"),N(qi,"defaults",{datasetElementType:!1,dataElementType:"point",animations:{numbers:{type:"number",properties:["x","y","borderWidth","radius"]}}}),N(qi,"overrides",{scales:{x:{type:"linear"},y:{type:"linear"}}});function M0(e,t,n){let s=1,i=1,o=0,r=0;if(t<xt){const a=e,l=a+t,c=Math.cos(a),u=Math.sin(a),f=Math.cos(l),h=Math.sin(l),d=(w,v,S)=>ai(w,a,l,!0)?1:Math.max(v,v*n,S,S*n),p=(w,v,S)=>ai(w,a,l,!0)?-1:Math.min(v,v*n,S,S*n),g=d(0,c,f),m=d(Et,u,h),y=p(vt,c,f),b=p(vt+Et,u,h);s=(g-y)/2,i=(m-b)/2,o=-(g+y)/2,r=-(m+b)/2}return{ratioX:s,ratioY:i,offsetX:o,offsetY:r}}class Ln extends ye{constructor(t,n){super(t,n),this.enableOptionSharing=!0,this.innerRadius=void 0,this.outerRadius=void 0,this.offsetX=void 0,this.offsetY=void 0}linkScales(){}parse(t,n){const s=this.getDataset().data,i=this._cachedMeta;if(this._parsing===!1)i._parsed=s;else{let o=l=>+s[l];if(ot(s[t])){const{key:l="value"}=this._parsing;o=c=>+bn(s[c],l)}let r,a;for(r=t,a=t+n;r<a;++r)i._parsed[r]=o(r)}}_getRotation(){return me(this.options.rotation-90)}_getCircumference(){return me(this.options.circumference)}_getRotationExtents(){let t=xt,n=-xt;for(let s=0;s<this.chart.data.datasets.length;++s)if(this.chart.isDatasetVisible(s)&&this.chart.getDatasetMeta(s).type===this._type){const i=this.chart.getDatasetMeta(s).controller,o=i._getRotation(),r=i._getCircumference();t=Math.min(t,o),n=Math.max(n,o+r)}return{rotation:t,circumference:n-t}}update(t){const n=this.chart,{chartArea:s}=n,i=this._cachedMeta,o=i.data,r=this.getMaxBorderWidth()+this.getMaxOffset(o)+this.options.spacing,a=Math.max((Math.min(s.width,s.height)-r)/2,0),l=Math.min(I_(this.options.cutout,a),1),c=this._getRingWeight(this.index),{circumference:u,rotation:f}=this._getRotationExtents(),{ratioX:h,ratioY:d,offsetX:p,offsetY:g}=M0(f,u,l),m=(s.width-r)/h,y=(s.height-r)/d,b=Math.max(Math.min(m,y)/2,0),w=td(this.options.radius,b),v=Math.max(w*l,0),S=(w-v)/this._getVisibleDatasetWeightTotal();this.offsetX=p*w,this.offsetY=g*w,i.total=this.calculateTotal(),this.outerRadius=w-S*this._getRingWeightOffset(this.index),this.innerRadius=Math.max(this.outerRadius-S*c,0),this.updateElements(o,0,o.length,t)}_circumference(t,n){const s=this.options,i=this._cachedMeta,o=this._getCircumference();return n&&s.animation.animateRotate||!this.chart.getDataVisibility(t)||i._parsed[t]===null||i.data[t].hidden?0:this.calculateCircumference(i._parsed[t]*o/xt)}updateElements(t,n,s,i){const o=i==="reset",r=this.chart,a=r.chartArea,c=r.options.animation,u=(a.left+a.right)/2,f=(a.top+a.bottom)/2,h=o&&c.animateScale,d=h?0:this.innerRadius,p=h?0:this.outerRadius,{sharedOptions:g,includeOptions:m}=this._getSharedOptions(n,i);let y=this._getRotation(),b;for(b=0;b<n;++b)y+=this._circumference(b,o);for(b=n;b<n+s;++b){const w=this._circumference(b,o),v=t[b],S={x:u+this.offsetX,y:f+this.offsetY,startAngle:y,endAngle:y+w,circumference:w,outerRadius:p,innerRadius:d};m&&(S.options=g||this.resolveDataElementOptions(b,v.active?"active":i)),y+=w,this.updateElement(v,b,S,i)}}calculateTotal(){const t=this._cachedMeta,n=t.data;let s=0,i;for(i=0;i<n.length;i++){const o=t._parsed[i];o!==null&&!isNaN(o)&&this.chart.getDataVisibility(i)&&!n[i].hidden&&(s+=Math.abs(o))}return s}calculateCircumference(t){const n=this._cachedMeta.total;return n>0&&!isNaN(t)?xt*(Math.abs(t)/n):0}getLabelAndValue(t){const n=this._cachedMeta,s=this.chart,i=s.data.labels||[],o=yi(n._parsed[t],s.options.locale);return{label:i[t]||"",value:o}}getMaxBorderWidth(t){let n=0;const s=this.chart;let i,o,r,a,l;if(!t){for(i=0,o=s.data.datasets.length;i<o;++i)if(s.isDatasetVisible(i)){r=s.getDatasetMeta(i),t=r.data,a=r.controller;break}}if(!t)return 0;for(i=0,o=t.length;i<o;++i)l=a.resolveDataElementOptions(i),l.borderAlign!=="inner"&&(n=Math.max(n,l.borderWidth||0,l.hoverBorderWidth||0));return n}getMaxOffset(t){let n=0;for(let s=0,i=t.length;s<i;++s){const o=this.resolveDataElementOptions(s);n=Math.max(n,o.offset||0,o.hoverOffset||0)}return n}_getRingWeightOffset(t){let n=0;for(let s=0;s<t;++s)this.chart.isDatasetVisible(s)&&(n+=this._getRingWeight(s));return n}_getRingWeight(t){return Math.max(st(this.chart.data.datasets[t].weight,1),0)}_getVisibleDatasetWeightTotal(){return this._getRingWeightOffset(this.chart.data.datasets.length)||1}}N(Ln,"id","doughnut"),N(Ln,"defaults",{datasetElementType:!1,dataElementType:"arc",animation:{animateRotate:!0,animateScale:!1},animations:{numbers:{type:"number",properties:["circumference","endAngle","innerRadius","outerRadius","startAngle","x","y","offset","borderWidth","spacing"]}},cutout:"50%",rotation:0,circumference:360,radius:"100%",spacing:0,indexAxis:"r"}),N(Ln,"descriptors",{_scriptable:t=>t!=="spacing",_indexable:t=>t!=="spacing"&&!t.startsWith("borderDash")&&!t.startsWith("hoverBorderDash")}),N(Ln,"overrides",{aspectRatio:1,plugins:{legend:{labels:{generateLabels(t){const n=t.data;if(n.labels.length&&n.datasets.length){const{labels:{pointStyle:s,color:i}}=t.legend.options;return n.labels.map((o,r)=>{const l=t.getDatasetMeta(0).controller.getStyle(r);return{text:o,fillStyle:l.backgroundColor,strokeStyle:l.borderColor,fontColor:i,lineWidth:l.borderWidth,pointStyle:s,hidden:!t.getDataVisibility(r),index:r}})}return[]}},onClick(t,n,s){s.chart.toggleDataVisibility(n.index),s.chart.update()}}}});class Yi extends ye{initialize(){this.enableOptionSharing=!0,this.supportsDecimation=!0,super.initialize()}update(t){const n=this._cachedMeta,{dataset:s,data:i=[],_dataset:o}=n,r=this.chart._animationsDisabled;let{start:a,count:l}=ld(n,i,r);this._drawStart=a,this._drawCount=l,cd(n)&&(a=0,l=i.length),s._chart=this.chart,s._datasetIndex=this.index,s._decimated=!!o._decimated,s.points=i;const c=this.resolveDatasetElementOptions(t);this.options.showLine||(c.borderWidth=0),c.segment=this.options.segment,this.updateElement(s,void 0,{animated:!r,options:c},t),this.updateElements(i,a,l,t)}updateElements(t,n,s,i){const o=i==="reset",{iScale:r,vScale:a,_stacked:l,_dataset:c}=this._cachedMeta,{sharedOptions:u,includeOptions:f}=this._getSharedOptions(n,i),h=r.axis,d=a.axis,{spanGaps:p,segment:g}=this.options,m=hs(p)?p:Number.POSITIVE_INFINITY,y=this.chart._animationsDisabled||o||i==="none",b=n+s,w=t.length;let v=n>0&&this.getParsed(n-1);for(let S=0;S<w;++S){const A=t[S],P=y?A:{};if(S<n||S>=b){P.skip=!0;continue}const E=this.getParsed(S),k=it(E[d]),I=P[h]=r.getPixelForValue(E[h],S),H=P[d]=o||k?a.getBasePixel():a.getPixelForValue(l?this.applyStack(a,E,l):E[d],S);P.skip=isNaN(I)||isNaN(H)||k,P.stop=S>0&&Math.abs(E[h]-v[h])>m,g&&(P.parsed=E,P.raw=c.data[S]),f&&(P.options=u||this.resolveDataElementOptions(S,A.active?"active":i)),y||this.updateElement(A,S,P,i),v=E}}getMaxOverflow(){const t=this._cachedMeta,n=t.dataset,s=n.options&&n.options.borderWidth||0,i=t.data||[];if(!i.length)return s;const o=i[0].size(this.resolveDataElementOptions(0)),r=i[i.length-1].size(this.resolveDataElementOptions(i.length-1));return Math.max(s,o,r)/2}draw(){const t=this._cachedMeta;t.dataset.updateControlPoints(this.chart.chartArea,t.iScale.axis),super.draw()}}N(Yi,"id","line"),N(Yi,"defaults",{datasetElementType:"line",dataElementType:"point",showLine:!0,spanGaps:!1}),N(Yi,"overrides",{scales:{_index_:{type:"category"},_value_:{type:"linear"}}});class Xs extends ye{constructor(t,n){super(t,n),this.innerRadius=void 0,this.outerRadius=void 0}getLabelAndValue(t){const n=this._cachedMeta,s=this.chart,i=s.data.labels||[],o=yi(n._parsed[t].r,s.options.locale);return{label:i[t]||"",value:o}}parseObjectData(t,n,s,i){return bd.bind(this)(t,n,s,i)}update(t){const n=this._cachedMeta.data;this._updateRadius(),this.updateElements(n,0,n.length,t)}getMinMax(){const t=this._cachedMeta,n={min:Number.POSITIVE_INFINITY,max:Number.NEGATIVE_INFINITY};return t.data.forEach((s,i)=>{const o=this.getParsed(i).r;!isNaN(o)&&this.chart.getDataVisibility(i)&&(o<n.min&&(n.min=o),o>n.max&&(n.max=o))}),n}_updateRadius(){const t=this.chart,n=t.chartArea,s=t.options,i=Math.min(n.right-n.left,n.bottom-n.top),o=Math.max(i/2,0),r=Math.max(s.cutoutPercentage?o/100*s.cutoutPercentage:1,0),a=(o-r)/t.getVisibleDatasetCount();this.outerRadius=o-a*this.index,this.innerRadius=this.outerRadius-a}updateElements(t,n,s,i){const o=i==="reset",r=this.chart,l=r.options.animation,c=this._cachedMeta.rScale,u=c.xCenter,f=c.yCenter,h=c.getIndexAngle(0)-.5*vt;let d=h,p;const g=360/this.countVisibleElements();for(p=0;p<n;++p)d+=this._computeAngle(p,i,g);for(p=n;p<n+s;p++){const m=t[p];let y=d,b=d+this._computeAngle(p,i,g),w=r.getDataVisibility(p)?c.getDistanceFromCenterForValue(this.getParsed(p).r):0;d=b,o&&(l.animateScale&&(w=0),l.animateRotate&&(y=b=h));const v={x:u,y:f,innerRadius:0,outerRadius:w,startAngle:y,endAngle:b,options:this.resolveDataElementOptions(p,m.active?"active":i)};this.updateElement(m,p,v,i)}}countVisibleElements(){const t=this._cachedMeta;let n=0;return t.data.forEach((s,i)=>{!isNaN(this.getParsed(i).r)&&this.chart.getDataVisibility(i)&&n++}),n}_computeAngle(t,n,s){return this.chart.getDataVisibility(t)?me(this.resolveDataElementOptions(t,n).angle||s):0}}N(Xs,"id","polarArea"),N(Xs,"defaults",{dataElementType:"arc",animation:{animateRotate:!0,animateScale:!0},animations:{numbers:{type:"number",properties:["x","y","startAngle","endAngle","innerRadius","outerRadius"]}},indexAxis:"r",startAngle:0}),N(Xs,"overrides",{aspectRatio:1,plugins:{legend:{labels:{generateLabels(t){const n=t.data;if(n.labels.length&&n.datasets.length){const{labels:{pointStyle:s,color:i}}=t.legend.options;return n.labels.map((o,r)=>{const l=t.getDatasetMeta(0).controller.getStyle(r);return{text:o,fillStyle:l.backgroundColor,strokeStyle:l.borderColor,fontColor:i,lineWidth:l.borderWidth,pointStyle:s,hidden:!t.getDataVisibility(r),index:r}})}return[]}},onClick(t,n,s){s.chart.toggleDataVisibility(n.index),s.chart.update()}}},scales:{r:{type:"radialLinear",angleLines:{display:!1},beginAtZero:!0,grid:{circular:!0},pointLabels:{display:!1},startAngle:0}}});class sa extends Ln{}N(sa,"id","pie"),N(sa,"defaults",{cutout:0,rotation:0,circumference:360,radius:"100%"});class Xi extends ye{getLabelAndValue(t){const n=this._cachedMeta.vScale,s=this.getParsed(t);return{label:n.getLabels()[t],value:""+n.getLabelForValue(s[n.axis])}}parseObjectData(t,n,s,i){return bd.bind(this)(t,n,s,i)}update(t){const n=this._cachedMeta,s=n.dataset,i=n.data||[],o=n.iScale.getLabels();if(s.points=i,t!=="resize"){const r=this.resolveDatasetElementOptions(t);this.options.showLine||(r.borderWidth=0);const a={_loop:!0,_fullLoop:o.length===i.length,options:r};this.updateElement(s,void 0,a,t)}this.updateElements(i,0,i.length,t)}updateElements(t,n,s,i){const o=this._cachedMeta.rScale,r=i==="reset";for(let a=n;a<n+s;a++){const l=t[a],c=this.resolveDataElementOptions(a,l.active?"active":i),u=o.getPointPositionForValue(a,this.getParsed(a).r),f=r?o.xCenter:u.x,h=r?o.yCenter:u.y,d={x:f,y:h,angle:u.angle,skip:isNaN(f)||isNaN(h),options:c};this.updateElement(l,a,d,i)}}}N(Xi,"id","radar"),N(Xi,"defaults",{datasetElementType:"line",dataElementType:"point",indexAxis:"r",showLine:!0,elements:{line:{fill:"start"}}}),N(Xi,"overrides",{aspectRatio:1,scales:{r:{type:"radialLinear"}}});class Gi extends ye{getLabelAndValue(t){const n=this._cachedMeta,s=this.chart.data.labels||[],{xScale:i,yScale:o}=n,r=this.getParsed(t),a=i.getLabelForValue(r.x),l=o.getLabelForValue(r.y);return{label:s[t]||"",value:"("+a+", "+l+")"}}update(t){const n=this._cachedMeta,{data:s=[]}=n,i=this.chart._animationsDisabled;let{start:o,count:r}=ld(n,s,i);if(this._drawStart=o,this._drawCount=r,cd(n)&&(o=0,r=s.length),this.options.showLine){this.datasetElementType||this.addElements();const{dataset:a,_dataset:l}=n;a._chart=this.chart,a._datasetIndex=this.index,a._decimated=!!l._decimated,a.points=s;const c=this.resolveDatasetElementOptions(t);c.segment=this.options.segment,this.updateElement(a,void 0,{animated:!i,options:c},t)}else this.datasetElementType&&(delete n.dataset,this.datasetElementType=!1);this.updateElements(s,o,r,t)}addElements(){const{showLine:t}=this.options;!this.datasetElementType&&t&&(this.datasetElementType=this.chart.registry.getElement("line")),super.addElements()}updateElements(t,n,s,i){const o=i==="reset",{iScale:r,vScale:a,_stacked:l,_dataset:c}=this._cachedMeta,u=this.resolveDataElementOptions(n,i),f=this.getSharedOptions(u),h=this.includeOptions(i,f),d=r.axis,p=a.axis,{spanGaps:g,segment:m}=this.options,y=hs(g)?g:Number.POSITIVE_INFINITY,b=this.chart._animationsDisabled||o||i==="none";let w=n>0&&this.getParsed(n-1);for(let v=n;v<n+s;++v){const S=t[v],A=this.getParsed(v),P=b?S:{},E=it(A[p]),k=P[d]=r.getPixelForValue(A[d],v),I=P[p]=o||E?a.getBasePixel():a.getPixelForValue(l?this.applyStack(a,A,l):A[p],v);P.skip=isNaN(k)||isNaN(I)||E,P.stop=v>0&&Math.abs(A[d]-w[d])>y,m&&(P.parsed=A,P.raw=c.data[v]),h&&(P.options=f||this.resolveDataElementOptions(v,S.active?"active":i)),b||this.updateElement(S,v,P,i),w=A}this.updateSharedOptions(f,i,u)}getMaxOverflow(){const t=this._cachedMeta,n=t.data||[];if(!this.options.showLine){let a=0;for(let l=n.length-1;l>=0;--l)a=Math.max(a,n[l].size(this.resolveDataElementOptions(l))/2);return a>0&&a}const s=t.dataset,i=s.options&&s.options.borderWidth||0;if(!n.length)return i;const o=n[0].size(this.resolveDataElementOptions(0)),r=n[n.length-1].size(this.resolveDataElementOptions(n.length-1));return Math.max(i,o,r)/2}}N(Gi,"id","scatter"),N(Gi,"defaults",{datasetElementType:!1,dataElementType:"point",showLine:!1,fill:!1}),N(Gi,"overrides",{interaction:{mode:"point"},scales:{x:{type:"linear"},y:{type:"linear"}}});var C0=Object.freeze({__proto__:null,BarController:Ki,BubbleController:qi,DoughnutController:Ln,LineController:Yi,PieController:sa,PolarAreaController:Xs,RadarController:Xi,ScatterController:Gi});function Pn(){throw new Error("This method is not implemented: Check that a complete date adapter is provided.")}class sl{constructor(t){N(this,"options");this.options=t||{}}static override(t){Object.assign(sl.prototype,t)}init(){}formats(){return Pn()}parse(){return Pn()}format(){return Pn()}add(){return Pn()}diff(){return Pn()}startOf(){return Pn()}endOf(){return Pn()}}var P0={_date:sl};function E0(e,t,n,s){const{controller:i,data:o,_sorted:r}=e,a=i._cachedMeta.iScale,l=e.dataset&&e.dataset.options?e.dataset.options.spanGaps:null;if(a&&t===a.axis&&t!=="r"&&r&&o.length){const c=a._reversePixels?X_:qe;if(s){if(i._sharedOptions){const u=o[0],f=typeof u.getRange=="function"&&u.getRange(t);if(f){const h=c(o,t,n-f),d=c(o,t,n+f);return{lo:h.lo,hi:d.hi}}}}else{const u=c(o,t,n);if(l){const{vScale:f}=i._cachedMeta,{_parsed:h}=e,d=h.slice(0,u.lo+1).reverse().findIndex(g=>!it(g[f.axis]));u.lo-=Math.max(0,d);const p=h.slice(u.hi).findIndex(g=>!it(g[f.axis]));u.hi+=Math.max(0,p)}return u}}return{lo:0,hi:o.length-1}}function Xo(e,t,n,s,i){const o=e.getSortedVisibleDatasetMetas(),r=n[t];for(let a=0,l=o.length;a<l;++a){const{index:c,data:u}=o[a],{lo:f,hi:h}=E0(o[a],t,r,i);for(let d=f;d<=h;++d){const p=u[d];p.skip||s(p,c,d)}}}function k0(e){const t=e.indexOf("x")!==-1,n=e.indexOf("y")!==-1;return function(s,i){const o=t?Math.abs(s.x-i.x):0,r=n?Math.abs(s.y-i.y):0;return Math.sqrt(Math.pow(o,2)+Math.pow(r,2))}}function wr(e,t,n,s,i){const o=[];return!i&&!e.isPointInArea(t)||Xo(e,n,t,function(a,l,c){!i&&!Ye(a,e.chartArea,0)||a.inRange(t.x,t.y,s)&&o.push({element:a,datasetIndex:l,index:c})},!0),o}function A0(e,t,n,s){let i=[];function o(r,a,l){const{startAngle:c,endAngle:u}=r.getProps(["startAngle","endAngle"],s),{angle:f}=sd(r,{x:t.x,y:t.y});ai(f,c,u)&&i.push({element:r,datasetIndex:a,index:l})}return Xo(e,n,t,o),i}function O0(e,t,n,s,i,o){let r=[];const a=k0(n);let l=Number.POSITIVE_INFINITY;function c(u,f,h){const d=u.inRange(t.x,t.y,i);if(s&&!d)return;const p=u.getCenterPoint(i);if(!(!!o||e.isPointInArea(p))&&!d)return;const m=a(t,p);m<l?(r=[{element:u,datasetIndex:f,index:h}],l=m):m===l&&r.push({element:u,datasetIndex:f,index:h})}return Xo(e,n,t,c),r}function Sr(e,t,n,s,i,o){return!o&&!e.isPointInArea(t)?[]:n==="r"&&!s?A0(e,t,n,i):O0(e,t,n,s,i,o)}function tu(e,t,n,s,i){const o=[],r=n==="x"?"inXRange":"inYRange";let a=!1;return Xo(e,n,t,(l,c,u)=>{l[r]&&l[r](t[n],i)&&(o.push({element:l,datasetIndex:c,index:u}),a=a||l.inRange(t.x,t.y,i))}),s&&!a?[]:o}var R0={modes:{index(e,t,n,s){const i=An(t,e),o=n.axis||"x",r=n.includeInvisible||!1,a=n.intersect?wr(e,i,o,s,r):Sr(e,i,o,!1,s,r),l=[];return a.length?(e.getSortedVisibleDatasetMetas().forEach(c=>{const u=a[0].index,f=c.data[u];f&&!f.skip&&l.push({element:f,datasetIndex:c.index,index:u})}),l):[]},dataset(e,t,n,s){const i=An(t,e),o=n.axis||"xy",r=n.includeInvisible||!1;let a=n.intersect?wr(e,i,o,s,r):Sr(e,i,o,!1,s,r);if(a.length>0){const l=a[0].datasetIndex,c=e.getDatasetMeta(l).data;a=[];for(let u=0;u<c.length;++u)a.push({element:c[u],datasetIndex:l,index:u})}return a},point(e,t,n,s){const i=An(t,e),o=n.axis||"xy",r=n.includeInvisible||!1;return wr(e,i,o,s,r)},nearest(e,t,n,s){const i=An(t,e),o=n.axis||"xy",r=n.includeInvisible||!1;return Sr(e,i,o,n.intersect,s,r)},x(e,t,n,s){const i=An(t,e);return tu(e,i,"x",n.intersect,s)},y(e,t,n,s){const i=An(t,e);return tu(e,i,"y",n.intersect,s)}}};const kd=["left","top","right","bottom"];function Cs(e,t){return e.filter(n=>n.pos===t)}function eu(e,t){return e.filter(n=>kd.indexOf(n.pos)===-1&&n.box.axis===t)}function Ps(e,t){return e.sort((n,s)=>{const i=t?s:n,o=t?n:s;return i.weight===o.weight?i.index-o.index:i.weight-o.weight})}function T0(e){const t=[];let n,s,i,o,r,a;for(n=0,s=(e||[]).length;n<s;++n)i=e[n],{position:o,options:{stack:r,stackWeight:a=1}}=i,t.push({index:n,box:i,pos:o,horizontal:i.isHorizontal(),weight:i.weight,stack:r&&o+r,stackWeight:a});return t}function D0(e){const t={};for(const n of e){const{stack:s,pos:i,stackWeight:o}=n;if(!s||!kd.includes(i))continue;const r=t[s]||(t[s]={count:0,placed:0,weight:0,size:0});r.count++,r.weight+=o}return t}function L0(e,t){const n=D0(e),{vBoxMaxWidth:s,hBoxMaxHeight:i}=t;let o,r,a;for(o=0,r=e.length;o<r;++o){a=e[o];const{fullSize:l}=a.box,c=n[a.stack],u=c&&a.stackWeight/c.weight;a.horizontal?(a.width=u?u*s:l&&t.availableWidth,a.height=i):(a.width=s,a.height=u?u*i:l&&t.availableHeight)}return n}function F0(e){const t=T0(e),n=Ps(t.filter(c=>c.box.fullSize),!0),s=Ps(Cs(t,"left"),!0),i=Ps(Cs(t,"right")),o=Ps(Cs(t,"top"),!0),r=Ps(Cs(t,"bottom")),a=eu(t,"x"),l=eu(t,"y");return{fullSize:n,leftAndTop:s.concat(o),rightAndBottom:i.concat(l).concat(r).concat(a),chartArea:Cs(t,"chartArea"),vertical:s.concat(i).concat(l),horizontal:o.concat(r).concat(a)}}function nu(e,t,n,s){return Math.max(e[n],t[n])+Math.max(e[s],t[s])}function Ad(e,t){e.top=Math.max(e.top,t.top),e.left=Math.max(e.left,t.left),e.bottom=Math.max(e.bottom,t.bottom),e.right=Math.max(e.right,t.right)}function I0(e,t,n,s){const{pos:i,box:o}=n,r=e.maxPadding;if(!ot(i)){n.size&&(e[i]-=n.size);const f=s[n.stack]||{size:0,count:1};f.size=Math.max(f.size,n.horizontal?o.height:o.width),n.size=f.size/f.count,e[i]+=n.size}o.getPadding&&Ad(r,o.getPadding());const a=Math.max(0,t.outerWidth-nu(r,e,"left","right")),l=Math.max(0,t.outerHeight-nu(r,e,"top","bottom")),c=a!==e.w,u=l!==e.h;return e.w=a,e.h=l,n.horizontal?{same:c,other:u}:{same:u,other:c}}function N0(e){const t=e.maxPadding;function n(s){const i=Math.max(t[s]-e[s],0);return e[s]+=i,i}e.y+=n("top"),e.x+=n("left"),n("right"),n("bottom")}function B0(e,t){const n=t.maxPadding;function s(i){const o={left:0,top:0,right:0,bottom:0};return i.forEach(r=>{o[r]=Math.max(t[r],n[r])}),o}return s(e?["left","right"]:["top","bottom"])}function Ds(e,t,n,s){const i=[];let o,r,a,l,c,u;for(o=0,r=e.length,c=0;o<r;++o){a=e[o],l=a.box,l.update(a.width||t.w,a.height||t.h,B0(a.horizontal,t));const{same:f,other:h}=I0(t,n,a,s);c|=f&&i.length,u=u||h,l.fullSize||i.push(a)}return c&&Ds(i,t,n,s)||u}function Ri(e,t,n,s,i){e.top=n,e.left=t,e.right=t+s,e.bottom=n+i,e.width=s,e.height=i}function su(e,t,n,s){const i=n.padding;let{x:o,y:r}=t;for(const a of e){const l=a.box,c=s[a.stack]||{placed:0,weight:1},u=a.stackWeight/c.weight||1;if(a.horizontal){const f=t.w*u,h=c.size||l.height;ri(c.start)&&(r=c.start),l.fullSize?Ri(l,i.left,r,n.outerWidth-i.right-i.left,h):Ri(l,t.left+c.placed,r,f,h),c.start=r,c.placed+=f,r=l.bottom}else{const f=t.h*u,h=c.size||l.width;ri(c.start)&&(o=c.start),l.fullSize?Ri(l,o,i.top,h,n.outerHeight-i.bottom-i.top):Ri(l,o,t.top+c.placed,h,f),c.start=o,c.placed+=f,o=l.right}}t.x=o,t.y=r}var Wt={addBox(e,t){e.boxes||(e.boxes=[]),t.fullSize=t.fullSize||!1,t.position=t.position||"top",t.weight=t.weight||0,t._layers=t._layers||function(){return[{z:0,draw(n){t.draw(n)}}]},e.boxes.push(t)},removeBox(e,t){const n=e.boxes?e.boxes.indexOf(t):-1;n!==-1&&e.boxes.splice(n,1)},configure(e,t,n){t.fullSize=n.fullSize,t.position=n.position,t.weight=n.weight},update(e,t,n,s){if(!e)return;const i=$t(e.options.layout.padding),o=Math.max(t-i.width,0),r=Math.max(n-i.height,0),a=F0(e.boxes),l=a.vertical,c=a.horizontal;ht(e.boxes,g=>{typeof g.beforeLayout=="function"&&g.beforeLayout()});const u=l.reduce((g,m)=>m.box.options&&m.box.options.display===!1?g:g+1,0)||1,f=Object.freeze({outerWidth:t,outerHeight:n,padding:i,availableWidth:o,availableHeight:r,vBoxMaxWidth:o/2/u,hBoxMaxHeight:r/2}),h=Object.assign({},i);Ad(h,$t(s));const d=Object.assign({maxPadding:h,w:o,h:r,x:i.left,y:i.top},i),p=L0(l.concat(c),f);Ds(a.fullSize,d,f,p),Ds(l,d,f,p),Ds(c,d,f,p)&&Ds(l,d,f,p),N0(d),su(a.leftAndTop,d,f,p),d.x+=d.w,d.y+=d.h,su(a.rightAndBottom,d,f,p),e.chartArea={left:d.left,top:d.top,right:d.left+d.w,bottom:d.top+d.h,height:d.h,width:d.w},ht(a.chartArea,g=>{const m=g.box;Object.assign(m,e.chartArea),m.update(d.w,d.h,{left:0,top:0,right:0,bottom:0})})}};class Od{acquireContext(t,n){}releaseContext(t){return!1}addEventListener(t,n,s){}removeEventListener(t,n,s){}getDevicePixelRatio(){return 1}getMaximumSize(t,n,s,i){return n=Math.max(0,n||t.width),s=s||t.height,{width:n,height:Math.max(0,i?Math.floor(n/i):s)}}isAttached(t){return!0}updateConfig(t){}}class z0 extends Od{acquireContext(t){return t&&t.getContext&&t.getContext("2d")||null}updateConfig(t){t.options.animation=!1}}const Ji="$chartjs",H0={touchstart:"mousedown",touchmove:"mousemove",touchend:"mouseup",pointerenter:"mouseenter",pointerdown:"mousedown",pointermove:"mousemove",pointerup:"mouseup",pointerleave:"mouseout",pointerout:"mouseout"},iu=e=>e===null||e==="";function j0(e,t){const n=e.style,s=e.getAttribute("height"),i=e.getAttribute("width");if(e[Ji]={initial:{height:s,width:i,style:{display:n.display,height:n.height,width:n.width}}},n.display=n.display||"block",n.boxSizing=n.boxSizing||"border-box",iu(i)){const o=jc(e,"width");o!==void 0&&(e.width=o)}if(iu(s))if(e.style.height==="")e.height=e.width/(t||2);else{const o=jc(e,"height");o!==void 0&&(e.height=o)}return e}const Rd=Vx?{passive:!0}:!1;function V0(e,t,n){e&&e.addEventListener(t,n,Rd)}function W0(e,t,n){e&&e.canvas&&e.canvas.removeEventListener(t,n,Rd)}function $0(e,t){const n=H0[e.type]||e.type,{x:s,y:i}=An(e,t);return{type:n,chart:t,native:e,x:s!==void 0?s:null,y:i!==void 0?i:null}}function vo(e,t){for(const n of e)if(n===t||n.contains(t))return!0}function U0(e,t,n){const s=e.canvas,i=new MutationObserver(o=>{let r=!1;for(const a of o)r=r||vo(a.addedNodes,s),r=r&&!vo(a.removedNodes,s);r&&n()});return i.observe(document,{childList:!0,subtree:!0}),i}function K0(e,t,n){const s=e.canvas,i=new MutationObserver(o=>{let r=!1;for(const a of o)r=r||vo(a.removedNodes,s),r=r&&!vo(a.addedNodes,s);r&&n()});return i.observe(document,{childList:!0,subtree:!0}),i}const ci=new Map;let ou=0;function Td(){const e=window.devicePixelRatio;e!==ou&&(ou=e,ci.forEach((t,n)=>{n.currentDevicePixelRatio!==e&&t()}))}function q0(e,t){ci.size||window.addEventListener("resize",Td),ci.set(e,t)}function Y0(e){ci.delete(e),ci.size||window.removeEventListener("resize",Td)}function X0(e,t,n){const s=e.canvas,i=s&&nl(s);if(!i)return;const o=ad((a,l)=>{const c=i.clientWidth;n(a,l),c<i.clientWidth&&n()},window),r=new ResizeObserver(a=>{const l=a[0],c=l.contentRect.width,u=l.contentRect.height;c===0&&u===0||o(c,u)});return r.observe(i),q0(e,o),r}function Mr(e,t,n){n&&n.disconnect(),t==="resize"&&Y0(e)}function G0(e,t,n){const s=e.canvas,i=ad(o=>{e.ctx!==null&&n($0(o,e))},e);return V0(s,t,i),i}class J0 extends Od{acquireContext(t,n){const s=t&&t.getContext&&t.getContext("2d");return s&&s.canvas===t?(j0(t,n),s):null}releaseContext(t){const n=t.canvas;if(!n[Ji])return!1;const s=n[Ji].initial;["height","width"].forEach(o=>{const r=s[o];it(r)?n.removeAttribute(o):n.setAttribute(o,r)});const i=s.style||{};return Object.keys(i).forEach(o=>{n.style[o]=i[o]}),n.width=n.width,delete n[Ji],!0}addEventListener(t,n,s){this.removeEventListener(t,n);const i=t.$proxies||(t.$proxies={}),r={attach:U0,detach:K0,resize:X0}[n]||G0;i[n]=r(t,n,s)}removeEventListener(t,n){const s=t.$proxies||(t.$proxies={}),i=s[n];if(!i)return;({attach:Mr,detach:Mr,resize:Mr}[n]||W0)(t,n,i),s[n]=void 0}getDevicePixelRatio(){return window.devicePixelRatio}getMaximumSize(t,n,s,i){return jx(t,n,s,i)}isAttached(t){const n=t&&nl(t);return!!(n&&n.isConnected)}}function Q0(e){return!el()||typeof OffscreenCanvas<"u"&&e instanceof OffscreenCanvas?z0:J0}var Bi;let Qe=(Bi=class{constructor(){N(this,"x");N(this,"y");N(this,"active",!1);N(this,"options");N(this,"$animations")}tooltipPosition(t){const{x:n,y:s}=this.getProps(["x","y"],t);return{x:n,y:s}}hasValue(){return hs(this.x)&&hs(this.y)}getProps(t,n){const s=this.$animations;if(!n||!s)return this;const i={};return t.forEach(o=>{i[o]=s[o]&&s[o].active()?s[o]._to:this[o]}),i}},N(Bi,"defaults",{}),N(Bi,"defaultRoutes"),Bi);function Z0(e,t){const n=e.options.ticks,s=tv(e),i=Math.min(n.maxTicksLimit||s,s),o=n.major.enabled?nv(t):[],r=o.length,a=o[0],l=o[r-1],c=[];if(r>i)return sv(t,c,o,r/i),c;const u=ev(o,t,i);if(r>0){let f,h;const d=r>1?Math.round((l-a)/(r-1)):null;for(Ti(t,c,u,it(d)?0:a-d,a),f=0,h=r-1;f<h;f++)Ti(t,c,u,o[f],o[f+1]);return Ti(t,c,u,l,it(d)?t.length:l+d),c}return Ti(t,c,u),c}function tv(e){const t=e.options.offset,n=e._tickSize(),s=e._length/n+(t?0:1),i=e._maxLength/n;return Math.floor(Math.min(s,i))}function ev(e,t,n){const s=iv(e),i=t.length/n;if(!s)return Math.max(i,1);const o=$_(s);for(let r=0,a=o.length-1;r<a;r++){const l=o[r];if(l>i)return l}return Math.max(i,1)}function nv(e){const t=[];let n,s;for(n=0,s=e.length;n<s;n++)e[n].major&&t.push(n);return t}function sv(e,t,n,s){let i=0,o=n[0],r;for(s=Math.ceil(s),r=0;r<e.length;r++)r===o&&(t.push(e[r]),i++,o=n[i*s])}function Ti(e,t,n,s,i){const o=st(s,0),r=Math.min(st(i,e.length),e.length);let a=0,l,c,u;for(n=Math.ceil(n),i&&(l=i-s,n=l/Math.floor(l/n)),u=o;u<0;)a++,u=Math.round(o+a*n);for(c=Math.max(o,0);c<r;c++)c===u&&(t.push(e[c]),a++,u=Math.round(o+a*n))}function iv(e){const t=e.length;let n,s;if(t<2)return!1;for(s=e[0],n=1;n<t;++n)if(e[n]-e[n-1]!==s)return!1;return s}const ov=e=>e==="left"?"right":e==="right"?"left":e,ru=(e,t,n)=>t==="top"||t==="left"?e[t]+n:e[t]-n,au=(e,t)=>Math.min(t||e,e);function lu(e,t){const n=[],s=e.length/t,i=e.length;let o=0;for(;o<i;o+=s)n.push(e[Math.floor(o)]);return n}function rv(e,t,n){const s=e.ticks.length,i=Math.min(t,s-1),o=e._startPixel,r=e._endPixel,a=1e-6;let l=e.getPixelForTick(i),c;if(!(n&&(s===1?c=Math.max(l-o,r-l):t===0?c=(e.getPixelForTick(1)-l)/2:c=(l-e.getPixelForTick(i-1))/2,l+=i<t?c:-c,l<o-a||l>r+a)))return l}function av(e,t){ht(e,n=>{const s=n.gc,i=s.length/2;let o;if(i>t){for(o=0;o<i;++o)delete n.data[s[o]];s.splice(0,i)}})}function Es(e){return e.drawTicks?e.tickLength:0}function cu(e,t){if(!e.display)return 0;const n=Tt(e.font,t),s=$t(e.padding);return(wt(e.text)?e.text.length:1)*n.lineHeight+s.height}function lv(e,t){return xn(e,{scale:t,type:"scale"})}function cv(e,t,n){return xn(e,{tick:n,index:t,type:"tick"})}function uv(e,t,n){let s=Xa(e);return(n&&t!=="right"||!n&&t==="right")&&(s=ov(s)),s}function fv(e,t,n,s){const{top:i,left:o,bottom:r,right:a,chart:l}=e,{chartArea:c,scales:u}=l;let f=0,h,d,p;const g=r-i,m=a-o;if(e.isHorizontal()){if(d=Bt(s,o,a),ot(n)){const y=Object.keys(n)[0],b=n[y];p=u[y].getPixelForValue(b)+g-t}else n==="center"?p=(c.bottom+c.top)/2+g-t:p=ru(e,n,t);h=a-o}else{if(ot(n)){const y=Object.keys(n)[0],b=n[y];d=u[y].getPixelForValue(b)-m+t}else n==="center"?d=(c.left+c.right)/2-m+t:d=ru(e,n,t);p=Bt(s,r,i),f=n==="left"?-Et:Et}return{titleX:d,titleY:p,maxWidth:h,rotation:f}}class Un extends Qe{constructor(t){super(),this.id=t.id,this.type=t.type,this.options=void 0,this.ctx=t.ctx,this.chart=t.chart,this.top=void 0,this.bottom=void 0,this.left=void 0,this.right=void 0,this.width=void 0,this.height=void 0,this._margins={left:0,right:0,top:0,bottom:0},this.maxWidth=void 0,this.maxHeight=void 0,this.paddingTop=void 0,this.paddingBottom=void 0,this.paddingLeft=void 0,this.paddingRight=void 0,this.axis=void 0,this.labelRotation=void 0,this.min=void 0,this.max=void 0,this._range=void 0,this.ticks=[],this._gridLineItems=null,this._labelItems=null,this._labelSizes=null,this._length=0,this._maxLength=0,this._longestTextCache={},this._startPixel=void 0,this._endPixel=void 0,this._reversePixels=!1,this._userMax=void 0,this._userMin=void 0,this._suggestedMax=void 0,this._suggestedMin=void 0,this._ticksLength=0,this._borderValue=0,this._cache={},this._dataLimitsCached=!1,this.$context=void 0}init(t){this.options=t.setContext(this.getContext()),this.axis=t.axis,this._userMin=this.parse(t.min),this._userMax=this.parse(t.max),this._suggestedMin=this.parse(t.suggestedMin),this._suggestedMax=this.parse(t.suggestedMax)}parse(t,n){return t}getUserBounds(){let{_userMin:t,_userMax:n,_suggestedMin:s,_suggestedMax:i}=this;return t=ie(t,Number.POSITIVE_INFINITY),n=ie(n,Number.NEGATIVE_INFINITY),s=ie(s,Number.POSITIVE_INFINITY),i=ie(i,Number.NEGATIVE_INFINITY),{min:ie(t,s),max:ie(n,i),minDefined:Ct(t),maxDefined:Ct(n)}}getMinMax(t){let{min:n,max:s,minDefined:i,maxDefined:o}=this.getUserBounds(),r;if(i&&o)return{min:n,max:s};const a=this.getMatchingVisibleMetas();for(let l=0,c=a.length;l<c;++l)r=a[l].controller.getMinMax(this,t),i||(n=Math.min(n,r.min)),o||(s=Math.max(s,r.max));return n=o&&n>s?s:n,s=i&&n>s?n:s,{min:ie(n,ie(s,n)),max:ie(s,ie(n,s))}}getPadding(){return{left:this.paddingLeft||0,top:this.paddingTop||0,right:this.paddingRight||0,bottom:this.paddingBottom||0}}getTicks(){return this.ticks}getLabels(){const t=this.chart.data;return this.options.labels||(this.isHorizontal()?t.xLabels:t.yLabels)||t.labels||[]}getLabelItems(t=this.chart.chartArea){return this._labelItems||(this._labelItems=this._computeLabelItems(t))}beforeLayout(){this._cache={},this._dataLimitsCached=!1}beforeUpdate(){_t(this.options.beforeUpdate,[this])}update(t,n,s){const{beginAtZero:i,grace:o,ticks:r}=this.options,a=r.sampleSize;this.beforeUpdate(),this.maxWidth=t,this.maxHeight=n,this._margins=s=Object.assign({left:0,right:0,top:0,bottom:0},s),this.ticks=null,this._labelSizes=null,this._gridLineItems=null,this._labelItems=null,this.beforeSetDimensions(),this.setDimensions(),this.afterSetDimensions(),this._maxLength=this.isHorizontal()?this.width+s.left+s.right:this.height+s.top+s.bottom,this._dataLimitsCached||(this.beforeDataLimits(),this.determineDataLimits(),this.afterDataLimits(),this._range=_x(this,o,i),this._dataLimitsCached=!0),this.beforeBuildTicks(),this.ticks=this.buildTicks()||[],this.afterBuildTicks();const l=a<this.ticks.length;this._convertTicksToLabels(l?lu(this.ticks,a):this.ticks),this.configure(),this.beforeCalculateLabelRotation(),this.calculateLabelRotation(),this.afterCalculateLabelRotation(),r.display&&(r.autoSkip||r.source==="auto")&&(this.ticks=Z0(this,this.ticks),this._labelSizes=null,this.afterAutoSkip()),l&&this._convertTicksToLabels(this.ticks),this.beforeFit(),this.fit(),this.afterFit(),this.afterUpdate()}configure(){let t=this.options.reverse,n,s;this.isHorizontal()?(n=this.left,s=this.right):(n=this.top,s=this.bottom,t=!t),this._startPixel=n,this._endPixel=s,this._reversePixels=t,this._length=s-n,this._alignToPixels=this.options.alignToPixels}afterUpdate(){_t(this.options.afterUpdate,[this])}beforeSetDimensions(){_t(this.options.beforeSetDimensions,[this])}setDimensions(){this.isHorizontal()?(this.width=this.maxWidth,this.left=0,this.right=this.width):(this.height=this.maxHeight,this.top=0,this.bottom=this.height),this.paddingLeft=0,this.paddingTop=0,this.paddingRight=0,this.paddingBottom=0}afterSetDimensions(){_t(this.options.afterSetDimensions,[this])}_callHooks(t){this.chart.notifyPlugins(t,this.getContext()),_t(this.options[t],[this])}beforeDataLimits(){this._callHooks("beforeDataLimits")}determineDataLimits(){}afterDataLimits(){this._callHooks("afterDataLimits")}beforeBuildTicks(){this._callHooks("beforeBuildTicks")}buildTicks(){return[]}afterBuildTicks(){this._callHooks("afterBuildTicks")}beforeTickToLabelConversion(){_t(this.options.beforeTickToLabelConversion,[this])}generateTickLabels(t){const n=this.options.ticks;let s,i,o;for(s=0,i=t.length;s<i;s++)o=t[s],o.label=_t(n.callback,[o.value,s,t],this)}afterTickToLabelConversion(){_t(this.options.afterTickToLabelConversion,[this])}beforeCalculateLabelRotation(){_t(this.options.beforeCalculateLabelRotation,[this])}calculateLabelRotation(){const t=this.options,n=t.ticks,s=au(this.ticks.length,t.ticks.maxTicksLimit),i=n.minRotation||0,o=n.maxRotation;let r=i,a,l,c;if(!this._isVisible()||!n.display||i>=o||s<=1||!this.isHorizontal()){this.labelRotation=i;return}const u=this._getLabelSizes(),f=u.widest.width,h=u.highest.height,d=Ft(this.chart.width-f,0,this.maxWidth);a=t.offset?this.maxWidth/s:d/(s-1),f+6>a&&(a=d/(s-(t.offset?.5:1)),l=this.maxHeight-Es(t.grid)-n.padding-cu(t.title,this.chart.options.font),c=Math.sqrt(f*f+h*h),r=qa(Math.min(Math.asin(Ft((u.highest.height+6)/a,-1,1)),Math.asin(Ft(l/c,-1,1))-Math.asin(Ft(h/c,-1,1)))),r=Math.max(i,Math.min(o,r))),this.labelRotation=r}afterCalculateLabelRotation(){_t(this.options.afterCalculateLabelRotation,[this])}afterAutoSkip(){}beforeFit(){_t(this.options.beforeFit,[this])}fit(){const t={width:0,height:0},{chart:n,options:{ticks:s,title:i,grid:o}}=this,r=this._isVisible(),a=this.isHorizontal();if(r){const l=cu(i,n.options.font);if(a?(t.width=this.maxWidth,t.height=Es(o)+l):(t.height=this.maxHeight,t.width=Es(o)+l),s.display&&this.ticks.length){const{first:c,last:u,widest:f,highest:h}=this._getLabelSizes(),d=s.padding*2,p=me(this.labelRotation),g=Math.cos(p),m=Math.sin(p);if(a){const y=s.mirror?0:m*f.width+g*h.height;t.height=Math.min(this.maxHeight,t.height+y+d)}else{const y=s.mirror?0:g*f.width+m*h.height;t.width=Math.min(this.maxWidth,t.width+y+d)}this._calculatePadding(c,u,m,g)}}this._handleMargins(),a?(this.width=this._length=n.width-this._margins.left-this._margins.right,this.height=t.height):(this.width=t.width,this.height=this._length=n.height-this._margins.top-this._margins.bottom)}_calculatePadding(t,n,s,i){const{ticks:{align:o,padding:r},position:a}=this.options,l=this.labelRotation!==0,c=a!=="top"&&this.axis==="x";if(this.isHorizontal()){const u=this.getPixelForTick(0)-this.left,f=this.right-this.getPixelForTick(this.ticks.length-1);let h=0,d=0;l?c?(h=i*t.width,d=s*n.height):(h=s*t.height,d=i*n.width):o==="start"?d=n.width:o==="end"?h=t.width:o!=="inner"&&(h=t.width/2,d=n.width/2),this.paddingLeft=Math.max((h-u+r)*this.width/(this.width-u),0),this.paddingRight=Math.max((d-f+r)*this.width/(this.width-f),0)}else{let u=n.height/2,f=t.height/2;o==="start"?(u=0,f=t.height):o==="end"&&(u=n.height,f=0),this.paddingTop=u+r,this.paddingBottom=f+r}}_handleMargins(){this._margins&&(this._margins.left=Math.max(this.paddingLeft,this._margins.left),this._margins.top=Math.max(this.paddingTop,this._margins.top),this._margins.right=Math.max(this.paddingRight,this._margins.right),this._margins.bottom=Math.max(this.paddingBottom,this._margins.bottom))}afterFit(){_t(this.options.afterFit,[this])}isHorizontal(){const{axis:t,position:n}=this.options;return n==="top"||n==="bottom"||t==="x"}isFullSize(){return this.options.fullSize}_convertTicksToLabels(t){this.beforeTickToLabelConversion(),this.generateTickLabels(t);let n,s;for(n=0,s=t.length;n<s;n++)it(t[n].label)&&(t.splice(n,1),s--,n--);this.afterTickToLabelConversion()}_getLabelSizes(){let t=this._labelSizes;if(!t){const n=this.options.ticks.sampleSize;let s=this.ticks;n<s.length&&(s=lu(s,n)),this._labelSizes=t=this._computeLabelSizes(s,s.length,this.options.ticks.maxTicksLimit)}return t}_computeLabelSizes(t,n,s){const{ctx:i,_longestTextCache:o}=this,r=[],a=[],l=Math.floor(n/au(n,s));let c=0,u=0,f,h,d,p,g,m,y,b,w,v,S;for(f=0;f<n;f+=l){if(p=t[f].label,g=this._resolveTickFontOptions(f),i.font=m=g.string,y=o[m]=o[m]||{data:{},gc:[]},b=g.lineHeight,w=v=0,!it(p)&&!wt(p))w=_o(i,y.data,y.gc,w,p),v=b;else if(wt(p))for(h=0,d=p.length;h<d;++h)S=p[h],!it(S)&&!wt(S)&&(w=_o(i,y.data,y.gc,w,S),v+=b);r.push(w),a.push(v),c=Math.max(w,c),u=Math.max(v,u)}av(o,n);const A=r.indexOf(c),P=a.indexOf(u),E=k=>({width:r[k]||0,height:a[k]||0});return{first:E(0),last:E(n-1),widest:E(A),highest:E(P),widths:r,heights:a}}getLabelForValue(t){return t}getPixelForValue(t,n){return NaN}getValueForPixel(t){}getPixelForTick(t){const n=this.ticks;return t<0||t>n.length-1?null:this.getPixelForValue(n[t].value)}getPixelForDecimal(t){this._reversePixels&&(t=1-t);const n=this._startPixel+t*this._length;return Y_(this._alignToPixels?Cn(this.chart,n,0):n)}getDecimalForPixel(t){const n=(t-this._startPixel)/this._length;return this._reversePixels?1-n:n}getBasePixel(){return this.getPixelForValue(this.getBaseValue())}getBaseValue(){const{min:t,max:n}=this;return t<0&&n<0?n:t>0&&n>0?t:0}getContext(t){const n=this.ticks||[];if(t>=0&&t<n.length){const s=n[t];return s.$context||(s.$context=cv(this.getContext(),t,s))}return this.$context||(this.$context=lv(this.chart.getContext(),this))}_tickSize(){const t=this.options.ticks,n=me(this.labelRotation),s=Math.abs(Math.cos(n)),i=Math.abs(Math.sin(n)),o=this._getLabelSizes(),r=t.autoSkipPadding||0,a=o?o.widest.width+r:0,l=o?o.highest.height+r:0;return this.isHorizontal()?l*s>a*i?a/s:l/i:l*i<a*s?l/s:a/i}_isVisible(){const t=this.options.display;return t!=="auto"?!!t:this.getMatchingVisibleMetas().length>0}_computeGridLineItems(t){const n=this.axis,s=this.chart,i=this.options,{grid:o,position:r,border:a}=i,l=o.offset,c=this.isHorizontal(),f=this.ticks.length+(l?1:0),h=Es(o),d=[],p=a.setContext(this.getContext()),g=p.display?p.width:0,m=g/2,y=function(G){return Cn(s,G,g)};let b,w,v,S,A,P,E,k,I,H,L,X;if(r==="top")b=y(this.bottom),P=this.bottom-h,k=b-m,H=y(t.top)+m,X=t.bottom;else if(r==="bottom")b=y(this.top),H=t.top,X=y(t.bottom)-m,P=b+m,k=this.top+h;else if(r==="left")b=y(this.right),A=this.right-h,E=b-m,I=y(t.left)+m,L=t.right;else if(r==="right")b=y(this.left),I=t.left,L=y(t.right)-m,A=b+m,E=this.left+h;else if(n==="x"){if(r==="center")b=y((t.top+t.bottom)/2+.5);else if(ot(r)){const G=Object.keys(r)[0],U=r[G];b=y(this.chart.scales[G].getPixelForValue(U))}H=t.top,X=t.bottom,P=b+m,k=P+h}else if(n==="y"){if(r==="center")b=y((t.left+t.right)/2);else if(ot(r)){const G=Object.keys(r)[0],U=r[G];b=y(this.chart.scales[G].getPixelForValue(U))}A=b-m,E=A-h,I=t.left,L=t.right}const rt=st(i.ticks.maxTicksLimit,f),Z=Math.max(1,Math.ceil(f/rt));for(w=0;w<f;w+=Z){const G=this.getContext(w),U=o.setContext(G),et=a.setContext(G),mt=U.lineWidth,Ut=U.color,Kt=et.dash||[],Pt=et.dashOffset,ae=U.tickWidth,Xt=U.tickColor,Me=U.tickBorderDash||[],At=U.tickBorderDashOffset;v=rv(this,w,l),v!==void 0&&(S=Cn(s,v,mt),c?A=E=I=L=S:P=k=H=X=S,d.push({tx1:A,ty1:P,tx2:E,ty2:k,x1:I,y1:H,x2:L,y2:X,width:mt,color:Ut,borderDash:Kt,borderDashOffset:Pt,tickWidth:ae,tickColor:Xt,tickBorderDash:Me,tickBorderDashOffset:At}))}return this._ticksLength=f,this._borderValue=b,d}_computeLabelItems(t){const n=this.axis,s=this.options,{position:i,ticks:o}=s,r=this.isHorizontal(),a=this.ticks,{align:l,crossAlign:c,padding:u,mirror:f}=o,h=Es(s.grid),d=h+u,p=f?-u:d,g=-me(this.labelRotation),m=[];let y,b,w,v,S,A,P,E,k,I,H,L,X="middle";if(i==="top")A=this.bottom-p,P=this._getXAxisLabelAlignment();else if(i==="bottom")A=this.top+p,P=this._getXAxisLabelAlignment();else if(i==="left"){const Z=this._getYAxisLabelAlignment(h);P=Z.textAlign,S=Z.x}else if(i==="right"){const Z=this._getYAxisLabelAlignment(h);P=Z.textAlign,S=Z.x}else if(n==="x"){if(i==="center")A=(t.top+t.bottom)/2+d;else if(ot(i)){const Z=Object.keys(i)[0],G=i[Z];A=this.chart.scales[Z].getPixelForValue(G)+d}P=this._getXAxisLabelAlignment()}else if(n==="y"){if(i==="center")S=(t.left+t.right)/2-d;else if(ot(i)){const Z=Object.keys(i)[0],G=i[Z];S=this.chart.scales[Z].getPixelForValue(G)}P=this._getYAxisLabelAlignment(h).textAlign}n==="y"&&(l==="start"?X="top":l==="end"&&(X="bottom"));const rt=this._getLabelSizes();for(y=0,b=a.length;y<b;++y){w=a[y],v=w.label;const Z=o.setContext(this.getContext(y));E=this.getPixelForTick(y)+o.labelOffset,k=this._resolveTickFontOptions(y),I=k.lineHeight,H=wt(v)?v.length:1;const G=H/2,U=Z.color,et=Z.textStrokeColor,mt=Z.textStrokeWidth;let Ut=P;r?(S=E,P==="inner"&&(y===b-1?Ut=this.options.reverse?"left":"right":y===0?Ut=this.options.reverse?"right":"left":Ut="center"),i==="top"?c==="near"||g!==0?L=-H*I+I/2:c==="center"?L=-rt.highest.height/2-G*I+I:L=-rt.highest.height+I/2:c==="near"||g!==0?L=I/2:c==="center"?L=rt.highest.height/2-G*I:L=rt.highest.height-H*I,f&&(L*=-1),g!==0&&!Z.showLabelBackdrop&&(S+=I/2*Math.sin(g))):(A=E,L=(1-H)*I/2);let Kt;if(Z.showLabelBackdrop){const Pt=$t(Z.backdropPadding),ae=rt.heights[y],Xt=rt.widths[y];let Me=L-Pt.top,At=0-Pt.left;switch(X){case"middle":Me-=ae/2;break;case"bottom":Me-=ae;break}switch(P){case"center":At-=Xt/2;break;case"right":At-=Xt;break;case"inner":y===b-1?At-=Xt:y>0&&(At-=Xt/2);break}Kt={left:At,top:Me,width:Xt+Pt.width,height:ae+Pt.height,color:Z.backdropColor}}m.push({label:v,font:k,textOffset:L,options:{rotation:g,color:U,strokeColor:et,strokeWidth:mt,textAlign:Ut,textBaseline:X,translation:[S,A],backdrop:Kt}})}return m}_getXAxisLabelAlignment(){const{position:t,ticks:n}=this.options;if(-me(this.labelRotation))return t==="top"?"left":"right";let i="center";return n.align==="start"?i="left":n.align==="end"?i="right":n.align==="inner"&&(i="inner"),i}_getYAxisLabelAlignment(t){const{position:n,ticks:{crossAlign:s,mirror:i,padding:o}}=this.options,r=this._getLabelSizes(),a=t+o,l=r.widest.width;let c,u;return n==="left"?i?(u=this.right+o,s==="near"?c="left":s==="center"?(c="center",u+=l/2):(c="right",u+=l)):(u=this.right-a,s==="near"?c="right":s==="center"?(c="center",u-=l/2):(c="left",u=this.left)):n==="right"?i?(u=this.left+o,s==="near"?c="right":s==="center"?(c="center",u-=l/2):(c="left",u-=l)):(u=this.left+a,s==="near"?c="left":s==="center"?(c="center",u+=l/2):(c="right",u=this.right)):c="right",{textAlign:c,x:u}}_computeLabelArea(){if(this.options.ticks.mirror)return;const t=this.chart,n=this.options.position;if(n==="left"||n==="right")return{top:0,left:this.left,bottom:t.height,right:this.right};if(n==="top"||n==="bottom")return{top:this.top,left:0,bottom:this.bottom,right:t.width}}drawBackground(){const{ctx:t,options:{backgroundColor:n},left:s,top:i,width:o,height:r}=this;n&&(t.save(),t.fillStyle=n,t.fillRect(s,i,o,r),t.restore())}getLineWidthForValue(t){const n=this.options.grid;if(!this._isVisible()||!n.display)return 0;const i=this.ticks.findIndex(o=>o.value===t);return i>=0?n.setContext(this.getContext(i)).lineWidth:0}drawGrid(t){const n=this.options.grid,s=this.ctx,i=this._gridLineItems||(this._gridLineItems=this._computeGridLineItems(t));let o,r;const a=(l,c,u)=>{!u.width||!u.color||(s.save(),s.lineWidth=u.width,s.strokeStyle=u.color,s.setLineDash(u.borderDash||[]),s.lineDashOffset=u.borderDashOffset,s.beginPath(),s.moveTo(l.x,l.y),s.lineTo(c.x,c.y),s.stroke(),s.restore())};if(n.display)for(o=0,r=i.length;o<r;++o){const l=i[o];n.drawOnChartArea&&a({x:l.x1,y:l.y1},{x:l.x2,y:l.y2},l),n.drawTicks&&a({x:l.tx1,y:l.ty1},{x:l.tx2,y:l.ty2},{color:l.tickColor,width:l.tickWidth,borderDash:l.tickBorderDash,borderDashOffset:l.tickBorderDashOffset})}}drawBorder(){const{chart:t,ctx:n,options:{border:s,grid:i}}=this,o=s.setContext(this.getContext()),r=s.display?o.width:0;if(!r)return;const a=i.setContext(this.getContext(0)).lineWidth,l=this._borderValue;let c,u,f,h;this.isHorizontal()?(c=Cn(t,this.left,r)-r/2,u=Cn(t,this.right,a)+a/2,f=h=l):(f=Cn(t,this.top,r)-r/2,h=Cn(t,this.bottom,a)+a/2,c=u=l),n.save(),n.lineWidth=o.width,n.strokeStyle=o.color,n.beginPath(),n.moveTo(c,f),n.lineTo(u,h),n.stroke(),n.restore()}drawLabels(t){if(!this.options.ticks.display)return;const s=this.ctx,i=this._computeLabelArea();i&&Ko(s,i);const o=this.getLabelItems(t);for(const r of o){const a=r.options,l=r.font,c=r.label,u=r.textOffset;$n(s,c,0,u,l,a)}i&&qo(s)}drawTitle(){const{ctx:t,options:{position:n,title:s,reverse:i}}=this;if(!s.display)return;const o=Tt(s.font),r=$t(s.padding),a=s.align;let l=o.lineHeight/2;n==="bottom"||n==="center"||ot(n)?(l+=r.bottom,wt(s.text)&&(l+=o.lineHeight*(s.text.length-1))):l+=r.top;const{titleX:c,titleY:u,maxWidth:f,rotation:h}=fv(this,l,n,a);$n(t,s.text,0,0,o,{color:s.color,maxWidth:f,rotation:h,textAlign:uv(a,n,i),textBaseline:"middle",translation:[c,u]})}draw(t){this._isVisible()&&(this.drawBackground(),this.drawGrid(t),this.drawBorder(),this.drawTitle(),this.drawLabels(t))}_layers(){const t=this.options,n=t.ticks&&t.ticks.z||0,s=st(t.grid&&t.grid.z,-1),i=st(t.border&&t.border.z,0);return!this._isVisible()||this.draw!==Un.prototype.draw?[{z:n,draw:o=>{this.draw(o)}}]:[{z:s,draw:o=>{this.drawBackground(),this.drawGrid(o),this.drawTitle()}},{z:i,draw:()=>{this.drawBorder()}},{z:n,draw:o=>{this.drawLabels(o)}}]}getMatchingVisibleMetas(t){const n=this.chart.getSortedVisibleDatasetMetas(),s=this.axis+"AxisID",i=[];let o,r;for(o=0,r=n.length;o<r;++o){const a=n[o];a[s]===this.id&&(!t||a.type===t)&&i.push(a)}return i}_resolveTickFontOptions(t){const n=this.options.ticks.setContext(this.getContext(t));return Tt(n.font)}_maxDigits(){const t=this._resolveTickFontOptions(0).lineHeight;return(this.isHorizontal()?this.width:this.height)/t}}class Di{constructor(t,n,s){this.type=t,this.scope=n,this.override=s,this.items=Object.create(null)}isForType(t){return Object.prototype.isPrototypeOf.call(this.type.prototype,t.prototype)}register(t){const n=Object.getPrototypeOf(t);let s;pv(n)&&(s=this.register(n));const i=this.items,o=t.id,r=this.scope+"."+o;if(!o)throw new Error("class does not have id: "+t);return o in i||(i[o]=t,hv(t,r,s),this.override&&St.override(t.id,t.overrides)),r}get(t){return this.items[t]}unregister(t){const n=this.items,s=t.id,i=this.scope;s in n&&delete n[s],i&&s in St[i]&&(delete St[i][s],this.override&&delete Wn[s])}}function hv(e,t,n){const s=oi(Object.create(null),[n?St.get(n):{},St.get(t),e.defaults]);St.set(t,s),e.defaultRoutes&&dv(t,e.defaultRoutes),e.descriptors&&St.describe(t,e.descriptors)}function dv(e,t){Object.keys(t).forEach(n=>{const s=n.split("."),i=s.pop(),o=[e].concat(s).join("."),r=t[n].split("."),a=r.pop(),l=r.join(".");St.route(o,i,l,a)})}function pv(e){return"id"in e&&"defaults"in e}class gv{constructor(){this.controllers=new Di(ye,"datasets",!0),this.elements=new Di(Qe,"elements"),this.plugins=new Di(Object,"plugins"),this.scales=new Di(Un,"scales"),this._typedRegistries=[this.controllers,this.scales,this.elements]}add(...t){this._each("register",t)}remove(...t){this._each("unregister",t)}addControllers(...t){this._each("register",t,this.controllers)}addElements(...t){this._each("register",t,this.elements)}addPlugins(...t){this._each("register",t,this.plugins)}addScales(...t){this._each("register",t,this.scales)}getController(t){return this._get(t,this.controllers,"controller")}getElement(t){return this._get(t,this.elements,"element")}getPlugin(t){return this._get(t,this.plugins,"plugin")}getScale(t){return this._get(t,this.scales,"scale")}removeControllers(...t){this._each("unregister",t,this.controllers)}removeElements(...t){this._each("unregister",t,this.elements)}removePlugins(...t){this._each("unregister",t,this.plugins)}removeScales(...t){this._each("unregister",t,this.scales)}_each(t,n,s){[...n].forEach(i=>{const o=s||this._getRegistryForType(i);s||o.isForType(i)||o===this.plugins&&i.id?this._exec(t,o,i):ht(i,r=>{const a=s||this._getRegistryForType(r);this._exec(t,a,r)})})}_exec(t,n,s){const i=Ka(t);_t(s["before"+i],[],s),n[t](s),_t(s["after"+i],[],s)}_getRegistryForType(t){for(let n=0;n<this._typedRegistries.length;n++){const s=this._typedRegistries[n];if(s.isForType(t))return s}return this.plugins}_get(t,n,s){const i=n.get(t);if(i===void 0)throw new Error('"'+t+'" is not a registered '+s+".");return i}}var Te=new gv;class mv{constructor(){this._init=[]}notify(t,n,s,i){n==="beforeInit"&&(this._init=this._createDescriptors(t,!0),this._notify(this._init,t,"install"));const o=i?this._descriptors(t).filter(i):this._descriptors(t),r=this._notify(o,t,n,s);return n==="afterDestroy"&&(this._notify(o,t,"stop"),this._notify(this._init,t,"uninstall")),r}_notify(t,n,s,i){i=i||{};for(const o of t){const r=o.plugin,a=r[s],l=[n,i,o.options];if(_t(a,l,r)===!1&&i.cancelable)return!1}return!0}invalidate(){it(this._cache)||(this._oldCache=this._cache,this._cache=void 0)}_descriptors(t){if(this._cache)return this._cache;const n=this._cache=this._createDescriptors(t);return this._notifyStateChanges(t),n}_createDescriptors(t,n){const s=t&&t.config,i=st(s.options&&s.options.plugins,{}),o=bv(s);return i===!1&&!n?[]:_v(t,o,i,n)}_notifyStateChanges(t){const n=this._oldCache||[],s=this._cache,i=(o,r)=>o.filter(a=>!r.some(l=>a.plugin.id===l.plugin.id));this._notify(i(n,s),t,"stop"),this._notify(i(s,n),t,"start")}}function bv(e){const t={},n=[],s=Object.keys(Te.plugins.items);for(let o=0;o<s.length;o++)n.push(Te.getPlugin(s[o]));const i=e.plugins||[];for(let o=0;o<i.length;o++){const r=i[o];n.indexOf(r)===-1&&(n.push(r),t[r.id]=!0)}return{plugins:n,localIds:t}}function yv(e,t){return!t&&e===!1?null:e===!0?{}:e}function _v(e,{plugins:t,localIds:n},s,i){const o=[],r=e.getContext();for(const a of t){const l=a.id,c=yv(s[l],i);c!==null&&o.push({plugin:a,options:xv(e.config,{plugin:a,local:n[l]},c,r)})}return o}function xv(e,{plugin:t,local:n},s,i){const o=e.pluginScopeKeys(t),r=e.getOptionScopes(s,o);return n&&t.defaults&&r.push(t.defaults),e.createResolver(r,i,[""],{scriptable:!1,indexable:!1,allKeys:!0})}function ia(e,t){const n=St.datasets[e]||{};return((t.datasets||{})[e]||{}).indexAxis||t.indexAxis||n.indexAxis||"x"}function vv(e,t){let n=e;return e==="_index_"?n=t:e==="_value_"&&(n=t==="x"?"y":"x"),n}function wv(e,t){return e===t?"_index_":"_value_"}function uu(e){if(e==="x"||e==="y"||e==="r")return e}function Sv(e){if(e==="top"||e==="bottom")return"x";if(e==="left"||e==="right")return"y"}function oa(e,...t){if(uu(e))return e;for(const n of t){const s=n.axis||Sv(n.position)||e.length>1&&uu(e[0].toLowerCase());if(s)return s}throw new Error(`Cannot determine type of '${e}' axis. Please provide 'axis' or 'position' option.`)}function fu(e,t,n){if(n[t+"AxisID"]===e)return{axis:t}}function Mv(e,t){if(t.data&&t.data.datasets){const n=t.data.datasets.filter(s=>s.xAxisID===e||s.yAxisID===e);if(n.length)return fu(e,"x",n[0])||fu(e,"y",n[0])}return{}}function Cv(e,t){const n=Wn[e.type]||{scales:{}},s=t.scales||{},i=ia(e.type,t),o=Object.create(null);return Object.keys(s).forEach(r=>{const a=s[r];if(!ot(a))return console.error(`Invalid scale configuration for scale: ${r}`);if(a._proxy)return console.warn(`Ignoring resolver passed as options for scale: ${r}`);const l=oa(r,a,Mv(r,e),St.scales[a.type]),c=wv(l,i),u=n.scales||{};o[r]=Us(Object.create(null),[{axis:l},a,u[l],u[c]])}),e.data.datasets.forEach(r=>{const a=r.type||e.type,l=r.indexAxis||ia(a,t),u=(Wn[a]||{}).scales||{};Object.keys(u).forEach(f=>{const h=vv(f,l),d=r[h+"AxisID"]||h;o[d]=o[d]||Object.create(null),Us(o[d],[{axis:h},s[d],u[f]])})}),Object.keys(o).forEach(r=>{const a=o[r];Us(a,[St.scales[a.type],St.scale])}),o}function Dd(e){const t=e.options||(e.options={});t.plugins=st(t.plugins,{}),t.scales=Cv(e,t)}function Ld(e){return e=e||{},e.datasets=e.datasets||[],e.labels=e.labels||[],e}function Pv(e){return e=e||{},e.data=Ld(e.data),Dd(e),e}const hu=new Map,Fd=new Set;function Li(e,t){let n=hu.get(e);return n||(n=t(),hu.set(e,n),Fd.add(n)),n}const ks=(e,t,n)=>{const s=bn(t,n);s!==void 0&&e.add(s)};class Ev{constructor(t){this._config=Pv(t),this._scopeCache=new Map,this._resolverCache=new Map}get platform(){return this._config.platform}get type(){return this._config.type}set type(t){this._config.type=t}get data(){return this._config.data}set data(t){this._config.data=Ld(t)}get options(){return this._config.options}set options(t){this._config.options=t}get plugins(){return this._config.plugins}update(){const t=this._config;this.clearCache(),Dd(t)}clearCache(){this._scopeCache.clear(),this._resolverCache.clear()}datasetScopeKeys(t){return Li(t,()=>[[`datasets.${t}`,""]])}datasetAnimationScopeKeys(t,n){return Li(`${t}.transition.${n}`,()=>[[`datasets.${t}.transitions.${n}`,`transitions.${n}`],[`datasets.${t}`,""]])}datasetElementScopeKeys(t,n){return Li(`${t}-${n}`,()=>[[`datasets.${t}.elements.${n}`,`datasets.${t}`,`elements.${n}`,""]])}pluginScopeKeys(t){const n=t.id,s=this.type;return Li(`${s}-plugin-${n}`,()=>[[`plugins.${n}`,...t.additionalOptionScopes||[]]])}_cachedScopes(t,n){const s=this._scopeCache;let i=s.get(t);return(!i||n)&&(i=new Map,s.set(t,i)),i}getOptionScopes(t,n,s){const{options:i,type:o}=this,r=this._cachedScopes(t,s),a=r.get(n);if(a)return a;const l=new Set;n.forEach(u=>{t&&(l.add(t),u.forEach(f=>ks(l,t,f))),u.forEach(f=>ks(l,i,f)),u.forEach(f=>ks(l,Wn[o]||{},f)),u.forEach(f=>ks(l,St,f)),u.forEach(f=>ks(l,ea,f))});const c=Array.from(l);return c.length===0&&c.push(Object.create(null)),Fd.has(n)&&r.set(n,c),c}chartOptionScopes(){const{options:t,type:n}=this;return[t,Wn[n]||{},St.datasets[n]||{},{type:n},St,ea]}resolveNamedOptions(t,n,s,i=[""]){const o={$shared:!0},{resolver:r,subPrefixes:a}=du(this._resolverCache,t,i);let l=r;if(Av(r,n)){o.$shared=!1,s=yn(s)?s():s;const c=this.createResolver(t,s,a);l=ds(r,s,c)}for(const c of n)o[c]=l[c];return o}createResolver(t,n,s=[""],i){const{resolver:o}=du(this._resolverCache,t,s);return ot(n)?ds(o,n,void 0,i):o}}function du(e,t,n){let s=e.get(t);s||(s=new Map,e.set(t,s));const i=n.join();let o=s.get(i);return o||(o={resolver:Qa(t,n),subPrefixes:n.filter(a=>!a.toLowerCase().includes("hover"))},s.set(i,o)),o}const kv=e=>ot(e)&&Object.getOwnPropertyNames(e).some(t=>yn(e[t]));function Av(e,t){const{isScriptable:n,isIndexable:s}=dd(e);for(const i of t){const o=n(i),r=s(i),a=(r||o)&&e[i];if(o&&(yn(a)||kv(a))||r&&wt(a))return!0}return!1}var Ov="4.4.9";const Rv=["top","bottom","left","right","chartArea"];function pu(e,t){return e==="top"||e==="bottom"||Rv.indexOf(e)===-1&&t==="x"}function gu(e,t){return function(n,s){return n[e]===s[e]?n[t]-s[t]:n[e]-s[e]}}function mu(e){const t=e.chart,n=t.options.animation;t.notifyPlugins("afterRender"),_t(n&&n.onComplete,[e],t)}function Tv(e){const t=e.chart,n=t.options.animation;_t(n&&n.onProgress,[e],t)}function Id(e){return el()&&typeof e=="string"?e=document.getElementById(e):e&&e.length&&(e=e[0]),e&&e.canvas&&(e=e.canvas),e}const Qi={},bu=e=>{const t=Id(e);return Object.values(Qi).filter(n=>n.canvas===t).pop()};function Dv(e,t,n){const s=Object.keys(e);for(const i of s){const o=+i;if(o>=t){const r=e[i];delete e[i],(n>0||o>t)&&(e[o+n]=r)}}}function Lv(e,t,n,s){return!n||e.type==="mouseout"?null:s?t:e}class Rn{static register(...t){Te.add(...t),yu()}static unregister(...t){Te.remove(...t),yu()}constructor(t,n){const s=this.config=new Ev(n),i=Id(t),o=bu(i);if(o)throw new Error("Canvas is already in use. Chart with ID '"+o.id+"' must be destroyed before the canvas with ID '"+o.canvas.id+"' can be reused.");const r=s.createResolver(s.chartOptionScopes(),this.getContext());this.platform=new(s.platform||Q0(i)),this.platform.updateConfig(s);const a=this.platform.acquireContext(i,r.aspectRatio),l=a&&a.canvas,c=l&&l.height,u=l&&l.width;if(this.id=F_(),this.ctx=a,this.canvas=l,this.width=u,this.height=c,this._options=r,this._aspectRatio=this.aspectRatio,this._layers=[],this._metasets=[],this._stacks=void 0,this.boxes=[],this.currentDevicePixelRatio=void 0,this.chartArea=void 0,this._active=[],this._lastEvent=void 0,this._listeners={},this._responsiveListeners=void 0,this._sortedMetasets=[],this.scales={},this._plugins=new mv,this.$proxies={},this._hiddenIndices={},this.attached=!1,this._animationsDisabled=void 0,this.$context=void 0,this._doResize=Q_(f=>this.update(f),r.resizeDelay||0),this._dataChanges=[],Qi[this.id]=this,!a||!l){console.error("Failed to create chart: can't acquire context from the given item");return}ze.listen(this,"complete",mu),ze.listen(this,"progress",Tv),this._initialize(),this.attached&&this.update()}get aspectRatio(){const{options:{aspectRatio:t,maintainAspectRatio:n},width:s,height:i,_aspectRatio:o}=this;return it(t)?n&&o?o:i?s/i:null:t}get data(){return this.config.data}set data(t){this.config.data=t}get options(){return this._options}set options(t){this.config.options=t}get registry(){return Te}_initialize(){return this.notifyPlugins("beforeInit"),this.options.responsive?this.resize():Hc(this,this.options.devicePixelRatio),this.bindEvents(),this.notifyPlugins("afterInit"),this}clear(){return Nc(this.canvas,this.ctx),this}stop(){return ze.stop(this),this}resize(t,n){ze.running(this)?this._resizeBeforeDraw={width:t,height:n}:this._resize(t,n)}_resize(t,n){const s=this.options,i=this.canvas,o=s.maintainAspectRatio&&this.aspectRatio,r=this.platform.getMaximumSize(i,t,n,o),a=s.devicePixelRatio||this.platform.getDevicePixelRatio(),l=this.width?"resize":"attach";this.width=r.width,this.height=r.height,this._aspectRatio=this.aspectRatio,Hc(this,a,!0)&&(this.notifyPlugins("resize",{size:r}),_t(s.onResize,[this,r],this),this.attached&&this._doResize(l)&&this.render())}ensureScalesHaveIDs(){const n=this.options.scales||{};ht(n,(s,i)=>{s.id=i})}buildOrUpdateScales(){const t=this.options,n=t.scales,s=this.scales,i=Object.keys(s).reduce((r,a)=>(r[a]=!1,r),{});let o=[];n&&(o=o.concat(Object.keys(n).map(r=>{const a=n[r],l=oa(r,a),c=l==="r",u=l==="x";return{options:a,dposition:c?"chartArea":u?"bottom":"left",dtype:c?"radialLinear":u?"category":"linear"}}))),ht(o,r=>{const a=r.options,l=a.id,c=oa(l,a),u=st(a.type,r.dtype);(a.position===void 0||pu(a.position,c)!==pu(r.dposition))&&(a.position=r.dposition),i[l]=!0;let f=null;if(l in s&&s[l].type===u)f=s[l];else{const h=Te.getScale(u);f=new h({id:l,type:u,ctx:this.ctx,chart:this}),s[f.id]=f}f.init(a,t)}),ht(i,(r,a)=>{r||delete s[a]}),ht(s,r=>{Wt.configure(this,r,r.options),Wt.addBox(this,r)})}_updateMetasets(){const t=this._metasets,n=this.data.datasets.length,s=t.length;if(t.sort((i,o)=>i.index-o.index),s>n){for(let i=n;i<s;++i)this._destroyDatasetMeta(i);t.splice(n,s-n)}this._sortedMetasets=t.slice(0).sort(gu("order","index"))}_removeUnreferencedMetasets(){const{_metasets:t,data:{datasets:n}}=this;t.length>n.length&&delete this._stacks,t.forEach((s,i)=>{n.filter(o=>o===s._dataset).length===0&&this._destroyDatasetMeta(i)})}buildOrUpdateControllers(){const t=[],n=this.data.datasets;let s,i;for(this._removeUnreferencedMetasets(),s=0,i=n.length;s<i;s++){const o=n[s];let r=this.getDatasetMeta(s);const a=o.type||this.config.type;if(r.type&&r.type!==a&&(this._destroyDatasetMeta(s),r=this.getDatasetMeta(s)),r.type=a,r.indexAxis=o.indexAxis||ia(a,this.options),r.order=o.order||0,r.index=s,r.label=""+o.label,r.visible=this.isDatasetVisible(s),r.controller)r.controller.updateIndex(s),r.controller.linkScales();else{const l=Te.getController(a),{datasetElementType:c,dataElementType:u}=St.datasets[a];Object.assign(l,{dataElementType:Te.getElement(u),datasetElementType:c&&Te.getElement(c)}),r.controller=new l(this,s),t.push(r.controller)}}return this._updateMetasets(),t}_resetElements(){ht(this.data.datasets,(t,n)=>{this.getDatasetMeta(n).controller.reset()},this)}reset(){this._resetElements(),this.notifyPlugins("reset")}update(t){const n=this.config;n.update();const s=this._options=n.createResolver(n.chartOptionScopes(),this.getContext()),i=this._animationsDisabled=!s.animation;if(this._updateScales(),this._checkEventBindings(),this._updateHiddenIndices(),this._plugins.invalidate(),this.notifyPlugins("beforeUpdate",{mode:t,cancelable:!0})===!1)return;const o=this.buildOrUpdateControllers();this.notifyPlugins("beforeElementsUpdate");let r=0;for(let c=0,u=this.data.datasets.length;c<u;c++){const{controller:f}=this.getDatasetMeta(c),h=!i&&o.indexOf(f)===-1;f.buildOrUpdateElements(h),r=Math.max(+f.getMaxOverflow(),r)}r=this._minPadding=s.layout.autoPadding?r:0,this._updateLayout(r),i||ht(o,c=>{c.reset()}),this._updateDatasets(t),this.notifyPlugins("afterUpdate",{mode:t}),this._layers.sort(gu("z","_idx"));const{_active:a,_lastEvent:l}=this;l?this._eventHandler(l,!0):a.length&&this._updateHoverStyles(a,a,!0),this.render()}_updateScales(){ht(this.scales,t=>{Wt.removeBox(this,t)}),this.ensureScalesHaveIDs(),this.buildOrUpdateScales()}_checkEventBindings(){const t=this.options,n=new Set(Object.keys(this._listeners)),s=new Set(t.events);(!kc(n,s)||!!this._responsiveListeners!==t.responsive)&&(this.unbindEvents(),this.bindEvents())}_updateHiddenIndices(){const{_hiddenIndices:t}=this,n=this._getUniformDataChanges()||[];for(const{method:s,start:i,count:o}of n){const r=s==="_removeElements"?-o:o;Dv(t,i,r)}}_getUniformDataChanges(){const t=this._dataChanges;if(!t||!t.length)return;this._dataChanges=[];const n=this.data.datasets.length,s=o=>new Set(t.filter(r=>r[0]===o).map((r,a)=>a+","+r.splice(1).join(","))),i=s(0);for(let o=1;o<n;o++)if(!kc(i,s(o)))return;return Array.from(i).map(o=>o.split(",")).map(o=>({method:o[1],start:+o[2],count:+o[3]}))}_updateLayout(t){if(this.notifyPlugins("beforeLayout",{cancelable:!0})===!1)return;Wt.update(this,this.width,this.height,t);const n=this.chartArea,s=n.width<=0||n.height<=0;this._layers=[],ht(this.boxes,i=>{s&&i.position==="chartArea"||(i.configure&&i.configure(),this._layers.push(...i._layers()))},this),this._layers.forEach((i,o)=>{i._idx=o}),this.notifyPlugins("afterLayout")}_updateDatasets(t){if(this.notifyPlugins("beforeDatasetsUpdate",{mode:t,cancelable:!0})!==!1){for(let n=0,s=this.data.datasets.length;n<s;++n)this.getDatasetMeta(n).controller.configure();for(let n=0,s=this.data.datasets.length;n<s;++n)this._updateDataset(n,yn(t)?t({datasetIndex:n}):t);this.notifyPlugins("afterDatasetsUpdate",{mode:t})}}_updateDataset(t,n){const s=this.getDatasetMeta(t),i={meta:s,index:t,mode:n,cancelable:!0};this.notifyPlugins("beforeDatasetUpdate",i)!==!1&&(s.controller._update(n),i.cancelable=!1,this.notifyPlugins("afterDatasetUpdate",i))}render(){this.notifyPlugins("beforeRender",{cancelable:!0})!==!1&&(ze.has(this)?this.attached&&!ze.running(this)&&ze.start(this):(this.draw(),mu({chart:this})))}draw(){let t;if(this._resizeBeforeDraw){const{width:s,height:i}=this._resizeBeforeDraw;this._resizeBeforeDraw=null,this._resize(s,i)}if(this.clear(),this.width<=0||this.height<=0||this.notifyPlugins("beforeDraw",{cancelable:!0})===!1)return;const n=this._layers;for(t=0;t<n.length&&n[t].z<=0;++t)n[t].draw(this.chartArea);for(this._drawDatasets();t<n.length;++t)n[t].draw(this.chartArea);this.notifyPlugins("afterDraw")}_getSortedDatasetMetas(t){const n=this._sortedMetasets,s=[];let i,o;for(i=0,o=n.length;i<o;++i){const r=n[i];(!t||r.visible)&&s.push(r)}return s}getSortedVisibleDatasetMetas(){return this._getSortedDatasetMetas(!0)}_drawDatasets(){if(this.notifyPlugins("beforeDatasetsDraw",{cancelable:!0})===!1)return;const t=this.getSortedVisibleDatasetMetas();for(let n=t.length-1;n>=0;--n)this._drawDataset(t[n]);this.notifyPlugins("afterDatasetsDraw")}_drawDataset(t){const n=this.ctx,s={meta:t,index:t.index,cancelable:!0},i=Md(this,t);this.notifyPlugins("beforeDatasetDraw",s)!==!1&&(i&&Ko(n,i),t.controller.draw(),i&&qo(n),s.cancelable=!1,this.notifyPlugins("afterDatasetDraw",s))}isPointInArea(t){return Ye(t,this.chartArea,this._minPadding)}getElementsAtEventForMode(t,n,s,i){const o=R0.modes[n];return typeof o=="function"?o(this,t,s,i):[]}getDatasetMeta(t){const n=this.data.datasets[t],s=this._metasets;let i=s.filter(o=>o&&o._dataset===n).pop();return i||(i={type:null,data:[],dataset:null,controller:null,hidden:null,xAxisID:null,yAxisID:null,order:n&&n.order||0,index:t,_dataset:n,_parsed:[],_sorted:!1},s.push(i)),i}getContext(){return this.$context||(this.$context=xn(null,{chart:this,type:"chart"}))}getVisibleDatasetCount(){return this.getSortedVisibleDatasetMetas().length}isDatasetVisible(t){const n=this.data.datasets[t];if(!n)return!1;const s=this.getDatasetMeta(t);return typeof s.hidden=="boolean"?!s.hidden:!n.hidden}setDatasetVisibility(t,n){const s=this.getDatasetMeta(t);s.hidden=!n}toggleDataVisibility(t){this._hiddenIndices[t]=!this._hiddenIndices[t]}getDataVisibility(t){return!this._hiddenIndices[t]}_updateVisibility(t,n,s){const i=s?"show":"hide",o=this.getDatasetMeta(t),r=o.controller._resolveAnimations(void 0,i);ri(n)?(o.data[n].hidden=!s,this.update()):(this.setDatasetVisibility(t,s),r.update(o,{visible:s}),this.update(a=>a.datasetIndex===t?i:void 0))}hide(t,n){this._updateVisibility(t,n,!1)}show(t,n){this._updateVisibility(t,n,!0)}_destroyDatasetMeta(t){const n=this._metasets[t];n&&n.controller&&n.controller._destroy(),delete this._metasets[t]}_stop(){let t,n;for(this.stop(),ze.remove(this),t=0,n=this.data.datasets.length;t<n;++t)this._destroyDatasetMeta(t)}destroy(){this.notifyPlugins("beforeDestroy");const{canvas:t,ctx:n}=this;this._stop(),this.config.clearCache(),t&&(this.unbindEvents(),Nc(t,n),this.platform.releaseContext(n),this.canvas=null,this.ctx=null),delete Qi[this.id],this.notifyPlugins("afterDestroy")}toBase64Image(...t){return this.canvas.toDataURL(...t)}bindEvents(){this.bindUserEvents(),this.options.responsive?this.bindResponsiveEvents():this.attached=!0}bindUserEvents(){const t=this._listeners,n=this.platform,s=(o,r)=>{n.addEventListener(this,o,r),t[o]=r},i=(o,r,a)=>{o.offsetX=r,o.offsetY=a,this._eventHandler(o)};ht(this.options.events,o=>s(o,i))}bindResponsiveEvents(){this._responsiveListeners||(this._responsiveListeners={});const t=this._responsiveListeners,n=this.platform,s=(l,c)=>{n.addEventListener(this,l,c),t[l]=c},i=(l,c)=>{t[l]&&(n.removeEventListener(this,l,c),delete t[l])},o=(l,c)=>{this.canvas&&this.resize(l,c)};let r;const a=()=>{i("attach",a),this.attached=!0,this.resize(),s("resize",o),s("detach",r)};r=()=>{this.attached=!1,i("resize",o),this._stop(),this._resize(0,0),s("attach",a)},n.isAttached(this.canvas)?a():r()}unbindEvents(){ht(this._listeners,(t,n)=>{this.platform.removeEventListener(this,n,t)}),this._listeners={},ht(this._responsiveListeners,(t,n)=>{this.platform.removeEventListener(this,n,t)}),this._responsiveListeners=void 0}updateHoverStyle(t,n,s){const i=s?"set":"remove";let o,r,a,l;for(n==="dataset"&&(o=this.getDatasetMeta(t[0].datasetIndex),o.controller["_"+i+"DatasetHoverStyle"]()),a=0,l=t.length;a<l;++a){r=t[a];const c=r&&this.getDatasetMeta(r.datasetIndex).controller;c&&c[i+"HoverStyle"](r.element,r.datasetIndex,r.index)}}getActiveElements(){return this._active||[]}setActiveElements(t){const n=this._active||[],s=t.map(({datasetIndex:o,index:r})=>{const a=this.getDatasetMeta(o);if(!a)throw new Error("No dataset found at index "+o);return{datasetIndex:o,element:a.data[r],index:r}});!mo(s,n)&&(this._active=s,this._lastEvent=null,this._updateHoverStyles(s,n))}notifyPlugins(t,n,s){return this._plugins.notify(this,t,n,s)}isPluginEnabled(t){return this._plugins._cache.filter(n=>n.plugin.id===t).length===1}_updateHoverStyles(t,n,s){const i=this.options.hover,o=(l,c)=>l.filter(u=>!c.some(f=>u.datasetIndex===f.datasetIndex&&u.index===f.index)),r=o(n,t),a=s?t:o(t,n);r.length&&this.updateHoverStyle(r,i.mode,!1),a.length&&i.mode&&this.updateHoverStyle(a,i.mode,!0)}_eventHandler(t,n){const s={event:t,replay:n,cancelable:!0,inChartArea:this.isPointInArea(t)},i=r=>(r.options.events||this.options.events).includes(t.native.type);if(this.notifyPlugins("beforeEvent",s,i)===!1)return;const o=this._handleEvent(t,n,s.inChartArea);return s.cancelable=!1,this.notifyPlugins("afterEvent",s,i),(o||s.changed)&&this.render(),this}_handleEvent(t,n,s){const{_active:i=[],options:o}=this,r=n,a=this._getActiveElements(t,i,s,r),l=j_(t),c=Lv(t,this._lastEvent,s,l);s&&(this._lastEvent=null,_t(o.onHover,[t,a,this],this),l&&_t(o.onClick,[t,a,this],this));const u=!mo(a,i);return(u||n)&&(this._active=a,this._updateHoverStyles(a,i,n)),this._lastEvent=c,u}_getActiveElements(t,n,s,i){if(t.type==="mouseout")return[];if(!s)return n;const o=this.options.hover;return this.getElementsAtEventForMode(t,o.mode,o,i)}}N(Rn,"defaults",St),N(Rn,"instances",Qi),N(Rn,"overrides",Wn),N(Rn,"registry",Te),N(Rn,"version",Ov),N(Rn,"getChart",bu);function yu(){return ht(Rn.instances,e=>e._plugins.invalidate())}function Fv(e,t,n){const{startAngle:s,pixelMargin:i,x:o,y:r,outerRadius:a,innerRadius:l}=t;let c=i/a;e.beginPath(),e.arc(o,r,a,s-c,n+c),l>i?(c=i/l,e.arc(o,r,l,n+c,s-c,!0)):e.arc(o,r,i,n+Et,s-Et),e.closePath(),e.clip()}function Iv(e){return Ja(e,["outerStart","outerEnd","innerStart","innerEnd"])}function Nv(e,t,n,s){const i=Iv(e.options.borderRadius),o=(n-t)/2,r=Math.min(o,s*t/2),a=l=>{const c=(n-Math.min(o,l))*s/2;return Ft(l,0,Math.min(o,c))};return{outerStart:a(i.outerStart),outerEnd:a(i.outerEnd),innerStart:Ft(i.innerStart,0,r),innerEnd:Ft(i.innerEnd,0,r)}}function Gn(e,t,n,s){return{x:n+e*Math.cos(t),y:s+e*Math.sin(t)}}function wo(e,t,n,s,i,o){const{x:r,y:a,startAngle:l,pixelMargin:c,innerRadius:u}=t,f=Math.max(t.outerRadius+s+n-c,0),h=u>0?u+s+n+c:0;let d=0;const p=i-l;if(s){const Z=u>0?u-s:0,G=f>0?f-s:0,U=(Z+G)/2,et=U!==0?p*U/(U+s):p;d=(p-et)/2}const g=Math.max(.001,p*f-n/vt)/f,m=(p-g)/2,y=l+m+d,b=i-m-d,{outerStart:w,outerEnd:v,innerStart:S,innerEnd:A}=Nv(t,h,f,b-y),P=f-w,E=f-v,k=y+w/P,I=b-v/E,H=h+S,L=h+A,X=y+S/H,rt=b-A/L;if(e.beginPath(),o){const Z=(k+I)/2;if(e.arc(r,a,f,k,Z),e.arc(r,a,f,Z,I),v>0){const mt=Gn(E,I,r,a);e.arc(mt.x,mt.y,v,I,b+Et)}const G=Gn(L,b,r,a);if(e.lineTo(G.x,G.y),A>0){const mt=Gn(L,rt,r,a);e.arc(mt.x,mt.y,A,b+Et,rt+Math.PI)}const U=(b-A/h+(y+S/h))/2;if(e.arc(r,a,h,b-A/h,U,!0),e.arc(r,a,h,U,y+S/h,!0),S>0){const mt=Gn(H,X,r,a);e.arc(mt.x,mt.y,S,X+Math.PI,y-Et)}const et=Gn(P,y,r,a);if(e.lineTo(et.x,et.y),w>0){const mt=Gn(P,k,r,a);e.arc(mt.x,mt.y,w,y-Et,k)}}else{e.moveTo(r,a);const Z=Math.cos(k)*f+r,G=Math.sin(k)*f+a;e.lineTo(Z,G);const U=Math.cos(I)*f+r,et=Math.sin(I)*f+a;e.lineTo(U,et)}e.closePath()}function Bv(e,t,n,s,i){const{fullCircles:o,startAngle:r,circumference:a}=t;let l=t.endAngle;if(o){wo(e,t,n,s,l,i);for(let c=0;c<o;++c)e.fill();isNaN(a)||(l=r+(a%xt||xt))}return wo(e,t,n,s,l,i),e.fill(),l}function zv(e,t,n,s,i){const{fullCircles:o,startAngle:r,circumference:a,options:l}=t,{borderWidth:c,borderJoinStyle:u,borderDash:f,borderDashOffset:h}=l,d=l.borderAlign==="inner";if(!c)return;e.setLineDash(f||[]),e.lineDashOffset=h,d?(e.lineWidth=c*2,e.lineJoin=u||"round"):(e.lineWidth=c,e.lineJoin=u||"bevel");let p=t.endAngle;if(o){wo(e,t,n,s,p,i);for(let g=0;g<o;++g)e.stroke();isNaN(a)||(p=r+(a%xt||xt))}d&&Fv(e,t,p),o||(wo(e,t,n,s,p,i),e.stroke())}class Ls extends Qe{constructor(n){super();N(this,"circumference");N(this,"endAngle");N(this,"fullCircles");N(this,"innerRadius");N(this,"outerRadius");N(this,"pixelMargin");N(this,"startAngle");this.options=void 0,this.circumference=void 0,this.startAngle=void 0,this.endAngle=void 0,this.innerRadius=void 0,this.outerRadius=void 0,this.pixelMargin=0,this.fullCircles=0,n&&Object.assign(this,n)}inRange(n,s,i){const o=this.getProps(["x","y"],i),{angle:r,distance:a}=sd(o,{x:n,y:s}),{startAngle:l,endAngle:c,innerRadius:u,outerRadius:f,circumference:h}=this.getProps(["startAngle","endAngle","innerRadius","outerRadius","circumference"],i),d=(this.options.spacing+this.options.borderWidth)/2,p=st(h,c-l),g=ai(r,l,c)&&l!==c,m=p>=xt||g,y=Ke(a,u+d,f+d);return m&&y}getCenterPoint(n){const{x:s,y:i,startAngle:o,endAngle:r,innerRadius:a,outerRadius:l}=this.getProps(["x","y","startAngle","endAngle","innerRadius","outerRadius"],n),{offset:c,spacing:u}=this.options,f=(o+r)/2,h=(a+l+u+c)/2;return{x:s+Math.cos(f)*h,y:i+Math.sin(f)*h}}tooltipPosition(n){return this.getCenterPoint(n)}draw(n){const{options:s,circumference:i}=this,o=(s.offset||0)/4,r=(s.spacing||0)/2,a=s.circular;if(this.pixelMargin=s.borderAlign==="inner"?.33:0,this.fullCircles=i>xt?Math.floor(i/xt):0,i===0||this.innerRadius<0||this.outerRadius<0)return;n.save();const l=(this.startAngle+this.endAngle)/2;n.translate(Math.cos(l)*o,Math.sin(l)*o);const c=1-Math.sin(Math.min(vt,i||0)),u=o*c;n.fillStyle=s.backgroundColor,n.strokeStyle=s.borderColor,Bv(n,this,u,r,a),zv(n,this,u,r,a),n.restore()}}N(Ls,"id","arc"),N(Ls,"defaults",{borderAlign:"center",borderColor:"#fff",borderDash:[],borderDashOffset:0,borderJoinStyle:void 0,borderRadius:0,borderWidth:2,offset:0,spacing:0,angle:void 0,circular:!0}),N(Ls,"defaultRoutes",{backgroundColor:"backgroundColor"}),N(Ls,"descriptors",{_scriptable:!0,_indexable:n=>n!=="borderDash"});function Nd(e,t,n=t){e.lineCap=st(n.borderCapStyle,t.borderCapStyle),e.setLineDash(st(n.borderDash,t.borderDash)),e.lineDashOffset=st(n.borderDashOffset,t.borderDashOffset),e.lineJoin=st(n.borderJoinStyle,t.borderJoinStyle),e.lineWidth=st(n.borderWidth,t.borderWidth),e.strokeStyle=st(n.borderColor,t.borderColor)}function Hv(e,t,n){e.lineTo(n.x,n.y)}function jv(e){return e.stepped?ux:e.tension||e.cubicInterpolationMode==="monotone"?fx:Hv}function Bd(e,t,n={}){const s=e.length,{start:i=0,end:o=s-1}=n,{start:r,end:a}=t,l=Math.max(i,r),c=Math.min(o,a),u=i<r&&o<r||i>a&&o>a;return{count:s,start:l,loop:t.loop,ilen:c<l&&!u?s+c-l:c-l}}function Vv(e,t,n,s){const{points:i,options:o}=t,{count:r,start:a,loop:l,ilen:c}=Bd(i,n,s),u=jv(o);let{move:f=!0,reverse:h}=s||{},d,p,g;for(d=0;d<=c;++d)p=i[(a+(h?c-d:d))%r],!p.skip&&(f?(e.moveTo(p.x,p.y),f=!1):u(e,g,p,h,o.stepped),g=p);return l&&(p=i[(a+(h?c:0))%r],u(e,g,p,h,o.stepped)),!!l}function Wv(e,t,n,s){const i=t.points,{count:o,start:r,ilen:a}=Bd(i,n,s),{move:l=!0,reverse:c}=s||{};let u=0,f=0,h,d,p,g,m,y;const b=v=>(r+(c?a-v:v))%o,w=()=>{g!==m&&(e.lineTo(u,m),e.lineTo(u,g),e.lineTo(u,y))};for(l&&(d=i[b(0)],e.moveTo(d.x,d.y)),h=0;h<=a;++h){if(d=i[b(h)],d.skip)continue;const v=d.x,S=d.y,A=v|0;A===p?(S<g?g=S:S>m&&(m=S),u=(f*u+v)/++f):(w(),e.lineTo(v,S),p=A,f=0,g=m=S),y=S}w()}function ra(e){const t=e.options,n=t.borderDash&&t.borderDash.length;return!e._decimated&&!e._loop&&!t.tension&&t.cubicInterpolationMode!=="monotone"&&!t.stepped&&!n?Wv:Vv}function $v(e){return e.stepped?Wx:e.tension||e.cubicInterpolationMode==="monotone"?$x:On}function Uv(e,t,n,s){let i=t._path;i||(i=t._path=new Path2D,t.path(i,n,s)&&i.closePath()),Nd(e,t.options),e.stroke(i)}function Kv(e,t,n,s){const{segments:i,options:o}=t,r=ra(t);for(const a of i)Nd(e,o,a.style),e.beginPath(),r(e,t,a,{start:n,end:n+s-1})&&e.closePath(),e.stroke()}const qv=typeof Path2D=="function";function Yv(e,t,n,s){qv&&!t.options.segment?Uv(e,t,n,s):Kv(e,t,n,s)}class cn extends Qe{constructor(t){super(),this.animated=!0,this.options=void 0,this._chart=void 0,this._loop=void 0,this._fullLoop=void 0,this._path=void 0,this._points=void 0,this._segments=void 0,this._decimated=!1,this._pointsUpdated=!1,this._datasetIndex=void 0,t&&Object.assign(this,t)}updateControlPoints(t,n){const s=this.options;if((s.tension||s.cubicInterpolationMode==="monotone")&&!s.stepped&&!this._pointsUpdated){const i=s.spanGaps?this._loop:this._fullLoop;Fx(this._points,s,t,i,n),this._pointsUpdated=!0}}set points(t){this._points=t,delete this._segments,delete this._path,this._pointsUpdated=!1}get points(){return this._points}get segments(){return this._segments||(this._segments=Gx(this,this.options.segment))}first(){const t=this.segments,n=this.points;return t.length&&n[t[0].start]}last(){const t=this.segments,n=this.points,s=t.length;return s&&n[t[s-1].end]}interpolate(t,n){const s=this.options,i=t[n],o=this.points,r=Sd(this,{property:n,start:i,end:i});if(!r.length)return;const a=[],l=$v(s);let c,u;for(c=0,u=r.length;c<u;++c){const{start:f,end:h}=r[c],d=o[f],p=o[h];if(d===p){a.push(d);continue}const g=Math.abs((i-d[n])/(p[n]-d[n])),m=l(d,p,g,s.stepped);m[n]=t[n],a.push(m)}return a.length===1?a[0]:a}pathSegment(t,n,s){return ra(this)(t,this,n,s)}path(t,n,s){const i=this.segments,o=ra(this);let r=this._loop;n=n||0,s=s||this.points.length-n;for(const a of i)r&=o(t,this,a,{start:n,end:n+s-1});return!!r}draw(t,n,s,i){const o=this.options||{};(this.points||[]).length&&o.borderWidth&&(t.save(),Yv(t,this,s,i),t.restore()),this.animated&&(this._pointsUpdated=!1,this._path=void 0)}}N(cn,"id","line"),N(cn,"defaults",{borderCapStyle:"butt",borderDash:[],borderDashOffset:0,borderJoinStyle:"miter",borderWidth:3,capBezierPoints:!0,cubicInterpolationMode:"default",fill:!1,spanGaps:!1,stepped:!1,tension:0}),N(cn,"defaultRoutes",{backgroundColor:"backgroundColor",borderColor:"borderColor"}),N(cn,"descriptors",{_scriptable:!0,_indexable:t=>t!=="borderDash"&&t!=="fill"});function _u(e,t,n,s){const i=e.options,{[n]:o}=e.getProps([n],s);return Math.abs(t-o)<i.radius+i.hitRadius}class Zi extends Qe{constructor(n){super();N(this,"parsed");N(this,"skip");N(this,"stop");this.options=void 0,this.parsed=void 0,this.skip=void 0,this.stop=void 0,n&&Object.assign(this,n)}inRange(n,s,i){const o=this.options,{x:r,y:a}=this.getProps(["x","y"],i);return Math.pow(n-r,2)+Math.pow(s-a,2)<Math.pow(o.hitRadius+o.radius,2)}inXRange(n,s){return _u(this,n,"x",s)}inYRange(n,s){return _u(this,n,"y",s)}getCenterPoint(n){const{x:s,y:i}=this.getProps(["x","y"],n);return{x:s,y:i}}size(n){n=n||this.options||{};let s=n.radius||0;s=Math.max(s,s&&n.hoverRadius||0);const i=s&&n.borderWidth||0;return(s+i)*2}draw(n,s){const i=this.options;this.skip||i.radius<.1||!Ye(this,s,this.size(i)/2)||(n.strokeStyle=i.borderColor,n.lineWidth=i.borderWidth,n.fillStyle=i.backgroundColor,na(n,i,this.x,this.y))}getRange(){const n=this.options||{};return n.radius+n.hitRadius}}N(Zi,"id","point"),N(Zi,"defaults",{borderWidth:1,hitRadius:1,hoverBorderWidth:1,hoverRadius:4,pointStyle:"circle",radius:3,rotation:0}),N(Zi,"defaultRoutes",{backgroundColor:"backgroundColor",borderColor:"borderColor"});function zd(e,t){const{x:n,y:s,base:i,width:o,height:r}=e.getProps(["x","y","base","width","height"],t);let a,l,c,u,f;return e.horizontal?(f=r/2,a=Math.min(n,i),l=Math.max(n,i),c=s-f,u=s+f):(f=o/2,a=n-f,l=n+f,c=Math.min(s,i),u=Math.max(s,i)),{left:a,top:c,right:l,bottom:u}}function un(e,t,n,s){return e?0:Ft(t,n,s)}function Xv(e,t,n){const s=e.options.borderWidth,i=e.borderSkipped,o=hd(s);return{t:un(i.top,o.top,0,n),r:un(i.right,o.right,0,t),b:un(i.bottom,o.bottom,0,n),l:un(i.left,o.left,0,t)}}function Gv(e,t,n){const{enableBorderRadius:s}=e.getProps(["enableBorderRadius"]),i=e.options.borderRadius,o=zn(i),r=Math.min(t,n),a=e.borderSkipped,l=s||ot(i);return{topLeft:un(!l||a.top||a.left,o.topLeft,0,r),topRight:un(!l||a.top||a.right,o.topRight,0,r),bottomLeft:un(!l||a.bottom||a.left,o.bottomLeft,0,r),bottomRight:un(!l||a.bottom||a.right,o.bottomRight,0,r)}}function Jv(e){const t=zd(e),n=t.right-t.left,s=t.bottom-t.top,i=Xv(e,n/2,s/2),o=Gv(e,n/2,s/2);return{outer:{x:t.left,y:t.top,w:n,h:s,radius:o},inner:{x:t.left+i.l,y:t.top+i.t,w:n-i.l-i.r,h:s-i.t-i.b,radius:{topLeft:Math.max(0,o.topLeft-Math.max(i.t,i.l)),topRight:Math.max(0,o.topRight-Math.max(i.t,i.r)),bottomLeft:Math.max(0,o.bottomLeft-Math.max(i.b,i.l)),bottomRight:Math.max(0,o.bottomRight-Math.max(i.b,i.r))}}}}function Cr(e,t,n,s){const i=t===null,o=n===null,a=e&&!(i&&o)&&zd(e,s);return a&&(i||Ke(t,a.left,a.right))&&(o||Ke(n,a.top,a.bottom))}function Qv(e){return e.topLeft||e.topRight||e.bottomLeft||e.bottomRight}function Zv(e,t){e.rect(t.x,t.y,t.w,t.h)}function Pr(e,t,n={}){const s=e.x!==n.x?-t:0,i=e.y!==n.y?-t:0,o=(e.x+e.w!==n.x+n.w?t:0)-s,r=(e.y+e.h!==n.y+n.h?t:0)-i;return{x:e.x+s,y:e.y+i,w:e.w+o,h:e.h+r,radius:e.radius}}class to extends Qe{constructor(t){super(),this.options=void 0,this.horizontal=void 0,this.base=void 0,this.width=void 0,this.height=void 0,this.inflateAmount=void 0,t&&Object.assign(this,t)}draw(t){const{inflateAmount:n,options:{borderColor:s,backgroundColor:i}}=this,{inner:o,outer:r}=Jv(this),a=Qv(r.radius)?li:Zv;t.save(),(r.w!==o.w||r.h!==o.h)&&(t.beginPath(),a(t,Pr(r,n,o)),t.clip(),a(t,Pr(o,-n,r)),t.fillStyle=s,t.fill("evenodd")),t.beginPath(),a(t,Pr(o,n)),t.fillStyle=i,t.fill(),t.restore()}inRange(t,n,s){return Cr(this,t,n,s)}inXRange(t,n){return Cr(this,t,null,n)}inYRange(t,n){return Cr(this,null,t,n)}getCenterPoint(t){const{x:n,y:s,base:i,horizontal:o}=this.getProps(["x","y","base","horizontal"],t);return{x:o?(n+i)/2:n,y:o?s:(s+i)/2}}getRange(t){return t==="x"?this.width/2:this.height/2}}N(to,"id","bar"),N(to,"defaults",{borderSkipped:"start",borderWidth:0,borderRadius:0,inflateAmount:"auto",pointStyle:void 0}),N(to,"defaultRoutes",{backgroundColor:"backgroundColor",borderColor:"borderColor"});var tw=Object.freeze({__proto__:null,ArcElement:Ls,BarElement:to,LineElement:cn,PointElement:Zi});const aa=["rgb(54, 162, 235)","rgb(255, 99, 132)","rgb(255, 159, 64)","rgb(255, 205, 86)","rgb(75, 192, 192)","rgb(153, 102, 255)","rgb(201, 203, 207)"],xu=aa.map(e=>e.replace("rgb(","rgba(").replace(")",", 0.5)"));function Hd(e){return aa[e%aa.length]}function jd(e){return xu[e%xu.length]}function ew(e,t){return e.borderColor=Hd(t),e.backgroundColor=jd(t),++t}function nw(e,t){return e.backgroundColor=e.data.map(()=>Hd(t++)),t}function sw(e,t){return e.backgroundColor=e.data.map(()=>jd(t++)),t}function iw(e){let t=0;return(n,s)=>{const i=e.getDatasetMeta(s).controller;i instanceof Ln?t=nw(n,t):i instanceof Xs?t=sw(n,t):i&&(t=ew(n,t))}}function vu(e){let t;for(t in e)if(e[t].borderColor||e[t].backgroundColor)return!0;return!1}function ow(e){return e&&(e.borderColor||e.backgroundColor)}function rw(){return St.borderColor!=="rgba(0,0,0,0.1)"||St.backgroundColor!=="rgba(0,0,0,0.1)"}var aw={id:"colors",defaults:{enabled:!0,forceOverride:!1},beforeLayout(e,t,n){if(!n.enabled)return;const{data:{datasets:s},options:i}=e.config,{elements:o}=i,r=vu(s)||ow(i)||o&&vu(o)||rw();if(!n.forceOverride&&r)return;const a=iw(e);s.forEach(a)}};function lw(e,t,n,s,i){const o=i.samples||s;if(o>=n)return e.slice(t,t+n);const r=[],a=(n-2)/(o-2);let l=0;const c=t+n-1;let u=t,f,h,d,p,g;for(r[l++]=e[u],f=0;f<o-2;f++){let m=0,y=0,b;const w=Math.floor((f+1)*a)+1+t,v=Math.min(Math.floor((f+2)*a)+1,n)+t,S=v-w;for(b=w;b<v;b++)m+=e[b].x,y+=e[b].y;m/=S,y/=S;const A=Math.floor(f*a)+1+t,P=Math.min(Math.floor((f+1)*a)+1,n)+t,{x:E,y:k}=e[u];for(d=p=-1,b=A;b<P;b++)p=.5*Math.abs((E-m)*(e[b].y-k)-(E-e[b].x)*(y-k)),p>d&&(d=p,h=e[b],g=b);r[l++]=h,u=g}return r[l++]=e[c],r}function cw(e,t,n,s){let i=0,o=0,r,a,l,c,u,f,h,d,p,g;const m=[],y=t+n-1,b=e[t].x,v=e[y].x-b;for(r=t;r<t+n;++r){a=e[r],l=(a.x-b)/v*s,c=a.y;const S=l|0;if(S===u)c<p?(p=c,f=r):c>g&&(g=c,h=r),i=(o*i+a.x)/++o;else{const A=r-1;if(!it(f)&&!it(h)){const P=Math.min(f,h),E=Math.max(f,h);P!==d&&P!==A&&m.push({...e[P],x:i}),E!==d&&E!==A&&m.push({...e[E],x:i})}r>0&&A!==d&&m.push(e[A]),m.push(a),u=S,o=0,p=g=c,f=h=d=r}}return m}function Vd(e){if(e._decimated){const t=e._data;delete e._decimated,delete e._data,Object.defineProperty(e,"data",{configurable:!0,enumerable:!0,writable:!0,value:t})}}function wu(e){e.data.datasets.forEach(t=>{Vd(t)})}function uw(e,t){const n=t.length;let s=0,i;const{iScale:o}=e,{min:r,max:a,minDefined:l,maxDefined:c}=o.getUserBounds();return l&&(s=Ft(qe(t,o.axis,r).lo,0,n-1)),c?i=Ft(qe(t,o.axis,a).hi+1,s,n)-s:i=n-s,{start:s,count:i}}var fw={id:"decimation",defaults:{algorithm:"min-max",enabled:!1},beforeElementsUpdate:(e,t,n)=>{if(!n.enabled){wu(e);return}const s=e.width;e.data.datasets.forEach((i,o)=>{const{_data:r,indexAxis:a}=i,l=e.getDatasetMeta(o),c=r||i.data;if(Ts([a,e.options.indexAxis])==="y"||!l.controller.supportsDecimation)return;const u=e.scales[l.xAxisID];if(u.type!=="linear"&&u.type!=="time"||e.options.parsing)return;let{start:f,count:h}=uw(l,c);const d=n.threshold||4*s;if(h<=d){Vd(i);return}it(r)&&(i._data=c,delete i.data,Object.defineProperty(i,"data",{configurable:!0,enumerable:!0,get:function(){return this._decimated},set:function(g){this._data=g}}));let p;switch(n.algorithm){case"lttb":p=lw(c,f,h,s,n);break;case"min-max":p=cw(c,f,h,s);break;default:throw new Error(`Unsupported decimation algorithm '${n.algorithm}'`)}i._decimated=p})},destroy(e){wu(e)}};function hw(e,t,n){const s=e.segments,i=e.points,o=t.points,r=[];for(const a of s){let{start:l,end:c}=a;c=il(l,c,i);const u=la(n,i[l],i[c],a.loop);if(!t.segments){r.push({source:a,target:u,start:i[l],end:i[c]});continue}const f=Sd(t,u);for(const h of f){const d=la(n,o[h.start],o[h.end],h.loop),p=wd(a,i,d);for(const g of p)r.push({source:g,target:h,start:{[n]:Su(u,d,"start",Math.max)},end:{[n]:Su(u,d,"end",Math.min)}})}}return r}function la(e,t,n,s){if(s)return;let i=t[e],o=n[e];return e==="angle"&&(i=oe(i),o=oe(o)),{property:e,start:i,end:o}}function dw(e,t){const{x:n=null,y:s=null}=e||{},i=t.points,o=[];return t.segments.forEach(({start:r,end:a})=>{a=il(r,a,i);const l=i[r],c=i[a];s!==null?(o.push({x:l.x,y:s}),o.push({x:c.x,y:s})):n!==null&&(o.push({x:n,y:l.y}),o.push({x:n,y:c.y}))}),o}function il(e,t,n){for(;t>e;t--){const s=n[t];if(!isNaN(s.x)&&!isNaN(s.y))break}return t}function Su(e,t,n,s){return e&&t?s(e[n],t[n]):e?e[n]:t?t[n]:0}function Wd(e,t){let n=[],s=!1;return wt(e)?(s=!0,n=e):n=dw(e,t),n.length?new cn({points:n,options:{tension:0},_loop:s,_fullLoop:s}):null}function Mu(e){return e&&e.fill!==!1}function pw(e,t,n){let i=e[t].fill;const o=[t];let r;if(!n)return i;for(;i!==!1&&o.indexOf(i)===-1;){if(!Ct(i))return i;if(r=e[i],!r)return!1;if(r.visible)return i;o.push(i),i=r.fill}return!1}function gw(e,t,n){const s=_w(e);if(ot(s))return isNaN(s.value)?!1:s;let i=parseFloat(s);return Ct(i)&&Math.floor(i)===i?mw(s[0],t,i,n):["origin","start","end","stack","shape"].indexOf(s)>=0&&s}function mw(e,t,n,s){return(e==="-"||e==="+")&&(n=t+n),n===t||n<0||n>=s?!1:n}function bw(e,t){let n=null;return e==="start"?n=t.bottom:e==="end"?n=t.top:ot(e)?n=t.getPixelForValue(e.value):t.getBasePixel&&(n=t.getBasePixel()),n}function yw(e,t,n){let s;return e==="start"?s=n:e==="end"?s=t.options.reverse?t.min:t.max:ot(e)?s=e.value:s=t.getBaseValue(),s}function _w(e){const t=e.options,n=t.fill;let s=st(n&&n.target,n);return s===void 0&&(s=!!t.backgroundColor),s===!1||s===null?!1:s===!0?"origin":s}function xw(e){const{scale:t,index:n,line:s}=e,i=[],o=s.segments,r=s.points,a=vw(t,n);a.push(Wd({x:null,y:t.bottom},s));for(let l=0;l<o.length;l++){const c=o[l];for(let u=c.start;u<=c.end;u++)ww(i,r[u],a)}return new cn({points:i,options:{}})}function vw(e,t){const n=[],s=e.getMatchingVisibleMetas("line");for(let i=0;i<s.length;i++){const o=s[i];if(o.index===t)break;o.hidden||n.unshift(o.dataset)}return n}function ww(e,t,n){const s=[];for(let i=0;i<n.length;i++){const o=n[i],{first:r,last:a,point:l}=Sw(o,t,"x");if(!(!l||r&&a)){if(r)s.unshift(l);else if(e.push(l),!a)break}}e.push(...s)}function Sw(e,t,n){const s=e.interpolate(t,n);if(!s)return{};const i=s[n],o=e.segments,r=e.points;let a=!1,l=!1;for(let c=0;c<o.length;c++){const u=o[c],f=r[u.start][n],h=r[u.end][n];if(Ke(i,f,h)){a=i===f,l=i===h;break}}return{first:a,last:l,point:s}}class $d{constructor(t){this.x=t.x,this.y=t.y,this.radius=t.radius}pathSegment(t,n,s){const{x:i,y:o,radius:r}=this;return n=n||{start:0,end:xt},t.arc(i,o,r,n.end,n.start,!0),!s.bounds}interpolate(t){const{x:n,y:s,radius:i}=this,o=t.angle;return{x:n+Math.cos(o)*i,y:s+Math.sin(o)*i,angle:o}}}function Mw(e){const{chart:t,fill:n,line:s}=e;if(Ct(n))return Cw(t,n);if(n==="stack")return xw(e);if(n==="shape")return!0;const i=Pw(e);return i instanceof $d?i:Wd(i,s)}function Cw(e,t){const n=e.getDatasetMeta(t);return n&&e.isDatasetVisible(t)?n.dataset:null}function Pw(e){return(e.scale||{}).getPointPositionForValue?kw(e):Ew(e)}function Ew(e){const{scale:t={},fill:n}=e,s=bw(n,t);if(Ct(s)){const i=t.isHorizontal();return{x:i?s:null,y:i?null:s}}return null}function kw(e){const{scale:t,fill:n}=e,s=t.options,i=t.getLabels().length,o=s.reverse?t.max:t.min,r=yw(n,t,o),a=[];if(s.grid.circular){const l=t.getPointPositionForValue(0,o);return new $d({x:l.x,y:l.y,radius:t.getDistanceFromCenterForValue(r)})}for(let l=0;l<i;++l)a.push(t.getPointPositionForValue(l,r));return a}function Er(e,t,n){const s=Mw(t),{chart:i,index:o,line:r,scale:a,axis:l}=t,c=r.options,u=c.fill,f=c.backgroundColor,{above:h=f,below:d=f}=u||{},p=i.getDatasetMeta(o),g=Md(i,p);s&&r.points.length&&(Ko(e,n),Aw(e,{line:r,target:s,above:h,below:d,area:n,scale:a,axis:l,clip:g}),qo(e))}function Aw(e,t){const{line:n,target:s,above:i,below:o,area:r,scale:a,clip:l}=t,c=n._loop?"angle":t.axis;e.save(),c==="x"&&o!==i&&(Cu(e,s,r.top),Pu(e,{line:n,target:s,color:i,scale:a,property:c,clip:l}),e.restore(),e.save(),Cu(e,s,r.bottom)),Pu(e,{line:n,target:s,color:o,scale:a,property:c,clip:l}),e.restore()}function Cu(e,t,n){const{segments:s,points:i}=t;let o=!0,r=!1;e.beginPath();for(const a of s){const{start:l,end:c}=a,u=i[l],f=i[il(l,c,i)];o?(e.moveTo(u.x,u.y),o=!1):(e.lineTo(u.x,n),e.lineTo(u.x,u.y)),r=!!t.pathSegment(e,a,{move:r}),r?e.closePath():e.lineTo(f.x,n)}e.lineTo(t.first().x,n),e.closePath(),e.clip()}function Pu(e,t){const{line:n,target:s,property:i,color:o,scale:r,clip:a}=t,l=hw(n,s,i);for(const{source:c,target:u,start:f,end:h}of l){const{style:{backgroundColor:d=o}={}}=c,p=s!==!0;e.save(),e.fillStyle=d,Ow(e,r,a,p&&la(i,f,h)),e.beginPath();const g=!!n.pathSegment(e,c);let m;if(p){g?e.closePath():Eu(e,s,h,i);const y=!!s.pathSegment(e,u,{move:g,reverse:!0});m=g&&y,m||Eu(e,s,f,i)}e.closePath(),e.fill(m?"evenodd":"nonzero"),e.restore()}}function Ow(e,t,n,s){const i=t.chart.chartArea,{property:o,start:r,end:a}=s||{};if(o==="x"||o==="y"){let l,c,u,f;o==="x"?(l=r,c=i.top,u=a,f=i.bottom):(l=i.left,c=r,u=i.right,f=a),e.beginPath(),n&&(l=Math.max(l,n.left),u=Math.min(u,n.right),c=Math.max(c,n.top),f=Math.min(f,n.bottom)),e.rect(l,c,u-l,f-c),e.clip()}}function Eu(e,t,n,s){const i=t.interpolate(n,s);i&&e.lineTo(i.x,i.y)}var Rw={id:"filler",afterDatasetsUpdate(e,t,n){const s=(e.data.datasets||[]).length,i=[];let o,r,a,l;for(r=0;r<s;++r)o=e.getDatasetMeta(r),a=o.dataset,l=null,a&&a.options&&a instanceof cn&&(l={visible:e.isDatasetVisible(r),index:r,fill:gw(a,r,s),chart:e,axis:o.controller.options.indexAxis,scale:o.vScale,line:a}),o.$filler=l,i.push(l);for(r=0;r<s;++r)l=i[r],!(!l||l.fill===!1)&&(l.fill=pw(i,r,n.propagate))},beforeDraw(e,t,n){const s=n.drawTime==="beforeDraw",i=e.getSortedVisibleDatasetMetas(),o=e.chartArea;for(let r=i.length-1;r>=0;--r){const a=i[r].$filler;a&&(a.line.updateControlPoints(o,a.axis),s&&a.fill&&Er(e.ctx,a,o))}},beforeDatasetsDraw(e,t,n){if(n.drawTime!=="beforeDatasetsDraw")return;const s=e.getSortedVisibleDatasetMetas();for(let i=s.length-1;i>=0;--i){const o=s[i].$filler;Mu(o)&&Er(e.ctx,o,e.chartArea)}},beforeDatasetDraw(e,t,n){const s=t.meta.$filler;!Mu(s)||n.drawTime!=="beforeDatasetDraw"||Er(e.ctx,s,e.chartArea)},defaults:{propagate:!0,drawTime:"beforeDatasetDraw"}};const ku=(e,t)=>{let{boxHeight:n=t,boxWidth:s=t}=e;return e.usePointStyle&&(n=Math.min(n,t),s=e.pointStyleWidth||Math.min(s,t)),{boxWidth:s,boxHeight:n,itemHeight:Math.max(t,n)}},Tw=(e,t)=>e!==null&&t!==null&&e.datasetIndex===t.datasetIndex&&e.index===t.index;class Au extends Qe{constructor(t){super(),this._added=!1,this.legendHitBoxes=[],this._hoveredItem=null,this.doughnutMode=!1,this.chart=t.chart,this.options=t.options,this.ctx=t.ctx,this.legendItems=void 0,this.columnSizes=void 0,this.lineWidths=void 0,this.maxHeight=void 0,this.maxWidth=void 0,this.top=void 0,this.bottom=void 0,this.left=void 0,this.right=void 0,this.height=void 0,this.width=void 0,this._margins=void 0,this.position=void 0,this.weight=void 0,this.fullSize=void 0}update(t,n,s){this.maxWidth=t,this.maxHeight=n,this._margins=s,this.setDimensions(),this.buildLabels(),this.fit()}setDimensions(){this.isHorizontal()?(this.width=this.maxWidth,this.left=this._margins.left,this.right=this.width):(this.height=this.maxHeight,this.top=this._margins.top,this.bottom=this.height)}buildLabels(){const t=this.options.labels||{};let n=_t(t.generateLabels,[this.chart],this)||[];t.filter&&(n=n.filter(s=>t.filter(s,this.chart.data))),t.sort&&(n=n.sort((s,i)=>t.sort(s,i,this.chart.data))),this.options.reverse&&n.reverse(),this.legendItems=n}fit(){const{options:t,ctx:n}=this;if(!t.display){this.width=this.height=0;return}const s=t.labels,i=Tt(s.font),o=i.size,r=this._computeTitleHeight(),{boxWidth:a,itemHeight:l}=ku(s,o);let c,u;n.font=i.string,this.isHorizontal()?(c=this.maxWidth,u=this._fitRows(r,o,a,l)+10):(u=this.maxHeight,c=this._fitCols(r,i,a,l)+10),this.width=Math.min(c,t.maxWidth||this.maxWidth),this.height=Math.min(u,t.maxHeight||this.maxHeight)}_fitRows(t,n,s,i){const{ctx:o,maxWidth:r,options:{labels:{padding:a}}}=this,l=this.legendHitBoxes=[],c=this.lineWidths=[0],u=i+a;let f=t;o.textAlign="left",o.textBaseline="middle";let h=-1,d=-u;return this.legendItems.forEach((p,g)=>{const m=s+n/2+o.measureText(p.text).width;(g===0||c[c.length-1]+m+2*a>r)&&(f+=u,c[c.length-(g>0?0:1)]=0,d+=u,h++),l[g]={left:0,top:d,row:h,width:m,height:i},c[c.length-1]+=m+a}),f}_fitCols(t,n,s,i){const{ctx:o,maxHeight:r,options:{labels:{padding:a}}}=this,l=this.legendHitBoxes=[],c=this.columnSizes=[],u=r-t;let f=a,h=0,d=0,p=0,g=0;return this.legendItems.forEach((m,y)=>{const{itemWidth:b,itemHeight:w}=Dw(s,n,o,m,i);y>0&&d+w+2*a>u&&(f+=h+a,c.push({width:h,height:d}),p+=h+a,g++,h=d=0),l[y]={left:p,top:d,col:g,width:b,height:w},h=Math.max(h,b),d+=w+a}),f+=h,c.push({width:h,height:d}),f}adjustHitBoxes(){if(!this.options.display)return;const t=this._computeTitleHeight(),{legendHitBoxes:n,options:{align:s,labels:{padding:i},rtl:o}}=this,r=os(o,this.left,this.width);if(this.isHorizontal()){let a=0,l=Bt(s,this.left+i,this.right-this.lineWidths[a]);for(const c of n)a!==c.row&&(a=c.row,l=Bt(s,this.left+i,this.right-this.lineWidths[a])),c.top+=this.top+t+i,c.left=r.leftForLtr(r.x(l),c.width),l+=c.width+i}else{let a=0,l=Bt(s,this.top+t+i,this.bottom-this.columnSizes[a].height);for(const c of n)c.col!==a&&(a=c.col,l=Bt(s,this.top+t+i,this.bottom-this.columnSizes[a].height)),c.top=l,c.left+=this.left+i,c.left=r.leftForLtr(r.x(c.left),c.width),l+=c.height+i}}isHorizontal(){return this.options.position==="top"||this.options.position==="bottom"}draw(){if(this.options.display){const t=this.ctx;Ko(t,this),this._draw(),qo(t)}}_draw(){const{options:t,columnSizes:n,lineWidths:s,ctx:i}=this,{align:o,labels:r}=t,a=St.color,l=os(t.rtl,this.left,this.width),c=Tt(r.font),{padding:u}=r,f=c.size,h=f/2;let d;this.drawTitle(),i.textAlign=l.textAlign("left"),i.textBaseline="middle",i.lineWidth=.5,i.font=c.string;const{boxWidth:p,boxHeight:g,itemHeight:m}=ku(r,f),y=function(A,P,E){if(isNaN(p)||p<=0||isNaN(g)||g<0)return;i.save();const k=st(E.lineWidth,1);if(i.fillStyle=st(E.fillStyle,a),i.lineCap=st(E.lineCap,"butt"),i.lineDashOffset=st(E.lineDashOffset,0),i.lineJoin=st(E.lineJoin,"miter"),i.lineWidth=k,i.strokeStyle=st(E.strokeStyle,a),i.setLineDash(st(E.lineDash,[])),r.usePointStyle){const I={radius:g*Math.SQRT2/2,pointStyle:E.pointStyle,rotation:E.rotation,borderWidth:k},H=l.xPlus(A,p/2),L=P+h;fd(i,I,H,L,r.pointStyleWidth&&p)}else{const I=P+Math.max((f-g)/2,0),H=l.leftForLtr(A,p),L=zn(E.borderRadius);i.beginPath(),Object.values(L).some(X=>X!==0)?li(i,{x:H,y:I,w:p,h:g,radius:L}):i.rect(H,I,p,g),i.fill(),k!==0&&i.stroke()}i.restore()},b=function(A,P,E){$n(i,E.text,A,P+m/2,c,{strikethrough:E.hidden,textAlign:l.textAlign(E.textAlign)})},w=this.isHorizontal(),v=this._computeTitleHeight();w?d={x:Bt(o,this.left+u,this.right-s[0]),y:this.top+u+v,line:0}:d={x:this.left+u,y:Bt(o,this.top+v+u,this.bottom-n[0].height),line:0},_d(this.ctx,t.textDirection);const S=m+u;this.legendItems.forEach((A,P)=>{i.strokeStyle=A.fontColor,i.fillStyle=A.fontColor;const E=i.measureText(A.text).width,k=l.textAlign(A.textAlign||(A.textAlign=r.textAlign)),I=p+h+E;let H=d.x,L=d.y;l.setWidth(this.width),w?P>0&&H+I+u>this.right&&(L=d.y+=S,d.line++,H=d.x=Bt(o,this.left+u,this.right-s[d.line])):P>0&&L+S>this.bottom&&(H=d.x=H+n[d.line].width+u,d.line++,L=d.y=Bt(o,this.top+v+u,this.bottom-n[d.line].height));const X=l.x(H);if(y(X,L,A),H=Z_(k,H+p+h,w?H+I:this.right,t.rtl),b(l.x(H),L,A),w)d.x+=I+u;else if(typeof A.text!="string"){const rt=c.lineHeight;d.y+=Ud(A,rt)+u}else d.y+=S}),xd(this.ctx,t.textDirection)}drawTitle(){const t=this.options,n=t.title,s=Tt(n.font),i=$t(n.padding);if(!n.display)return;const o=os(t.rtl,this.left,this.width),r=this.ctx,a=n.position,l=s.size/2,c=i.top+l;let u,f=this.left,h=this.width;if(this.isHorizontal())h=Math.max(...this.lineWidths),u=this.top+c,f=Bt(t.align,f,this.right-h);else{const p=this.columnSizes.reduce((g,m)=>Math.max(g,m.height),0);u=c+Bt(t.align,this.top,this.bottom-p-t.labels.padding-this._computeTitleHeight())}const d=Bt(a,f,f+h);r.textAlign=o.textAlign(Xa(a)),r.textBaseline="middle",r.strokeStyle=n.color,r.fillStyle=n.color,r.font=s.string,$n(r,n.text,d,u,s)}_computeTitleHeight(){const t=this.options.title,n=Tt(t.font),s=$t(t.padding);return t.display?n.lineHeight+s.height:0}_getLegendItemAt(t,n){let s,i,o;if(Ke(t,this.left,this.right)&&Ke(n,this.top,this.bottom)){for(o=this.legendHitBoxes,s=0;s<o.length;++s)if(i=o[s],Ke(t,i.left,i.left+i.width)&&Ke(n,i.top,i.top+i.height))return this.legendItems[s]}return null}handleEvent(t){const n=this.options;if(!Iw(t.type,n))return;const s=this._getLegendItemAt(t.x,t.y);if(t.type==="mousemove"||t.type==="mouseout"){const i=this._hoveredItem,o=Tw(i,s);i&&!o&&_t(n.onLeave,[t,i,this],this),this._hoveredItem=s,s&&!o&&_t(n.onHover,[t,s,this],this)}else s&&_t(n.onClick,[t,s,this],this)}}function Dw(e,t,n,s,i){const o=Lw(s,e,t,n),r=Fw(i,s,t.lineHeight);return{itemWidth:o,itemHeight:r}}function Lw(e,t,n,s){let i=e.text;return i&&typeof i!="string"&&(i=i.reduce((o,r)=>o.length>r.length?o:r)),t+n.size/2+s.measureText(i).width}function Fw(e,t,n){let s=e;return typeof t.text!="string"&&(s=Ud(t,n)),s}function Ud(e,t){const n=e.text?e.text.length:0;return t*n}function Iw(e,t){return!!((e==="mousemove"||e==="mouseout")&&(t.onHover||t.onLeave)||t.onClick&&(e==="click"||e==="mouseup"))}var Nw={id:"legend",_element:Au,start(e,t,n){const s=e.legend=new Au({ctx:e.ctx,options:n,chart:e});Wt.configure(e,s,n),Wt.addBox(e,s)},stop(e){Wt.removeBox(e,e.legend),delete e.legend},beforeUpdate(e,t,n){const s=e.legend;Wt.configure(e,s,n),s.options=n},afterUpdate(e){const t=e.legend;t.buildLabels(),t.adjustHitBoxes()},afterEvent(e,t){t.replay||e.legend.handleEvent(t.event)},defaults:{display:!0,position:"top",align:"center",fullSize:!0,reverse:!1,weight:1e3,onClick(e,t,n){const s=t.datasetIndex,i=n.chart;i.isDatasetVisible(s)?(i.hide(s),t.hidden=!0):(i.show(s),t.hidden=!1)},onHover:null,onLeave:null,labels:{color:e=>e.chart.options.color,boxWidth:40,padding:10,generateLabels(e){const t=e.data.datasets,{labels:{usePointStyle:n,pointStyle:s,textAlign:i,color:o,useBorderRadius:r,borderRadius:a}}=e.legend.options;return e._getSortedDatasetMetas().map(l=>{const c=l.controller.getStyle(n?0:void 0),u=$t(c.borderWidth);return{text:t[l.index].label,fillStyle:c.backgroundColor,fontColor:o,hidden:!l.visible,lineCap:c.borderCapStyle,lineDash:c.borderDash,lineDashOffset:c.borderDashOffset,lineJoin:c.borderJoinStyle,lineWidth:(u.width+u.height)/4,strokeStyle:c.borderColor,pointStyle:s||c.pointStyle,rotation:c.rotation,textAlign:i||c.textAlign,borderRadius:r&&(a||c.borderRadius),datasetIndex:l.index}},this)}},title:{color:e=>e.chart.options.color,display:!1,position:"center",text:""}},descriptors:{_scriptable:e=>!e.startsWith("on"),labels:{_scriptable:e=>!["generateLabels","filter","sort"].includes(e)}}};class ol extends Qe{constructor(t){super(),this.chart=t.chart,this.options=t.options,this.ctx=t.ctx,this._padding=void 0,this.top=void 0,this.bottom=void 0,this.left=void 0,this.right=void 0,this.width=void 0,this.height=void 0,this.position=void 0,this.weight=void 0,this.fullSize=void 0}update(t,n){const s=this.options;if(this.left=0,this.top=0,!s.display){this.width=this.height=this.right=this.bottom=0;return}this.width=this.right=t,this.height=this.bottom=n;const i=wt(s.text)?s.text.length:1;this._padding=$t(s.padding);const o=i*Tt(s.font).lineHeight+this._padding.height;this.isHorizontal()?this.height=o:this.width=o}isHorizontal(){const t=this.options.position;return t==="top"||t==="bottom"}_drawArgs(t){const{top:n,left:s,bottom:i,right:o,options:r}=this,a=r.align;let l=0,c,u,f;return this.isHorizontal()?(u=Bt(a,s,o),f=n+t,c=o-s):(r.position==="left"?(u=s+t,f=Bt(a,i,n),l=vt*-.5):(u=o-t,f=Bt(a,n,i),l=vt*.5),c=i-n),{titleX:u,titleY:f,maxWidth:c,rotation:l}}draw(){const t=this.ctx,n=this.options;if(!n.display)return;const s=Tt(n.font),o=s.lineHeight/2+this._padding.top,{titleX:r,titleY:a,maxWidth:l,rotation:c}=this._drawArgs(o);$n(t,n.text,0,0,s,{color:n.color,maxWidth:l,rotation:c,textAlign:Xa(n.align),textBaseline:"middle",translation:[r,a]})}}function Bw(e,t){const n=new ol({ctx:e.ctx,options:t,chart:e});Wt.configure(e,n,t),Wt.addBox(e,n),e.titleBlock=n}var zw={id:"title",_element:ol,start(e,t,n){Bw(e,n)},stop(e){const t=e.titleBlock;Wt.removeBox(e,t),delete e.titleBlock},beforeUpdate(e,t,n){const s=e.titleBlock;Wt.configure(e,s,n),s.options=n},defaults:{align:"center",display:!1,font:{weight:"bold"},fullSize:!0,padding:10,position:"top",text:"",weight:2e3},defaultRoutes:{color:"color"},descriptors:{_scriptable:!0,_indexable:!1}};const Fi=new WeakMap;var Hw={id:"subtitle",start(e,t,n){const s=new ol({ctx:e.ctx,options:n,chart:e});Wt.configure(e,s,n),Wt.addBox(e,s),Fi.set(e,s)},stop(e){Wt.removeBox(e,Fi.get(e)),Fi.delete(e)},beforeUpdate(e,t,n){const s=Fi.get(e);Wt.configure(e,s,n),s.options=n},defaults:{align:"center",display:!1,font:{weight:"normal"},fullSize:!0,padding:0,position:"top",text:"",weight:1500},defaultRoutes:{color:"color"},descriptors:{_scriptable:!0,_indexable:!1}};const Fs={average(e){if(!e.length)return!1;let t,n,s=new Set,i=0,o=0;for(t=0,n=e.length;t<n;++t){const a=e[t].element;if(a&&a.hasValue()){const l=a.tooltipPosition();s.add(l.x),i+=l.y,++o}}return o===0||s.size===0?!1:{x:[...s].reduce((a,l)=>a+l)/s.size,y:i/o}},nearest(e,t){if(!e.length)return!1;let n=t.x,s=t.y,i=Number.POSITIVE_INFINITY,o,r,a;for(o=0,r=e.length;o<r;++o){const l=e[o].element;if(l&&l.hasValue()){const c=l.getCenterPoint(),u=ta(t,c);u<i&&(i=u,a=l)}}if(a){const l=a.tooltipPosition();n=l.x,s=l.y}return{x:n,y:s}}};function Oe(e,t){return t&&(wt(t)?Array.prototype.push.apply(e,t):e.push(t)),e}function He(e){return(typeof e=="string"||e instanceof String)&&e.indexOf(`
`)>-1?e.split(`
`):e}function jw(e,t){const{element:n,datasetIndex:s,index:i}=t,o=e.getDatasetMeta(s).controller,{label:r,value:a}=o.getLabelAndValue(i);return{chart:e,label:r,parsed:o.getParsed(i),raw:e.data.datasets[s].data[i],formattedValue:a,dataset:o.getDataset(),dataIndex:i,datasetIndex:s,element:n}}function Ou(e,t){const n=e.chart.ctx,{body:s,footer:i,title:o}=e,{boxWidth:r,boxHeight:a}=t,l=Tt(t.bodyFont),c=Tt(t.titleFont),u=Tt(t.footerFont),f=o.length,h=i.length,d=s.length,p=$t(t.padding);let g=p.height,m=0,y=s.reduce((v,S)=>v+S.before.length+S.lines.length+S.after.length,0);if(y+=e.beforeBody.length+e.afterBody.length,f&&(g+=f*c.lineHeight+(f-1)*t.titleSpacing+t.titleMarginBottom),y){const v=t.displayColors?Math.max(a,l.lineHeight):l.lineHeight;g+=d*v+(y-d)*l.lineHeight+(y-1)*t.bodySpacing}h&&(g+=t.footerMarginTop+h*u.lineHeight+(h-1)*t.footerSpacing);let b=0;const w=function(v){m=Math.max(m,n.measureText(v).width+b)};return n.save(),n.font=c.string,ht(e.title,w),n.font=l.string,ht(e.beforeBody.concat(e.afterBody),w),b=t.displayColors?r+2+t.boxPadding:0,ht(s,v=>{ht(v.before,w),ht(v.lines,w),ht(v.after,w)}),b=0,n.font=u.string,ht(e.footer,w),n.restore(),m+=p.width,{width:m,height:g}}function Vw(e,t){const{y:n,height:s}=t;return n<s/2?"top":n>e.height-s/2?"bottom":"center"}function Ww(e,t,n,s){const{x:i,width:o}=s,r=n.caretSize+n.caretPadding;if(e==="left"&&i+o+r>t.width||e==="right"&&i-o-r<0)return!0}function $w(e,t,n,s){const{x:i,width:o}=n,{width:r,chartArea:{left:a,right:l}}=e;let c="center";return s==="center"?c=i<=(a+l)/2?"left":"right":i<=o/2?c="left":i>=r-o/2&&(c="right"),Ww(c,e,t,n)&&(c="center"),c}function Ru(e,t,n){const s=n.yAlign||t.yAlign||Vw(e,n);return{xAlign:n.xAlign||t.xAlign||$w(e,t,n,s),yAlign:s}}function Uw(e,t){let{x:n,width:s}=e;return t==="right"?n-=s:t==="center"&&(n-=s/2),n}function Kw(e,t,n){let{y:s,height:i}=e;return t==="top"?s+=n:t==="bottom"?s-=i+n:s-=i/2,s}function Tu(e,t,n,s){const{caretSize:i,caretPadding:o,cornerRadius:r}=e,{xAlign:a,yAlign:l}=n,c=i+o,{topLeft:u,topRight:f,bottomLeft:h,bottomRight:d}=zn(r);let p=Uw(t,a);const g=Kw(t,l,c);return l==="center"?a==="left"?p+=c:a==="right"&&(p-=c):a==="left"?p-=Math.max(u,h)+i:a==="right"&&(p+=Math.max(f,d)+i),{x:Ft(p,0,s.width-t.width),y:Ft(g,0,s.height-t.height)}}function Ii(e,t,n){const s=$t(n.padding);return t==="center"?e.x+e.width/2:t==="right"?e.x+e.width-s.right:e.x+s.left}function Du(e){return Oe([],He(e))}function qw(e,t,n){return xn(e,{tooltip:t,tooltipItems:n,type:"tooltip"})}function Lu(e,t){const n=t&&t.dataset&&t.dataset.tooltip&&t.dataset.tooltip.callbacks;return n?e.override(n):e}const Kd={beforeTitle:Be,title(e){if(e.length>0){const t=e[0],n=t.chart.data.labels,s=n?n.length:0;if(this&&this.options&&this.options.mode==="dataset")return t.dataset.label||"";if(t.label)return t.label;if(s>0&&t.dataIndex<s)return n[t.dataIndex]}return""},afterTitle:Be,beforeBody:Be,beforeLabel:Be,label(e){if(this&&this.options&&this.options.mode==="dataset")return e.label+": "+e.formattedValue||e.formattedValue;let t=e.dataset.label||"";t&&(t+=": ");const n=e.formattedValue;return it(n)||(t+=n),t},labelColor(e){const n=e.chart.getDatasetMeta(e.datasetIndex).controller.getStyle(e.dataIndex);return{borderColor:n.borderColor,backgroundColor:n.backgroundColor,borderWidth:n.borderWidth,borderDash:n.borderDash,borderDashOffset:n.borderDashOffset,borderRadius:0}},labelTextColor(){return this.options.bodyColor},labelPointStyle(e){const n=e.chart.getDatasetMeta(e.datasetIndex).controller.getStyle(e.dataIndex);return{pointStyle:n.pointStyle,rotation:n.rotation}},afterLabel:Be,afterBody:Be,beforeFooter:Be,footer:Be,afterFooter:Be};function Jt(e,t,n,s){const i=e[t].call(n,s);return typeof i>"u"?Kd[t].call(n,s):i}class ca extends Qe{constructor(t){super(),this.opacity=0,this._active=[],this._eventPosition=void 0,this._size=void 0,this._cachedAnimations=void 0,this._tooltipItems=[],this.$animations=void 0,this.$context=void 0,this.chart=t.chart,this.options=t.options,this.dataPoints=void 0,this.title=void 0,this.beforeBody=void 0,this.body=void 0,this.afterBody=void 0,this.footer=void 0,this.xAlign=void 0,this.yAlign=void 0,this.x=void 0,this.y=void 0,this.height=void 0,this.width=void 0,this.caretX=void 0,this.caretY=void 0,this.labelColors=void 0,this.labelPointStyles=void 0,this.labelTextColors=void 0}initialize(t){this.options=t,this._cachedAnimations=void 0,this.$context=void 0}_resolveAnimations(){const t=this._cachedAnimations;if(t)return t;const n=this.chart,s=this.options.setContext(this.getContext()),i=s.enabled&&n.options.animation&&s.animations,o=new Cd(this.chart,i);return i._cacheable&&(this._cachedAnimations=Object.freeze(o)),o}getContext(){return this.$context||(this.$context=qw(this.chart.getContext(),this,this._tooltipItems))}getTitle(t,n){const{callbacks:s}=n,i=Jt(s,"beforeTitle",this,t),o=Jt(s,"title",this,t),r=Jt(s,"afterTitle",this,t);let a=[];return a=Oe(a,He(i)),a=Oe(a,He(o)),a=Oe(a,He(r)),a}getBeforeBody(t,n){return Du(Jt(n.callbacks,"beforeBody",this,t))}getBody(t,n){const{callbacks:s}=n,i=[];return ht(t,o=>{const r={before:[],lines:[],after:[]},a=Lu(s,o);Oe(r.before,He(Jt(a,"beforeLabel",this,o))),Oe(r.lines,Jt(a,"label",this,o)),Oe(r.after,He(Jt(a,"afterLabel",this,o))),i.push(r)}),i}getAfterBody(t,n){return Du(Jt(n.callbacks,"afterBody",this,t))}getFooter(t,n){const{callbacks:s}=n,i=Jt(s,"beforeFooter",this,t),o=Jt(s,"footer",this,t),r=Jt(s,"afterFooter",this,t);let a=[];return a=Oe(a,He(i)),a=Oe(a,He(o)),a=Oe(a,He(r)),a}_createItems(t){const n=this._active,s=this.chart.data,i=[],o=[],r=[];let a=[],l,c;for(l=0,c=n.length;l<c;++l)a.push(jw(this.chart,n[l]));return t.filter&&(a=a.filter((u,f,h)=>t.filter(u,f,h,s))),t.itemSort&&(a=a.sort((u,f)=>t.itemSort(u,f,s))),ht(a,u=>{const f=Lu(t.callbacks,u);i.push(Jt(f,"labelColor",this,u)),o.push(Jt(f,"labelPointStyle",this,u)),r.push(Jt(f,"labelTextColor",this,u))}),this.labelColors=i,this.labelPointStyles=o,this.labelTextColors=r,this.dataPoints=a,a}update(t,n){const s=this.options.setContext(this.getContext()),i=this._active;let o,r=[];if(!i.length)this.opacity!==0&&(o={opacity:0});else{const a=Fs[s.position].call(this,i,this._eventPosition);r=this._createItems(s),this.title=this.getTitle(r,s),this.beforeBody=this.getBeforeBody(r,s),this.body=this.getBody(r,s),this.afterBody=this.getAfterBody(r,s),this.footer=this.getFooter(r,s);const l=this._size=Ou(this,s),c=Object.assign({},a,l),u=Ru(this.chart,s,c),f=Tu(s,c,u,this.chart);this.xAlign=u.xAlign,this.yAlign=u.yAlign,o={opacity:1,x:f.x,y:f.y,width:l.width,height:l.height,caretX:a.x,caretY:a.y}}this._tooltipItems=r,this.$context=void 0,o&&this._resolveAnimations().update(this,o),t&&s.external&&s.external.call(this,{chart:this.chart,tooltip:this,replay:n})}drawCaret(t,n,s,i){const o=this.getCaretPosition(t,s,i);n.lineTo(o.x1,o.y1),n.lineTo(o.x2,o.y2),n.lineTo(o.x3,o.y3)}getCaretPosition(t,n,s){const{xAlign:i,yAlign:o}=this,{caretSize:r,cornerRadius:a}=s,{topLeft:l,topRight:c,bottomLeft:u,bottomRight:f}=zn(a),{x:h,y:d}=t,{width:p,height:g}=n;let m,y,b,w,v,S;return o==="center"?(v=d+g/2,i==="left"?(m=h,y=m-r,w=v+r,S=v-r):(m=h+p,y=m+r,w=v-r,S=v+r),b=m):(i==="left"?y=h+Math.max(l,u)+r:i==="right"?y=h+p-Math.max(c,f)-r:y=this.caretX,o==="top"?(w=d,v=w-r,m=y-r,b=y+r):(w=d+g,v=w+r,m=y+r,b=y-r),S=w),{x1:m,x2:y,x3:b,y1:w,y2:v,y3:S}}drawTitle(t,n,s){const i=this.title,o=i.length;let r,a,l;if(o){const c=os(s.rtl,this.x,this.width);for(t.x=Ii(this,s.titleAlign,s),n.textAlign=c.textAlign(s.titleAlign),n.textBaseline="middle",r=Tt(s.titleFont),a=s.titleSpacing,n.fillStyle=s.titleColor,n.font=r.string,l=0;l<o;++l)n.fillText(i[l],c.x(t.x),t.y+r.lineHeight/2),t.y+=r.lineHeight+a,l+1===o&&(t.y+=s.titleMarginBottom-a)}}_drawColorBox(t,n,s,i,o){const r=this.labelColors[s],a=this.labelPointStyles[s],{boxHeight:l,boxWidth:c}=o,u=Tt(o.bodyFont),f=Ii(this,"left",o),h=i.x(f),d=l<u.lineHeight?(u.lineHeight-l)/2:0,p=n.y+d;if(o.usePointStyle){const g={radius:Math.min(c,l)/2,pointStyle:a.pointStyle,rotation:a.rotation,borderWidth:1},m=i.leftForLtr(h,c)+c/2,y=p+l/2;t.strokeStyle=o.multiKeyBackground,t.fillStyle=o.multiKeyBackground,na(t,g,m,y),t.strokeStyle=r.borderColor,t.fillStyle=r.backgroundColor,na(t,g,m,y)}else{t.lineWidth=ot(r.borderWidth)?Math.max(...Object.values(r.borderWidth)):r.borderWidth||1,t.strokeStyle=r.borderColor,t.setLineDash(r.borderDash||[]),t.lineDashOffset=r.borderDashOffset||0;const g=i.leftForLtr(h,c),m=i.leftForLtr(i.xPlus(h,1),c-2),y=zn(r.borderRadius);Object.values(y).some(b=>b!==0)?(t.beginPath(),t.fillStyle=o.multiKeyBackground,li(t,{x:g,y:p,w:c,h:l,radius:y}),t.fill(),t.stroke(),t.fillStyle=r.backgroundColor,t.beginPath(),li(t,{x:m,y:p+1,w:c-2,h:l-2,radius:y}),t.fill()):(t.fillStyle=o.multiKeyBackground,t.fillRect(g,p,c,l),t.strokeRect(g,p,c,l),t.fillStyle=r.backgroundColor,t.fillRect(m,p+1,c-2,l-2))}t.fillStyle=this.labelTextColors[s]}drawBody(t,n,s){const{body:i}=this,{bodySpacing:o,bodyAlign:r,displayColors:a,boxHeight:l,boxWidth:c,boxPadding:u}=s,f=Tt(s.bodyFont);let h=f.lineHeight,d=0;const p=os(s.rtl,this.x,this.width),g=function(E){n.fillText(E,p.x(t.x+d),t.y+h/2),t.y+=h+o},m=p.textAlign(r);let y,b,w,v,S,A,P;for(n.textAlign=r,n.textBaseline="middle",n.font=f.string,t.x=Ii(this,m,s),n.fillStyle=s.bodyColor,ht(this.beforeBody,g),d=a&&m!=="right"?r==="center"?c/2+u:c+2+u:0,v=0,A=i.length;v<A;++v){for(y=i[v],b=this.labelTextColors[v],n.fillStyle=b,ht(y.before,g),w=y.lines,a&&w.length&&(this._drawColorBox(n,t,v,p,s),h=Math.max(f.lineHeight,l)),S=0,P=w.length;S<P;++S)g(w[S]),h=f.lineHeight;ht(y.after,g)}d=0,h=f.lineHeight,ht(this.afterBody,g),t.y-=o}drawFooter(t,n,s){const i=this.footer,o=i.length;let r,a;if(o){const l=os(s.rtl,this.x,this.width);for(t.x=Ii(this,s.footerAlign,s),t.y+=s.footerMarginTop,n.textAlign=l.textAlign(s.footerAlign),n.textBaseline="middle",r=Tt(s.footerFont),n.fillStyle=s.footerColor,n.font=r.string,a=0;a<o;++a)n.fillText(i[a],l.x(t.x),t.y+r.lineHeight/2),t.y+=r.lineHeight+s.footerSpacing}}drawBackground(t,n,s,i){const{xAlign:o,yAlign:r}=this,{x:a,y:l}=t,{width:c,height:u}=s,{topLeft:f,topRight:h,bottomLeft:d,bottomRight:p}=zn(i.cornerRadius);n.fillStyle=i.backgroundColor,n.strokeStyle=i.borderColor,n.lineWidth=i.borderWidth,n.beginPath(),n.moveTo(a+f,l),r==="top"&&this.drawCaret(t,n,s,i),n.lineTo(a+c-h,l),n.quadraticCurveTo(a+c,l,a+c,l+h),r==="center"&&o==="right"&&this.drawCaret(t,n,s,i),n.lineTo(a+c,l+u-p),n.quadraticCurveTo(a+c,l+u,a+c-p,l+u),r==="bottom"&&this.drawCaret(t,n,s,i),n.lineTo(a+d,l+u),n.quadraticCurveTo(a,l+u,a,l+u-d),r==="center"&&o==="left"&&this.drawCaret(t,n,s,i),n.lineTo(a,l+f),n.quadraticCurveTo(a,l,a+f,l),n.closePath(),n.fill(),i.borderWidth>0&&n.stroke()}_updateAnimationTarget(t){const n=this.chart,s=this.$animations,i=s&&s.x,o=s&&s.y;if(i||o){const r=Fs[t.position].call(this,this._active,this._eventPosition);if(!r)return;const a=this._size=Ou(this,t),l=Object.assign({},r,this._size),c=Ru(n,t,l),u=Tu(t,l,c,n);(i._to!==u.x||o._to!==u.y)&&(this.xAlign=c.xAlign,this.yAlign=c.yAlign,this.width=a.width,this.height=a.height,this.caretX=r.x,this.caretY=r.y,this._resolveAnimations().update(this,u))}}_willRender(){return!!this.opacity}draw(t){const n=this.options.setContext(this.getContext());let s=this.opacity;if(!s)return;this._updateAnimationTarget(n);const i={width:this.width,height:this.height},o={x:this.x,y:this.y};s=Math.abs(s)<.001?0:s;const r=$t(n.padding),a=this.title.length||this.beforeBody.length||this.body.length||this.afterBody.length||this.footer.length;n.enabled&&a&&(t.save(),t.globalAlpha=s,this.drawBackground(o,t,i,n),_d(t,n.textDirection),o.y+=r.top,this.drawTitle(o,t,n),this.drawBody(o,t,n),this.drawFooter(o,t,n),xd(t,n.textDirection),t.restore())}getActiveElements(){return this._active||[]}setActiveElements(t,n){const s=this._active,i=t.map(({datasetIndex:a,index:l})=>{const c=this.chart.getDatasetMeta(a);if(!c)throw new Error("Cannot find a dataset at index "+a);return{datasetIndex:a,element:c.data[l],index:l}}),o=!mo(s,i),r=this._positionChanged(i,n);(o||r)&&(this._active=i,this._eventPosition=n,this._ignoreReplayEvents=!0,this.update(!0))}handleEvent(t,n,s=!0){if(n&&this._ignoreReplayEvents)return!1;this._ignoreReplayEvents=!1;const i=this.options,o=this._active||[],r=this._getActiveElements(t,o,n,s),a=this._positionChanged(r,t),l=n||!mo(r,o)||a;return l&&(this._active=r,(i.enabled||i.external)&&(this._eventPosition={x:t.x,y:t.y},this.update(!0,n))),l}_getActiveElements(t,n,s,i){const o=this.options;if(t.type==="mouseout")return[];if(!i)return n.filter(a=>this.chart.data.datasets[a.datasetIndex]&&this.chart.getDatasetMeta(a.datasetIndex).controller.getParsed(a.index)!==void 0);const r=this.chart.getElementsAtEventForMode(t,o.mode,o,s);return o.reverse&&r.reverse(),r}_positionChanged(t,n){const{caretX:s,caretY:i,options:o}=this,r=Fs[o.position].call(this,t,n);return r!==!1&&(s!==r.x||i!==r.y)}}N(ca,"positioners",Fs);var Yw={id:"tooltip",_element:ca,positioners:Fs,afterInit(e,t,n){n&&(e.tooltip=new ca({chart:e,options:n}))},beforeUpdate(e,t,n){e.tooltip&&e.tooltip.initialize(n)},reset(e,t,n){e.tooltip&&e.tooltip.initialize(n)},afterDraw(e){const t=e.tooltip;if(t&&t._willRender()){const n={tooltip:t};if(e.notifyPlugins("beforeTooltipDraw",{...n,cancelable:!0})===!1)return;t.draw(e.ctx),e.notifyPlugins("afterTooltipDraw",n)}},afterEvent(e,t){if(e.tooltip){const n=t.replay;e.tooltip.handleEvent(t.event,n,t.inChartArea)&&(t.changed=!0)}},defaults:{enabled:!0,external:null,position:"average",backgroundColor:"rgba(0,0,0,0.8)",titleColor:"#fff",titleFont:{weight:"bold"},titleSpacing:2,titleMarginBottom:6,titleAlign:"left",bodyColor:"#fff",bodySpacing:2,bodyFont:{},bodyAlign:"left",footerColor:"#fff",footerSpacing:2,footerMarginTop:6,footerFont:{weight:"bold"},footerAlign:"left",padding:6,caretPadding:2,caretSize:5,cornerRadius:6,boxHeight:(e,t)=>t.bodyFont.size,boxWidth:(e,t)=>t.bodyFont.size,multiKeyBackground:"#fff",displayColors:!0,boxPadding:0,borderColor:"rgba(0,0,0,0)",borderWidth:0,animation:{duration:400,easing:"easeOutQuart"},animations:{numbers:{type:"number",properties:["x","y","width","height","caretX","caretY"]},opacity:{easing:"linear",duration:200}},callbacks:Kd},defaultRoutes:{bodyFont:"font",footerFont:"font",titleFont:"font"},descriptors:{_scriptable:e=>e!=="filter"&&e!=="itemSort"&&e!=="external",_indexable:!1,callbacks:{_scriptable:!1,_indexable:!1},animation:{_fallback:!1},animations:{_fallback:"animation"}},additionalOptionScopes:["interaction"]},Xw=Object.freeze({__proto__:null,Colors:aw,Decimation:fw,Filler:Rw,Legend:Nw,SubTitle:Hw,Title:zw,Tooltip:Yw});const Gw=(e,t,n,s)=>(typeof t=="string"?(n=e.push(t)-1,s.unshift({index:n,label:t})):isNaN(t)&&(n=null),n);function Jw(e,t,n,s){const i=e.indexOf(t);if(i===-1)return Gw(e,t,n,s);const o=e.lastIndexOf(t);return i!==o?n:i}const Qw=(e,t)=>e===null?null:Ft(Math.round(e),0,t);function Fu(e){const t=this.getLabels();return e>=0&&e<t.length?t[e]:e}class ua extends Un{constructor(t){super(t),this._startValue=void 0,this._valueRange=0,this._addedLabels=[]}init(t){const n=this._addedLabels;if(n.length){const s=this.getLabels();for(const{index:i,label:o}of n)s[i]===o&&s.splice(i,1);this._addedLabels=[]}super.init(t)}parse(t,n){if(it(t))return null;const s=this.getLabels();return n=isFinite(n)&&s[n]===t?n:Jw(s,t,st(n,t),this._addedLabels),Qw(n,s.length-1)}determineDataLimits(){const{minDefined:t,maxDefined:n}=this.getUserBounds();let{min:s,max:i}=this.getMinMax(!0);this.options.bounds==="ticks"&&(t||(s=0),n||(i=this.getLabels().length-1)),this.min=s,this.max=i}buildTicks(){const t=this.min,n=this.max,s=this.options.offset,i=[];let o=this.getLabels();o=t===0&&n===o.length-1?o:o.slice(t,n+1),this._valueRange=Math.max(o.length-(s?0:1),1),this._startValue=this.min-(s?.5:0);for(let r=t;r<=n;r++)i.push({value:r});return i}getLabelForValue(t){return Fu.call(this,t)}configure(){super.configure(),this.isHorizontal()||(this._reversePixels=!this._reversePixels)}getPixelForValue(t){return typeof t!="number"&&(t=this.parse(t)),t===null?NaN:this.getPixelForDecimal((t-this._startValue)/this._valueRange)}getPixelForTick(t){const n=this.ticks;return t<0||t>n.length-1?null:this.getPixelForValue(n[t].value)}getValueForPixel(t){return Math.round(this._startValue+this.getDecimalForPixel(t)*this._valueRange)}getBasePixel(){return this.bottom}}N(ua,"id","category"),N(ua,"defaults",{ticks:{callback:Fu}});function Zw(e,t){const n=[],{bounds:i,step:o,min:r,max:a,precision:l,count:c,maxTicks:u,maxDigits:f,includeBounds:h}=e,d=o||1,p=u-1,{min:g,max:m}=t,y=!it(r),b=!it(a),w=!it(c),v=(m-g)/(f+1);let S=Oc((m-g)/p/d)*d,A,P,E,k;if(S<1e-14&&!y&&!b)return[{value:g},{value:m}];k=Math.ceil(m/S)-Math.floor(g/S),k>p&&(S=Oc(k*S/p/d)*d),it(l)||(A=Math.pow(10,l),S=Math.ceil(S*A)/A),i==="ticks"?(P=Math.floor(g/S)*S,E=Math.ceil(m/S)*S):(P=g,E=m),y&&b&&o&&K_((a-r)/o,S/1e3)?(k=Math.round(Math.min((a-r)/S,u)),S=(a-r)/k,P=r,E=a):w?(P=y?r:P,E=b?a:E,k=c-1,S=(E-P)/k):(k=(E-P)/S,Ks(k,Math.round(k),S/1e3)?k=Math.round(k):k=Math.ceil(k));const I=Math.max(Rc(S),Rc(P));A=Math.pow(10,it(l)?I:l),P=Math.round(P*A)/A,E=Math.round(E*A)/A;let H=0;for(y&&(h&&P!==r?(n.push({value:r}),P<r&&H++,Ks(Math.round((P+H*S)*A)/A,r,Iu(r,v,e))&&H++):P<r&&H++);H<k;++H){const L=Math.round((P+H*S)*A)/A;if(b&&L>a)break;n.push({value:L})}return b&&h&&E!==a?n.length&&Ks(n[n.length-1].value,a,Iu(a,v,e))?n[n.length-1].value=a:n.push({value:a}):(!b||E===a)&&n.push({value:E}),n}function Iu(e,t,{horizontal:n,minRotation:s}){const i=me(s),o=(n?Math.sin(i):Math.cos(i))||.001,r=.75*t*(""+e).length;return Math.min(t/o,r)}class So extends Un{constructor(t){super(t),this.start=void 0,this.end=void 0,this._startValue=void 0,this._endValue=void 0,this._valueRange=0}parse(t,n){return it(t)||(typeof t=="number"||t instanceof Number)&&!isFinite(+t)?null:+t}handleTickRangeOptions(){const{beginAtZero:t}=this.options,{minDefined:n,maxDefined:s}=this.getUserBounds();let{min:i,max:o}=this;const r=l=>i=n?i:l,a=l=>o=s?o:l;if(t){const l=Fe(i),c=Fe(o);l<0&&c<0?a(0):l>0&&c>0&&r(0)}if(i===o){let l=o===0?1:Math.abs(o*.05);a(o+l),t||r(i-l)}this.min=i,this.max=o}getTickLimit(){const t=this.options.ticks;let{maxTicksLimit:n,stepSize:s}=t,i;return s?(i=Math.ceil(this.max/s)-Math.floor(this.min/s)+1,i>1e3&&(console.warn(`scales.${this.id}.ticks.stepSize: ${s} would result generating up to ${i} ticks. Limiting to 1000.`),i=1e3)):(i=this.computeTickLimit(),n=n||11),n&&(i=Math.min(n,i)),i}computeTickLimit(){return Number.POSITIVE_INFINITY}buildTicks(){const t=this.options,n=t.ticks;let s=this.getTickLimit();s=Math.max(2,s);const i={maxTicks:s,bounds:t.bounds,min:t.min,max:t.max,precision:n.precision,step:n.stepSize,count:n.count,maxDigits:this._maxDigits(),horizontal:this.isHorizontal(),minRotation:n.minRotation||0,includeBounds:n.includeBounds!==!1},o=this._range||this,r=Zw(i,o);return t.bounds==="ticks"&&nd(r,this,"value"),t.reverse?(r.reverse(),this.start=this.max,this.end=this.min):(this.start=this.min,this.end=this.max),r}configure(){const t=this.ticks;let n=this.min,s=this.max;if(super.configure(),this.options.offset&&t.length){const i=(s-n)/Math.max(t.length-1,1)/2;n-=i,s+=i}this._startValue=n,this._endValue=s,this._valueRange=s-n}getLabelForValue(t){return yi(t,this.chart.options.locale,this.options.ticks.format)}}class fa extends So{determineDataLimits(){const{min:t,max:n}=this.getMinMax(!0);this.min=Ct(t)?t:0,this.max=Ct(n)?n:1,this.handleTickRangeOptions()}computeTickLimit(){const t=this.isHorizontal(),n=t?this.width:this.height,s=me(this.options.ticks.minRotation),i=(t?Math.sin(s):Math.cos(s))||.001,o=this._resolveTickFontOptions(0);return Math.ceil(n/Math.min(40,o.lineHeight/i))}getPixelForValue(t){return t===null?NaN:this.getPixelForDecimal((t-this._startValue)/this._valueRange)}getValueForPixel(t){return this._startValue+this.getDecimalForPixel(t)*this._valueRange}}N(fa,"id","linear"),N(fa,"defaults",{ticks:{callback:Uo.formatters.numeric}});const ui=e=>Math.floor(ln(e)),En=(e,t)=>Math.pow(10,ui(e)+t);function Nu(e){return e/Math.pow(10,ui(e))===1}function Bu(e,t,n){const s=Math.pow(10,n),i=Math.floor(e/s);return Math.ceil(t/s)-i}function tS(e,t){const n=t-e;let s=ui(n);for(;Bu(e,t,s)>10;)s++;for(;Bu(e,t,s)<10;)s--;return Math.min(s,ui(e))}function eS(e,{min:t,max:n}){t=ie(e.min,t);const s=[],i=ui(t);let o=tS(t,n),r=o<0?Math.pow(10,Math.abs(o)):1;const a=Math.pow(10,o),l=i>o?Math.pow(10,i):0,c=Math.round((t-l)*r)/r,u=Math.floor((t-l)/a/10)*a*10;let f=Math.floor((c-u)/Math.pow(10,o)),h=ie(e.min,Math.round((l+u+f*Math.pow(10,o))*r)/r);for(;h<n;)s.push({value:h,major:Nu(h),significand:f}),f>=10?f=f<15?15:20:f++,f>=20&&(o++,f=2,r=o>=0?1:r),h=Math.round((l+u+f*Math.pow(10,o))*r)/r;const d=ie(e.max,h);return s.push({value:d,major:Nu(d),significand:f}),s}class ha extends Un{constructor(t){super(t),this.start=void 0,this.end=void 0,this._startValue=void 0,this._valueRange=0}parse(t,n){const s=So.prototype.parse.apply(this,[t,n]);if(s===0){this._zero=!0;return}return Ct(s)&&s>0?s:null}determineDataLimits(){const{min:t,max:n}=this.getMinMax(!0);this.min=Ct(t)?Math.max(0,t):null,this.max=Ct(n)?Math.max(0,n):null,this.options.beginAtZero&&(this._zero=!0),this._zero&&this.min!==this._suggestedMin&&!Ct(this._userMin)&&(this.min=t===En(this.min,0)?En(this.min,-1):En(this.min,0)),this.handleTickRangeOptions()}handleTickRangeOptions(){const{minDefined:t,maxDefined:n}=this.getUserBounds();let s=this.min,i=this.max;const o=a=>s=t?s:a,r=a=>i=n?i:a;s===i&&(s<=0?(o(1),r(10)):(o(En(s,-1)),r(En(i,1)))),s<=0&&o(En(i,-1)),i<=0&&r(En(s,1)),this.min=s,this.max=i}buildTicks(){const t=this.options,n={min:this._userMin,max:this._userMax},s=eS(n,this);return t.bounds==="ticks"&&nd(s,this,"value"),t.reverse?(s.reverse(),this.start=this.max,this.end=this.min):(this.start=this.min,this.end=this.max),s}getLabelForValue(t){return t===void 0?"0":yi(t,this.chart.options.locale,this.options.ticks.format)}configure(){const t=this.min;super.configure(),this._startValue=ln(t),this._valueRange=ln(this.max)-ln(t)}getPixelForValue(t){return(t===void 0||t===0)&&(t=this.min),t===null||isNaN(t)?NaN:this.getPixelForDecimal(t===this.min?0:(ln(t)-this._startValue)/this._valueRange)}getValueForPixel(t){const n=this.getDecimalForPixel(t);return Math.pow(10,this._startValue+n*this._valueRange)}}N(ha,"id","logarithmic"),N(ha,"defaults",{ticks:{callback:Uo.formatters.logarithmic,major:{enabled:!0}}});function da(e){const t=e.ticks;if(t.display&&e.display){const n=$t(t.backdropPadding);return st(t.font&&t.font.size,St.font.size)+n.height}return 0}function nS(e,t,n){return n=wt(n)?n:[n],{w:cx(e,t.string,n),h:n.length*t.lineHeight}}function zu(e,t,n,s,i){return e===s||e===i?{start:t-n/2,end:t+n/2}:e<s||e>i?{start:t-n,end:t}:{start:t,end:t+n}}function sS(e){const t={l:e.left+e._padding.left,r:e.right-e._padding.right,t:e.top+e._padding.top,b:e.bottom-e._padding.bottom},n=Object.assign({},t),s=[],i=[],o=e._pointLabels.length,r=e.options.pointLabels,a=r.centerPointLabels?vt/o:0;for(let l=0;l<o;l++){const c=r.setContext(e.getPointLabelContext(l));i[l]=c.padding;const u=e.getPointPosition(l,e.drawingArea+i[l],a),f=Tt(c.font),h=nS(e.ctx,f,e._pointLabels[l]);s[l]=h;const d=oe(e.getIndexAngle(l)+a),p=Math.round(qa(d)),g=zu(p,u.x,h.w,0,180),m=zu(p,u.y,h.h,90,270);iS(n,t,d,g,m)}e.setCenterPoint(t.l-n.l,n.r-t.r,t.t-n.t,n.b-t.b),e._pointLabelItems=aS(e,s,i)}function iS(e,t,n,s,i){const o=Math.abs(Math.sin(n)),r=Math.abs(Math.cos(n));let a=0,l=0;s.start<t.l?(a=(t.l-s.start)/o,e.l=Math.min(e.l,t.l-a)):s.end>t.r&&(a=(s.end-t.r)/o,e.r=Math.max(e.r,t.r+a)),i.start<t.t?(l=(t.t-i.start)/r,e.t=Math.min(e.t,t.t-l)):i.end>t.b&&(l=(i.end-t.b)/r,e.b=Math.max(e.b,t.b+l))}function oS(e,t,n){const s=e.drawingArea,{extra:i,additionalAngle:o,padding:r,size:a}=n,l=e.getPointPosition(t,s+i+r,o),c=Math.round(qa(oe(l.angle+Et))),u=uS(l.y,a.h,c),f=lS(c),h=cS(l.x,a.w,f);return{visible:!0,x:l.x,y:u,textAlign:f,left:h,top:u,right:h+a.w,bottom:u+a.h}}function rS(e,t){if(!t)return!0;const{left:n,top:s,right:i,bottom:o}=e;return!(Ye({x:n,y:s},t)||Ye({x:n,y:o},t)||Ye({x:i,y:s},t)||Ye({x:i,y:o},t))}function aS(e,t,n){const s=[],i=e._pointLabels.length,o=e.options,{centerPointLabels:r,display:a}=o.pointLabels,l={extra:da(o)/2,additionalAngle:r?vt/i:0};let c;for(let u=0;u<i;u++){l.padding=n[u],l.size=t[u];const f=oS(e,u,l);s.push(f),a==="auto"&&(f.visible=rS(f,c),f.visible&&(c=f))}return s}function lS(e){return e===0||e===180?"center":e<180?"left":"right"}function cS(e,t,n){return n==="right"?e-=t:n==="center"&&(e-=t/2),e}function uS(e,t,n){return n===90||n===270?e-=t/2:(n>270||n<90)&&(e-=t),e}function fS(e,t,n){const{left:s,top:i,right:o,bottom:r}=n,{backdropColor:a}=t;if(!it(a)){const l=zn(t.borderRadius),c=$t(t.backdropPadding);e.fillStyle=a;const u=s-c.left,f=i-c.top,h=o-s+c.width,d=r-i+c.height;Object.values(l).some(p=>p!==0)?(e.beginPath(),li(e,{x:u,y:f,w:h,h:d,radius:l}),e.fill()):e.fillRect(u,f,h,d)}}function hS(e,t){const{ctx:n,options:{pointLabels:s}}=e;for(let i=t-1;i>=0;i--){const o=e._pointLabelItems[i];if(!o.visible)continue;const r=s.setContext(e.getPointLabelContext(i));fS(n,r,o);const a=Tt(r.font),{x:l,y:c,textAlign:u}=o;$n(n,e._pointLabels[i],l,c+a.lineHeight/2,a,{color:r.color,textAlign:u,textBaseline:"middle"})}}function qd(e,t,n,s){const{ctx:i}=e;if(n)i.arc(e.xCenter,e.yCenter,t,0,xt);else{let o=e.getPointPosition(0,t);i.moveTo(o.x,o.y);for(let r=1;r<s;r++)o=e.getPointPosition(r,t),i.lineTo(o.x,o.y)}}function dS(e,t,n,s,i){const o=e.ctx,r=t.circular,{color:a,lineWidth:l}=t;!r&&!s||!a||!l||n<0||(o.save(),o.strokeStyle=a,o.lineWidth=l,o.setLineDash(i.dash||[]),o.lineDashOffset=i.dashOffset,o.beginPath(),qd(e,n,r,s),o.closePath(),o.stroke(),o.restore())}function pS(e,t,n){return xn(e,{label:n,index:t,type:"pointLabel"})}class Is extends So{constructor(t){super(t),this.xCenter=void 0,this.yCenter=void 0,this.drawingArea=void 0,this._pointLabels=[],this._pointLabelItems=[]}setDimensions(){const t=this._padding=$t(da(this.options)/2),n=this.width=this.maxWidth-t.width,s=this.height=this.maxHeight-t.height;this.xCenter=Math.floor(this.left+n/2+t.left),this.yCenter=Math.floor(this.top+s/2+t.top),this.drawingArea=Math.floor(Math.min(n,s)/2)}determineDataLimits(){const{min:t,max:n}=this.getMinMax(!1);this.min=Ct(t)&&!isNaN(t)?t:0,this.max=Ct(n)&&!isNaN(n)?n:0,this.handleTickRangeOptions()}computeTickLimit(){return Math.ceil(this.drawingArea/da(this.options))}generateTickLabels(t){So.prototype.generateTickLabels.call(this,t),this._pointLabels=this.getLabels().map((n,s)=>{const i=_t(this.options.pointLabels.callback,[n,s],this);return i||i===0?i:""}).filter((n,s)=>this.chart.getDataVisibility(s))}fit(){const t=this.options;t.display&&t.pointLabels.display?sS(this):this.setCenterPoint(0,0,0,0)}setCenterPoint(t,n,s,i){this.xCenter+=Math.floor((t-n)/2),this.yCenter+=Math.floor((s-i)/2),this.drawingArea-=Math.min(this.drawingArea/2,Math.max(t,n,s,i))}getIndexAngle(t){const n=xt/(this._pointLabels.length||1),s=this.options.startAngle||0;return oe(t*n+me(s))}getDistanceFromCenterForValue(t){if(it(t))return NaN;const n=this.drawingArea/(this.max-this.min);return this.options.reverse?(this.max-t)*n:(t-this.min)*n}getValueForDistanceFromCenter(t){if(it(t))return NaN;const n=t/(this.drawingArea/(this.max-this.min));return this.options.reverse?this.max-n:this.min+n}getPointLabelContext(t){const n=this._pointLabels||[];if(t>=0&&t<n.length){const s=n[t];return pS(this.getContext(),t,s)}}getPointPosition(t,n,s=0){const i=this.getIndexAngle(t)-Et+s;return{x:Math.cos(i)*n+this.xCenter,y:Math.sin(i)*n+this.yCenter,angle:i}}getPointPositionForValue(t,n){return this.getPointPosition(t,this.getDistanceFromCenterForValue(n))}getBasePosition(t){return this.getPointPositionForValue(t||0,this.getBaseValue())}getPointLabelPosition(t){const{left:n,top:s,right:i,bottom:o}=this._pointLabelItems[t];return{left:n,top:s,right:i,bottom:o}}drawBackground(){const{backgroundColor:t,grid:{circular:n}}=this.options;if(t){const s=this.ctx;s.save(),s.beginPath(),qd(this,this.getDistanceFromCenterForValue(this._endValue),n,this._pointLabels.length),s.closePath(),s.fillStyle=t,s.fill(),s.restore()}}drawGrid(){const t=this.ctx,n=this.options,{angleLines:s,grid:i,border:o}=n,r=this._pointLabels.length;let a,l,c;if(n.pointLabels.display&&hS(this,r),i.display&&this.ticks.forEach((u,f)=>{if(f!==0||f===0&&this.min<0){l=this.getDistanceFromCenterForValue(u.value);const h=this.getContext(f),d=i.setContext(h),p=o.setContext(h);dS(this,d,l,r,p)}}),s.display){for(t.save(),a=r-1;a>=0;a--){const u=s.setContext(this.getPointLabelContext(a)),{color:f,lineWidth:h}=u;!h||!f||(t.lineWidth=h,t.strokeStyle=f,t.setLineDash(u.borderDash),t.lineDashOffset=u.borderDashOffset,l=this.getDistanceFromCenterForValue(n.reverse?this.min:this.max),c=this.getPointPosition(a,l),t.beginPath(),t.moveTo(this.xCenter,this.yCenter),t.lineTo(c.x,c.y),t.stroke())}t.restore()}}drawBorder(){}drawLabels(){const t=this.ctx,n=this.options,s=n.ticks;if(!s.display)return;const i=this.getIndexAngle(0);let o,r;t.save(),t.translate(this.xCenter,this.yCenter),t.rotate(i),t.textAlign="center",t.textBaseline="middle",this.ticks.forEach((a,l)=>{if(l===0&&this.min>=0&&!n.reverse)return;const c=s.setContext(this.getContext(l)),u=Tt(c.font);if(o=this.getDistanceFromCenterForValue(this.ticks[l].value),c.showLabelBackdrop){t.font=u.string,r=t.measureText(a.label).width,t.fillStyle=c.backdropColor;const f=$t(c.backdropPadding);t.fillRect(-r/2-f.left,-o-u.size/2-f.top,r+f.width,u.size+f.height)}$n(t,a.label,0,-o,u,{color:c.color,strokeColor:c.textStrokeColor,strokeWidth:c.textStrokeWidth})}),t.restore()}drawTitle(){}}N(Is,"id","radialLinear"),N(Is,"defaults",{display:!0,animate:!0,position:"chartArea",angleLines:{display:!0,lineWidth:1,borderDash:[],borderDashOffset:0},grid:{circular:!1},startAngle:0,ticks:{showLabelBackdrop:!0,callback:Uo.formatters.numeric},pointLabels:{backdropColor:void 0,backdropPadding:2,display:!0,font:{size:10},callback(t){return t},padding:5,centerPointLabels:!1}}),N(Is,"defaultRoutes",{"angleLines.color":"borderColor","pointLabels.color":"color","ticks.color":"color"}),N(Is,"descriptors",{angleLines:{_fallback:"grid"}});const Go={millisecond:{common:!0,size:1,steps:1e3},second:{common:!0,size:1e3,steps:60},minute:{common:!0,size:6e4,steps:60},hour:{common:!0,size:36e5,steps:24},day:{common:!0,size:864e5,steps:30},week:{common:!1,size:6048e5,steps:4},month:{common:!0,size:2628e6,steps:12},quarter:{common:!1,size:7884e6,steps:4},year:{common:!0,size:3154e7}},Zt=Object.keys(Go);function Hu(e,t){return e-t}function ju(e,t){if(it(t))return null;const n=e._adapter,{parser:s,round:i,isoWeekday:o}=e._parseOpts;let r=t;return typeof s=="function"&&(r=s(r)),Ct(r)||(r=typeof s=="string"?n.parse(r,s):n.parse(r)),r===null?null:(i&&(r=i==="week"&&(hs(o)||o===!0)?n.startOf(r,"isoWeek",o):n.startOf(r,i)),+r)}function Vu(e,t,n,s){const i=Zt.length;for(let o=Zt.indexOf(e);o<i-1;++o){const r=Go[Zt[o]],a=r.steps?r.steps:Number.MAX_SAFE_INTEGER;if(r.common&&Math.ceil((n-t)/(a*r.size))<=s)return Zt[o]}return Zt[i-1]}function gS(e,t,n,s,i){for(let o=Zt.length-1;o>=Zt.indexOf(n);o--){const r=Zt[o];if(Go[r].common&&e._adapter.diff(i,s,r)>=t-1)return r}return Zt[n?Zt.indexOf(n):0]}function mS(e){for(let t=Zt.indexOf(e)+1,n=Zt.length;t<n;++t)if(Go[Zt[t]].common)return Zt[t]}function Wu(e,t,n){if(!n)e[t]=!0;else if(n.length){const{lo:s,hi:i}=Ya(n,t),o=n[s]>=t?n[s]:n[i];e[o]=!0}}function bS(e,t,n,s){const i=e._adapter,o=+i.startOf(t[0].value,s),r=t[t.length-1].value;let a,l;for(a=o;a<=r;a=+i.add(a,1,s))l=n[a],l>=0&&(t[l].major=!0);return t}function $u(e,t,n){const s=[],i={},o=t.length;let r,a;for(r=0;r<o;++r)a=t[r],i[a]=r,s.push({value:a,major:!1});return o===0||!n?s:bS(e,s,i,n)}class fi extends Un{constructor(t){super(t),this._cache={data:[],labels:[],all:[]},this._unit="day",this._majorUnit=void 0,this._offsets={},this._normalized=!1,this._parseOpts=void 0}init(t,n={}){const s=t.time||(t.time={}),i=this._adapter=new P0._date(t.adapters.date);i.init(n),Us(s.displayFormats,i.formats()),this._parseOpts={parser:s.parser,round:s.round,isoWeekday:s.isoWeekday},super.init(t),this._normalized=n.normalized}parse(t,n){return t===void 0?null:ju(this,t)}beforeLayout(){super.beforeLayout(),this._cache={data:[],labels:[],all:[]}}determineDataLimits(){const t=this.options,n=this._adapter,s=t.time.unit||"day";let{min:i,max:o,minDefined:r,maxDefined:a}=this.getUserBounds();function l(c){!r&&!isNaN(c.min)&&(i=Math.min(i,c.min)),!a&&!isNaN(c.max)&&(o=Math.max(o,c.max))}(!r||!a)&&(l(this._getLabelBounds()),(t.bounds!=="ticks"||t.ticks.source!=="labels")&&l(this.getMinMax(!1))),i=Ct(i)&&!isNaN(i)?i:+n.startOf(Date.now(),s),o=Ct(o)&&!isNaN(o)?o:+n.endOf(Date.now(),s)+1,this.min=Math.min(i,o-1),this.max=Math.max(i+1,o)}_getLabelBounds(){const t=this.getLabelTimestamps();let n=Number.POSITIVE_INFINITY,s=Number.NEGATIVE_INFINITY;return t.length&&(n=t[0],s=t[t.length-1]),{min:n,max:s}}buildTicks(){const t=this.options,n=t.time,s=t.ticks,i=s.source==="labels"?this.getLabelTimestamps():this._generate();t.bounds==="ticks"&&i.length&&(this.min=this._userMin||i[0],this.max=this._userMax||i[i.length-1]);const o=this.min,r=this.max,a=G_(i,o,r);return this._unit=n.unit||(s.autoSkip?Vu(n.minUnit,this.min,this.max,this._getLabelCapacity(o)):gS(this,a.length,n.minUnit,this.min,this.max)),this._majorUnit=!s.major.enabled||this._unit==="year"?void 0:mS(this._unit),this.initOffsets(i),t.reverse&&a.reverse(),$u(this,a,this._majorUnit)}afterAutoSkip(){this.options.offsetAfterAutoskip&&this.initOffsets(this.ticks.map(t=>+t.value))}initOffsets(t=[]){let n=0,s=0,i,o;this.options.offset&&t.length&&(i=this.getDecimalForValue(t[0]),t.length===1?n=1-i:n=(this.getDecimalForValue(t[1])-i)/2,o=this.getDecimalForValue(t[t.length-1]),t.length===1?s=o:s=(o-this.getDecimalForValue(t[t.length-2]))/2);const r=t.length<3?.5:.25;n=Ft(n,0,r),s=Ft(s,0,r),this._offsets={start:n,end:s,factor:1/(n+1+s)}}_generate(){const t=this._adapter,n=this.min,s=this.max,i=this.options,o=i.time,r=o.unit||Vu(o.minUnit,n,s,this._getLabelCapacity(n)),a=st(i.ticks.stepSize,1),l=r==="week"?o.isoWeekday:!1,c=hs(l)||l===!0,u={};let f=n,h,d;if(c&&(f=+t.startOf(f,"isoWeek",l)),f=+t.startOf(f,c?"day":r),t.diff(s,n,r)>1e5*a)throw new Error(n+" and "+s+" are too far apart with stepSize of "+a+" "+r);const p=i.ticks.source==="data"&&this.getDataTimestamps();for(h=f,d=0;h<s;h=+t.add(h,a,r),d++)Wu(u,h,p);return(h===s||i.bounds==="ticks"||d===1)&&Wu(u,h,p),Object.keys(u).sort(Hu).map(g=>+g)}getLabelForValue(t){const n=this._adapter,s=this.options.time;return s.tooltipFormat?n.format(t,s.tooltipFormat):n.format(t,s.displayFormats.datetime)}format(t,n){const i=this.options.time.displayFormats,o=this._unit,r=n||i[o];return this._adapter.format(t,r)}_tickFormatFunction(t,n,s,i){const o=this.options,r=o.ticks.callback;if(r)return _t(r,[t,n,s],this);const a=o.time.displayFormats,l=this._unit,c=this._majorUnit,u=l&&a[l],f=c&&a[c],h=s[n],d=c&&f&&h&&h.major;return this._adapter.format(t,i||(d?f:u))}generateTickLabels(t){let n,s,i;for(n=0,s=t.length;n<s;++n)i=t[n],i.label=this._tickFormatFunction(i.value,n,t)}getDecimalForValue(t){return t===null?NaN:(t-this.min)/(this.max-this.min)}getPixelForValue(t){const n=this._offsets,s=this.getDecimalForValue(t);return this.getPixelForDecimal((n.start+s)*n.factor)}getValueForPixel(t){const n=this._offsets,s=this.getDecimalForPixel(t)/n.factor-n.end;return this.min+s*(this.max-this.min)}_getLabelSize(t){const n=this.options.ticks,s=this.ctx.measureText(t).width,i=me(this.isHorizontal()?n.maxRotation:n.minRotation),o=Math.cos(i),r=Math.sin(i),a=this._resolveTickFontOptions(0).size;return{w:s*o+a*r,h:s*r+a*o}}_getLabelCapacity(t){const n=this.options.time,s=n.displayFormats,i=s[n.unit]||s.millisecond,o=this._tickFormatFunction(t,0,$u(this,[t],this._majorUnit),i),r=this._getLabelSize(o),a=Math.floor(this.isHorizontal()?this.width/r.w:this.height/r.h)-1;return a>0?a:1}getDataTimestamps(){let t=this._cache.data||[],n,s;if(t.length)return t;const i=this.getMatchingVisibleMetas();if(this._normalized&&i.length)return this._cache.data=i[0].controller.getAllParsedValues(this);for(n=0,s=i.length;n<s;++n)t=t.concat(i[n].controller.getAllParsedValues(this));return this._cache.data=this.normalize(t)}getLabelTimestamps(){const t=this._cache.labels||[];let n,s;if(t.length)return t;const i=this.getLabels();for(n=0,s=i.length;n<s;++n)t.push(ju(this,i[n]));return this._cache.labels=this._normalized?t:this.normalize(t)}normalize(t){return od(t.sort(Hu))}}N(fi,"id","time"),N(fi,"defaults",{bounds:"data",adapters:{},time:{parser:!1,unit:!1,round:!1,isoWeekday:!1,minUnit:"millisecond",displayFormats:{}},ticks:{source:"auto",callback:!1,major:{enabled:!1}}});function Ni(e,t,n){let s=0,i=e.length-1,o,r,a,l;n?(t>=e[s].pos&&t<=e[i].pos&&({lo:s,hi:i}=qe(e,"pos",t)),{pos:o,time:a}=e[s],{pos:r,time:l}=e[i]):(t>=e[s].time&&t<=e[i].time&&({lo:s,hi:i}=qe(e,"time",t)),{time:o,pos:a}=e[s],{time:r,pos:l}=e[i]);const c=r-o;return c?a+(l-a)*(t-o)/c:a}class pa extends fi{constructor(t){super(t),this._table=[],this._minPos=void 0,this._tableRange=void 0}initOffsets(){const t=this._getTimestampsForTable(),n=this._table=this.buildLookupTable(t);this._minPos=Ni(n,this.min),this._tableRange=Ni(n,this.max)-this._minPos,super.initOffsets(t)}buildLookupTable(t){const{min:n,max:s}=this,i=[],o=[];let r,a,l,c,u;for(r=0,a=t.length;r<a;++r)c=t[r],c>=n&&c<=s&&i.push(c);if(i.length<2)return[{time:n,pos:0},{time:s,pos:1}];for(r=0,a=i.length;r<a;++r)u=i[r+1],l=i[r-1],c=i[r],Math.round((u+l)/2)!==c&&o.push({time:c,pos:r/(a-1)});return o}_generate(){const t=this.min,n=this.max;let s=super.getDataTimestamps();return(!s.includes(t)||!s.length)&&s.splice(0,0,t),(!s.includes(n)||s.length===1)&&s.push(n),s.sort((i,o)=>i-o)}_getTimestampsForTable(){let t=this._cache.all||[];if(t.length)return t;const n=this.getDataTimestamps(),s=this.getLabelTimestamps();return n.length&&s.length?t=this.normalize(n.concat(s)):t=n.length?n:s,t=this._cache.all=t,t}getDecimalForValue(t){return(Ni(this._table,t)-this._minPos)/this._tableRange}getValueForPixel(t){const n=this._offsets,s=this.getDecimalForPixel(t)/n.factor-n.end;return Ni(this._table,s*this._tableRange+this._minPos,!0)}}N(pa,"id","timeseries"),N(pa,"defaults",fi.defaults);var yS=Object.freeze({__proto__:null,CategoryScale:ua,LinearScale:fa,LogarithmicScale:ha,RadialLinearScale:Is,TimeScale:fi,TimeSeriesScale:pa});const lM=[C0,tw,Xw,yS];export{Ta as A,CS as B,RS as C,SS as D,Rn as E,Qt as F,lM as G,DS as H,ES as I,HS as J,zS as K,IS as L,NS as M,hi as N,vS as O,TS as P,AS as T,Vt as a,wS as b,PS as c,Ot as d,BS as e,fe as f,kS as g,Nr as h,$p as i,Zf as j,MS as k,jS as l,Bg as m,xa as n,Ir as o,ns as p,Oo as q,Oa as r,LS as s,up as t,VS as u,xS as v,is as w,OS as x,FS as y,_a as z};
