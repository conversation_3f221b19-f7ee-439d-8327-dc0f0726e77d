<template>
  <header class="bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700">
    <div class="flex justify-between items-center px-4 py-4 sm:px-6 lg:px-8">
      <div class="flex items-center space-x-4">
        <!-- Mobile Menu Button -->
        <button
          @click="$emit('toggle-mobile-sidebar')"
          class="lg:hidden p-2 rounded-md text-gray-400 hover:text-gray-600 hover:bg-gray-100 dark:text-gray-500 dark:hover:text-gray-300 dark:hover:bg-gray-700"
        >
          <svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
          </svg>
        </button>

        <!-- Page Title and Breadcrumbs -->
        <div class="flex flex-col">
          <h2 class="text-lg font-semibold text-gray-900 dark:text-white">
            {{ pageTitle }}
          </h2>
          <HeaderBreadcrumbs v-if="breadcrumbs.length > 0" :breadcrumbs="breadcrumbs" />
        </div>
      </div>

      <div class="flex items-center space-x-4">
        <!-- Quick Actions -->
        <div class="hidden md:flex items-center space-x-2">
          <HeaderQuickActions
            @quick-create-project="$emit('quick-create-project')"
            @quick-add-task="$emit('quick-add-task')"
          />
        </div>

        <!-- Notifications -->
        <HeaderNotifications />

        <!-- Search -->
        <HeaderSearch />

        <!-- User Menu -->
        <HeaderUserMenu />
      </div>
    </div>
  </header>
</template>

<script setup>
import HeaderBreadcrumbs from './HeaderBreadcrumbs.vue'
import HeaderQuickActions from './HeaderQuickActions.vue'
import HeaderNotifications from './HeaderNotifications.vue'
import HeaderSearch from './HeaderSearch.vue'
import HeaderUserMenu from './HeaderUserMenu.vue'

defineProps({
  pageTitle: {
    type: String,
    required: true
  },
  breadcrumbs: {
    type: Array,
    default: () => []
  }
})

defineEmits(['toggle-mobile-sidebar', 'quick-create-project', 'quick-add-task'])
</script>