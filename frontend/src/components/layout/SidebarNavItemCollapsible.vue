<template>
  <div>
    <!-- Main Collapsible Button -->
    <button
      @click="toggleExpanded"
      class="group w-full flex items-center px-2 py-2 text-sm font-medium rounded-md transition-colors duration-150"
      :class="[
        navItemClasses,
        { 'justify-center': isCollapsed }
      ]"
    >
      <SidebarIcon
        :icon="item.icon"
        :class="[
          'flex-shrink-0 h-6 w-6',
          { 'mr-0': isCollapsed, 'mr-3': !isCollapsed }
        ]"
      />
      <span
        v-if="!isCollapsed"
        class="flex-1 text-left truncate"
      >
        {{ item.name }}
      </span>
      <svg
        v-if="!isCollapsed"
        :class="{ 'rotate-90': isExpanded }"
        class="ml-2 h-4 w-4 transition-transform duration-150"
        fill="none"
        viewBox="0 0 24 24"
        stroke="currentColor"
      >
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
      </svg>
    </button>

    <!-- Submenu Items -->
    <div
      v-if="isExpanded && !isCollapsed"
      class="ml-6 space-y-1 mt-1"
    >
      <router-link
        v-for="child in visibleChildren"
        :key="child.name"
        :to="child.path"
        :class="[
          'group flex items-center px-2 py-2 text-sm font-medium rounded-md transition-colors duration-150',
          child.path === '#'
            ? 'text-gray-400 dark:text-gray-500 hover:text-gray-500 dark:hover:text-gray-400 cursor-not-allowed opacity-75'
            : 'text-gray-600 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 hover:text-primary-600 dark:hover:text-primary-400'
        ]"
        active-class="text-primary-600 dark:text-primary-400 bg-primary-50 dark:bg-primary-900"
        @click="handleChildClick(child)"
      >
        <SidebarIcon
          v-if="child.icon"
          :icon="child.icon"
          class="flex-shrink-0 h-4 w-4 mr-2"
        />
        <span class="truncate">{{ child.name }}</span>
      </router-link>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { useRoute } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import SidebarIcon from './SidebarIcon.vue'

const props = defineProps({
  item: {
    type: Object,
    required: true
  },
  isCollapsed: {
    type: Boolean,
    default: false
  }
})

defineEmits(['click'])

const route = useRoute()
const authStore = useAuthStore()
const isExpanded = ref(false)

const navItemClasses = computed(() => [
  'text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 hover:text-primary-600 dark:hover:text-primary-400',
  {
    'text-primary-600 dark:text-primary-400 bg-primary-50 dark:bg-primary-900': isActiveParent.value
  }
])

const isActiveParent = computed(() => {
  if (!props.item.children) return false
  return props.item.children.some(child =>
    child.path !== '#' && route.path.startsWith(child.path)
  )
})

const visibleChildren = computed(() => {
  if (!props.item.children) return []

  return props.item.children.filter(child => {
    // Se l'elemento richiede permessi admin, verifica il ruolo utente
    if (child.admin) {
      return authStore.user?.role === 'admin'
    }
    return true
  })
})

// Auto-expand if any child is active
if (isActiveParent.value) {
  isExpanded.value = true
}

function toggleExpanded() {
  if (props.isCollapsed) {
    // Se la sidebar è collassata, espandi la sidebar prima
    return
  }
  isExpanded.value = !isExpanded.value
}

function handleChildClick(child) {
  if (child.path === '#') {
    // Prevent navigation for placeholder links
    return false
  }
}
</script>