<template>
  <div class="space-y-6">
    <div class="bg-white dark:bg-gray-800 shadow rounded-lg">
      <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
        <div class="flex items-center justify-between">
          <h3 class="text-lg font-medium text-gray-900 dark:text-white">
            Team del Progetto
          </h3>
          <button
            @click="showAddMemberModal = true"
            class="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
          >
            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
            </svg>
            Aggiungi Membro
          </button>
        </div>
      </div>

      <!-- Team Statistics -->
      <div class="p-6 border-b border-gray-200 dark:border-gray-700">
        <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div class="text-center">
            <div class="text-2xl font-bold text-primary-600 dark:text-primary-400">{{ teamMembers.length }}</div>
            <div class="text-sm text-gray-500 dark:text-gray-400">Membri Totali</div>
          </div>
          <div class="text-center">
            <div class="text-2xl font-bold text-green-600">{{ totalHoursWorked }}h</div>
            <div class="text-sm text-gray-500 dark:text-gray-400">Ore Totali</div>
          </div>
          <div class="text-center">
            <div class="text-2xl font-bold text-blue-600">{{ averageHoursPerMember }}h</div>
            <div class="text-sm text-gray-500 dark:text-gray-400">Media per Membro</div>
          </div>
          <div class="text-center">
            <div class="text-2xl font-bold text-purple-600">{{ activeMembersCount }}</div>
            <div class="text-sm text-gray-500 dark:text-gray-400">Membri Attivi</div>
          </div>
        </div>
      </div>

      <!-- Team Members List -->
      <div class="p-6">
        <div class="space-y-4">
          <div
            v-for="member in teamMembers"
            :key="member.id"
            class="border border-gray-200 dark:border-gray-700 rounded-lg p-4 hover:shadow-md transition-all duration-200"
          >
            <div class="flex items-center justify-between">
              <div class="flex items-center space-x-4">
                <div class="flex-shrink-0">
                  <img
                    v-if="member.profile_image"
                    :src="member.profile_image"
                    :alt="member.full_name"
                    class="w-12 h-12 rounded-full"
                  >
                  <div v-else class="w-12 h-12 bg-gray-300 dark:bg-gray-600 rounded-full flex items-center justify-center">
                    <span class="text-sm font-medium text-gray-600 dark:text-gray-300">{{ getInitials(member.full_name) }}</span>
                  </div>
                </div>
                <div class="flex-1">
                  <div class="flex items-center space-x-2">
                    <h4 class="text-lg font-medium text-gray-900 dark:text-white">{{ member.full_name }}</h4>
                    <span
                      v-if="member.id === project?.manager_id"
                      class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200"
                    >
                      Project Manager
                    </span>
                  </div>
                  <p class="text-sm text-gray-600 dark:text-gray-400">{{ member.role || 'Team Member' }}</p>
                  <p class="text-xs text-gray-500 dark:text-gray-500">{{ member.email }}</p>
                </div>
              </div>

              <div class="flex items-center space-x-4">
                <!-- Member stats -->
                <div class="text-right">
                  <div class="text-sm font-medium text-gray-900 dark:text-white">{{ formatHours(member.hours_worked || 0) }}h</div>
                  <div class="text-xs text-gray-500 dark:text-gray-400">ore lavorate</div>
                </div>
                <div class="text-right">
                  <div class="text-sm font-medium text-gray-900 dark:text-white">{{ getAssignedTasksCount(member.id) }}</div>
                  <div class="text-xs text-gray-500 dark:text-gray-400">task assegnati</div>
                </div>
                <div class="text-right">
                  <div class="text-sm font-medium text-gray-900 dark:text-white">{{ getCompletedTasksCount(member.id) }}</div>
                  <div class="text-xs text-gray-500 dark:text-gray-400">completati</div>
                </div>

                <!-- Actions -->
                <div class="flex items-center space-x-2">
                  <button
                    @click="editMember(member)"
                    class="p-1 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
                    title="Modifica membro"
                  >
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                    </svg>
                  </button>
                  <button
                    v-if="member.id !== project?.manager_id"
                    @click="removeMember(member)"
                    class="p-1 text-gray-400 hover:text-red-600"
                    title="Rimuovi dal progetto"
                  >
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                    </svg>
                  </button>
                </div>
              </div>
            </div>

            <!-- Productivity meter -->
            <div class="mt-4">
              <div class="flex items-center justify-between text-xs text-gray-500 dark:text-gray-400 mb-1">
                <span>Produttività</span>
                <span>{{ getProductivityPercentage(member.id) }}%</span>
              </div>
              <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                <div
                  class="h-2 rounded-full transition-all duration-300"
                  :class="getProductivityColor(member.id)"
                  :style="{ width: getProductivityPercentage(member.id) + '%' }"
                ></div>
              </div>
            </div>
          </div>

          <!-- Empty state -->
          <div v-if="teamMembers.length === 0" class="text-center py-8">
            <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
            </svg>
            <h3 class="mt-2 text-sm font-medium text-gray-900 dark:text-white">Nessun membro del team</h3>
            <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">Inizia aggiungendo membri al progetto.</p>
            <div class="mt-6">
              <button
                @click="showAddMemberModal = true"
                class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700"
              >
                <svg class="-ml-1 mr-2 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                </svg>
                Aggiungi primo membro
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Add Member Modal -->
    <div v-if="showAddMemberModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50" @click="closeAddMemberModal">
      <div class="relative top-20 mx-auto p-5 border w-[500px] shadow-lg rounded-md bg-white dark:bg-gray-800" @click.stop>
        <div class="mt-3">
          <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">
            Aggiungi Membro al Team
          </h3>

          <form @submit.prevent="addMember">
            <div class="space-y-4">
              <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Utente</label>
                <select
                  v-model="newMemberForm.user_id"
                  required
                  class="w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"
                >
                  <option value="">Seleziona utente</option>
                  <option
                    v-for="user in availableUsers"
                    :key="user.id"
                    :value="user.id"
                  >
                    {{ user.full_name }} ({{ user.email }})
                  </option>
                </select>
              </div>

              <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Ruolo</label>
                <select
                  v-model="newMemberForm.role"
                  required
                  class="w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"
                >
                  <option value="">Seleziona ruolo</option>
                  <option value="Team Member">Team Member</option>
                  <option value="Developer">Developer</option>
                  <option value="Designer">Designer</option>
                  <option value="QA Tester">QA Tester</option>
                  <option value="Business Analyst">Business Analyst</option>
                  <option value="Technical Lead">Technical Lead</option>
                </select>
              </div>
            </div>

            <div class="flex justify-end space-x-3 mt-6">
              <button
                type="button"
                @click="closeAddMemberModal"
                class="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-200 hover:bg-gray-300 rounded-md"
              >
                Annulla
              </button>
              <button
                type="submit"
                :disabled="adding"
                class="px-4 py-2 text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 rounded-md disabled:opacity-50"
              >
                {{ adding ? 'Aggiungendo...' : 'Aggiungi' }}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import { useAuthStore } from '@/stores/auth'

const props = defineProps({
  project: {
    type: Object,
    default: null
  },
  loading: {
    type: Boolean,
    default: false
  }
})

const authStore = useAuthStore()

// State
const showAddMemberModal = ref(false)
const availableUsers = ref([])
const adding = ref(false)
const newMemberForm = ref({
  user_id: '',
  role: ''
})

// Computed
const teamMembers = computed(() => {
  return props.project?.team_members || []
})

const totalHoursWorked = computed(() => {
  return teamMembers.value.reduce((total, member) => total + (member.hours_worked || 0), 0)
})

const averageHoursPerMember = computed(() => {
  if (teamMembers.value.length === 0) return 0
  return Math.round(totalHoursWorked.value / teamMembers.value.length)
})

const activeMembersCount = computed(() => {
  return teamMembers.value.filter(member => (member.hours_worked || 0) > 0).length
})

// Methods
const getInitials = (fullName) => {
  if (!fullName) return '??'
  return fullName
    .split(' ')
    .map(name => name.charAt(0).toUpperCase())
    .slice(0, 2)
    .join('')
}

const getAssignedTasksCount = (memberId) => {
  const tasks = props.project?.tasks || []
  return tasks.filter(task => task.assignee_id === memberId).length
}

const getCompletedTasksCount = (memberId) => {
  const tasks = props.project?.tasks || []
  return tasks.filter(task => task.assignee_id === memberId && task.status === 'done').length
}

const getProductivityPercentage = (memberId) => {
  const assigned = getAssignedTasksCount(memberId)
  const completed = getCompletedTasksCount(memberId)
  if (assigned === 0) return 0
  return Math.round((completed / assigned) * 100)
}

const getProductivityColor = (memberId) => {
  const percentage = getProductivityPercentage(memberId)
  if (percentage >= 80) return 'bg-green-600'
  if (percentage >= 60) return 'bg-yellow-600'
  if (percentage >= 40) return 'bg-orange-600'
  return 'bg-red-600'
}

const formatHours = (hours) => {
  if (!hours || hours === 0) return '0.00'
  return parseFloat(hours).toFixed(2)
}

const loadAvailableUsers = async () => {
  try {
    const response = await fetch('/api/personnel/users', {
      headers: {
        'Content-Type': 'application/json',
        'X-CSRFToken': authStore.csrfToken
      }
    })

    if (response.ok) {
      const result = await response.json()
      // Filtra utenti già nel team
      const currentMemberIds = teamMembers.value.map(m => m.id)
      availableUsers.value = result.data?.users ? result.data.users.filter(user => !currentMemberIds.includes(user.id)) : []
    }
  } catch (error) {
    console.error('Errore nel caricamento utenti:', error)
    availableUsers.value = []
  }
}

const addMember = async () => {
  adding.value = true

  try {
    const response = await fetch(`/api/projects/${props.project.id}/team`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-CSRFToken': authStore.csrfToken
      },
      body: JSON.stringify(newMemberForm.value)
    })

    if (response.ok) {
      // Ricarica progetto per aggiornare team
      emit('refresh')
      closeAddMemberModal()
    } else {
      const error = await response.json()
      alert(error.message || 'Errore nell\'aggiunta del membro')
    }
  } catch (error) {
    alert('Errore nell\'aggiunta del membro')
  } finally {
    adding.value = false
  }
}

const editMember = (member) => {
  // TODO: Implementare modifica membro
  console.log('Edit member:', member)
}

const removeMember = async (member) => {
  if (!confirm(`Rimuovere ${member.full_name} dal progetto?`)) return

  try {
    const response = await fetch(`/api/projects/${props.project.id}/team/${member.id}`, {
      method: 'DELETE',
      headers: {
        'Content-Type': 'application/json',
        'X-CSRFToken': authStore.csrfToken
      }
    })

    if (response.ok) {
      emit('refresh')
    } else {
      const error = await response.json()
      alert(error.message || 'Errore nella rimozione del membro')
    }
  } catch (error) {
    alert('Errore nella rimozione del membro')
  }
}

const closeAddMemberModal = () => {
  showAddMemberModal.value = false
  newMemberForm.value = {
    user_id: '',
    role: ''
  }
}

// Events
const emit = defineEmits(['refresh'])

// Lifecycle
onMounted(() => {
  loadAvailableUsers()
})

// Watchers
watch(() => showAddMemberModal.value, (newVal) => {
  if (newVal) {
    loadAvailableUsers()
  }
})

watch(() => props.project?.team_members, () => {
  if (showAddMemberModal.value) {
    loadAvailableUsers()
  }
})

// Expose methods
defineExpose({
  refresh: loadAvailableUsers
})
</script>