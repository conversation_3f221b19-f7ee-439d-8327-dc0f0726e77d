<template>
  <div class="max-w-4xl mx-auto">
    <div class="bg-white shadow rounded-lg">
      <div class="px-6 py-4 border-b border-gray-200">
        <h1 class="text-xl font-semibold text-gray-900">Il tuo Profilo</h1>
        <p class="mt-1 text-sm text-gray-600">Gestisci le informazioni del tuo account</p>
      </div>

      <div v-if="loading" class="flex items-center justify-center h-64">
        <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
      </div>

      <form v-else @submit.prevent="saveProfile" class="p-6 space-y-6">
        <!-- Avatar e Info Base -->
        <div class="flex items-center space-x-6">
          <div class="flex-shrink-0">
            <div class="h-20 w-20 rounded-full bg-primary-100 flex items-center justify-center">
              <span class="text-2xl font-medium text-primary-700">
                {{ userInitials }}
              </span>
            </div>
          </div>
          <div class="flex-1">
            <h3 class="text-lg font-medium text-gray-900">{{ user?.username }}</h3>
            <p class="text-sm text-gray-500">{{ user?.email }}</p>
            <p class="text-sm text-gray-500">Ruolo: {{ roleLabel }}</p>
          </div>
        </div>

        <!-- Informazioni Personali -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label for="first_name" class="block text-sm font-medium text-gray-700">Nome</label>
            <input
              id="first_name"
              v-model="form.first_name"
              type="text"
              class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
            />
          </div>
          <div>
            <label for="last_name" class="block text-sm font-medium text-gray-700">Cognome</label>
            <input
              id="last_name"
              v-model="form.last_name"
              type="text"
              class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
            />
          </div>
        </div>

        <!-- Email e Telefono -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label for="email" class="block text-sm font-medium text-gray-700">Email</label>
            <input
              id="email"
              v-model="form.email"
              type="email"
              class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
            />
          </div>
          <div>
            <label for="phone" class="block text-sm font-medium text-gray-700">Telefono</label>
            <input
              id="phone"
              v-model="form.phone"
              type="tel"
              class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
            />
          </div>
        </div>

        <!-- Posizione e Dipartimento -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label for="position" class="block text-sm font-medium text-gray-700">Posizione</label>
            <input
              id="position"
              v-model="form.position"
              type="text"
              class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
            />
          </div>
          <div>
            <label for="department" class="block text-sm font-medium text-gray-700">Dipartimento</label>
            <input
              id="department"
              v-model="form.department"
              type="text"
              readonly
              class="mt-1 block w-full rounded-md border-gray-300 bg-gray-50 shadow-sm"
            />
          </div>
        </div>

        <!-- Bio -->
        <div>
          <label for="bio" class="block text-sm font-medium text-gray-700">Bio</label>
          <textarea
            id="bio"
            v-model="form.bio"
            rows="3"
            class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
            placeholder="Racconta qualcosa di te..."
          ></textarea>
        </div>

        <!-- Preferenze -->
        <div class="border-t border-gray-200 pt-6">
          <h3 class="text-lg font-medium text-gray-900 mb-4">Preferenze</h3>
          <div class="flex items-center">
            <input
              id="dark_mode"
              v-model="form.dark_mode"
              type="checkbox"
              class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
            />
            <label for="dark_mode" class="ml-2 block text-sm text-gray-900">
              Modalità scura
            </label>
          </div>
        </div>

        <!-- Azioni -->
        <div class="flex justify-end space-x-3 pt-6 border-t border-gray-200">
          <button
            type="button"
            @click="resetForm"
            class="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
          >
            Ripristina
          </button>
          <button
            type="submit"
            :disabled="saving"
            class="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50"
          >
            {{ saving ? 'Salvataggio...' : 'Salva Profilo' }}
          </button>
        </div>
      </form>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useAuthStore } from '@/stores/auth'

const authStore = useAuthStore()

// State
const loading = ref(true)
const saving = ref(false)
const user = ref(null)
const form = ref({
  first_name: '',
  last_name: '',
  email: '',
  phone: '',
  position: '',
  department: '',
  bio: '',
  dark_mode: false
})

// Computed
const userInitials = computed(() => {
  if (!user.value) return 'U'
  const firstName = user.value.first_name || ''
  const lastName = user.value.last_name || ''
  return (firstName.charAt(0) + lastName.charAt(0)).toUpperCase() || user.value.username?.charAt(0).toUpperCase() || 'U'
})

const roleLabel = computed(() => {
  const roles = {
    admin: 'Amministratore',
    manager: 'Manager',
    employee: 'Dipendente',
    human_resources: 'Risorse Umane',
    sales: 'Vendite'
  }
  return roles[user.value?.role] || user.value?.role || 'Utente'
})

// Methods
const loadProfile = async () => {
  try {
    // Usa i dati dell'authStore se disponibili
    if (authStore.user) {
      user.value = authStore.user
      populateForm()
    } else {
      // Altrimenti carica dal server
      const response = await fetch('/api/auth/profile', {
        headers: {
          'Content-Type': 'application/json',
          'X-CSRFToken': authStore.csrfToken
        }
      })
      
      if (response.ok) {
        const result = await response.json()
        user.value = result.data.user
        populateForm()
      }
    }
  } catch (error) {
    console.error('Error loading profile:', error)
  } finally {
    loading.value = false
  }
}

const populateForm = () => {
  if (user.value) {
    form.value = {
      first_name: user.value.first_name || '',
      last_name: user.value.last_name || '',
      email: user.value.email || '',
      phone: user.value.phone || '',
      position: user.value.position || '',
      department: user.value.department || '',
      bio: user.value.bio || '',
      dark_mode: user.value.dark_mode || false
    }
  }
}

const resetForm = () => {
  populateForm()
}

const saveProfile = async () => {
  saving.value = true
  
  try {
    const response = await fetch('/api/auth/profile', {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
        'X-CSRFToken': authStore.csrfToken
      },
      body: JSON.stringify(form.value)
    })

    if (!response.ok) {
      throw new Error('Errore nel salvataggio del profilo')
    }

    const result = await response.json()
    user.value = result.data.user
    
    // Aggiorna anche l'authStore
    await authStore.refreshUser()
    
    alert('Profilo aggiornato con successo!')
  } catch (error) {
    console.error('Error saving profile:', error)
    alert('Errore nel salvataggio del profilo')
  } finally {
    saving.value = false
  }
}

// Lifecycle
onMounted(() => {
  loadProfile()
})
</script>
