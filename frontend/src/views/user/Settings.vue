<template>
  <div class="max-w-4xl mx-auto space-y-6">
    <!-- Impostazioni Account -->
    <div class="bg-white shadow rounded-lg">
      <div class="px-6 py-4 border-b border-gray-200">
        <h2 class="text-lg font-semibold text-gray-900">Impostazioni Account</h2>
        <p class="mt-1 text-sm text-gray-600">Gestisci le impostazioni del tuo account</p>
      </div>

      <div class="p-6 space-y-6">
        <!-- Cambio Password -->
        <div>
          <h3 class="text-md font-medium text-gray-900 mb-4">Cambia Password</h3>
          <form @submit.prevent="changePassword" class="space-y-4">
            <div>
              <label for="current_password" class="block text-sm font-medium text-gray-700">Password Attuale</label>
              <input
                id="current_password"
                v-model="passwordForm.current_password"
                type="password"
                required
                class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
              />
            </div>
            <div>
              <label for="new_password" class="block text-sm font-medium text-gray-700">Nuova Password</label>
              <input
                id="new_password"
                v-model="passwordForm.new_password"
                type="password"
                required
                minlength="8"
                class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
              />
            </div>
            <div>
              <label for="confirm_password" class="block text-sm font-medium text-gray-700">Conferma Password</label>
              <input
                id="confirm_password"
                v-model="passwordForm.confirm_password"
                type="password"
                required
                class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
              />
            </div>
            <button
              type="submit"
              :disabled="changingPassword"
              class="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50"
            >
              {{ changingPassword ? 'Aggiornamento...' : 'Cambia Password' }}
            </button>
          </form>
        </div>
      </div>
    </div>

    <!-- Preferenze Interfaccia -->
    <div class="bg-white shadow rounded-lg">
      <div class="px-6 py-4 border-b border-gray-200">
        <h2 class="text-lg font-semibold text-gray-900">Preferenze Interfaccia</h2>
        <p class="mt-1 text-sm text-gray-600">Personalizza l'aspetto dell'applicazione</p>
      </div>

      <div class="p-6 space-y-6">
        <!-- Tema -->
        <div>
          <h3 class="text-md font-medium text-gray-900 mb-4">Tema</h3>
          <div class="space-y-3">
            <div class="flex items-center">
              <input
                id="theme_light"
                v-model="preferences.theme"
                type="radio"
                value="light"
                class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300"
              />
              <label for="theme_light" class="ml-2 block text-sm text-gray-900">
                🌞 Modalità Chiara
              </label>
            </div>
            <div class="flex items-center">
              <input
                id="theme_dark"
                v-model="preferences.theme"
                type="radio"
                value="dark"
                class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300"
              />
              <label for="theme_dark" class="ml-2 block text-sm text-gray-900">
                🌙 Modalità Scura
              </label>
            </div>
            <div class="flex items-center">
              <input
                id="theme_auto"
                v-model="preferences.theme"
                type="radio"
                value="auto"
                class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300"
              />
              <label for="theme_auto" class="ml-2 block text-sm text-gray-900">
                🔄 Automatico (segue il sistema)
              </label>
            </div>
          </div>
        </div>

        <!-- Lingua -->
        <div>
          <h3 class="text-md font-medium text-gray-900 mb-4">Lingua</h3>
          <select
            v-model="preferences.language"
            class="block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
          >
            <option value="it">🇮🇹 Italiano</option>
            <option value="en">🇺🇸 English</option>
            <option value="fr">🇫🇷 Français</option>
            <option value="de">🇩🇪 Deutsch</option>
          </select>
        </div>

        <!-- Sidebar -->
        <div>
          <h3 class="text-md font-medium text-gray-900 mb-4">Sidebar</h3>
          <div class="flex items-center">
            <input
              id="sidebar_collapsed"
              v-model="preferences.sidebar_collapsed"
              type="checkbox"
              class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
            />
            <label for="sidebar_collapsed" class="ml-2 block text-sm text-gray-900">
              Mantieni sidebar collassata di default
            </label>
          </div>
        </div>
      </div>
    </div>

    <!-- Notifiche -->
    <div class="bg-white shadow rounded-lg">
      <div class="px-6 py-4 border-b border-gray-200">
        <h2 class="text-lg font-semibold text-gray-900">Notifiche</h2>
        <p class="mt-1 text-sm text-gray-600">Gestisci le tue preferenze di notifica</p>
      </div>

      <div class="p-6 space-y-6">
        <div class="space-y-4">
          <div class="flex items-center justify-between">
            <div>
              <h4 class="text-sm font-medium text-gray-900">Notifiche Email</h4>
              <p class="text-sm text-gray-500">Ricevi notifiche via email</p>
            </div>
            <input
              v-model="notifications.email"
              type="checkbox"
              class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
            />
          </div>

          <div class="flex items-center justify-between">
            <div>
              <h4 class="text-sm font-medium text-gray-900">Notifiche Progetti</h4>
              <p class="text-sm text-gray-500">Aggiornamenti sui progetti a cui partecipi</p>
            </div>
            <input
              v-model="notifications.projects"
              type="checkbox"
              class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
            />
          </div>

          <div class="flex items-center justify-between">
            <div>
              <h4 class="text-sm font-medium text-gray-900">Notifiche Task</h4>
              <p class="text-sm text-gray-500">Quando ti vengono assegnati nuovi task</p>
            </div>
            <input
              v-model="notifications.tasks"
              type="checkbox"
              class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
            />
          </div>

          <div class="flex items-center justify-between">
            <div>
              <h4 class="text-sm font-medium text-gray-900">Notifiche Sistema</h4>
              <p class="text-sm text-gray-500">Aggiornamenti e manutenzioni del sistema</p>
            </div>
            <input
              v-model="notifications.system"
              type="checkbox"
              class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
            />
          </div>
        </div>
      </div>
    </div>

    <!-- Azioni -->
    <div class="flex justify-end space-x-3">
      <button
        @click="resetSettings"
        class="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
      >
        Ripristina Default
      </button>
      <button
        @click="saveSettings"
        :disabled="saving"
        class="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50"
      >
        {{ saving ? 'Salvataggio...' : 'Salva Impostazioni' }}
      </button>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useAuthStore } from '@/stores/auth'
import { useDarkMode } from '@/composables/useDarkMode'

const authStore = useAuthStore()
const { isDarkMode, toggleDarkMode } = useDarkMode()

// State
const saving = ref(false)
const changingPassword = ref(false)

const passwordForm = ref({
  current_password: '',
  new_password: '',
  confirm_password: ''
})

const preferences = ref({
  theme: 'light',
  language: 'it',
  sidebar_collapsed: false
})

const notifications = ref({
  email: true,
  projects: true,
  tasks: true,
  system: false
})

// Methods
const loadSettings = async () => {
  try {
    const response = await fetch('/api/auth/settings', {
      headers: {
        'Content-Type': 'application/json',
        'X-CSRFToken': authStore.csrfToken
      }
    })
    
    if (response.ok) {
      const result = await response.json()
      const settings = result.data.settings
      
      preferences.value = {
        theme: settings.theme || 'light',
        language: settings.language || 'it',
        sidebar_collapsed: settings.sidebar_collapsed || false
      }
      
      notifications.value = {
        email: settings.notifications?.email !== false,
        projects: settings.notifications?.projects !== false,
        tasks: settings.notifications?.tasks !== false,
        system: settings.notifications?.system || false
      }
    }
  } catch (error) {
    console.error('Error loading settings:', error)
  }
}

const saveSettings = async () => {
  saving.value = true
  
  try {
    const response = await fetch('/api/auth/settings', {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
        'X-CSRFToken': authStore.csrfToken
      },
      body: JSON.stringify({
        preferences: preferences.value,
        notifications: notifications.value
      })
    })

    if (!response.ok) {
      throw new Error('Errore nel salvataggio delle impostazioni')
    }

    alert('Impostazioni salvate con successo!')
  } catch (error) {
    console.error('Error saving settings:', error)
    alert('Errore nel salvataggio delle impostazioni')
  } finally {
    saving.value = false
  }
}

const changePassword = async () => {
  if (passwordForm.value.new_password !== passwordForm.value.confirm_password) {
    alert('Le password non coincidono')
    return
  }
  
  changingPassword.value = true
  
  try {
    const response = await fetch('/api/auth/change-password', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-CSRFToken': authStore.csrfToken
      },
      body: JSON.stringify({
        current_password: passwordForm.value.current_password,
        new_password: passwordForm.value.new_password
      })
    })

    if (!response.ok) {
      throw new Error('Errore nel cambio password')
    }

    alert('Password cambiata con successo!')
    passwordForm.value = {
      current_password: '',
      new_password: '',
      confirm_password: ''
    }
  } catch (error) {
    console.error('Error changing password:', error)
    alert('Errore nel cambio password')
  } finally {
    changingPassword.value = false
  }
}

const resetSettings = () => {
  preferences.value = {
    theme: 'light',
    language: 'it',
    sidebar_collapsed: false
  }
  
  notifications.value = {
    email: true,
    projects: true,
    tasks: true,
    system: false
  }
}

// Lifecycle
onMounted(() => {
  loadSettings()
})
</script>
