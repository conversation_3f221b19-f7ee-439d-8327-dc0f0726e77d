# Personnel/HR Module - Vue.js Migration Plan

## Analisi Sistema Legacy

### ✅ Funzionalità Esistenti Identificate

**1. Personnel Management (Core)**
- Lista dipendenti con filtri (dipartimento, competenze, ricerca)
- Profilo dipendente dettagliato con tab (progetti, task, competenze, timesheet, CV)
- Modifica profilo con informazioni HR estese
- Directory aziendale con ricerca avanzata
- Gestione competenze (skills) con livelli di proficiency

**2. Department Management**
- Lista dipartimenti con gerarchia
- Dashboard dipartimento con statistiche
- Creazione/modifica/eliminazione dipartimenti
- Gestione dipendenti per dipartimento
- Trasferimento dipendenti tra dipartimenti

**3. Organization Chart**
- Visualizzazione organigramma aziendale
- Struttura gerarchica dipartimenti
- Filtri per dipartimento e ruolo

**4. Skills Management**
- Matrice competenze (utenti vs skills)
- Gestione competenze individuali
- Livelli di proficiency (1-5)
- Certificazioni e anni di esperienza

### 📋 Modelli Dati Esistenti

**User Model (già disponibile):**
- Campi base: username, email, first_name, last_name, role
- Campi HR: department_id, position, hire_date, phone, bio
- Relazioni: department_obj, detailed_skills, profile

**UserProfile Model (già disponibile):**
- employee_id, job_title, birth_date, address
- emergency_contact_name, emergency_contact_phone
- employment_type, work_location, weekly_hours
- profile_completion (calcolato)

**Department Model (già disponibile):**
- name, description, parent_id, manager_id, budget
- Relazioni: parent, subdepartments, employees, manager

**Skill & UserSkill Models (già disponibili):**
- Skill: name, category, description
- UserSkill: proficiency_level, years_experience, is_certified

## Piano di Migrazione Vue.js

### 🎯 Fase 1: Struttura Base e Routing

**1.1 Routing Vue.js**
```javascript
// Aggiungi a router/index.js
{
  path: '/app/personnel',
  component: () => import('@/views/personnel/PersonnelLayout.vue'),
  children: [
    { path: '', name: 'PersonnelList', component: () => import('@/views/personnel/PersonnelList.vue') },
    { path: 'directory', name: 'PersonnelDirectory', component: () => import('@/views/personnel/PersonnelDirectory.vue') },
    { path: 'orgchart', name: 'PersonnelOrgChart', component: () => import('@/views/personnel/PersonnelOrgChart.vue') },
    { path: 'departments', name: 'DepartmentList', component: () => import('@/views/personnel/DepartmentList.vue') },
    { path: 'departments/create', name: 'DepartmentCreate', component: () => import('@/views/personnel/DepartmentCreate.vue') },
    { path: 'departments/:id', name: 'DepartmentView', component: () => import('@/views/personnel/DepartmentView.vue') },
    { path: 'departments/:id/edit', name: 'DepartmentEdit', component: () => import('@/views/personnel/DepartmentEdit.vue') },
    { path: 'skills', name: 'SkillsMatrix', component: () => import('@/views/personnel/SkillsMatrix.vue') },
    { path: ':id', name: 'PersonnelProfile', component: () => import('@/views/personnel/PersonnelProfile.vue') }
  ]
}
```

**1.2 Store Pinia**
```javascript
// stores/personnel.js
export const usePersonnelStore = defineStore('personnel', {
  state: () => ({
    users: [],
    departments: [],
    skills: [],
    currentUser: null,
    loading: false,
    filters: {
      search: '',
      department: null,
      skill: null,
      role: null
    }
  }),
  actions: {
    async fetchUsers(params = {}),
    async fetchUser(id),
    async updateUser(id, data),
    async fetchDepartments(),
    async createDepartment(data),
    async updateDepartment(id, data),
    async deleteDepartment(id),
    async fetchSkills(),
    async updateUserSkill(userId, skillData)
  }
})
```

### 🎯 Fase 2: Componenti Core

**2.1 PersonnelList.vue (Isofunctional Port)**
- Grid layout 3 colonne responsive
- Filtri: dipartimento, competenze, ricerca
- Card dipendente con avatar, info base, competenze
- Paginazione
- Azioni: profilo, telefono, email

**2.2 PersonnelProfile.vue (Isofunctional Port)**
- Layout 2 colonne: info personali + tab attività
- Tab: Progetti, Task, Competenze, Timesheet, CV
- Gestione competenze con livelli proficiency
- Informazioni HR estese
- Progress bar completamento profilo

**2.3 PersonnelDirectory.vue**
- Lista compatta con ricerca avanzata
- Filtri: dipartimento, ruolo, location
- Ordinamento multiplo
- Export funzionalità

### 🎯 Fase 3: Department Management

**3.1 DepartmentList.vue**
- Tabella dipartimenti con gerarchia
- Statistiche: totale dipartimenti, manager, dipendenti
- Filtri: ricerca, livello gerarchia
- Azioni: dashboard, modifica, elimina

**3.2 DepartmentView.vue**
- Dashboard dipartimento con KPI
- Lista dipendenti del dipartimento
- Sottodepartimenti
- Distribuzione competenze
- Gestione dipendenti (assegna/trasferisci)

**3.3 DepartmentCreate/Edit.vue**
- Form completo dipartimento
- Selezione parent department
- Assegnazione manager
- Budget e descrizione

### 🎯 Fase 4: Organization Chart & Skills

**4.1 PersonnelOrgChart.vue**
- Visualizzazione albero gerarchico
- Componenti ricorsivi per nodi
- Filtri dipartimento e ruolo
- Statistiche organizzazione

**4.2 SkillsMatrix.vue**
- Tabella utenti vs competenze
- Heatmap livelli proficiency
- Filtri categoria e dipartimento
- Export matrice competenze

## API Endpoints da Implementare

### ✅ Già Disponibili
- `GET /api/personnel/users` - Lista utenti con filtri
- `GET /api/personnel/users/:id` - Dettaglio utente
- `PUT /api/personnel/users/:id` - Aggiorna utente

### 🔄 Da Implementare/Estendere
- `GET /api/personnel/departments` - Lista dipartimenti
- `POST /api/personnel/departments` - Crea dipartimento
- `PUT /api/personnel/departments/:id` - Aggiorna dipartimento
- `DELETE /api/personnel/departments/:id` - Elimina dipartimento
- `GET /api/personnel/departments/:id/employees` - Dipendenti dipartimento
- `POST /api/personnel/departments/:id/transfer` - Trasferisci dipendente
- `GET /api/personnel/skills` - Lista competenze
- `POST /api/personnel/users/:id/skills` - Aggiungi competenza
- `PUT /api/personnel/users/:id/skills/:skillId` - Aggiorna competenza
- `DELETE /api/personnel/users/:id/skills/:skillId` - Rimuovi competenza
- `GET /api/personnel/orgchart` - Dati organigramma
- `GET /api/personnel/skills-matrix` - Matrice competenze

## Priorità Implementazione

### 🚀 Sprint 1 (Alta Priorità)
1. PersonnelList.vue - Lista dipendenti base
2. PersonnelProfile.vue - Profilo dipendente
3. Store personnel.js - Gestione stato
4. API departments base

### 🚀 Sprint 2 (Media Priorità)
1. DepartmentList.vue - Gestione dipartimenti
2. DepartmentView.vue - Dashboard dipartimento
3. PersonnelDirectory.vue - Directory aziendale

### 🚀 Sprint 3 (Bassa Priorità)
1. PersonnelOrgChart.vue - Organigramma
2. SkillsMatrix.vue - Matrice competenze
3. Funzionalità avanzate (CV, export, etc.)

## Note Tecniche

**Mantenimento Parità Funzionale:**
- Stessi filtri e ordinamenti del legacy
- Stessa struttura dati e validazioni
- Stessi permessi RBAC
- Stessa UX e layout responsive

**Miglioramenti Vue.js:**
- SPA navigation senza reload
- Caching intelligente nello store
- Componenti riutilizzabili
- Performance ottimizzate
